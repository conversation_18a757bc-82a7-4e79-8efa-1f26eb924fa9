{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "paths": {"~/*": ["./src/*"], "~assets/*": ["./assets/*"], "@squads/utils/*": ["./src/vendor/squads/utils/*"], "@squads/models/*": ["./src/vendor/squads/models/*"]}, "jsx": "preserve"}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "node_modules/react-native-reanimated/src/reanimated2/globals.d.ts", "src/types/**/*.d.ts"]}