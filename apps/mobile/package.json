{"name": "mobile", "version": "1.0.33", "main": "index.js", "private": true, "installConfig": {"hoistingLimits": "workspaces"}, "scripts": {"prebuild": "expo prebuild --platform ios", "dev": "EXPO_PUBLIC_LOCAL_API_URL=http://$(ipconfig getifaddr en0):4444 expo start", "build:dev": "yarn eas build --platform ios --profile development", "build:devsim": "yarn eas build --platform ios --profile development-simulator", "build:staging": "ENVIRONMENT=staging yarn eas build --profile staging --platform ios", "production": "expo start --no-dev --minify", "xcode": "open -a Xcode ios", "simulator": "EXPO_PUBLIC_LOCAL_API_URL=http://$(ipconfig getifaddr en0):4444 expo run:ios", "device": "EXPO_PUBLIC_LOCAL_API_URL=http://$(ipconfig getifaddr en0):4444 expo run:ios --device", "test": "jest --watchAll", "ts": "tsc --noEmit", "android": "expo run:android", "ios": "expo run:ios", "eas": "npx eas-cli@latest"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@dev-plugins/react-navigation": "0.3.1", "@dev-plugins/react-query": "0.3.1", "@ethersproject/shims": "5.7.0", "@gorhom/portal": "1.0.14", "@keystonehq/keystone-sdk": "0.9.0", "@ledgerhq/hw-app-solana": "7.2.4", "@ledgerhq/react-native-hw-transport-ble": "6.33.4", "@magic-ext/solana": "25.0.2", "@magic-sdk/react-native-expo": "29.0.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "11.4.1", "@react-native-masked-view/masked-view": "0.3.2", "@react-native-menu/menu": "0.9.1", "@react-native-picker/picker": "2.11.0", "@react-navigation/core": "7.9.2", "@react-navigation/elements": "2.4.2", "@react-navigation/native": "7.1.9", "@react-three/drei": "10.0.7", "@react-three/fiber": "9.1.2", "@react-three/postprocessing": "3.0.4", "@sentry/react-native": "6.10.0", "@shopify/react-native-skia": "v2.0.0-next.4", "@solana/spl-token": "0.4.6", "@solana/web3.js": "*", "@sqds/expo-fuse-app-attest": "*", "@sqds/expo-fuse-keyring": "*", "@sqds/expo-fuse-push-provisioning": "*", "@tanstack/query-async-storage-persister": "5.80.10", "@tanstack/react-query": "5.80.10", "@tanstack/react-query-persist-client": "5.80.10", "@turnkey/api-key-stamper": "^0.4.0", "@turnkey/crypto": "^0.1.1", "@turnkey/encoding": "^0.2.0", "@turnkey/http": "^2.10.0", "bs58": "5.0.0", "buffer": "6.0.3", "crypto-browserify": "3.12.1", "expo": "53.0.9", "expo-alternate-app-icons": "^1.3.1", "expo-application": "6.1.4", "expo-blur": "14.1.4", "expo-build-properties": "0.14.6", "expo-clipboard": "7.1.4", "expo-constants": "17.1.6", "expo-crypto": "14.1.4", "expo-dev-client": "5.1.8", "expo-device": "7.1.4", "expo-file-system": "18.1.10", "expo-font": "13.3.1", "expo-gl": "15.1.5", "expo-haptics": "14.1.4", "expo-image": "2.1.7", "expo-insights": "0.9.3", "expo-linear-gradient": "14.1.4", "expo-linking": "7.1.5", "expo-local-authentication": "16.0.4", "expo-notifications": "0.31.2", "expo-router": "5.0.6", "expo-secure-store": "14.2.3", "expo-sharing": "13.1.5", "expo-splash-screen": "0.30.8", "expo-status-bar": "2.2.3", "expo-symbols": "0.4.4", "expo-updates": "0.28.13", "expo-video-thumbnails": "9.1.3", "formik": "2.4.5", "ibantools": "^4.5.1", "invariant": "2.2.4", "luxon": "3.4.4", "nanoid": "5.0.9", "node-libs-react-native": "1.2.1", "react": "*", "react-dom": "*", "react-error-boundary": "4.0.12", "react-native": "0.79.2", "react-native-ble-plx": "*", "react-native-confirmation-code-field": "7.3.2", "react-native-flash-message": "0.4.2", "react-native-gesture-handler": "2.24.0", "react-native-get-random-values": "1.11.0", "react-native-graph": "1.1.0", "react-native-keyboard-controller": "^1.15.0", "react-native-persona": "2.9.6", "react-native-qrcode-styled": "0.2.2", "react-native-reanimated": "3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "4.10.0", "react-native-sfsymbols": "1.2.1", "react-native-svg": "15.11.2", "react-native-vision-camera": "4.5.3", "react-native-webview": "13.13.5", "react-transition-group": "^4.4.5", "readable-stream": "4.5.2", "text-encoding": "0.7.0", "three": "0.164.1", "tweetnacl": "1.0.3", "uuid": "11.0.3", "victory-native": "^41.16.0", "zod": "3.22.4", "zod-formik-adapter": "1.2.0", "zustand": "4.5.7"}, "devDependencies": {"@babel/core": "^7.25.2", "@eslint/js": "^9.20.0", "@types/invariant": "2.2.37", "@types/luxon": "3.4.2", "@types/react": "19.0.14", "@types/react-transition-group": "^4.4.10", "@types/three": "0.164.0", "@types/url-parse": "1.4.11", "eas-build-cache-provider": "^16.4.2", "eslint": "^9.20.1", "eslint-plugin-react": "^7.37.4", "globals": "^15.15.0", "jest": "29.7.0", "jest-expo": "53.0.5", "typescript": "*", "typescript-eslint": "^8.24.0"}, "overrides": {"react-refresh": "0.14.0"}, "expo": {"autolinking": {"nativeModulesDir": "../../packages"}, "doctor": {"listUnknownPackages": false}}}