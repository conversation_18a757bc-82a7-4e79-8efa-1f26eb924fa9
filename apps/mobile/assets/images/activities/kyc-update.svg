<svg width="130" height="151" viewBox="0 0 130 151" fill="none" xmlns="http://www.w3.org/2000/svg">
<path opacity="0.1" d="M25.584 120.108V29.3962C25.584 27.0294 27.1468 25.4819 29.5371 25.4819H100.463C102.853 25.4819 104.416 27.0294 104.416 29.3962V120.108C104.416 122.475 102.853 124.022 100.463 124.022H29.5371C27.1468 124.022 25.584 122.475 25.584 120.108ZM34.4095 113.963C34.4095 114.919 34.7773 115.283 35.6966 115.283H94.2577C95.223 115.283 95.5907 114.919 95.5907 113.963V35.5408C95.5907 34.585 95.223 34.2208 94.2577 34.2208H35.6966C34.7773 34.2208 34.4095 34.585 34.4095 35.5408V113.963ZM39.0981 108.183V41.3212C39.0981 39.7737 39.9715 38.8634 41.5343 38.8634H88.466C89.9828 38.8634 90.9022 39.7737 90.9022 41.3212V108.183C90.9022 109.731 89.9828 110.641 88.466 110.641H41.5343C39.9715 110.641 39.0981 109.731 39.0981 108.183ZM64.9771 86.6543C75.5034 86.6543 82.4444 92.0706 86.0297 97.3049V44.6893C86.0297 44.0521 85.662 43.7335 85.0644 43.7335H44.9358C44.2923 43.7335 43.9705 44.0521 43.9705 44.6893V97.2139C47.5559 92.0251 54.4509 86.6543 64.9771 86.6543ZM64.9771 82.9676C58.312 82.9221 52.934 77.4147 52.934 69.9958C52.934 63.032 58.312 57.2515 64.9771 57.2515C71.5963 57.2515 76.9744 63.032 76.9744 69.9958C76.9744 77.4147 71.5963 83.0131 64.9771 82.9676Z" fill="black"/>
<g opacity="0.2" filter="url(#filter0_i_5231_1792)">
<rect x="9.16016" y="9.05859" width="111.679" height="133.029" rx="13.1387" stroke="#CCCCCC" stroke-opacity="0.4" stroke-width="1.08963" stroke-linecap="round"/>
</g>
<g opacity="0.1" filter="url(#filter1_i_5231_1792)">
<rect x="0.949219" y="0.84668" width="128.102" height="149.453" rx="13.1387" stroke="#CCCCCC" stroke-opacity="0.4" stroke-width="1.08963" stroke-linecap="round"/>
</g>
<g opacity="0.2" filter="url(#filter2_i_5231_1792)">
<rect x="17.3721" y="17.27" width="95.2557" height="116.606" rx="8.2117" stroke="#CCCCCC" stroke-opacity="0.4" stroke-width="0.992607" stroke-linecap="round"/>
</g>
<defs>
<filter id="filter0_i_5231_1792" x="8.61523" y="8.51367" width="112.769" height="134.119" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.36321"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5231_1792"/>
</filter>
<filter id="filter1_i_5231_1792" x="0.404297" y="0.301758" width="129.192" height="150.542" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.36321"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5231_1792"/>
</filter>
<filter id="filter2_i_5231_1792" x="16.876" y="16.7739" width="96.248" height="117.599" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.330869"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_5231_1792"/>
</filter>
</defs>
</svg>
