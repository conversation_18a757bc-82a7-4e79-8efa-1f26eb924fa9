import { PropsWithChildren, ReactNode } from "react";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import Animated, { useAnimatedStyle } from "react-native-reanimated";
import { LinearGradient } from "expo-linear-gradient";
import { View } from "~/components/Themed";
import { useAnimatedKeyboard } from "~/hooks/useAnimatedKeyboard";

export function FullscreenFlowContainer({
  body,
  footer,
}: {
  body: ReactNode;
  footer: ReactNode;
}) {
  return (
    <View style={{ flex: 1, position: "relative", overflow: "visible" }}>
      <View style={{ flex: 1 }}>{body}</View>
      <FullscreenFlowFloatingFooter>{footer}</FullscreenFlowFloatingFooter>
    </View>
  );
}

function FullscreenFlowFloatingFooter({ children }: PropsWithChildren) {
  const backgroundColor = "rgba(243,243,243, 1)";
  const transparent = "rgba(243,243,243, 0)";
  const insets = useSafeAreaInsets();

  const animatedKeyboard = useAnimatedKeyboard();
  const animatedStyles = useAnimatedStyle(() => ({
    transform: [
      { translateY: Math.min(0, -(animatedKeyboard.height.value - 20)) },
    ],
  }));

  return (
    <Animated.View
      style={[
        {
          position: "absolute",
          bottom: 0,
          left: 0,
          right: 0,
          paddingHorizontal: 20,
          paddingTop: 30, // with linear gradient
          paddingBottom: insets.bottom,
        },
        animatedStyles,
      ]}
    >
      <LinearGradient
        style={{
          position: "absolute",
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        }}
        start={[0, 0]}
        end={[0, 1]}
        locations={[0, 0.2, 1]}
        colors={[transparent, backgroundColor, backgroundColor]}
      />
      {children}
    </Animated.View>
  );
}
