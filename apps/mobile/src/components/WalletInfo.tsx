import { Text, View } from "~/components/Themed";
import * as Clipboard from "expo-clipboard";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import { useActiveWallet } from "~/state/wallet";
import { useToast } from "~/components/Toaster";
import Animated, {
  FadeIn,
  FadeOut,
  LayoutAnimationConfig,
} from "react-native-reanimated";
import { DURATION_FAST } from "~/constants/animations";
import { FuseWalletIcon } from "~/components/icons/FuseWalletIcon";
import { CopyText } from "~/components/CopyText";

export function WalletInfo({
  variant = "horizontal",
}: {
  variant?: "horizontal" | "vertical";
}) {
  const { wallet } = useActiveWallet();

  const walletName = wallet.vaults.find((v) => v.index === 0)?.name ?? "Wallet";

  const { toast } = useToast();

  return (
    <View
      style={{
        flexDirection: variant === "horizontal" ? "row" : "column",
        gap: variant === "horizontal" ? 12 : 16,
        alignItems: "center",
      }}
    >
      <FuseWalletIcon size={variant === "horizontal" ? "medium" : "large"} />

      <View
        style={{
          gap: variant === "horizontal" ? 0 : 4,
          alignItems: variant === "horizontal" ? "flex-start" : "center",
        }}
      >
        <LayoutAnimationConfig skipEntering={true}>
          <Animated.View
            key={walletName}
            entering={FadeIn.duration(DURATION_FAST).delay(100)}
            exiting={FadeOut.duration(DURATION_FAST)}
            style={{ height: 24 }}
          >
            <Text
              variant="display-bold"
              style={{
                fontSize: variant === "horizontal" ? 18 : 20,
              }}
            >
              {walletName}
            </Text>
          </Animated.View>
        </LayoutAnimationConfig>
        <CopyText
          colorToken={"textSecondary"}
          variant="medium"
          size={variant === "horizontal" ? 15 : 16}
          onPress={async () => {
            await Clipboard.setStringAsync(wallet.defaultVault);
            toast.info("Copied", {
              id: "copy-address",
            });
          }}
        >
          {abbreviateAddress(wallet.defaultVault, 4)}
        </CopyText>
      </View>
    </View>
  );
}
