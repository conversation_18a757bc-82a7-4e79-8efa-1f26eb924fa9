import { useUsdBalance } from "~/state/balances";
import { Text, View } from "~/components/Themed";
import Ticker from "~/components/Ticker";
import { formatUsdValue } from "@squads/utils/numberFormats";
import { ContentSkeleton } from "~/components/Skeleton";
import { FuseSuspense } from "~/components/FuseSuspense";
import { FuseErrorBoundary } from "~/components/errors/FuseErrorBoundary";
import { Address } from "@squads/models/solana";
import { Row } from "~/components/Grid";
import { Gesture, GestureDetector, State } from "react-native-gesture-handler";
import Animated, {
  Easing,
  Extrapolation,
  interpolate,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from "react-native-reanimated";
import { SPRING } from "~/constants/animations";
import { useBalanceSettings } from "~/state/balanceSettings";
import * as Haptics from "expo-haptics";

type Size = "small" | "medium" | "large";
const Params = {
  variant: {
    small: "bold",
    medium: "display-bold",
    large: "display-bold",
  } as const,
  fontSize: {
    small: 26,
    medium: 32,
    large: 45,
  } satisfies Record<Size, number>,
  sign: {
    marginTop: {
      small: 0,
      medium: 0,
      large: 6,
    } satisfies Record<Size, number>,
    fontSize: {
      small: 26,
      medium: 32,
      large: 28,
    } satisfies Record<Size, number>,
  },
};

export type BalanceProps = {
  size: Size;
  address: Address;
  valueOverride?: number | null;
};

export function Balance({ size, address, valueOverride = null }: BalanceProps) {
  return (
    <FuseErrorBoundary
      FallbackComponent={() => <BalanceFallback size={size} />}
    >
      <FuseSuspense fallback={<BalanceSkeleton size={size} />}>
        <AddressBalanceView
          size={size}
          address={address}
          valueOverride={valueOverride}
        />
      </FuseSuspense>
    </FuseErrorBoundary>
  );
}

function AddressBalanceView({
  size,
  address,
  valueOverride = null,
}: {
  size: Size;
  address: Address;
  valueOverride?: number | null;
}) {
  const usdcBalance = useUsdBalance({ address });
  const amount = valueOverride ?? usdcBalance;

  return <BalanceView size={size} amount={amount} />;
}

export function BalanceView({ size, amount }: { size: Size; amount: number }) {
  const { isBalanceHidden, hidingBalance, toggleIsBalanceHidden } =
    useBalanceSettings();
  const amountString = formatUsdValue(amount, {
    renderCurrency: false,
    minimumFractionDigits: 2,
  });

  const balanceScale =
    amount >= 1_000_000_000
      ? 0.55
      : amount >= 100_000_000
        ? 0.6
        : amount >= 10_000_000
          ? 0.7
          : amount >= 1_000_000
            ? 0.78
            : amount >= 100_000
              ? 0.8
              : amount >= 10_000
                ? 0.9
                : 1;

  const scale = useSharedValue(1);

  const translateY = 30;
  const tickerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { perspective: 100 },
      { translateY: interpolate(hidingBalance.value, [0, 1], [0, translateY]) },
      { scale: scale.value },
      {
        rotateX: `${interpolate(hidingBalance.value, [0.05, 0.95], [0, -45], Extrapolation.CLAMP)}deg`,
      },
    ],
    opacity: interpolate(
      hidingBalance.value,
      [0.1, 0.9],
      [1, 0],
      Extrapolation.CLAMP
    ),
  }));

  const asteriskAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { perspective: 100 },
      {
        translateY: interpolate(hidingBalance.value, [0, 1], [-translateY, 0]),
      },
      { scale: scale.value },
      {
        rotateX: `${interpolate(hidingBalance.value, [0.05, 0.95], [45, 0], Extrapolation.CLAMP)}deg`,
      },
    ],
    opacity: interpolate(
      hidingBalance.value,
      [0.1, 0.9],
      [0, 1],
      Extrapolation.CLAMP
    ),
  }));

  const gestureTriggerDuration = 400;

  const longPressGesture = Gesture.LongPress()
    .minDuration(gestureTriggerDuration)
    .onBegin(() => {
      scale.value = withSpring(0.98, SPRING);
      hidingBalance.value = withTiming(isBalanceHidden ? 0.9 : 0.1, {
        duration: gestureTriggerDuration,
        easing: Easing.inOut(Easing.ease),
      });
    })
    .onStart(() => {
      runOnJS(Haptics.impactAsync)(
        isBalanceHidden
          ? Haptics.ImpactFeedbackStyle.Rigid
          : Haptics.ImpactFeedbackStyle.Soft
      );
      scale.value = withSpring(1, SPRING);
      hidingBalance.value = withSpring(isBalanceHidden ? 0 : 1, SPRING, () => {
        runOnJS(toggleIsBalanceHidden)();
      });
    })
    .onFinalize((event) => {
      if (event.state !== State.END) {
        hidingBalance.value = withSpring(isBalanceHidden ? 1 : 0, SPRING);
      }
      scale.value = withSpring(1, SPRING);
    });

  return (
    <GestureDetector gesture={longPressGesture}>
      <View
        style={{
          flexGrow: 1,
          transformOrigin: "left",
          transform: [{ scale: balanceScale }],
          overflow: "hidden",
        }}
      >
        <Animated.View
          style={[
            {
              flexDirection: "row",
            },
            tickerAnimatedStyle,
          ]}
        >
          <Text
            variant={Params.variant[size]}
            style={{
              fontSize: Params.sign.fontSize[size],
              marginTop: Params.sign.marginTop[size],
            }}
          >
            $
          </Text>
          <Ticker
            textVariant={Params.variant[size]}
            value={amountString}
            fontSize={Params.fontSize[size]}
            wholeNumbersColorToken={"text"}
            decimalsColorToken={"textSecondary"}
          />
        </Animated.View>
        <Animated.View
          style={[
            { position: "absolute", transformOrigin: "left" },
            asteriskAnimatedStyle,
          ]}
        >
          <Text
            variant={"display-bold"}
            style={{ fontSize: Params.fontSize[size] }}
          >
            ∗∗∗∗∗
          </Text>
        </Animated.View>
      </View>
    </GestureDetector>
  );
}

export function BalanceFallback({ size }: { size: Size }) {
  return (
    <View style={{ flexDirection: "row" }}>
      <Ticker
        value={"--"}
        textVariant={Params.variant[size]}
        fontSize={Params.fontSize[size]}
        wholeNumbersColorToken={"textSecondary"}
        decimalsColorToken={"textSecondary"}
      />
    </View>
  );
}

export function BalanceSkeleton({ size }: { size: Size }) {
  return (
    <Row>
      <ContentSkeleton>
        <Text
          variant={Params.variant[size]}
          style={{ fontSize: Params.fontSize[size] }}
        >
          {formatUsdValue(0)}
        </Text>
      </ContentSkeleton>
    </Row>
  );
}
