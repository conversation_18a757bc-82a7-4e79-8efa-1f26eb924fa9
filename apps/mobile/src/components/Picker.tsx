import { Text, View } from "~/components/Themed";
import { InputBackground } from "~/components/TextInput";
import { TouchableScale } from "~/components/TouchableScale";
import { Flex, Row } from "~/components/Grid";
import { RefObject, useEffect, useId, useRef, useState } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Picker as RNPicker } from "@react-native-picker/picker";
import { Button } from "~/components/Button";
import { hapticSelect } from "~/utils/haptics";
import { Keyboard } from "react-native";
import { Icon } from "~/components/Icon";

export function Picker({
  title,
  disabled,
  placeholder,
  options,
  value,
  onSelect,
  error,
}: {
  title: string;
  placeholder?: string;
  disabled?: boolean;
  options: Array<{ label: string; value: string }>;
  value: string | undefined;
  onSelect: (value: string) => void;
  error?: string;
}) {
  const modalRef = useRef<BottomModalImperativeMethods>(null);
  const selectedLabel = options.find((option) => option.value === value)?.label;

  return (
    <>
      <TouchableScale
        activeScale={0.98}
        onPress={() => {
          Keyboard.dismiss();
          modalRef.current?.present();
        }}
        disabled={disabled}
      >
        <View style={{ height: 54 }}>
          <InputBackground error={error} theme={"light"}>
            <Row
              flex
              justify={"space-between"}
              style={{ paddingHorizontal: 10 }}
            >
              <Text
                numberOfLines={1}
                colorToken={selectedLabel ? "text" : "textSecondary"}
                size={15}
              >
                {selectedLabel ?? placeholder ?? ""}
              </Text>
              <Icon
                name={"chevron.down"}
                size={9}
                weight="bold"
                colorToken={"text"}
              />
            </Row>
          </InputBackground>
        </View>
      </TouchableScale>

      <PickerModal
        modalRef={modalRef}
        title={title}
        value={value}
        options={options}
        onSelect={onSelect}
      />
    </>
  );
}

const PickerModal = ({
  title,
  modalRef,
  options,
  value,
  onSelect,
}: {
  title: string;
  modalRef: RefObject<BottomModalImperativeMethods | null>;
  options: Array<{ label: string; value: string }>;
  value: string | undefined;
  onSelect: (value: string) => void;
}) => {
  const id = useId();
  const [innerSelected, setInnerSelected] = useState<string>(
    value || options[0]?.value
  );

  useEffect(() => {
    if (value && value !== innerSelected) {
      setInnerSelected(value);
    }
  }, [value]);

  return (
    <BottomModal
      title={title}
      modalRef={modalRef}
      modalId={id}
      body={() => {
        return (
          <RNPicker
            selectedValue={innerSelected}
            onValueChange={setInnerSelected}
          >
            {options.map((option) => (
              <RNPicker.Item
                key={option.value}
                label={option.label}
                value={option.value}
              />
            ))}
          </RNPicker>
        );
      }}
      footer={() => {
        return (
          <Button
            variant="primary"
            onPress={() => {
              hapticSelect();
              onSelect(innerSelected);
              modalRef.current?.close();
            }}
          >
            Select
          </Button>
        );
      }}
    />
  );
};
