import { Contact, useContacts } from "~/state/contacts";
import { Address } from "@squads/models/solana";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import { useActiveWallet } from "~/state/wallet";
import { useAccountActivity, useDomain } from "~/state/account";
import { WalletCircleIcon } from "~/components/icons/WalletCircleIcon";
import { Image } from "expo-image";
import { ContactPfpIcon } from "~/components/ContactsPfp";
import { Suspense } from "react";
import { Text, View } from "~/components/Themed";
import { ModalRow, ModalRowSubTitle } from "~/components/ModalRow";
import { FuseErrorBoundary } from "~/components/errors/FuseErrorBoundary";
import { ContentSkeleton } from "~/components/Skeleton";
import { FuseLogo } from "~/components/icons/FuseLogo";
import { IconWrapper } from "~/components/IconWrapper";

export type AddressDetails =
  | { type: "wallet"; name: string | null }
  | { type: "card"; last4: string }
  | { type: "contact"; contact: Contact }
  | { type: "domain"; domain: string }
  | { type: "unknown"; address: Address };

export const AddressDetails = {
  title(addressDetails: AddressDetails) {
    switch (addressDetails.type) {
      case "wallet":
        return addressDetails.name ?? "Main account";
      case "card":
        return "Fuse Card";
      case "contact":
        return addressDetails.contact.name;
      case "domain":
        return addressDetails.domain;
      case "unknown":
        return abbreviateAddress(addressDetails.address);
    }
  },
};

export function useAddressDetails(address: Address): AddressDetails {
  const { wallet } = useActiveWallet();
  const card = wallet.card;
  const contacts = useContacts();
  const contact = contacts.find((c) => c.address === address);
  const domain = useDomain(address);

  if (address === wallet.defaultVault) {
    return { type: "wallet", name: wallet.vaults[0].name } as const;
  }

  if (card?.status === "issued" && address === card.cardVaultAddress) {
    return { type: "card", last4: card.last4 } as const;
  }

  if (contact) {
    return { type: "contact", contact } as const;
  }

  if (domain) {
    return { type: "domain", domain } as const;
  }

  return { type: "unknown", address } as const;
}

export function AddressIcon({
  addressDetails,
  size = 54,
}: {
  addressDetails: AddressDetails;
  size?: number;
}) {
  switch (addressDetails.type) {
    case "wallet":
      return (
        <IconWrapper
          backgroundColorToken="backgroundSecondary"
          size={size}
          variant="square"
        >
          <FuseLogo color={"black"} size={size * 0.375} />
        </IconWrapper>
      );
    case "card":
      return (
        <Image
          source={require("../../../assets/images/card/icon.png")}
          style={{
            width: size,
            aspectRatio: 1.5,
          }}
        />
      );
    case "contact":
      return <ContactPfpIcon pfp={addressDetails.contact.pfp} size={size} />;
    case "domain":
    case "unknown":
      return <WalletCircleIcon size={size} />;
    default:
      addressDetails satisfies never;
      return null;
  }
}

export function AddressTag({ address }: { address: Address }) {
  const addressDetails = useAddressDetails(address);

  return (
    <ModalRow
      icon={<AddressIcon addressDetails={addressDetails} size={42} />}
      title={
        <Text size={16} variant={"semibold"} colorToken="textSecondary">
          To:{" "}
          <Text size={16} variant={"semibold"} colorToken="text">
            {AddressDetails.title(addressDetails)}
          </Text>
        </Text>
      }
      description={
        <View style={{ alignSelf: "flex-start" }}>
          <AddressInteractions address={address} />
        </View>
      }
    />
  );
}

function AddressInteractions({ address }: { address: Address }) {
  const addressDetails = useAddressDetails(address);

  if (addressDetails.type === "card") {
    return <ModalRowSubTitle>· · · · {addressDetails.last4}</ModalRowSubTitle>;
  }

  if (addressDetails.type === "wallet") {
    return <ModalRowSubTitle>{abbreviateAddress(address)}</ModalRowSubTitle>;
  }

  return (
    <FuseErrorBoundary
      FallbackComponent={() => <ModalRowSubTitle>{""}</ModalRowSubTitle>}
    >
      <Suspense
        fallback={
          <ContentSkeleton>
            <ModalRowSubTitle>No previous sends</ModalRowSubTitle>
          </ContentSkeleton>
        }
      >
        <AddressInteractionsInner address={address} />
      </Suspense>
    </FuseErrorBoundary>
  );
}

function AddressInteractionsInner({ address }: { address: Address }) {
  const activity = useAccountActivity({ address });
  const addressDetails = useAddressDetails(address);

  if (activity.used === 0) {
    if (
      activity.balance === 0 &&
      addressDetails.type !== "card" &&
      addressDetails.type !== "wallet"
    ) {
      return <ModalRowSubTitle>Empty wallet</ModalRowSubTitle>;
    }

    return <ModalRowSubTitle>No previous sends</ModalRowSubTitle>;
  }

  return (
    <ModalRowSubTitle>
      {`${activity.used} ${activity.used === 1 ? "send" : "sends"}`}
    </ModalRowSubTitle>
  );
}
