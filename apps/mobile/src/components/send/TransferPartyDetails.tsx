import { ExternalAccount } from "~/services/bridge";
import { Address } from "@squads/models/solana";
import { formatTokenAmount } from "@squads/utils/numberFormats";
import { Text, View } from "~/components/Themed";
import { CoinLogo } from "~/components/CoinLogo";
import { Icon } from "~/components/Icon";
import { IconWrapper } from "~/components/IconWrapper";
import { BankIcon } from "~/components/icons/home/<USER>";
import { Suspense, type JSX } from "react";
import {
  AddressDetails,
  AddressIcon,
  useAddressDetails,
} from "~/components/send/AddressDetails";
import { useSuspenseToken } from "~/state/tokens";
import invariant from "invariant";
import { Row } from "~/components/Grid";
import { useAccountActivity } from "~/state/account";
import { Tag } from "~/components/Tag";
import { S2 } from "~/components/typography/S2";
import { ContentSkeleton } from "~/components/Skeleton";
import { DetailedNft } from "~/services/nfts";
import { NftImage } from "~/components/NftImage";

export function NftTransferPartyDetails({
  nft,
  recipient,
}: {
  nft: DetailedNft;
  recipient: Address;
}) {
  return (
    <View style={{ gap: 14 }}>
      <TransferParty
        icon={
          <NftImage
            nft={nft}
            width={REVIEW_TRANSFER_PARTY_ICON_SIZE}
            borderRadius={16}
          />
        }
        title={"You send"}
        subtitle={nft.name}
      />
      <Icon
        name={"arrow.down"}
        size={12}
        style={{ width: REVIEW_TRANSFER_PARTY_ICON_SIZE }}
        colorToken={"textSecondary"}
      />
      <OnchainTransferParty address={recipient} />
    </View>
  );
}

type Destination =
  | { type: "off-ramp"; externalAccount: ExternalAccount }
  | { type: "onchain"; address: Address };

export function TransferPartyDetails({
  token,
  destination,
}: {
  token: { mint: Address | "SOL"; amountFloat: number };
  destination: Destination;
}) {
  const mint = token.mint ?? "SOL";
  const tokenMetadata = useSuspenseToken({ mint });
  invariant(tokenMetadata, "Token metadata not found");

  const tokenAmount =
    destination.type === "off-ramp"
      ? formatTokenAmount(token.amountFloat, undefined, {
          maximumFractionDigits: 2,
          minimumFractionDigits: 2,
        })
      : formatTokenAmount(token.amountFloat, undefined);

  return (
    <View style={{ gap: 14 }}>
      <TransferParty
        icon={
          <CoinLogo mint={token.mint} size={REVIEW_TRANSFER_PARTY_ICON_SIZE} />
        }
        title={"You send"}
        subtitle={
          <>
            {tokenAmount}{" "}
            <Text size={18} variant="medium" colorToken="textSecondary">
              {tokenMetadata.symbol}
            </Text>
          </>
        }
      />
      <Icon
        name={"arrow.down"}
        size={12}
        style={{ width: REVIEW_TRANSFER_PARTY_ICON_SIZE }}
        colorToken={"textSecondary"}
      />
      {destination.type === "off-ramp" ? (
        <OffRampTransferParty externalAccount={destination.externalAccount} />
      ) : (
        <OnchainTransferParty address={destination.address} />
      )}
    </View>
  );
}

export const REVIEW_TRANSFER_PARTY_ICON_SIZE = 46;

function OffRampTransferParty({
  externalAccount,
}: {
  externalAccount: ExternalAccount;
}) {
  return (
    <TransferParty
      icon={
        <IconWrapper
          backgroundColorToken="backgroundSecondary"
          size={REVIEW_TRANSFER_PARTY_ICON_SIZE}
          variant={"square"}
        >
          <BankIcon colorToken={"textSecondary"} size={20} />
        </IconWrapper>
      }
      title={"To"}
      subtitle={`${externalAccount.bankName} ${externalAccount.accountNumber}`}
    />
  );
}

function OnchainTransferParty({ address }: { address: Address }) {
  const addressDetails = useAddressDetails(address);

  return (
    <Row justify={"space-between"} gap={4}>
      <TransferParty
        icon={
          <AddressIcon
            addressDetails={addressDetails}
            size={REVIEW_TRANSFER_PARTY_ICON_SIZE}
          />
        }
        title={"To"}
        subtitle={AddressDetails.title(addressDetails)}
      />

      {addressDetails.type !== "card" && addressDetails.type !== "wallet" && (
        <AccountActivityTag address={address} />
      )}
    </Row>
  );
}

function TransferParty({
  icon,
  title,
  subtitle,
}: {
  icon: JSX.Element;
  title: string | JSX.Element;
  subtitle: string | JSX.Element;
}) {
  return (
    <View
      style={{
        gap: 20,
        flexDirection: "row",
        alignItems: "center",
        flexShrink: 1,
      }}
    >
      {icon}
      <View style={{ flex: 1, gap: 4 }}>
        <Text variant="medium" size={15} colorToken="textTertiary">
          {title}
        </Text>
        <Text
          variant="semibold"
          size={20}
          adjustsFontSizeToFit={true}
          minimumFontScale={0.7}
          numberOfLines={1}
        >
          {subtitle}
        </Text>
      </View>
    </View>
  );
}

export function AccountActivityTag({ address }: { address: Address }) {
  return (
    <Suspense fallback={<AccountActivitySkeleton />}>
      <AccountActivityInner address={address} />
    </Suspense>
  );
}

function AccountActivityInner({ address }: { address: Address }) {
  const activity = useAccountActivity({ address });

  if (activity.used === 0) {
    if (activity.balance === 0) {
      return (
        <Tag background={"backgroundToastWarning"}>
          <S2 colorToken="textToastWarning">Empty wallet</S2>
        </Tag>
      );
    }

    return (
      <Tag>
        <S2 colorToken="textSecondary">No previous sends</S2>
      </Tag>
    );
  }

  return (
    <Tag>
      <S2 colorToken={"textSecondary"}>
        {activity.used} {activity.used === 1 ? "send" : "sends"}
      </S2>
    </Tag>
  );
}

function AccountActivitySkeleton() {
  return (
    <ContentSkeleton>
      <Tag>
        <S2>125 sends</S2>
      </Tag>
    </ContentSkeleton>
  );
}
