import { RefObject, useId } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, useColor, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import { hexToRgba } from "~/constants/Colors";
import { hapticDismissBottomTray } from "~/utils/haptics";

export function AlwaysSponsoredFeesModal({
  modalRef,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  const id = useId();
  const green = useColor("green");

  return (
    <BottomModal
      modalId={id}
      modalRef={modalRef}
      title={" "}
      body={
        <View gap={16} style={{ alignItems: "center", paddingTop: 12 }}>
          <View
            style={{
              padding: 10,
              borderRadius: 42,
              borderCurve: "continuous",
              backgroundColor: hexToRgba(green, 0.1),
            }}
          >
            <View
              background={"green"}
              style={{
                paddingVertical: 11,
                paddingHorizontal: 13,
                borderRadius: 32,
                borderCurve: "continuous",
              }}
            >
              <Text
                variant="semibold"
                size={21}
                colorToken={"textButtonPrimary"}
              >
                􀋦 Free
              </Text>
            </View>
          </View>

          <View gap={8} style={{ alignItems: "center" }}>
            <Text variant="semibold" size={20} align={"center"}>
              Fees are covered by Fuse
            </Text>
            <Text
              variant="medium"
              colorToken="textSecondary"
              size={15}
              align={"center"}
            >
              Fuse covers the onchain fees, so it{"\n"}doesn’t cost you
              anything.
            </Text>
          </View>
        </View>
      }
      footer={
        <Button
          variant="secondary"
          onPress={() => {
            hapticDismissBottomTray();
            modalRef.current?.close();
          }}
        >
          Got it
        </Button>
      }
    />
  );
}
