import { RefObject, useId } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import { hapticDismissBottomTray } from "~/utils/haptics";

import Svg, {
  Defs,
  G,
  LinearGradient,
  Mask,
  Path,
  Rect,
  Stop,
} from "react-native-svg";
import { DashedBorder } from "~/components/DashedBorder";
import { Row } from "~/components/Grid";
import { Icon } from "~/components/Icon";
import { formatUsdValue } from "@squads/utils/numberFormats";

export function FreeOfframpFeeLimitModal({
  modalRef,
  remainingFreeAmountUsd,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
  remainingFreeAmountUsd: number;
}) {
  const id = useId();

  return (
    <BottomModal
      modalId={id}
      modalRef={modalRef}
      title={" "}
      body={
        <View gap={16} style={{ alignItems: "center", paddingTop: 12 }}>
          <Visual />

          <View gap={8} style={{ alignItems: "center" }}>
            <Text variant="semibold" size={20} align={"center"}>
              Free Off-Ramp Limit
            </Text>
            <Text
              variant="medium"
              colorToken="textSecondary"
              size={14}
              align={"center"}
            >
              You have{" "}
              <Text variant="medium" colorToken="text" size={14}>
                {formatUsdValue(remainingFreeAmountUsd)}
              </Text>{" "}
              remaining in your free{"\n"}
              off-ramp allowance this month.
            </Text>
          </View>

          <Warning />
        </View>
      }
      footer={
        <Button
          variant="secondary"
          onPress={() => {
            hapticDismissBottomTray();
            modalRef.current?.close();
          }}
        >
          Got it
        </Button>
      }
    />
  );
}

function Visual() {
  return (
    <Svg width={52} height={52} viewBox="0 0 52 52" fill="none">
      <Rect width={52} height={52} rx={13} fill="#FF4A4A" />
      <G opacity={0.3}>
        <Mask id="a" fill="#fff">
          <Path d="M46 26c0 11.046-8.954 20-20 20S6 37.046 6 26 14.954 6 26 6s20 8.954 20 20zM8.798 26c0 9.5 7.702 17.202 17.202 17.202 9.5 0 17.202-7.702 17.202-17.202C43.202 16.5 35.5 8.798 26 8.798 16.5 8.798 8.798 16.5 8.798 26z" />
        </Mask>
        <Path
          d="M46 26c0 11.046-8.954 20-20 20S6 37.046 6 26 14.954 6 26 6s20 8.954 20 20zM8.798 26c0 9.5 7.702 17.202 17.202 17.202 9.5 0 17.202-7.702 17.202-17.202C43.202 16.5 35.5 8.798 26 8.798 16.5 8.798 8.798 16.5 8.798 26z"
          stroke="#fff"
          strokeWidth={3.07692}
          mask="url(#a)"
        />
      </G>
      <Mask id="b" fill="#fff">
        <Path d="M26 46a20 20 0 01-16.994-9.454l2.367-1.469A17.215 17.215 0 0026 43.215V46z" />
      </Mask>
      <Path
        d="M26 46a20 20 0 01-16.994-9.454l2.367-1.469A17.215 17.215 0 0026 43.215V46z"
        stroke="url(#paint0_linear_6646_28095)"
        strokeWidth={3.07692}
        mask="url(#b)"
      />
      <Mask id="c" fill="#fff">
        <Path d="M26.7 6.012a20 20 0 0116.114 9.158l-2.317 1.493a17.244 17.244 0 00-13.894-7.896l.097-2.755z" />
      </Mask>
      <Path
        d="M26.7 6.012a20 20 0 0116.114 9.158l-2.317 1.493a17.244 17.244 0 00-13.894-7.896l.097-2.755z"
        stroke="url(#paint1_linear_6646_28095)"
        strokeWidth={3.07692}
        mask="url(#c)"
      />
      <Path
        d="M12.52 26.662c0-.27.194-.47.448-.47h1.449a9.64 9.64 0 01-.038-.822v-.015c0-.27.015-.523.038-.777h-1.45a.442.442 0 01-.447-.456c0-.268.194-.47.448-.47h1.576c.68-3.256 3.346-4.944 6.834-4.944.433 0 .963.015 1.404.127.492.134.873.41.873.956 0 .582-.41.836-1 .836-.187 0-.762-.052-1.128-.052-2.442 0-4.13 1.09-4.713 3.077l4.474.007a.44.44 0 01.463.463c0 .27-.187.456-.463.456h-4.653c-.03.247-.045.493-.045.762v.045c0 .284.015.552.045.806h4.653c.276 0 .463.195.463.47 0 .27-.187.456-.463.456h-4.459c.605 2.01 2.338 3.055 4.765 3.055.53 0 .702-.052 1.06-.052.568 0 1.001.231 1.001.836 0 .546-.358.8-.873.941-.419.112-.979.135-1.434.135-3.63 0-6.132-1.785-6.797-4.915h-1.583a.442.442 0 01-.448-.455zM32.827 33.772a.459.459 0 01-.456-.463v-1.315c-1.3-.067-2.599-.448-3.517-1.307-.523-.485-.994-1.254-.994-1.949 0-.553.411-.911 1.001-.911.478 0 .8.246 1.001.717.329 1.075 1.27 1.62 2.51 1.733v-4.1l-.553-.128c-2.04-.455-3.81-1.411-3.81-3.584 0-2.323 2.04-3.593 4.362-3.727v-1.292c0-.254.202-.456.456-.456s.456.202.456.456v1.292c1.135.074 2.188.433 3.017 1.12.702.523 1.262 1.352 1.262 2.128 0 .546-.41.852-.948.852-.508 0-.83-.269-1.046-.71-.373-.986-1.195-1.538-2.285-1.658v3.921l.642.15c2.143.493 3.928 1.322 3.928 3.614 0 2.42-2.083 3.697-4.57 3.84v1.314a.459.459 0 01-.456.463zm-2.718-11.494c0 1.053.918 1.576 2.262 1.897V20.47c-1.232.12-2.263.732-2.263 1.808zm5.653 6.094c0-1.217-.963-1.65-2.48-1.994v3.899c1.308-.098 2.48-.643 2.48-1.905z"
        fill="#fff"
      />
      <Defs>
        <LinearGradient
          id="paint0_linear_6646_28095"
          x1={9.84615}
          y1={36.7692}
          x2={22.1538}
          y2={46}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#fff" />
          <Stop offset={1} stopColor="#FF8080" />
        </LinearGradient>
        <LinearGradient
          id="paint1_linear_6646_28095"
          x1={42.1538}
          y1={15.2308}
          x2={30.6154}
          y2={6.76923}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#fff" />
          <Stop offset={1} stopColor="#FF8080" />
        </LinearGradient>
      </Defs>
    </Svg>
  );
}

function Warning() {
  return (
    <View>
      <DashedBorder
        borderWidth={1.5}
        borderColorToken={"dashedListSeparator"}
      />
      <Row gap={16} style={{ gap: 18, padding: 20 }}>
        <Icon
          name={"info.square.fill"}
          size={12}
          colorToken={"textSecondary"}
        />
        <Text
          variant="medium"
          colorToken="textSecondary"
          size={14}
          style={{ flexShrink: 1 }}
        >
          For amounts exceeding this limit,{"\n"}a fee of 0.1% will be applied.
        </Text>
      </Row>
    </View>
  );
}
