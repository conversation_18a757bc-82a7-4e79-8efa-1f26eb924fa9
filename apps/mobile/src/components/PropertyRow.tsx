import { Text, View } from "~/components/Themed";
import { ReactNode, useRef } from "react";
import {
  PrepareActionResponse,
  SolanaFees,
  SubscriptionPlan,
  TransactionFees,
} from "~/services/wallets";
import { ContentSkeleton } from "~/components/Skeleton";
import { formatTokenAmount } from "@squads/utils/numberFormats";
import { Address } from "@squads/models/solana";
import { Flex, Row } from "~/components/Grid";
import { Icon } from "~/components/Icon";
import { formatSolAmount } from "~/utils/tokens";
import { useSuspenseToken, useTokenAmountFloat } from "~/state/tokens";
import {
  AddressDetails,
  AddressIcon,
  useAddressDetails,
} from "~/components/send/AddressDetails";
import { AlwaysSponsoredFeesModal } from "~/components/send/AlwaysSponsoredFeesModal";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import { isClientErrorReasonType } from "~/services/utils";
import { BottomModalImperativeMethods } from "~/components/BottomModal";
import { hapticOpenBottomTray } from "~/utils/haptics";
import { showNotEnoughSolModal } from "~/components/modals/NotEnoughSolModal";
import { SponsoredActionInfoModal } from "~/components/modals/SponsoredActionInfoModal";
import { FusePlusText } from "~/components/FusePlusText";
import { FusePlusInfoModal } from "~/components/modals/FusePlusInfoModal";

export function PropertyRow({
  label,
  value,
  onInfoPress,
}: {
  label: string;
  value: ReactNode;
  onInfoPress?: () => void;
}) {
  return (
    <View
      style={{
        minHeight: 20,
        flexDirection: "row",
        alignItems: "center",
        gap: 12,
      }}
    >
      {onInfoPress ? (
        <AnimatedTouchableScale onPress={onInfoPress}>
          <Row gap={6}>
            <Text
              variant="medium"
              colorToken="textSecondary"
              style={{ fontSize: 15 }}
            >
              {label}
            </Text>
            <Icon
              name={"info.circle"}
              colorToken={"textSecondary"}
              size={10}
              weight={"semibold"}
            />
          </Row>
        </AnimatedTouchableScale>
      ) : (
        <Text
          variant="medium"
          colorToken="textSecondary"
          style={{ fontSize: 15 }}
        >
          {label}
        </Text>
      )}
      <Flex style={{ alignItems: "flex-end" }}>{value}</Flex>
    </View>
  );
}

export type SolanaFeesData =
  | { status: "loading" }
  | { status: "success"; solanaFees?: SolanaFees }
  | { status: "notEnoughSol"; available: number; needed: number }
  | { status: "error"; error: unknown };

export const SolanaFeesData = {
  fromMutation: (mutation: {
    data?: PrepareActionResponse;
    error?: unknown;
    isPending: boolean;
  }): SolanaFeesData => {
    if (mutation.isPending) {
      return { status: "loading" };
    }

    if (mutation.data) {
      return {
        status: "success",
        solanaFees: mutation.data.solanaFees ?? undefined,
      };
    }

    if (isClientErrorReasonType(mutation.error, "notEnoughSol")) {
      return {
        status: "notEnoughSol",
        available: mutation.error.reason.available,
        needed: mutation.error.reason.needed,
      };
    }

    return {
      status: "error",
      error: mutation.error,
    };
  },
};

export function TransactionFeesRows({ data }: { data: SolanaFeesData }) {
  if (data.status === "loading" || data.status === "error") {
    return (
      <PropertyRow
        label={"Onchain fees"}
        value={
          <ContentSkeleton>
            <Text variant="medium">0.00649 SOL</Text>
          </ContentSkeleton>
        }
      />
    );
  }

  if (data.status === "success") {
    if (!data.solanaFees) {
      return null;
    }

    if (data.solanaFees.type === "alwaysSponsored") {
      return <SponsoredFeesRow label={"Network Fee"} />;
    }

    if (data.solanaFees.type === "sponsoredBySubscription") {
      if (SubscriptionPlan.isFree(data.solanaFees.subscriptionUsage.planId)) {
        const actionsUsed = data.solanaFees.subscriptionUsage.activitiesUsed;
        const actionsLeft =
          data.solanaFees.subscriptionUsage.activitiesLimit - actionsUsed;

        return (
          <FreeSponsoredFeesRow
            actionsLeft={actionsLeft}
            actionsUsed={actionsUsed}
          />
        );
      }

      return <FusePlusCoveredFeesRow />;
    }

    if (data.solanaFees.type === "selfFunded") {
      const actionsUsed = data.solanaFees.subscriptionUsage.activitiesUsed;
      const actionsLeft =
        data.solanaFees.subscriptionUsage.activitiesLimit - actionsUsed;

      return (
        <SelfFundedFeesRow
          totalFees={TransactionFees.total(data.solanaFees.fees)}
          actionsLeft={actionsLeft}
          actionsUsed={actionsUsed}
        />
      );
    }

    data.solanaFees satisfies never;
    return null;
  }

  if (data.status === "notEnoughSol") {
    return <NotEnoughSolRow />;
  }

  data satisfies never;
  return null;
}

function FusePlusCoveredFeesRow() {
  const modalRef = useRef<BottomModalImperativeMethods>(null);

  return (
    <>
      <FusePlusInfoModal modalRef={modalRef} />
      <PropertyRow
        label={"Onchain fees"}
        value={
          <AnimatedTouchableScale
            onPress={() => {
              hapticOpenBottomTray();
              modalRef.current?.present();
            }}
          >
            <Row gap={1}>
              <FusePlusText colorToken={"textSecondary"} />
              <Text variant="medium" colorToken="green">
                Covered
              </Text>
            </Row>
          </AnimatedTouchableScale>
        }
      />
    </>
  );
}

function FreeSponsoredFeesRow({
  actionsUsed,
  actionsLeft,
}: {
  actionsUsed: number;
  actionsLeft: number;
}) {
  const modalRef = useRef<BottomModalImperativeMethods>(null);

  return (
    <>
      <SponsoredActionInfoModal
        modalRef={modalRef}
        actionsLeft={actionsLeft}
        actionsUsed={actionsUsed}
      />
      <PropertyRow
        label={"Onchain fees"}
        value={
          <AnimatedTouchableScale
            onPress={() => {
              hapticOpenBottomTray();
              modalRef.current?.present();
            }}
          >
            <Text variant="medium" colorToken={"green"}>
              Sponsored · {actionsLeft} left
            </Text>
          </AnimatedTouchableScale>
        }
      />
    </>
  );
}

function SelfFundedFeesRow({
  actionsUsed,
  actionsLeft,
  totalFees,
}: {
  totalFees: number;
  actionsUsed: number;
  actionsLeft: number;
}) {
  const modalRef = useRef<BottomModalImperativeMethods>(null);

  return (
    <>
      <SponsoredActionInfoModal
        modalRef={modalRef}
        actionsLeft={actionsLeft}
        actionsUsed={actionsUsed}
      />
      <PropertyRow
        label={"Onchain fees"}
        onInfoPress={() => {
          hapticOpenBottomTray();
          modalRef.current?.present();
        }}
        value={<Text variant="medium">{formatSolAmount(totalFees)}</Text>}
      />
    </>
  );
}

function NotEnoughSolRow() {
  const noSponsoredActionsLeftModalRef =
    useRef<BottomModalImperativeMethods>(null);

  return (
    <>
      <SponsoredActionInfoModal
        modalRef={noSponsoredActionsLeftModalRef}
        actionsLeft={0}
        actionsUsed={5}
      />
      <PropertyRow
        label={"Onchain fees"}
        onInfoPress={() => {
          hapticOpenBottomTray();
          noSponsoredActionsLeftModalRef.current?.present();
        }}
        value={
          <AnimatedTouchableScale
            hitSlop={8}
            onPress={() => {
              showNotEnoughSolModal();
            }}
          >
            <Row gap={6}>
              <Text variant="medium" colorToken={"systemRed"}>
                Insufficient SOL
              </Text>
              <View
                background={"systemRed"}
                style={{
                  width: 6,
                  aspectRatio: 1,
                  borderRadius: 999,
                }}
              />
            </Row>
          </AnimatedTouchableScale>
        }
      />
    </>
  );
}

export function AddressRow({
  label,
  address,
}: {
  label: string;
  address: Address;
}) {
  const addressDetails = useAddressDetails(address);

  return (
    <PropertyRow
      label={label}
      value={
        <View style={{ flexDirection: "row", gap: 6, alignItems: "center" }}>
          <AddressIcon addressDetails={addressDetails} size={20} />
          <Text variant="medium">{AddressDetails.title(addressDetails)}</Text>
        </View>
      }
    />
  );
}

//token 2022 fee
export function TokenTransferFeeRow({
  mint,
  amount,
  feeBsp,
}: {
  mint: Address;
  amount: number;
  feeBsp: number;
}) {
  const token = useSuspenseToken({ mint });

  const transferFee = (amount / 10000) * feeBsp;

  const transferFeeFloat = useTokenAmountFloat({
    mint,
    amount: transferFee,
  });

  if (!transferFeeFloat || transferFee == 0 || !token) return null;
  return (
    <PropertyRow
      label={`${token.symbol} Transfer fee`}
      value={
        <Text variant="medium">
          {formatTokenAmount(transferFeeFloat, " ", {
            maximumFractionDigits: 6,
          })}
          <Text colorToken="textSecondary" variant={"medium"}>
            {token.symbol}
          </Text>
        </Text>
      }
    />
  );
}

export function SponsoredFeesRow({ label = "Fees" }: { label?: string }) {
  const modalRef = useRef<BottomModalImperativeMethods>(null);

  return (
    <>
      <PropertyRow
        label={label}
        value={
          <AnimatedTouchableScale
            hitSlop={8}
            onPress={() => {
              hapticOpenBottomTray();
              modalRef.current?.present();
            }}
          >
            <Text variant="medium" colorToken="green">
              Sponsored
            </Text>
          </AnimatedTouchableScale>
        }
      />
      <AlwaysSponsoredFeesModal modalRef={modalRef} />
    </>
  );
}
