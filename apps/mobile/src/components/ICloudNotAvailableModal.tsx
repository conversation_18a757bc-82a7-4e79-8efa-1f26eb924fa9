import { RefObject } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { Flex, Row } from "~/components/Grid";
import { Button } from "~/components/Button";
import { Linking } from "react-native";
import { IconWrapper } from "~/components/IconWrapper";
import { Icon } from "~/components/Icon";

export function ICloudNotAvailableModal({
  modalRef,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  return (
    <BottomModal
      modalRef={modalRef}
      modalId="icloud-not-available"
      themeOverride="dark"
      title=" "
      body={
        <View gap={24} style={{ paddingHorizontal: 14 }}>
          <View gap={24} style={{ alignItems: "center" }}>
            <IconWrapper
              backgroundColorToken={"backgroundIconWarning"}
              size={56}
              variant={"square"}
            >
              <Icon
                name="icloud.slash"
                size={20}
                colorToken={"iconWarning"}
                style={{ top: 2 }}
              />
            </IconWrapper>

            <View gap={4} style={{ alignItems: "center" }}>
              <Text variant="semibold" size={20}>
                iCloud is unavailable
              </Text>
            </View>
          </View>
          <Text size={16} variant="medium" colorToken="textSecondary">
            Fuse needs access to iCloud to properly manage your wallet.{"\n\n"}
            Please log into your iCloud account in Settings and try again.
          </Text>
        </View>
      }
      footer={
        <Row gap={12}>
          <Flex>
            <Button
              variant="primary"
              size={"medium-new"}
              onPress={() => {
                modalRef.current?.dismiss();
                Linking.openURL("App-Prefs:");
              }}
            >
              Open Settings
            </Button>
          </Flex>
        </Row>
      }
    />
  );
}
