import {
  executeAction,
  prepareAction,
  RecoveryKey,
  Wallet,
  WalletCard,
} from "~/services/wallets";
import { Text, useColor, View } from "~/components/Themed";
import { RefreshControl, SectionList } from "react-native";
import { Coin<PERSON>ogo, CoinLogoVariant, CoinPairLogo } from "~/components/CoinLogo";
import { Address } from "@squads/models/solana";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import { DateTime } from "luxon";
import { Icon } from "~/components/Icon";
import { IconWrapper } from "~/components/IconWrapper";
import {
  refetchActivities,
  updateActivity,
  useActivities,
  useActivitiesByMint,
} from "~/state/activities";
import {
  Activity,
  ActivityStatus,
  confirmActivity,
  DriftEarnDepositDetails,
  KaminoEarnDepositDetails,
  LuloProtectedEarnDepositDetails,
  RecipientBankAccount,
  SendFiatStatus,
  VirtualAccountTransferStatus,
} from "~/services/activities";
import { FuseSuspense } from "~/components/FuseSuspense";
import {
  ComponentProps,
  isValidElement,
  ReactElement,
  ReactNode,
  useRef,
  useState,
} from "react";
import { useSuspenseToken, useTokenAmountString } from "~/state/tokens";
import * as Haptics from "expo-haptics";
import { TouchableScale } from "~/components/TouchableScale";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Button } from "~/components/Button";
import {
  FallbackProps,
  FuseErrorBoundary,
} from "~/components/errors/FuseErrorBoundary";
import { H3 } from "~/components/typography/H3";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useActiveWallet } from "~/state/wallet";
import { toast } from "~/components/Toaster";
import { MarinadeIcon } from "~/components/icons/MarinadeIcon";
import {
  ActivityDetailsModal,
  ActivityDetailsModalParams,
} from "~/components/ActivityDetailsModal";
import { BottomModalImperativeMethods } from "~/components/BottomModal";
import { KeyChangeIcon } from "~/components/icons/KeyChange";
import { P3 } from "~/components/typography/P3";
import { Row } from "~/components/Grid";
import { H4 } from "~/components/typography/H4";
import {
  formatSolAmount,
  formatSPLAmount,
  tokenUsdValue,
} from "~/utils/tokens";
import { formatTokenAmount, formatUsdValue } from "@squads/utils/numberFormats";
import { ValidatorIcon } from "~/components/icons/ValidatorIcon";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { NftImage } from "~/components/NftImage";
import { LAMPORTS_PER_SOL } from "@solana/web3.js";
import { mints } from "~/constants/tokens";
import Animated, {
  AnimatedRef,
  FadeIn,
  FadeInUp,
  FadeOut,
  LayoutAnimationConfig,
  ReduceMotion,
} from "react-native-reanimated";
import { BoltIcon } from "~/components/icons/BoltIcon";
import { ContentSkeleton } from "~/components/Skeleton";
import { ScrollView } from "~/components/ScrollView";
import { DURATION_MEDIUM } from "~/constants/animations";
import {
  HIDDEN_BALANCE_TEXT,
  useBalanceSettings,
} from "~/state/balanceSettings";
import { refetchWalletRecoveryState } from "~/state/walletRecovery";
import { StampCheckmarkIcon } from "~/components/icons/StampCheckmarkIcon";
import { StampCrossIcon } from "~/components/icons/StampCrossIcon";
import { StampClockIcon } from "~/components/icons/StampClockIcon";
import { getKycStatus, useLazyBridgeAccount } from "~/state/bridge";
import { router } from "expo-router";
import { ActivityIcon } from "~/components/activities/ActivityIcon";

const SCROLL_VIEW_PADDING_TOP = 20;

export function Activities({
  wallet,
  scrollViewRef,
}: {
  wallet: Wallet;
  scrollViewRef: AnimatedRef<Animated.ScrollView>;
}) {
  return (
    <LayoutAnimationConfig skipEntering skipExiting>
      <FuseErrorBoundary
        FallbackComponent={({ error, resetErrorBoundary, errorId }) => {
          return (
            <ActivitiesErrorFallback
              error={error}
              errorId={errorId}
              resetErrorBoundary={resetErrorBoundary}
              wallet={wallet}
            />
          );
        }}
      >
        <FuseSuspense
          fallback={<ActivitiesListSkeleton scrollViewRef={scrollViewRef} />}
        >
          <ActivitiesListInner
            walletKey={wallet.walletKey}
            scrollViewRef={scrollViewRef}
          />
        </FuseSuspense>
      </FuseErrorBoundary>
    </LayoutAnimationConfig>
  );
}

export function ActivitiesListSkeleton({
  scrollViewRef,
}: {
  scrollViewRef?: AnimatedRef<Animated.ScrollView>;
}) {
  return (
    <Animated.View
      exiting={FadeOut.duration(DURATION_MEDIUM).reduceMotion(
        ReduceMotion.Never
      )}
    >
      <ScrollView
        ref={scrollViewRef}
        style={{ overflow: "visible" }}
        contentContainerStyle={[{ minHeight: "100%", overflow: "visible" }]}
      >
        <ActivityViewRowSkeleton />
        <View style={{ opacity: 0.5 }}>
          <ActivityViewRowSkeleton />
        </View>
        <View style={{ opacity: 0.4 }}>
          <ActivityViewRowSkeleton />
        </View>
        <View style={{ opacity: 0.3 }}>
          <ActivityViewRowSkeleton />
        </View>
      </ScrollView>
    </Animated.View>
  );
}

function ActivityViewRowSkeleton() {
  return (
    <ActivityViewRow
      activity={{ status: { type: "confirmed", cleanup: "done" } }}
      icon={
        <ContentSkeleton borderRadius={999}>
          <IconWrapper size={42} />
        </ContentSkeleton>
      }
      left={{
        up: (
          <ContentSkeleton>
            <Title>Solana</Title>
          </ContentSkeleton>
        ),
        down: (
          <ContentSkeleton>
            <P3 colorToken="textSecondary">From: abcd...abcd</P3>
          </ContentSkeleton>
        ),
      }}
      right={{
        up: (
          <ContentSkeleton>
            <P3 colorToken="green">+0.00 SOL</P3>
          </ContentSkeleton>
        ),
        down: (
          <ContentSkeleton>
            <P3 colorToken="textSecondary">$0.00</P3>
          </ContentSkeleton>
        ),
      }}
    />
  );
}

function ActivitiesListInner({
  walletKey,
  scrollViewRef,
}: {
  walletKey: Address;
  scrollViewRef: AnimatedRef<Animated.ScrollView>;
}) {
  const ignoredActivities = ["cardSLChange", "cardReferralReward"];

  const activities = useActivities({ wallet: walletKey }).filter(
    (activity) => !ignoredActivities.includes(activity.details.type)
  );

  return (
    <ActivitiesList
      scrollViewRef={scrollViewRef}
      activities={activities}
      walletKey={walletKey}
    />
  );
}

export function CoinActivitiesList({
  wallet,
  mint,
}: {
  wallet: Wallet;
  mint: Address;
}) {
  const activities = useActivitiesByMint({ wallet, mint });

  return (
    <ActivitiesList
      activities={activities}
      walletKey={wallet.walletKey}
      showEmptyListPlaceholder={false}
    />
  );
}

function ActivitiesList({
  scrollViewRef,
  activities,
  walletKey,
  showEmptyListPlaceholder = true,
}: {
  /** If passed the List enables its own scroll behaviour */
  scrollViewRef?: AnimatedRef<Animated.ScrollView>;
  activities: Activity[];
  walletKey: Address;
  showEmptyListPlaceholder?: boolean;
}) {
  const modalRef =
    useRef<BottomModalImperativeMethods<ActivityDetailsModalParams>>(null);

  const bridgeAccountQuery = useLazyBridgeAccount();
  const queryClient = useQueryClient();

  // NOTE: we can't use `isFetching` from react-query because it changes async
  // and causes refresh indicator to flicker weirdly. @see https://github.com/facebook/react-native/issues/32836
  const [refreshing, setRefreshing] = useState(false);
  const refreshControlColor = useColor("textTertiary");

  const [page, setPage] = useState(1);

  const activitiesGroupedByDate = activities.slice(0, page * 20).reduce(
    (acc, transaction) => {
      const date = DateTime.fromSeconds(transaction.timestamp).toISODate();
      if (!date) {
        return acc;
      }
      const transactions = acc[date] ?? [];
      return {
        ...acc,
        [date]: [...transactions, transaction],
      };
    },
    {} as Record<string, Activity[]>
  );

  let lastIndex = 0;
  const sections = Object.entries(activitiesGroupedByDate).map(
    ([date, activities]) => ({
      title: date,
      data: activities.map((activity) => ({
        ...activity,
        index: lastIndex++,
      })),
    })
  );

  const scrollEnabled = scrollViewRef != null;

  return (
    <>
      <SectionList
        contentInsetAdjustmentBehavior="automatic"
        ref={scrollViewRef as unknown as AnimatedRef<SectionList>}
        style={{ overflow: "visible" }}
        contentContainerStyle={[
          {
            minHeight: "100%",
            overflow: "visible",
            paddingTop: SCROLL_VIEW_PADDING_TOP,
          },
        ]}
        ListEmptyComponent={
          showEmptyListPlaceholder ? EmptyActivitiesList : undefined
        }
        showsVerticalScrollIndicator={false}
        sections={sections}
        stickySectionHeadersEnabled={false}
        initialNumToRender={14}
        scrollEnabled={scrollEnabled}
        refreshControl={
          scrollEnabled ? (
            <RefreshControl
              tintColor={refreshControlColor}
              refreshing={refreshing}
              progressViewOffset={100}
              onRefresh={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setRefreshing(true);
                refetchActivities({
                  queryClient,
                  walletKey,
                }).finally(() => setRefreshing(false));
              }}
            />
          ) : undefined
        }
        renderSectionHeader={({ section: { title } }) => (
          <Animated.View
            entering={FadeIn.duration(DURATION_MEDIUM).reduceMotion(
              ReduceMotion.Never
            )}
          >
            <View background="background">
              <Text
                variant="medium"
                colorToken="textSecondary"
                style={{ fontSize: 15 }}
              >
                {DateTime.fromISO(title).toLocaleString(DateTime.DATE_MED)}
              </Text>
            </View>
          </Animated.View>
        )}
        renderSectionFooter={SectionBottomSpacer}
        renderItem={({ item: activity }) => {
          return (
            <FuseSuspense fallback={<ActivityViewRowSkeleton />}>
              <Animated.View
                entering={FadeInUp.duration(DURATION_MEDIUM)
                  .delay(activity.index < 12 ? 15 * activity.index : 0)
                  .reduceMotion(ReduceMotion.Never)}
              >
                <ActivityRow
                  activity={activity}
                  onPress={() => {
                    if (
                      activity.details.type === "kycStarted" ||
                      activity.details.type === "kycRejected"
                    ) {
                      if (
                        bridgeAccountQuery.data &&
                        getKycStatus(bridgeAccountQuery.data) !== "approved"
                      ) {
                        router.push(`/unlocked/bridge/onboarding/kyc-status`);
                        return;
                      }
                    }

                    modalRef?.current?.present({ activity });
                  }}
                />
              </Animated.View>
            </FuseSuspense>
          );
        }}
        onEndReached={() => setPage(page + 1)}
      />
      <ActivityDetailsModal modalRef={modalRef} />
    </>
  );
}

function SectionBottomSpacer() {
  return <View style={{ height: 8 }} />;
}

function EmptyActivitiesList() {
  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        gap: 18,
      }}
    >
      <IconWrapper backgroundColorToken="backgroundSecondary" size={48}>
        <Icon
          size={18}
          name="tray.fill"
          weight="medium"
          colorToken="textSecondary"
        />
      </IconWrapper>
      <H3 colorToken="textSecondary" style={{ textAlign: "center" }}>
        Wallet has{"\n"}no activity yet
      </H3>
    </View>
  );
}

function ActivitiesErrorFallback({
  resetErrorBoundary,
  wallet,
}: FallbackProps & { wallet: Wallet }) {
  const insets = useSafeAreaInsets();
  const queryClient = useQueryClient();

  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        gap: 32,
        marginBottom: insets.bottom + 46,
      }}
    >
      <View style={{ gap: 18, justifyContent: "center", alignItems: "center" }}>
        <IconWrapper backgroundColorToken="backgroundSecondary" size={48}>
          <Icon
            size={18}
            rectSize={20}
            name="exclamationmark.triangle.fill"
            weight="medium"
            colorToken="textSecondary"
            style={{ transform: [{ translateY: -2 }] }}
          />
        </IconWrapper>
        <H3
          colorToken="textSecondary"
          style={{ maxWidth: 210, textAlign: "center" }}
        >
          Failed to load activities
        </H3>
      </View>
      <Button
        variant="secondary"
        size="medium"
        onPress={async () => {
          await refetchActivities({
            queryClient,
            walletKey: wallet.walletKey,
          });
          resetErrorBoundary();
        }}
      >
        Retry
      </Button>
    </View>
  );
}

function ActivityRow({
  activity,
  onPress,
}: {
  activity: Activity;
  onPress: () => void;
}) {
  let row;

  switch (activity.details.type) {
    case "walletRecovery":
      row = (
        <WalletRecoveryTransactionView
          activity={activity}
          oldKey={activity.details.oldKey}
          newKey={activity.details.newKey}
        />
      );
      break;

    case "walletCreated":
      row = <WalletCreatedTransactionView activity={activity} />;
      break;

    case "sendSol":
      row = (
        <TransferTransactionView
          decimals={9}
          activity={activity}
          type="outflow"
          amount={activity.details.lamports}
          mint="SOL"
          address={activity.details.to}
          usdPrice={activity.details.usdPrice}
        />
      );
      break;

    case "sendToken":
      row = (
        <TransferTransactionView
          decimals={activity.details.decimals}
          activity={activity}
          type="outflow"
          amount={activity.details.amount}
          mint={activity.details.tokenMint}
          address={activity.details.to}
          usdPrice={activity.details.usdPrice}
        />
      );
      break;

    case "receiveSol":
      row = (
        <TransferTransactionView
          type="inflow"
          decimals={9}
          activity={activity}
          amount={activity.details.lamports}
          mint="SOL"
          address={activity.details.from}
          usdPrice={activity.details.usdPrice}
        />
      );
      break;

    case "receiveToken":
      row = (
        <TransferTransactionView
          type="inflow"
          activity={activity}
          decimals={activity.details.decimals}
          amount={activity.details.amount}
          mint={activity.details.tokenMint}
          address={activity.details.from}
          usdPrice={activity.details.usdPrice}
        />
      );
      break;

    case "swap":
      row = (
        <SwapTransactionView
          activity={activity}
          activityId={activity.activityId}
          status={activity.status}
          inputMint={activity.details.inputMint}
          outputMint={activity.details.outputMint}
          inAmount={activity.details.inAmount}
          outAmount={activity.details.outAmount}
          routes={activity.details.routes}
        />
      );
      break;

    case "updateRecoveryKeys":
      row = (
        <UpdateRecoveryKeyView activity={activity} details={activity.details} />
      );
      break;

    case "updateCloudKey":
      row = (
        <UpdateCloudKeyView activity={activity} details={activity.details} />
      );
      break;

    case "unknown":
      row = <UnknownActivityView activity={activity} />;
      break;

    case "marinadeNativeStake":
    case "marinadeNativeDeactivate":
    case "marinadeNativeWithdraw":
      row = (
        <MarinadeNativeStakeView
          activity={activity}
          details={activity.details}
        />
      );
      break;

    case "stake":
      row = <StakeView activity={activity} details={activity.details} />;
      break;

    case "depositLiquidStake":
      row = (
        <DepositLiquidStakeView
          activity={activity}
          details={activity.details}
        />
      );
      break;

    case "liquidStakeWithdraw":
      row = (
        <LiquidStakeWithdrawView
          activity={activity}
          details={activity.details}
        />
      );
      break;

    case "receiveNft":
      row = <TransferNftView activity={activity} details={activity.details} />;
      break;

    case "sendNft":
      row = <TransferNftView activity={activity} details={activity.details} />;
      break;

    case "spendingLimit":
      row = (
        <SpendingLimitView activity={activity} details={activity.details} />
      );
      break;

    case "spendingLimitTransfer":
      row = (
        <SpendingLimitTransferView
          activity={activity}
          details={activity.details}
        />
      );
      break;

    case "spendingLimitUse":
      row = (
        <TransferTransactionView
          spendingLimitUse
          decimals={activity.details.decimals}
          activity={activity}
          type="outflow"
          amount={activity.details.amount}
          mint={activity.details.mint ?? "SOL"}
          address={activity.details.recipient}
          usdPrice={activity.details.usdPrice}
        />
      );
      break;

    case "payFeesWithFuseSolEnable":
    case "payFeesWithFuseSolDisable":
      row = (
        <PayFeesWithFuseSolView
          activity={activity}
          details={activity.details}
        />
      );
      break;

    case "relayerTopUp":
    case "relayerWithdraw":
      row = (
        <RelayerBalanceChangeView
          activity={activity}
          details={activity.details}
        />
      );
      break;

    case "driftEarnDeposit":
      row = (
        <DriftEarnDepositView activity={activity} details={activity.details} />
      );
      break;

    case "driftEarnWithdraw":
      row = (
        <DriftEarnWithdrawView activity={activity} details={activity.details} />
      );
      break;

    case "luloProtectedEarnDeposit":
      row = (
        <LuloProtectedEarnDepositView
          activity={activity}
          details={activity.details}
        />
      );
      break;
    case "luloProtectedEarnWithdraw":
      row = (
        <LuloProtectedEarnWithdrawView
          activity={activity}
          details={activity.details}
        />
      );
      break;

    case "kaminoEarnDeposit":
      row = (
        <KaminoEarnDepositView activity={activity} details={activity.details} />
      );
      break;

    case "kaminoEarnWithdraw":
      row = (
        <KaminoEarnWithdrawView
          activity={activity}
          details={activity.details}
        />
      );
      break;

    case "kycStarted":
    case "kycApproved":
    case "kycRejected":
      row = <KycView activity={activity} details={activity.details} />;
      break;

    case "virtualAccountReceive":
      row = (
        <VirtualAccountReceiveView
          activity={activity}
          details={activity.details}
        />
      );
      break;

    case "sendFiat":
      row = <SendFiatView activity={activity} details={activity.details} />;
      break;

    case "cardCreate":
      row = <CardCreateView activity={activity} details={activity.details} />;
      break;

    case "cardTopUp":
      row = <CardDepositView activity={activity} details={activity.details} />;
      break;

    case "cardWithdraw":
      row = <CardWithdrawView activity={activity} details={activity.details} />;
      break;

    case "cardSLChange":
      row = null;
      break;

    case "cardReferralReward":
      row = null;
      break;

    default:
      activity.details satisfies never;
      row = <UnknownActivityView activity={activity} />;
  }

  if (row === null) {
    return null;
  }

  return (
    <TouchableScale
      activeScale={0.99}
      onPress={() => {
        Haptics.selectionAsync();
        onPress();
      }}
    >
      {row}
    </TouchableScale>
  );
}

function WalletRecoveryTransactionView({
  activity,
  newKey,
}: {
  activity: Activity;
  oldKey: Address;
  newKey: {
    address: Address;
    keyType: "cloudKey" | "deviceKey";
  };
}) {
  const keyLabel = newKey.keyType === "deviceKey" ? "Device Key" : "Cloud Key";
  const status = activity.status;
  return (
    <ActivityViewRow
      activity={activity}
      icon={
        <IconWrapper backgroundColorToken="backgroundSecondary" size={42}>
          {activity.status.type === "confirmed" ? (
            <Icon
              name="checkmark.gobackward"
              weight="medium"
              size={16}
              colorToken="textSecondary"
              style={{ top: -1 }}
            />
          ) : activity.status.type === "cancelled" ? (
            <Icon
              name="xmark.circle"
              weight="medium"
              size={16}
              colorToken="textSecondary"
            />
          ) : (
            <Icon
              name="gobackward"
              weight="medium"
              size={16}
              colorToken="textSecondary"
              style={{ top: -1 }}
            />
          )}
        </IconWrapper>
      }
      left={{
        up: (
          <Title>
            Recovery{" "}
            {activity.status.type === "confirmed"
              ? "Completed"
              : activity.status.type === "cancelled"
                ? "Cancelled"
                : activity.status.type === "pending"
                  ? "Initiated"
                  : activity.status.type === "failed"
                    ? "Failed"
                    : " Status Unknown"}
          </Title>
        ),
        down: (
          <P3 colorToken="textSecondary">
            {keyLabel}: {abbreviateAddress(newKey.address)}
          </P3>
        ),
      }}
      right={
        status.type === "pending" ? (
          <CancelRecoveryButton activityId={activity.activityId} />
        ) : undefined
      }
      alwaysShowRight={status.type === "pending"}
    />
  );
}

function WalletCreatedTransactionView({ activity }: { activity: Activity }) {
  return (
    <ActivityViewRow
      activity={activity}
      icon={
        <IconWrapper backgroundColorToken="backgroundSecondary" size={42}>
          <Icon name="sparkles" size={16} colorToken="textSecondary" />
        </IconWrapper>
      }
      left={{
        up: <Title>Created Wallet</Title>,
        down: null,
      }}
    />
  );
}

function TransferTransactionView({
  spendingLimitUse,
  activity,
  type,
  amount,
  decimals,
  mint,
  address,
  usdPrice,
}: {
  spendingLimitUse?: boolean;
  activity: {
    activityId: string;
    timestamp: number;
    status: ActivityStatus;
  };
  type: "inflow" | "outflow";
  amount: number;
  decimals: number;
  mint: Address | "SOL";
  address: Address;
  usdPrice: number | null;
}) {
  const { wallet } = useActiveWallet();
  const token = useSuspenseToken({ mint });
  const tokenAmount = useTokenAmountString({
    mint,
    amount,
    filterSmallAmounts: true,
  });

  const usdValue = tokenUsdValue({
    amount,
    decimals,
    usdcPrice: usdPrice,
  });

  function value<A, B>({ inflow, outflow }: { inflow: A; outflow: B }) {
    return type === "inflow" ? inflow : outflow;
  }

  return (
    <ActivityViewRow
      activity={activity}
      icon={
        <CoinLogo
          mint={mint}
          variant={coinVariant(activity)}
          subIcon={spendingLimitUse ? "clock.arrow.2.circlepath" : undefined}
        />
      }
      left={{
        up: (
          <Title>
            {token?.name ??
              (mint === "SOL" ? "Solana" : abbreviateAddress(mint))}
          </Title>
        ),
        down: (
          <P3 colorToken="textSecondary">
            {value({ inflow: "From", outflow: "To" })}:{" "}
            {abbreviateAddress(address)}
          </P3>
        ),
      }}
      right={{
        up: (
          <P3 colorToken={value({ inflow: "green", outflow: "red" })}>
            {value({ inflow: "+", outflow: "-" })}
            {tokenAmount ?? amount}
          </P3>
        ),
        down: (
          <P3 colorToken="textSecondary">
            {usdValue > 0 ? formatUsdValue(usdValue) : null}
          </P3>
        ),
      }}
    />
  );
}

function ActivityViewRow({
  activity,
  left,
  right,
  icon,
  alwaysShowRight = false,
}: {
  activity: {
    status: ActivityStatus;
  };
  icon: ReactNode;
  left: { up: ReactNode; down: ReactNode };
  right?: { up: ReactNode; down: ReactNode } | ReactElement<any>;
  alwaysShowRight?: boolean;
}) {
  const status = activity.status;
  const opacity = status.type !== "confirmed" ? 0.5 : 1;

  function isReactElement(element: any): element is ReactElement<any> {
    return isValidElement(element);
  }

  const { isBalanceHidden } = useBalanceSettings();

  return (
    <Row
      gap={8}
      justify="space-between"
      style={{
        paddingVertical: 12,
      }}
    >
      <Row gap={12} style={{ opacity, flex: 1 }}>
        {icon}
        <View style={{ flexShrink: 1 }} gap={4}>
          {left.up}
          <View>{left.down}</View>
        </View>
      </Row>
      {isBalanceHidden && !alwaysShowRight ? (
        <P3 colorToken="textSecondary">{HIDDEN_BALANCE_TEXT}</P3>
      ) : (
        right &&
        (isReactElement(right) ? (
          right
        ) : (
          <View gap={4} style={{ opacity, alignItems: "flex-end" }}>
            {right.up}
            {right.down}
          </View>
        ))
      )}
    </Row>
  );
}

function CancelRecoveryButton({ activityId }: { activityId: string }) {
  const queryClient = useQueryClient();
  const { wallet } = useActiveWallet();
  const walletKey = wallet.walletKey;

  const reclaimRentMutation = useMutation({
    mutationFn: async () => {
      const action = await prepareAction({
        type: "recoveryCancel",
        activityId: activityId,
      });

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );

      await executeAction({ signedTransactions });
      const status = await confirmActivity({ activityId });
      updateActivity(queryClient, {
        activityId,
        walletKey,
        activity: { status },
      });
      await refetchWalletRecoveryState(queryClient, { walletKey });
    },
    onError: () => toast.error("Rent reclamation failed"),
  });

  return (
    <View style={{ minWidth: 100 }}>
      <Button
        variant="secondary"
        size="medium"
        loading={reclaimRentMutation.isPending}
        onPress={() => reclaimRentMutation.mutate()}
      >
        Cancel
      </Button>
    </View>
  );
}

function SwapTransactionView({
  activity,
  activityId,
  status,
  inputMint,
  outputMint,
  inAmount,
  outAmount,
  routes,
}: {
  activity: Activity;
  activityId: string;
  status: ActivityStatus;
  inputMint: Address;
  outputMint: Address;
  inAmount: number;
  outAmount: number;
  routes: string[];
}) {
  const inToken = useSuspenseToken({ mint: inputMint });
  const inTokenAmount = useTokenAmountString({
    mint: inputMint,
    amount: inAmount,
    filterSmallAmounts: true,
  });

  const outToken = useSuspenseToken({ mint: outputMint });
  const outTokenAmount = useTokenAmountString({
    mint: outputMint,
    amount: outAmount,
    filterSmallAmounts: true,
  });

  const [firstRoute, ...restRoutes] = routes;

  return (
    <ActivityViewRow
      activity={activity}
      icon={
        <CoinPairLogo
          variant={coinVariant(activity)}
          mint1={inputMint}
          mint2={outputMint}
        />
      }
      left={{
        up: (
          <Row gap={6} style={{ maxWidth: 170, alignItems: "center" }}>
            <Title>{inToken?.symbol ?? inputMint.slice(3)}</Title>
            <Icon name="arrow.right" weight="bold" size={8} />
            <Title>{outToken?.symbol ?? outputMint.slice(3)}</Title>
          </Row>
        ),
        down: (
          <P3 colorToken="textSecondary" numberOfLines={1} ellipsizeMode="tail">
            via {firstRoute}{" "}
            {restRoutes.length > 0 ? `+ ${restRoutes.length} more` : undefined}
          </P3>
        ),
      }}
      right={{
        up: <P3 colorToken="green">+{outTokenAmount}</P3>,
        down: <P3 colorToken="red">-{inTokenAmount}</P3>,
      }}
    />
  );
}

function UpdateRecoveryKeyView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "updateRecoveryKeys" };
}) {
  const keyValue =
    details.details.type === "addKey"
      ? RecoveryKey.getStringValue(details.details.newKey)
      : details.details.type === "changeKey"
        ? RecoveryKey.getStringValue(details.details.newKey)
        : details.details.type === "removeKey"
          ? RecoveryKey.getStringValue(details.details.oldKey)
          : (details.details satisfies never);

  const actionLabel =
    details.details.type === "addKey"
      ? "Added"
      : details.details.type === "changeKey"
        ? "Changed"
        : details.details.type === "removeKey"
          ? "Removed"
          : (details.details satisfies never);

  return (
    <ActivityViewRow
      activity={activity}
      icon={<ActivityIcon activity={activity} />}
      left={{
        up: <Title>{actionLabel} Recovery Key</Title>,
        down: <P3 colorToken="textSecondary">{keyValue}</P3>,
      }}
    />
  );
}

function MarinadeNativeStakeView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & {
    type:
      | "marinadeNativeStake"
      | "marinadeNativeDeactivate"
      | "marinadeNativeWithdraw";
  };
}) {
  const actionLabel =
    details.type === "marinadeNativeStake"
      ? "Stake"
      : details.type === "marinadeNativeDeactivate"
        ? "Unstake"
        : details.type === "marinadeNativeWithdraw"
          ? "Withdraw"
          : (details satisfies never);

  const amountColor =
    details.type === "marinadeNativeStake"
      ? "red"
      : details.type === "marinadeNativeDeactivate"
        ? "textSecondary"
        : details.type === "marinadeNativeWithdraw"
          ? "green"
          : (details satisfies never);

  const amount = useTokenAmountString({
    mint: "SOL",
    amount: details.lamports,
    filterSmallAmounts: true,
  });

  const usdValue = tokenUsdValue({
    amount: details.lamports,
    decimals: 9,
    usdcPrice: details.usdPrice,
  });

  const sign =
    details.type === "marinadeNativeStake"
      ? "-"
      : details.type === "marinadeNativeDeactivate"
        ? ""
        : details.type === "marinadeNativeWithdraw"
          ? "+"
          : (details satisfies never);

  const amountString = `${sign}${amount}`;

  return (
    <ActivityViewRow
      activity={activity}
      icon={<MarinadeIcon />}
      left={{
        up: <Title>{actionLabel}</Title>,
        down: <P3 colorToken="textSecondary">Marinade Native</P3>,
      }}
      right={{
        up: <P3 colorToken={amountColor}>{amountString}</P3>,
        down: <P3 colorToken="textSecondary">{formatUsdValue(usdValue)}</P3>,
      }}
    />
  );
}

function StakeView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "stake" };
}) {
  const actionLabel =
    details.details.type === "deposit"
      ? "Stake"
      : details.details.type === "deactivate"
        ? "Unstake"
        : details.details.type === "withdraw"
          ? "Withdraw"
          : (details.details satisfies never);

  const lamports = details.details.lamports;

  const amount = useTokenAmountString({
    mint: "SOL",
    amount: lamports,
    filterSmallAmounts: true,
  });

  const usdValue = tokenUsdValue({
    amount: lamports,
    decimals: 9,
    usdcPrice: details.usdPrice,
  });

  const amountColor =
    details.details.type === "deposit"
      ? "red"
      : details.details.type === "deactivate"
        ? "text"
        : details.details.type === "withdraw"
          ? "green"
          : (details.details satisfies never);

  const sign =
    details.details.type === "deposit"
      ? "-"
      : details.details.type === "deactivate"
        ? ""
        : details.details.type === "withdraw"
          ? "+"
          : (details.details satisfies never);

  const amountString = `${sign}${amount}`;

  return (
    <ActivityViewRow
      activity={activity}
      icon={<ValidatorIcon validator={details.details.voteKey} />}
      left={{
        up: <Title>{actionLabel}</Title>,
        down: <P3 colorToken="textSecondary">Squads Validator</P3>,
      }}
      right={{
        up: <P3 colorToken={amountColor}>{amountString}</P3>,
        down: <P3 colorToken="textSecondary">{formatUsdValue(usdValue)}</P3>,
      }}
    />
  );
}

function DepositLiquidStakeView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "depositLiquidStake" };
}) {
  const inTokenAmount = useTokenAmountString({
    mint: "SOL",
    amount: details.lamports,
    filterSmallAmounts: true,
  });

  const lstAmount = details.lamports / details.lstPrice;
  const outTokenAmount = formatTokenAmount(lstAmount, "fuseSOL", {
    filterSmallAmounts: true,
  });

  return (
    <ActivityViewRow
      activity={activity}
      icon={
        <CoinLogo
          mint={mints.fuseSol}
          size={42}
          variant={coinVariant(activity)}
        />
      }
      left={{
        up: <Title>Liquid Stake</Title>,
        down: <P3 colorToken="textSecondary">fuseSOL</P3>,
      }}
      right={{
        up: <P3 colorToken="green">+{outTokenAmount}</P3>,
        down: <P3 colorToken="red">-{inTokenAmount}</P3>,
      }}
    />
  );
}

function LiquidStakeWithdrawView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "liquidStakeWithdraw" };
}) {
  const solAmount = details.amount * (details.lstPrice / LAMPORTS_PER_SOL);

  const inTokenAmount = formatSPLAmount({
    amount: details.amount,
    decimals: 9,
    symbol: "fuseSOL",
  });
  const outTokenAmount = formatSolAmount(solAmount);

  return (
    <ActivityViewRow
      activity={activity}
      icon={
        <CoinLogo
          mint={mints.fuseSol}
          size={42}
          variant={coinVariant(activity)}
        />
      }
      left={{
        up: <Title>Liquid Unstake</Title>,
        down: <P3 colorToken="textSecondary">fuseSOL</P3>,
      }}
      right={{
        up: <P3 colorToken="green">+{outTokenAmount}</P3>,
        down: <P3 colorToken="red">-{inTokenAmount}</P3>,
      }}
    />
  );
}

function TransferNftView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "receiveNft" | "sendNft" };
}) {
  return (
    <ActivityViewRow
      activity={activity}
      icon={
        <NftImage
          nft={{
            imageUrl: details.metadata?.imageUrl ?? null,
            videoUrl: details.metadata?.videoUrl ?? null,
          }}
          width={42}
          borderRadius={8}
        />
      }
      left={{
        up: <Title>{details.metadata?.name ?? "Unknown NFT"}</Title>,
        down: (
          <P3 colorToken="textSecondary">
            {details.type === "sendNft"
              ? `To: ${abbreviateAddress(details.to)}`
              : details.from
                ? `From: ${abbreviateAddress(details.from)}`
                : null}
          </P3>
        ),
      }}
    />
  );
}

function SpendingLimitView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "spendingLimit" };
}) {
  const amount = useTokenAmountString({
    mint: details.details.mint ?? "SOL",
    amount: details.details.amount,
    filterSmallAmounts: true,
  });

  const period = details.details.period;

  return (
    <ActivityViewRow
      activity={activity}
      icon={
        <IconWrapper backgroundColorToken="backgroundSecondary" size={42}>
          <Icon
            name="clock.arrow.2.circlepath"
            weight="medium"
            size={16}
            colorToken="textSecondary"
          />
        </IconWrapper>
      }
      left={{
        up: (
          <Title>{`${details.details.type === "add" ? "Created" : "Removed"} Spending Limit`}</Title>
        ),
        down: (
          <P3 colorToken="textSecondary">
            {amount} •{" "}
            <P3
              colorToken="textSecondary"
              style={{ textTransform: "capitalize" }}
            >
              {period}
            </P3>
          </P3>
        ),
      }}
    />
  );
}

function SpendingLimitTransferView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "spendingLimitTransfer" };
}) {
  return (
    <ActivityViewRow
      activity={activity}
      icon={
        <IconWrapper backgroundColorToken="backgroundSecondary" size={42}>
          <Icon
            name="clock.arrow.2.circlepath"
            weight="medium"
            size={16}
            colorToken="textSecondary"
          />
        </IconWrapper>
      }
      left={{
        up: <Title>Spending Limit Transfer</Title>,
        down: (
          <P3 colorToken="textSecondary">
            New device • {abbreviateAddress(details.oldMember)}
          </P3>
        ),
      }}
    />
  );
}

function UpdateCloudKeyView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "updateCloudKey" };
}) {
  const keyValue = abbreviateAddress(details.newKey.address);

  const actionLabel =
    details.newKey.details.type === "iCloud"
      ? "Changed to Cloud Key"
      : "Changed 2FA Key";

  return (
    <ActivityViewRow
      activity={activity}
      icon={
        <IconWrapper backgroundColorToken="backgroundSecondary" size={42}>
          <KeyChangeIcon type="add" />
        </IconWrapper>
      }
      left={{
        up: <Title>{actionLabel}</Title>,
        down: <P3 colorToken="textSecondary">{keyValue}</P3>,
      }}
    />
  );
}

function PayFeesWithFuseSolView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & {
    type: "payFeesWithFuseSolEnable" | "payFeesWithFuseSolDisable";
  };
}) {
  return (
    <ActivityViewRow
      activity={activity}
      icon={
        <IconWrapper size={42} backgroundColorToken="backgroundSecondary">
          <BoltIcon colorToken="textSecondary" />
        </IconWrapper>
      }
      left={{
        up: <Title>fuseSOL top-up</Title>,
        down: (
          <P3 colorToken="textSecondary">
            {details.type === "payFeesWithFuseSolEnable"
              ? "Enabled"
              : "Disabled"}
          </P3>
        ),
      }}
    />
  );
}

function RelayerBalanceChangeView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & {
    type: "relayerWithdraw" | "relayerTopUp";
  };
}) {
  const amountString = useTokenAmountString({
    mint: activity.relayerTopUp?.type === "fuseSol" ? mints.fuseSol : "SOL",
    amount:
      details.type === "relayerWithdraw"
        ? details.amount
        : (activity?.relayerTopUp?.amount ?? 0),
    filterSmallAmounts: true,
  });

  const amountColor =
    details.type === "relayerWithdraw"
      ? "red"
      : details.type === "relayerTopUp"
        ? "green"
        : (details satisfies never);

  return (
    <ActivityViewRow
      activity={activity}
      icon={
        <IconWrapper size={42} backgroundColorToken="backgroundSecondary">
          <BoltIcon colorToken="textSecondary" />
        </IconWrapper>
      }
      left={{
        up: <Title>Paymaster</Title>,
        down: (
          <P3 colorToken="textSecondary">
            {details.type === "relayerWithdraw" ? "Withdraw" : "Top-up"}
          </P3>
        ),
      }}
      right={{
        up: <P3 colorToken={amountColor}>{amountString}</P3>,
        down: <P3></P3>,
      }}
    />
  );
}

function DriftEarnDepositView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "driftEarnDeposit" };
}) {
  const mint = DriftEarnDepositDetails.mint(details.deposit);

  const amountString = useTokenAmountString({
    mint,
    amount: details.deposit.amount,
    filterSmallAmounts: true,
  });

  return (
    <ActivityViewRow
      activity={activity}
      icon={<CoinLogo mint={mint} size={42} />}
      left={{
        up: <Title>Drift Earn</Title>,
        down: (
          <Text variant="medium" size={15} colorToken="textSecondary">
            Deposit
          </Text>
        ),
      }}
      right={{
        up: (
          <Text variant="medium" size={15}>
            {amountString}
          </Text>
        ),
        down: (
          <Text variant="medium" size={15}>
            {" "}
          </Text>
        ),
      }}
    />
  );
}

function DriftEarnWithdrawView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "driftEarnWithdraw" };
}) {
  const mint = DriftEarnDepositDetails.mint(details.deposit);

  const amountString = useTokenAmountString({
    mint,
    amount: details.deposit.amount,
    filterSmallAmounts: true,
  });

  return (
    <ActivityViewRow
      activity={activity}
      icon={<CoinLogo mint={mint} size={42} />}
      left={{
        up: <Title>Drift Earn</Title>,
        down: (
          <Text variant="medium" size={15} colorToken="textSecondary">
            Withdraw
          </Text>
        ),
      }}
      right={{
        up: (
          <Text variant="medium" size={15}>
            {amountString}
          </Text>
        ),
        down: (
          <Text variant="medium" size={15}>
            {" "}
          </Text>
        ),
      }}
    />
  );
}

function LuloProtectedEarnDepositView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "luloProtectedEarnDeposit" };
}) {
  const mint = LuloProtectedEarnDepositDetails.mint(details.deposit);

  const amountString = useTokenAmountString({
    mint,
    amount: details.deposit.amount,
    filterSmallAmounts: true,
  });

  return (
    <ActivityViewRow
      activity={activity}
      icon={<CoinLogo mint={mint} size={42} />}
      left={{
        up: <Title>Lulo Earn</Title>,
        down: (
          <Text variant="medium" size={15} colorToken="textSecondary">
            Deposit
          </Text>
        ),
      }}
      right={{
        up: (
          <Text variant="medium" size={15}>
            {amountString}
          </Text>
        ),
        down: (
          <Text variant="medium" size={15}>
            {" "}
          </Text>
        ),
      }}
    />
  );
}

function LuloProtectedEarnWithdrawView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "luloProtectedEarnWithdraw" };
}) {
  const mint = LuloProtectedEarnDepositDetails.mint(details.withdrawal);

  const amountString = useTokenAmountString({
    mint,
    amount: details.withdrawal.amount,
    filterSmallAmounts: true,
  });

  return (
    <ActivityViewRow
      activity={activity}
      icon={<CoinLogo mint={mint} size={42} />}
      left={{
        up: <Title>Lulo Earn</Title>,
        down: (
          <Text variant="medium" size={15} colorToken="textSecondary">
            Withdraw
          </Text>
        ),
      }}
      right={{
        up: (
          <Text variant="medium" size={15}>
            {amountString}
          </Text>
        ),
        down: (
          <Text variant="medium" size={15}>
            {" "}
          </Text>
        ),
      }}
    />
  );
}

function KaminoEarnDepositView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "kaminoEarnDeposit" };
}) {
  const mint = KaminoEarnDepositDetails.mint(details.deposit);

  const amountString = useTokenAmountString({
    mint,
    amount: details.deposit.amount,
    filterSmallAmounts: true,
  });

  return (
    <ActivityViewRow
      activity={activity}
      icon={<CoinLogo mint={mint} size={42} />}
      left={{
        up: <Title>Kamino Earn</Title>,
        down: (
          <Text variant="medium" size={15} colorToken="textSecondary">
            Deposit
          </Text>
        ),
      }}
      right={{
        up: (
          <Text variant="medium" size={15}>
            {amountString}
          </Text>
        ),
        down: (
          <Text variant="medium" size={15}>
            {" "}
          </Text>
        ),
      }}
    />
  );
}

function KaminoEarnWithdrawView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "kaminoEarnWithdraw" };
}) {
  const mint = KaminoEarnDepositDetails.mint(details.withdrawal);

  const amountString = useTokenAmountString({
    mint,
    amount: details.withdrawal.amount,
    filterSmallAmounts: true,
  });

  return (
    <ActivityViewRow
      activity={activity}
      icon={<CoinLogo mint={mint} size={42} />}
      left={{
        up: <Title>Kamino Earn</Title>,
        down: (
          <Text variant="medium" size={15} colorToken="textSecondary">
            Withdraw
          </Text>
        ),
      }}
      right={{
        up: (
          <Text variant="medium" size={15}>
            {amountString}
          </Text>
        ),
        down: (
          <Text variant="medium" size={15}>
            {" "}
          </Text>
        ),
      }}
    />
  );
}

function KycView({
  activity,
  details,
}: {
  activity: Activity;
  details:
    | (Activity["details"] & { type: "kycStarted" })
    | (Activity["details"] & { type: "kycApproved" })
    | (Activity["details"] & { type: "kycRejected" });
}) {
  const icon =
    details.type === "kycStarted" ? (
      <StampClockIcon />
    ) : details.type === "kycApproved" ? (
      <StampCheckmarkIcon />
    ) : details.type === "kycRejected" ? (
      <StampCrossIcon />
    ) : null;

  const label =
    details.type === "kycStarted"
      ? "Verification Pending"
      : details.type === "kycApproved"
        ? "Verification Successful"
        : details.type === "kycRejected"
          ? "Verification Failed"
          : null;

  const subLabel =
    details.type === "kycStarted"
      ? "Bridge is verifying your identity"
      : details.type === "kycApproved"
        ? "Bridge has verified your identity"
        : details.type === "kycRejected"
          ? (details.details?.reason ?? "Try to re-upload your ID")
          : null;

  return (
    <ActivityViewRow
      activity={activity}
      icon={
        <IconWrapper backgroundColorToken="backgroundSecondary" size={42}>
          <Icon
            name="person.fill"
            weight="medium"
            size={16}
            colorToken="textSecondary"
          />
          <View style={{ position: "absolute", right: -3, top: -3 }}>
            {icon}
          </View>
        </IconWrapper>
      }
      left={{
        up: <Title>{label}</Title>,
        down: <P3 colorToken="textSecondary">{subLabel}</P3>,
      }}
    />
  );
}

function VirtualAccountReceiveView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "virtualAccountReceive" };
}) {
  const status = details.status;
  let label;
  switch (status) {
    case "funds_received":
    case "payment_submitted":
    case "refunded":
      label = `${VirtualAccountTransferStatus.view(status).label}`;
      break;
    case "payment_processed":
    case "microdeposit":
      const from =
        details.source.payment_rail === "wire"
          ? details.source.bankBeneficiaryName
          : details.source.senderName;
      label = `From: ${from}`;
      break;
    default:
      status satisfies never;
  }

  return (
    <ActivityViewRow
      activity={activity}
      icon={<ActivityIcon activity={activity} size={42} />}
      left={{
        up: (
          <Title>
            {status === "microdeposit" ? "Microdeposit" : `USDC Deposit`}
          </Title>
        ),
        down: (
          <P3 numberOfLines={1} colorToken="textSecondary">
            {label}
          </P3>
        ),
      }}
      right={{
        up:
          status === "refunded" ? (
            <StrikeThrough>
              <P3 colorToken="textSecondary">
                {formatUsdValue(details.amount, { renderCurrency: false })} USDC
              </P3>
            </StrikeThrough>
          ) : (
            <P3 colorToken="green">
              +{formatUsdValue(details.amount, { renderCurrency: false })} USDC
            </P3>
          ),
        down: null,
      }}
    />
  );
}

function StrikeThrough({ children }: { children: ReactNode }) {
  return (
    <View style={{ justifyContent: "center", opacity: 0.5 }}>
      <View
        style={{
          backgroundColor: "#9e9e9e",
          height: 1.5,
          position: "absolute",
          left: -3,
          right: -3,
          zIndex: 1,
        }}
      />
      {children}
    </View>
  );
}

function SendFiatView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "sendFiat" };
}) {
  const bankAccount = details.destination.bankAccount;
  const source = details.source;

  const tokenAmount = useTokenAmountString({
    mint: source.mint,
    amount: source.amount,
  });

  const usdValue = tokenUsdValue({
    amount: source.amount,
    decimals: source.decimals,
    usdcPrice: source.usdPrice,
  });

  const isUnsuccessful = SendFiatStatus.isUnsuccessful(details.status);
  const label = isUnsuccessful
    ? SendFiatStatus.view(details.status).label
    : `To: ${RecipientBankAccount.getLabel(bankAccount)}`;

  return (
    <ActivityViewRow
      activity={activity}
      icon={<ActivityIcon activity={activity} size={42} />}
      left={{
        up: <Title>Bank transfer</Title>,
        down: (
          <P3 numberOfLines={1} colorToken="textSecondary">
            {label}
          </P3>
        ),
      }}
      right={{
        up: isUnsuccessful ? (
          <StrikeThrough>
            <P3 colorToken="textSecondary">{tokenAmount}</P3>
          </StrikeThrough>
        ) : (
          <P3 colorToken="red">-{tokenAmount}</P3>
        ),
        down: isUnsuccessful ? null : (
          <P3 colorToken="textSecondary">{formatUsdValue(usdValue)}</P3>
        ),
      }}
    />
  );
}

function CardCreateView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "cardCreate" };
}) {
  const { wallet } = useActiveWallet();
  const card = wallet.card;

  return (
    <ActivityViewRow
      activity={activity}
      icon={<ActivityIcon activity={activity} size={42} />}
      left={{
        up: <Title>Card activated</Title>,
        down: card?.status === "issued" && (
          <P3 colorToken="textSecondary">{WalletCard.label(card)}</P3>
        ),
      }}
    />
  );
}

function CardDepositView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "cardTopUp" };
}) {
  const { wallet } = useActiveWallet();
  const tokenAmount = useTokenAmountString({
    mint: details.mint,
    amount: details.amount,
  });

  const usdValue = tokenUsdValue({
    amount: details.amount,
    decimals: details.decimals,
    usdcPrice: 1.0,
  });

  const card = wallet.card;

  return (
    <ActivityViewRow
      activity={activity}
      icon={<ActivityIcon activity={activity} size={42} />}
      left={{
        up: <Title>Card Deposit</Title>,
        down: card?.status === "issued" && (
          <P3 colorToken="textSecondary">{WalletCard.label(card)}</P3>
        ),
      }}
      right={{
        up: <P3 colorToken="red">-{tokenAmount}</P3>,
        down: <P3 colorToken="textSecondary">{formatUsdValue(usdValue)}</P3>,
      }}
    />
  );
}

function CardWithdrawView({
  activity,
  details,
}: {
  activity: Activity;
  details: Activity["details"] & { type: "cardWithdraw" };
}) {
  const { wallet } = useActiveWallet();
  const tokenAmount = useTokenAmountString({
    mint: details.mint,
    amount: details.amount,
  });

  const usdValue = tokenUsdValue({
    amount: details.amount,
    decimals: details.decimals,
    usdcPrice: 1.0,
  });

  const card = wallet.card;

  return (
    <ActivityViewRow
      activity={activity}
      icon={<ActivityIcon activity={activity} size={42} />}
      left={{
        up: <Title>Card Withdrawal</Title>,
        down: card?.status === "issued" && (
          <P3 colorToken="textSecondary">{WalletCard.label(card)}</P3>
        ),
      }}
      right={{
        up: <P3 colorToken="green">+{tokenAmount}</P3>,
        down: <P3 colorToken="textSecondary">{formatUsdValue(usdValue)}</P3>,
      }}
    />
  );
}

function UnknownActivityView({ activity }: { activity: Activity }) {
  const executeTx = activity.transactions.find(
    (transaction) => transaction.transactionType === "execute"
  );

  return (
    <ActivityViewRow
      activity={activity}
      icon={
        <IconWrapper backgroundColorToken="backgroundSecondary" size={42}>
          <Icon
            name="questionmark.circle"
            weight="medium"
            size={16}
            colorToken="textSecondary"
          />
        </IconWrapper>
      }
      left={{
        up: <Title>Unknown activity</Title>,
        down: (
          <P3 colorToken="textSecondary">
            {executeTx ? abbreviateAddress(executeTx.signature) : ""}
          </P3>
        ),
      }}
    />
  );
}

function Title(props: ComponentProps<typeof Text>) {
  return <H4 numberOfLines={1} {...props} />;
}

function coinVariant(activity: {
  timestamp: number;
  status: ActivityStatus;
}): CoinLogoVariant {
  const is2MinsOld = activity.timestamp + 120 < Date.now() / 1000;

  const status =
    activity.status.type === "pending" && is2MinsOld
      ? "failed"
      : activity.status.type;

  switch (status) {
    case "confirmed":
      return "default";
    case "pending":
      return "loading";
    case "failed":
      return "error";
    case "cancelled":
      return "error";
    case "unknown":
      return "error";
    default:
      return status satisfies never;
  }
}
