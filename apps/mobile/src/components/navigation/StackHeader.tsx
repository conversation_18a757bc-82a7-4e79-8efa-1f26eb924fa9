import { Text, useColor, View } from "~/components/Themed";
import { AccessibleBlurView } from "~/components/AccessibleBlurView";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import Animated, {
  AnimatedRef,
  Extrapolation,
  interpolate,
  useAnimatedReaction,
  useAnimatedStyle,
  useScrollViewOffset,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";
import { DURATION_FAST } from "~/constants/animations";
import { ComponentProps, ReactNode } from "react";
import { ColorName } from "~/constants/Colors";
import { OptionalBackButton } from "~/components/headers/BackButton";

const MAIN_ROW_HEIGHT = 44;
export const LARGE_TITLE_ROW_HEIGHT = 52;
const LARGE_TITLE_HIDDEN_THRESHOLD = 32;
export const HEADER_HEIGHT = MAIN_ROW_HEIGHT + LARGE_TITLE_ROW_HEIGHT;

export function StackHeader({
  title,
  scrollViewRef,
  headerBackgroundColor,
  backButtonEnteringAnimation,
  rightButton,
}: {
  title: string | ((size: 17 | 34) => ReactNode);
  scrollViewRef: AnimatedRef<any>;
  headerBackgroundColor?: ColorName;
  rightButton?: ReactNode;
  backButtonEnteringAnimation?: ComponentProps<Animated.View>["entering"];
}) {
  const router = useRouter();

  const insets = useSafeAreaInsets();

  const blurredHeaderBackgroundColor = useColor("backgroundTabBar");
  const headerFallbackBackgroundColor = useColor("backgroundSecondary");

  const scrollOffset = useScrollViewOffset(
    scrollViewRef.current ? scrollViewRef : null
  );

  const largeTitleOpacity = useSharedValue(1);
  useAnimatedReaction(
    () => scrollOffset.value,
    (value, previous) => {
      if (previous == null) return;

      if (
        value >= LARGE_TITLE_HIDDEN_THRESHOLD &&
        previous < LARGE_TITLE_HIDDEN_THRESHOLD
      ) {
        largeTitleOpacity.value = withTiming(0, { duration: DURATION_FAST });
      } else if (
        value < LARGE_TITLE_HIDDEN_THRESHOLD &&
        previous >= LARGE_TITLE_HIDDEN_THRESHOLD
      ) {
        largeTitleOpacity.value = withTiming(1, { duration: DURATION_FAST });
      }
    }
  );

  const smallTitleAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(
        largeTitleOpacity.value,
        [1, 0],
        [0, 1],
        Extrapolation.CLAMP
      ),
    };
  });

  const largeTitleAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: largeTitleOpacity.value,
      transform: [
        {
          translateY: interpolate(
            scrollOffset.value,
            [0, LARGE_TITLE_ROW_HEIGHT],
            [0, -LARGE_TITLE_ROW_HEIGHT],
            Extrapolation.EXTEND
          ),
        },
        {
          scale: interpolate(
            scrollOffset.value,
            [-100, 0],
            [1.1, 1],
            Extrapolation.CLAMP
          ),
        },
      ],
    };
  });

  return (
    <View
      style={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1,
      }}
    >
      {/* Top row */}
      <AccessibleBlurView
        intensity={60}
        tint="light"
        fallbackBackgroundColor={headerFallbackBackgroundColor}
        style={{
          zIndex: 1,
          paddingTop: insets.top,
          backgroundColor:
            headerBackgroundColor ?? blurredHeaderBackgroundColor,
        }}
      >
        <View
          style={{
            gap: 12,
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            paddingHorizontal: 20,
            height: MAIN_ROW_HEIGHT,
          }}
        >
          <View>
            <OptionalBackButton />
          </View>
          <Animated.View style={[smallTitleAnimatedStyle, { flexShrink: 1 }]}>
            {typeof title === "string" ? (
              <Text variant="semibold" style={{ fontSize: 17 }}>
                {title}
              </Text>
            ) : (
              title(17)
            )}
          </Animated.View>
          <Animated.View
            entering={backButtonEnteringAnimation}
            style={{
              width: 30,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            {rightButton}
          </Animated.View>
        </View>
      </AccessibleBlurView>
      {/* Bottom row */}
      <Animated.View
        style={[
          {
            height: LARGE_TITLE_ROW_HEIGHT,
            paddingHorizontal: 20,
            transformOrigin: "left bottom",
          },
          largeTitleAnimatedStyle,
        ]}
      >
        {/*<Animated.View sharedTransitionTag="headerLargeTitle">*/}
        {typeof title === "string" ? (
          <Text
            adjustsFontSizeToFit
            numberOfLines={1}
            variant="bold"
            style={{ fontSize: 34 }}
          >
            {title}
          </Text>
        ) : (
          title(34)
        )}
        {/*</Animated.View>*/}
      </Animated.View>
    </View>
  );
}
