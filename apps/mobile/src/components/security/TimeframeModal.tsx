import { ComponentProps, RefObject, useState } from "react";
import { Text, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import * as Haptics from "expo-haptics";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import Animated, {
  FadeInUp,
  FadeOut,
  ReduceMotion,
} from "react-native-reanimated";
import { DURATION_FAST } from "~/constants/animations";
import { SpendingLimitPeriod } from "~/services/spendingLimits";
import { P3 } from "~/components/typography/P3";
import { Row } from "~/components/Grid";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";

const periods: SpendingLimitPeriod[] = ["daily", "weekly", "monthly"];

export function TimeframeModal({
  selected,
  onSelect,
  modalRef,
}: {
  selected: SpendingLimitPeriod | null;
  onSelect: (selected: SpendingLimitPeriod) => void;
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  return (
    <BottomModal
      modalRef={modalRef}
      modalId={"slippage-modal"}
      title={"Timeframe"}
      body={
        <Row style={{ gap: 12, paddingTop: 20, justifyContent: "center" }}>
          {periods.map((p) => (
            <View key={p} style={{ flex: 1 }}>
              <AnimatedTouchableScale
                onPress={() => {
                  modalRef.current?.close();
                  onSelect(p);
                  Haptics.selectionAsync();
                }}
                pressedScale={0.9}
              >
                <View
                  background={
                    p === selected
                      ? "backgroundTertiary"
                      : "backgroundSecondary"
                  }
                  style={{
                    paddingVertical: 12,
                    paddingHorizontal: 16,
                    borderRadius: 32,
                    borderCurve: "continuous",
                    flexDirection: "row",
                    justifyContent: "center",
                  }}
                >
                  <P3 style={{ textTransform: "capitalize" }}>{p}</P3>
                </View>
              </AnimatedTouchableScale>
            </View>
          ))}
        </Row>
      }
      footer={null}
    />
  );
}
