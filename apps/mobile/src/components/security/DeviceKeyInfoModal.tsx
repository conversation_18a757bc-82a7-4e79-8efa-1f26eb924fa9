import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Button } from "~/components/Button";
import { FeatureList, FeatureListItem } from "~/components/FeatureList";
import { RefObject } from "react";
import { hapticDismissBottomTray } from "~/utils/haptics";
import { Text, View } from "~/components/Themed";
import { IconWrapper } from "~/components/IconWrapper";
import { Icon } from "~/components/Icon";

export function DeviceKeyInfoModal(props: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  return (
    <BottomModal
      modalRef={props.modalRef}
      modalId={"device-key-info"}
      title=" "
      body={
        <View gap={24} style={{ paddingHorizontal: 14 }}>
          <View gap={24} style={{ alignItems: "center" }}>
            <IconWrapper
              backgroundColorToken={"text"}
              size={56}
              variant={"square"}
            >
              <Icon name="faceid" size={20} color={"white"} />
            </IconWrapper>

            <View gap={8} style={{ alignItems: "center" }}>
              <Text variant="semibold" size={20}>
                What's Device Key?
              </Text>
            </View>
          </View>

          <FeatureList>
            <FeatureListItem
              icon={"key.viewfinder"}
              label="Active Key"
              text="Used to approve transactions"
            />
            <FeatureListItem
              icon={"cloud.circle"}
              label="Secure"
              text="Stored on your device and protected by biometrics"
            />
            <FeatureListItem
              icon={"clock.arrow.2.circlepath"}
              label="Recovery"
              text="If lost, can be recovered by pairing your 2FA and Recovery Key"
            />
          </FeatureList>
        </View>
      }
      footer={
        <Button
          variant="secondary"
          size={"medium-new"}
          onPress={() => {
            hapticDismissBottomTray();
            props.modalRef.current?.close();
          }}
        >
          Got it
        </Button>
      }
    />
  );
}
