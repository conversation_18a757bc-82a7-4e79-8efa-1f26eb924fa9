import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { Row } from "~/components/Grid";
import { Button } from "~/components/Button";
import { SpendingLimit } from "~/services/spendingLimits";
import { P3 } from "~/components/typography/P3";
import { RefObject } from "react";
import { SpendingLimitTokenAmount } from "~/app/unlocked/add-spending-limit/sl-overview";
import { AnimatedButtonGroup } from "~/components/AnimatedButtonGroup";
import { IconWrapper } from "~/components/IconWrapper";
import { Icon } from "~/components/Icon";

export function DeleteSpendingLimitModal({
  modalRef,
  spendingLimit,
  isPending,
  onPressDelete,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
  spendingLimit: SpendingLimit;
  isPending: boolean;
  onPressDelete: () => void;
}) {
  return (
    <BottomModal
      modalId="delete-spendling-limit"
      modalRef={modalRef}
      title=" "
      body={
        <View gap={24} style={{ paddingHorizontal: 14 }}>
          <View gap={24} style={{ alignItems: "center" }}>
            <IconWrapper
              backgroundColorToken={"red"}
              size={56}
              variant={"square"}
            >
              <Icon
                name="dial.high.fill"
                size={20}
                color={"white"}
                style={{ top: -1 }}
              />
            </IconWrapper>

            <View gap={8} style={{ alignItems: "center" }}>
              <Text variant="semibold" size={20}>
                Remove Spending Limit?
              </Text>
              <Text
                variant="medium"
                size={16}
                colorToken="textSecondary"
                align="center"
              >
                Spending Limit will be removed.
              </Text>
            </View>
          </View>
          <View gap={12}>
            <Row justify={"space-between"}>
              <P3 colorToken={"textSecondary"}>Amount</P3>
              <P3>
                <SpendingLimitTokenAmount
                  token={{
                    mintOrSol: spendingLimit.mint ?? "SOL",
                    amount: spendingLimit.amount,
                  }}
                />
              </P3>
            </Row>
            <Row justify={"space-between"}>
              <P3 colorToken={"textSecondary"}>Timeframe</P3>
              <P3 style={{ textTransform: "capitalize" }}>
                {spendingLimit.period}
              </P3>
            </Row>
          </View>
        </View>
      }
      footer={
        <AnimatedButtonGroup
          left={
            isPending ? null : (
              <Button
                size={"medium-new"}
                variant="secondary"
                onPress={() => {
                  modalRef.current?.close();
                }}
              >
                Cancel
              </Button>
            )
          }
          right={
            <Button
              size={"medium-new"}
              variant="danger"
              loading={isPending}
              loadingText={"Removing"}
              onPress={onPressDelete}
            >
              Remove
            </Button>
          }
        />
      }
    />
  );
}
