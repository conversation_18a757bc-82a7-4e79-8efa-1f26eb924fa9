import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Button } from "~/components/Button";
import { CloudKey } from "~/services/wallets";
import { RefObject } from "react";
import { hapticDismissBottomTray } from "~/utils/haptics";
import { FeatureList, FeatureListItem } from "~/components/FeatureList";
import { IconWrapper } from "~/components/IconWrapper";
import { Icon } from "~/components/Icon";
import { Text, View } from "~/components/Themed";
import { TwoFAKeyFeatures } from "~/components/security/2FAKeyFeatures";
import { CloudKeyFeatures } from "~/components/security/CloudKeyFeatures";

export function CloudKeyInfoModal(props: {
  keyType: CloudKey["details"]["type"];
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  return (
    <BottomModal
      modalRef={props.modalRef}
      modalId={"cloud-key-info"}
      title={" "}
      body={props.keyType === "iCloud" ? <CloudKeyBody /> : <TwoFAKeyBody />}
      footer={
        <Button
          variant="secondary"
          size={"medium-new"}
          onPress={() => {
            hapticDismissBottomTray();
            props.modalRef.current?.close();
          }}
        >
          Got it
        </Button>
      }
    />
  );
}

function TwoFAKeyBody() {
  return (
    <View gap={24} style={{ paddingHorizontal: 14 }}>
      <View gap={24} style={{ alignItems: "center" }}>
        <IconWrapper backgroundColorToken={"blue"} size={56} variant={"square"}>
          <Icon name="key.radiowaves.forward" size={18} color={"white"} />
        </IconWrapper>

        <View gap={8} style={{ alignItems: "center" }}>
          <Text variant="semibold" size={20}>
            What's 2FA Key?
          </Text>
        </View>
      </View>

      <TwoFAKeyFeatures />
    </View>
  );
}

function CloudKeyBody() {
  return (
    <View gap={24} style={{ paddingHorizontal: 14 }}>
      <View gap={24} style={{ alignItems: "center" }}>
        <IconWrapper backgroundColorToken={"blue"} size={56} variant={"square"}>
          <Icon name="key.icloud.fill" size={20} color={"white"} />
        </IconWrapper>

        <View gap={8} style={{ alignItems: "center" }}>
          <Text variant="semibold" size={20}>
            What's Cloud Key?
          </Text>
        </View>
      </View>

      <CloudKeyFeatures />
    </View>
  );
}
