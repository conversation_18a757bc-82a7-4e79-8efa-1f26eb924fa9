import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text } from "~/components/Themed";
import { Flex, Row } from "~/components/Grid";
import { Button } from "~/components/Button";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "~/components/Toaster";
import { useActiveWallet } from "~/state/wallet";
import {
  refetchWalletRecoveryState,
  useWalletRecoveryState,
} from "~/state/walletRecovery";
import invariant from "invariant";
import { prepareAction } from "~/services/wallets";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { sendTransactionWithTrackingStatus } from "~/utils/sendTransactionWithTracking";
import { sleep } from "~/utils/promise";
import { RefObject } from "react";
import { isClientErrorReasonType } from "~/services/utils";
import { showNotEnoughSolModal } from "~/components/modals/NotEnoughSolModal";

export function Cancel2FAChangeModal({
  modalRef,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  const { wallet } = useActiveWallet();
  const walletKey = wallet.walletKey;
  const queryClient = useQueryClient();

  const activeRecovery = useWalletRecoveryState({
    walletKey: walletKey,
  });

  const cancel2FAChange = useMutation({
    mutationFn: async () => {
      invariant(activeRecovery, "activeRecovery should be set");
      invariant(
        activeRecovery.newKey.keyType === "cloudKey",
        "expected cloudKey recovery"
      );
      const activityId = activeRecovery.activityId;

      const action = await prepareAction({
        type: "recoveryCancel",
        activityId: activityId,
      }).catch((e) => {
        toast.error("Failed to initiate transaction");
        throw e;
      });

      modalRef.current?.close();
      //wait a bit for modal to close
      await sleep(300);

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      ).catch((e) => {
        toast.error("Failed to sign transaction");
        throw e;
      });

      await sendTransactionWithTrackingStatus({
        walletKey: wallet.walletKey,
        transaction: { type: "action", signedTransactions },
        sendSuccessMessage: `Cancelling 2FA Key change`,
        confirmationFailureMessage: `Failed to cancel 2FA change`,
      }).finally(() => refetchWalletRecoveryState(queryClient, { walletKey }));
    },
    onError: (error) => {
      if (isClientErrorReasonType(error, "notEnoughSol")) {
        showNotEnoughSolModal();
        return;
      }
    },
  });

  return (
    <BottomModal
      modalId="cancel-change-2fa"
      modalRef={modalRef}
      title="Cancel 2FA change"
      body={
        <Text
          variant="medium"
          colorToken="textSecondary"
          style={{ fontSize: 18 }}
        >
          Are you sure you want to cancel the change of the 2FA Key?
        </Text>
      }
      footer={
        <Row gap={12}>
          <Flex>
            <Button
              variant="secondary"
              onPress={() => {
                modalRef.current?.close();
              }}
            >
              Cancel
            </Button>
          </Flex>

          <Flex>
            <Button
              variant="danger"
              loading={cancel2FAChange.isPending}
              onPress={() => {
                cancel2FAChange.mutate();
              }}
            >
              Confirm
            </Button>
          </Flex>
        </Row>
      }
    />
  );
}
