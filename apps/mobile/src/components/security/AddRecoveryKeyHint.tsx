import {
  postponeSecurityWarning,
  useLazySecurityWarning,
} from "~/hooks/useSecurityWarning";
import { useLock } from "~/hooks/useLock";
import { useIsWalletInactive } from "~/hooks/useAssertWalletState";
import { View } from "~/components/Themed";
import { Row } from "~/components/Grid";
import { P3 } from "~/components/typography/P3";
import { ShieldIcon } from "~/components/icons/ShieldIcon";
import { Button } from "~/components/Button";
import * as Haptics from "expo-haptics";
import { router } from "expo-router";

export function AddRecoveryKeyHint() {
  const { showSecurityWarning, exceedUsdLimit } = useLazySecurityWarning();

  const withLock = useLock();
  const isWalletInactive = useIsWalletInactive({
    simplifiedSecurityCheck: false,
  });

  if (!showSecurityWarning) {
    return null;
  }

  return (
    <View
      gap={16}
      style={{
        backgroundColor: "#f9f9f9",
        borderRadius: 20,
        borderCurve: "continuous",
        padding: 20,
        borderColor: "white",
        borderWidth: 1,
      }}
    >
      <Row gap={12}>
        <P3 colorToken={"textSecondary"} style={{ flexShrink: 1 }}>
          Add a Recovery Key to enable multifactor authentication and advanced
          recovery.
        </P3>
        <View
          background={"red"}
          style={{
            position: "absolute",
            top: 0,
            right: 0,
            width: 10,
            height: 10,
            borderRadius: 999,
            alignSelf: "flex-start",
          }}
        />
      </Row>
      <Row justify={"space-between"}>
        <ShieldIcon
          size={32}
          style={{
            transform: [{ rotate: "-5deg" }],
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 8 },
            shadowOpacity: 0.3,
            shadowRadius: 10,
            elevation: 10,
          }}
        />

        <Row gap={9}>
          {!exceedUsdLimit && (
            <Button
              size={"small"}
              variant="secondary"
              onPress={() => {
                postponeSecurityWarning();
                Haptics.selectionAsync();
              }}
            >
              Do it later
            </Button>
          )}

          <Button
            size={"small"}
            variant="primary"
            onPress={withLock(async () => {
              if (await isWalletInactive()) {
                return;
              }

              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
              router.push("/unlocked/change-recovery/options");
            })}
          >
            Set up
          </Button>
        </Row>
      </Row>
    </View>
  );
}
