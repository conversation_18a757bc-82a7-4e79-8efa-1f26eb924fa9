import { Text, View } from "~/components/Themed";
import { Flex, Row } from "~/components/Grid";
import { Button } from "~/components/Button";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { useEffect, useId, useRef } from "react";
import { router } from "expo-router";
import * as Haptics from "expo-haptics";
import { Image } from "expo-image";
import { useIsWalletInactive } from "~/hooks/useAssertWalletState";
import { useImproveSecurityModalState } from "~/components/security/useImproveSecurityModalState";
import { postponeSecurityWarning } from "~/hooks/useSecurityWarning";
import Animated, {
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";

export function ImproveSecurityModal() {
  const id = useId();
  const modalRef = useRef<BottomModalImperativeMethods>(null);
  const { state, hide } = useImproveSecurityModalState();

  const isWalletInactive = useIsWalletInactive({
    simplifiedSecurityCheck: false,
  });

  useEffect(() => {
    if (state) {
      modalRef.current?.present();
    } else {
      modalRef.current?.close();
    }
  }, [state]);

  return (
    <BottomModal
      modalId={id}
      modalRef={modalRef}
      title="Add Recovery Key"
      onClose={() => hide({ postpone: false })}
      onDismiss={() => hide({ postpone: false })}
      body={
        <Flex gap={10}>
          <ImproveSecurityImage />
          <Text
            variant="medium"
            colorToken="textSecondary"
            style={{ fontSize: 14 }}
          >
            Add a Recovery Key to enable multifactor authentication and advanced
            recovery.
          </Text>
        </Flex>
      }
      footer={
        <Row gap={12}>
          <Flex>
            <Button
              size={"large"}
              variant="secondary"
              onPress={async () => {
                Haptics.selectionAsync();
                postponeSecurityWarning();
                await hide({ postpone: true });
              }}
            >
              Do it later
            </Button>
          </Flex>
          <Flex>
            <Button
              size={"large"}
              variant="primary"
              onPress={async () => {
                Haptics.selectionAsync();
                await hide({ postpone: false });

                if (await isWalletInactive()) {
                  return;
                }

                router.push("/unlocked/change-recovery/options");
              }}
            >
              Continue
            </Button>
          </Flex>
        </Row>
      }
    />
  );
}

const AnimatedImage = Animated.createAnimatedComponent(Image);

export function ImproveSecurityImage() {
  const scale = useSharedValue(0);

  useEffect(() => {
    scale.value = withTiming(1, { duration: 300 });
  }, []);

  const animatedLedgerStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: interpolate(scale.value, [0, 1], [0.9, 1]) },
        { translateY: interpolate(scale.value, [0, 1], [100, 0]) },
        { translateX: interpolate(scale.value, [0, 1], [50, 0]) },
      ],
    };
  });

  const animatedEnvelopeStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: interpolate(scale.value, [0, 1], [0.5, 1]) }],
    };
  });

  return (
    <View
      style={{
        backgroundColor: "#e8e8e8",
        borderRadius: 20,
        borderCurve: "continuous",
        height: 170,
        overflow: "hidden",
      }}
    >
      <AnimatedImage
        source={require("~/../assets/images/security/ledger.png")}
        style={[
          {
            position: "absolute",
            left: "5%",
            width: "110%",
            aspectRatio: 0.69,
            overflow: "visible",
          },
          animatedLedgerStyle,
        ]}
      />
      <AnimatedImage
        source={require("~/../assets/images/security/envelop.png")}
        style={[
          {
            position: "absolute",
            top: "20%",
            right: "20%",
            width: "28%",
            aspectRatio: 1.16,
            transformOrigin: "bottom",
          },
          animatedEnvelopeStyle,
        ]}
      />
    </View>
  );
}
