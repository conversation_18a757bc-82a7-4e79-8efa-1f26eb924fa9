import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import * as Haptics from "expo-haptics";
import { RefObject } from "react";
import { IconWrapper } from "~/components/IconWrapper";
import { Icon } from "~/components/Icon";

export function SpendingLimitInfoModal(props: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  return (
    <BottomModal
      modalRef={props.modalRef}
      modalId={"spending-limit-info"}
      title=" "
      body={
        <View gap={24} style={{ paddingHorizontal: 14 }}>
          <View gap={24} style={{ alignItems: "center" }}>
            <IconWrapper
              backgroundColorToken={"orange"}
              size={56}
              variant={"square"}
            >
              <Icon
                name="dial.high.fill"
                size={20}
                color={"white"}
                style={{ top: -1 }}
              />
            </IconWrapper>

            <View gap={4} style={{ alignItems: "center" }}>
              <Text variant="semibold" size={20}>
                Spending Limits
              </Text>
            </View>
          </View>
          <Text
            variant="medium"
            colorToken="textSecondary"
            style={{ fontSize: 18 }}
          >
            If you decide to enhance your security by using an external 2FA Key,
            you won't need to carry it with you for everyday tasks.{"\n\n"}
            Simply set a Spending Limit, and your Device Key can handle
            transactions within that limit without requiring 2FA.
          </Text>
        </View>
      }
      footer={
        <Button
          variant="secondary"
          size={"medium-new"}
          onPress={() => {
            Haptics.selectionAsync();
            props.modalRef.current?.close();
          }}
        >
          Got it
        </Button>
      }
    />
  );
}
