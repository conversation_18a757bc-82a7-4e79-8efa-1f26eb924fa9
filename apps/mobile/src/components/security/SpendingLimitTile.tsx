import { useActiveWallet } from "~/state/wallet";
import { useLock } from "~/hooks/useLock";
import { Text, View } from "~/components/Themed";
import { P2 } from "~/components/typography/P2";
import { SpendingLimit } from "~/services/spendingLimits";
import { Row } from "~/components/Grid";
import * as Haptics from "expo-haptics";
import { RefObject, useRef } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { DeleteSpendingLimitModal } from "~/components/security/DeleteSpendingLimitModal";
import { MenuView } from "@react-native-menu/menu";
import { TouchableScale } from "~/components/TouchableScale";
import { Icon } from "~/components/Icon";
import { CoinLogo } from "~/components/CoinLogo";
import { useTokenAmountString } from "~/state/tokens";
import { ContentSkeleton } from "~/components/Skeleton";
import { LineBreakdown } from "~/components/LineBreakdown";
import { Address } from "@squads/models/solana";
import { Button } from "~/components/Button";
import { H5 } from "~/components/typography/H5";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { prepareAction, Wallet } from "~/services/wallets";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { refetchSpendingLimits } from "~/state/spendingLimits";
import { sendTransactionWithTrackingStatus } from "~/utils/sendTransactionWithTracking";
import { useIsWalletInactive } from "~/hooks/useAssertWalletState";
import { sleep } from "~/utils/promise";
import { AnimatedButtonGroup } from "~/components/AnimatedButtonGroup";
import { IconWrapper } from "~/components/IconWrapper";

export function SpendingLimitTileSkeleton() {
  return (
    <ContentSkeleton>
      <View style={{ height: 88 }} />
    </ContentSkeleton>
  );
}

export function SpendingLimitTile({
  deviceKey,
  spendingLimit,
  onPress,
}: {
  deviceKey: Address;
  spendingLimit: SpendingLimit;
  onPress?: () => void;
}) {
  const mintOrSol = spendingLimit.mint ?? "SOL";
  const amountString = useTokenAmountString({
    mint: mintOrSol,
    amount: spendingLimit.amount,
  });

  const usedAmount = spendingLimit.amount - spendingLimit.remainingAmount;
  const usedAmountString = useTokenAmountString({
    mint: mintOrSol,
    amount: usedAmount,
    showSymbol: false,
  });

  const deleteSpendingLimitModalRef =
    useRef<BottomModalImperativeMethods>(null);
  const needsTransferModalRef = useRef<BottomModalImperativeMethods>(null);
  const needsTransfer = spendingLimit.member !== deviceKey;
  // const needsTransfer = spendingLimit.member === deviceKey;

  const { wallet } = useActiveWallet();
  const { walletKey, defaultVault: vaultKey } = wallet;
  const isWalletInactive = useIsWalletInactive();

  const queryClient = useQueryClient();
  const deleteMutation = useMutation({
    mutationFn: async (modalRef: RefObject<BottomModalImperativeMethods | null>) => {
      if (
        await isWalletInactive(async () => {
          modalRef.current?.close();
          //wait a bit for modal to close
          await sleep(300);
        })
      ) {
        return;
      }

      const action = await prepareAction({
        type: "spendingLimitRemove",
        address: spendingLimit.address,
        vaultIndex: 0,
      });

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );
      modalRef.current?.close();

      await sendTransactionWithTrackingStatus({
        walletKey: wallet.walletKey,
        transaction: { type: "action", signedTransactions },
        confirmationFailureMessage: `Failed to delete Spending Limit`,
      }).finally(() =>
        refetchSpendingLimits(queryClient, { walletKey, vaultKey })
      );
    },
  });

  const replaceMutation = useMutation({
    async mutationFn() {
      if (
        await isWalletInactive(async () => {
          needsTransferModalRef.current?.close();
          //wait a bit for modal to close
          await sleep(300);
        })
      ) {
        return;
      }

      const action = await prepareAction({
        type: "spendingLimitTransfer",
        vaultIndex: 0,
        oldSpendingLimit: spendingLimit.address,
        newMember: deviceKey,
      });

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );
      needsTransferModalRef.current?.close();

      await sendTransactionWithTrackingStatus({
        walletKey: wallet.walletKey,
        transaction: { type: "action", signedTransactions },
        confirmationFailureMessage: `Failed to transfer Spending Limit`,
      }).finally(() =>
        refetchSpendingLimits(queryClient, { walletKey, vaultKey })
      );
    },
  });

  const needTransferHandler = needsTransfer
    ? () => {
        needsTransferModalRef.current?.present();
      }
    : undefined;

  const onPressHandler = onPress ?? needTransferHandler;
  const isActionable = onPressHandler !== undefined;

  return (
    <>
      <TouchableScale disabled={!isActionable} onPress={onPressHandler}>
        <View>
          <View
            style={{
              position: "absolute",
              top: 16,
              right: 16,
              zIndex: 1,
            }}
          >
            {isActionable ? (
              <Icon
                name="exclamationmark.triangle"
                size={12}
                weight="semibold"
                colorToken="iconWarning"
              />
            ) : (
              <SpendingLimitMenu
                wallet={wallet}
                spendingLimit={spendingLimit}
                deleteModalRef={deleteSpendingLimitModalRef}
                isDeletePending={deleteMutation.isPending}
                onPressDelete={() =>
                  deleteMutation.mutate(deleteSpendingLimitModalRef)
                }
              />
            )}
          </View>
          <View
            gap={12}
            background={"backgroundSecondary"}
            style={{
              borderRadius: 16,
              padding: 16,
              opacity: isActionable ? 0.4 : 1,
            }}
          >
            <Row justify={"space-between"}>
              <P2 style={{ textTransform: "capitalize" }}>
                {spendingLimit.period}
              </P2>
            </Row>
            <LineBreakdown
              items={[
                { value: usedAmount, color: "text" },
                {
                  value: spendingLimit.remainingAmount,
                  color: "backgroundTertiary",
                },
              ]}
            />
            <Row justify={"space-between"}>
              <CoinLogo mint={mintOrSol} size={24} />
              <P2>
                {usedAmountString}
                <P2 colorToken={"textSecondary"}> / {amountString}</P2>
              </P2>
            </Row>
          </View>
        </View>
      </TouchableScale>
      <NeedsTransferModal
        modalRef={needsTransferModalRef}
        isTransferPending={replaceMutation.isPending}
        isDeletePending={deleteMutation.isPending}
        onPressTransfer={() => replaceMutation.mutate()}
        onPressDelete={() => deleteMutation.mutate(needsTransferModalRef)}
      />
    </>
  );
}

function SpendingLimitMenu({
  wallet,
  deleteModalRef,
  spendingLimit,
  isDeletePending,
  onPressDelete,
}: {
  wallet: Wallet;
  deleteModalRef: RefObject<BottomModalImperativeMethods | null>;
  spendingLimit: SpendingLimit;
  isDeletePending: boolean;
  onPressDelete: () => void;
}) {
  const withLock = useLock();
  const isWalletInactive = useIsWalletInactive();

  return (
    <>
      <MenuView
        themeVariant="light"
        onPressAction={withLock(async ({ nativeEvent }) => {
          Haptics.selectionAsync();
          switch (nativeEvent.event) {
            case "delete-spending-limit":
              if (await isWalletInactive()) {
                return;
              }

              deleteModalRef.current?.present();
              break;
          }
        })}
        actions={[
          {
            id: "delete-spending-limit",
            title: "Delete",
            image: "trash",
            attributes: { destructive: true },
          },
        ]}
        shouldOpenOnLongPress={false}
      >
        <TouchableScale onPress={() => Haptics.selectionAsync()}>
          <Icon
            name={"ellipsis"}
            size={12}
            rectSize={20}
            colorToken={"textSecondary"}
          />
        </TouchableScale>
      </MenuView>
      <DeleteSpendingLimitModal
        modalRef={deleteModalRef}
        spendingLimit={spendingLimit}
        isPending={isDeletePending}
        onPressDelete={onPressDelete}
      />
    </>
  );
}

function NeedsTransferModal({
  modalRef,
  isTransferPending,
  isDeletePending,
  onPressTransfer,
  onPressDelete,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
  isTransferPending: boolean;
  isDeletePending: boolean;
  onPressTransfer: () => void;
  onPressDelete: () => void;
}) {
  const isButtonsDisabled = isTransferPending || isDeletePending;

  return (
    <BottomModal
      modalRef={modalRef}
      modalId={"needs-migration"}
      title=" "
      body={
        <View gap={24} style={{ paddingHorizontal: 14 }}>
          <View gap={24} style={{ alignItems: "center" }}>
            <IconWrapper
              backgroundColorToken={"orange"}
              size={56}
              variant={"square"}
            >
              <Icon
                name="dial.high.fill"
                size={20}
                color={"white"}
                style={{ top: -1 }}
              />
            </IconWrapper>

            <View gap={4} style={{ alignItems: "center" }}>
              <Text variant="semibold" size={20}>
                Spending Limits
              </Text>
            </View>
          </View>
          <Text size={16} variant="medium" colorToken="textSecondary">
            Spending Limit is currently assigned to another device and cannot be
            used on this phone.{"\n\n"}You have the option to either transfer
            the Spending Limit to this device or delete it.
          </Text>
        </View>
      }
      footer={
        <AnimatedButtonGroup
          left={
            isDeletePending ? null : (
              <Button
                size={"medium-new"}
                disabled={isButtonsDisabled}
                loading={isTransferPending}
                loadingText={"Transferring"}
                onPress={onPressTransfer}
              >
                Transfer
              </Button>
            )
          }
          right={
            isTransferPending ? null : (
              <Button
                variant="danger"
                size={"medium-new"}
                disabled={isButtonsDisabled}
                loading={isDeletePending}
                loadingText={"Deleting"}
                onPress={onPressDelete}
              >
                Delete
              </Button>
            )
          }
        />
      }
    />
  );
}
