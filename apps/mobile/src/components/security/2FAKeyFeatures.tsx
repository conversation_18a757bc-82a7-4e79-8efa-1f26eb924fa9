import { FeatureList, FeatureListItem } from "~/components/FeatureList";

export function TwoFAKeyFeatures() {
  return (
    <FeatureList>
      <FeatureListItem
        icon={"key.viewfinder"}
        label="Active Key"
        text="Used to approve transactions"
      />
      <FeatureListItem
        icon={"cloud.circle"}
        label="Secure"
        text="The 2FA Key is stored and managed outside of Fuse"
      />
      <FeatureListItem
        icon={"clock.arrow.2.circlepath"}
        label="Recovery"
        text="If lost, can be recovered by pairing your Device and Recovery Key"
      />
    </FeatureList>
  );
}
