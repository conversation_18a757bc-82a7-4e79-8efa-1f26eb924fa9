import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Button } from "~/components/Button";
import { FeatureList, FeatureListItem } from "~/components/FeatureList";
import { RefObject } from "react";
import { hapticDismissBottomTray } from "~/utils/haptics";
import { IconWrapper } from "~/components/IconWrapper";
import { Text, View } from "~/components/Themed";
import { WalletIcon } from "~/components/icons/WalletIcon";

export function InfoRecoveryKeyModal(props: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  return (
    <BottomModal
      modalRef={props.modalRef}
      modalId="info-recovery-key"
      title=" "
      body={
        <View gap={24} style={{ paddingHorizontal: 14 }}>
          <View gap={24} style={{ alignItems: "center" }}>
            <IconWrapper
              backgroundColorToken={"green"}
              size={56}
              variant={"square"}
            >
              <WalletIcon size={24} colorToken={"textOpposite"} />
            </IconWrapper>

            <View gap={8} style={{ alignItems: "center" }}>
              <Text variant="semibold" size={20}>
                What's a Recovery Key?
              </Text>
            </View>
          </View>
          <FeatureList>
            <FeatureListItem
              icon={"key.viewfinder"}
              label={"Wallet recovery"}
              text={
                "A Recovery Key can restore access to your wallet when paired with your Device or 2FA Key. Fuse supports up to 3 Recovery Keys"
              }
            />
            <FeatureListItem
              icon={"clock.arrow.2.circlepath"}
              label={"Recovery only"}
              text={
                "Recovery Keys have limited rights and can never access your Smart Account without an associated Active Key"
              }
            />
          </FeatureList>
        </View>
      }
      footer={
        <Button
          variant="secondary"
          size={"medium-new"}
          onPress={() => {
            hapticDismissBottomTray();
            props.modalRef.current?.close();
          }}
        >
          Got it
        </Button>
      }
    />
  );
}
