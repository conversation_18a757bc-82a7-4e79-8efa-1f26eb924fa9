import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import * as Haptics from "expo-haptics";
import { RefObject } from "react";
import { IconWrapper } from "~/components/IconWrapper";
import { Icon } from "~/components/Icon";

export function SpendingLimitUnavailableModal(props: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  return (
    <BottomModal
      modalRef={props.modalRef}
      modalId={"spending-limit-unavailable"}
      title=" "
      body={
        <View gap={24} style={{ paddingHorizontal: 14 }}>
          <View gap={24} style={{ alignItems: "center" }}>
            <IconWrapper
              backgroundColorToken={"orange"}
              size={56}
              variant={"square"}
            >
              <Icon
                name="dial.high.fill"
                size={20}
                color={"white"}
                style={{ top: -1 }}
              />
            </IconWrapper>

            <View gap={4} style={{ alignItems: "center" }}>
              <Text variant="semibold" size={20}>
                Spending Limits
              </Text>
            </View>
          </View>
          <Text size={16} variant="medium" colorToken="textSecondary">
            Spending Limits are available exclusively when your security setup
            includes an external 2FA Key. This ensures enhanced security without
            affecting the user experience for daily operations.{"\n\n"}If you
            use Cloud 2FA, Spending Limits are unnecessary since the Cloud Key
            is always accessible from your device.
          </Text>
        </View>
      }
      footer={
        <Button
          variant="secondary"
          size={"medium-new"}
          onPress={() => {
            Haptics.selectionAsync();
            props.modalRef.current?.close();
          }}
        >
          Got it
        </Button>
      }
    />
  );
}
