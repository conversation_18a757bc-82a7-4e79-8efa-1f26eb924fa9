import { useEffect, useRef, useState } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
  BottomModalMenuOption,
} from "~/components/BottomModal";
import { LedgerAccountError, useLedgerDevice } from "~/hooks/useLedgerDevice";
import { Text, View } from "~/components/Themed";
import * as Haptics from "expo-haptics";
import { Address } from "@squads/models/solana";
import { useActiveWallet } from "~/state/wallet";
import {
  useInfiniteQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
import { ListSeparator } from "~/components/ListSeparator";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import { FlatList } from "react-native";
import Animated, {
  FadeInDown,
  FadeOut,
  FadeOutUp,
  ReduceMotion,
} from "react-native-reanimated";
import { DURATION_FAST } from "~/constants/animations";
import { Button } from "~/components/Button";
import invariant from "invariant";
import { getBalancesBatch } from "~/services/balances";
import { TouchableScale } from "~/components/TouchableScale";
import { formatSolAmount } from "~/utils/tokens";
import { ChooseLedgerDeviceView } from "~/components/Recovery2FAKeyModal";
import { CloudKey, RecoveryKey } from "~/services/wallets";
import { SelectOption } from "~/components/SelectOption";
import { LedgerIcon } from "~/components/icons/LedgerIcon";
import { getOrInitCloudKeyRecovery } from "~/app/unlocked/change-cloud-key/recovery/resume-recovery";
import { useMaybeCloudKey } from "~/state/cloudKey";
import { create } from "zustand";
import { useChangeCloudKeyState } from "~/app/unlocked/change-cloud-key/_layout";

type ModalState =
  | { type: "ledger" }
  | { type: "ledger/address"; deviceId: string }
  | { type: "signer"; newKey: CloudKey }
  | { type: "recovery"; newKey: CloudKey };

const ModalState = {
  getTitle: (state: ModalState) => {
    switch (state.type) {
      case "ledger":
        return "Choose Ledger device";
      case "ledger/address":
        return "Choose Ledger account";
      case "signer":
        return "Choose signer";
      case "recovery":
        return "Choose Recovery Key";
    }
  },
};

const useModalState = create<{
  modalState: ModalState | null;
  setModalState: (modalState: ModalState | null) => void;
}>((set) => ({
  modalState: null,
  setModalState: (modalState: ModalState | null) => set({ modalState }),
}));

export function showChange2FAKeyModal(
  initialState: ModalState = { type: "ledger" }
) {
  useModalState.setState({ modalState: initialState });
}

export function Change2FAKeyModal({
  recoveryKeys,
  onConfirm,
}: {
  recoveryKeys: RecoveryKey[];
  onConfirm: (value: {
    newKey: CloudKey;
    recovery: { key: RecoveryKey; activityId: string } | null;
  }) => void;
}) {
  const modalRef = useRef<BottomModalImperativeMethods>(null);
  const { modalState, setModalState } = useModalState();
  const [, updateRecoveryState] = useChangeCloudKeyState();

  useEffect(() => {
    if (modalState) {
      modalRef.current?.present();
    } else {
      modalRef.current?.dismiss();
    }
  }, [modalState]);

  const { wallet } = useActiveWallet();
  const queryClient = useQueryClient();
  const maybeCloudKey = useMaybeCloudKey();

  async function confirmRecoveryKey(
    newKey: CloudKey,
    recoveryKey: RecoveryKey
  ) {
    const activityId = await getOrInitCloudKeyRecovery(queryClient, {
      wallet,
      newKey: { keyType: "cloudKey", ...newKey },
    });

    onConfirm({
      newKey: newKey,
      recovery: { key: recoveryKey, activityId },
    });

    setModalState(null);
  }

  async function proceedToRecovery(newKey: CloudKey) {
    if (recoveryKeys.length === 1) {
      const recoveryKey = recoveryKeys[0];
      await confirmRecoveryKey(newKey, recoveryKey);
    } else {
      setModalState({
        type: "recovery",
        newKey: newKey,
      });
    }
  }

  if (modalState === null) {
    return null;
  }

  return (
    <BottomModal
      modalRef={modalRef}
      modalId={modalState.type}
      title={ModalState.getTitle(modalState)}
      body={() => {
        switch (modalState.type) {
          case "ledger":
            return (
              <ChooseLedgerDeviceView
                onSelect={(deviceId: string) => {
                  setModalState({ type: "ledger/address", deviceId });
                }}
              />
            );
          case "ledger/address":
            return (
              <ChooseLedgerAddressView
                deviceId={modalState.deviceId}
                onSelect={async (ledgerAccount: LedgerAccount) => {
                  const newKey: CloudKey = {
                    address: ledgerAccount.address,
                    details: {
                      type: "ledger",
                      derivationPath: ledgerAccount.derivationPath,
                      deviceId: modalState.deviceId,
                    },
                  };

                  if (wallet.keys.cloudKey.details.type === "iCloud") {
                    if (maybeCloudKey === null) {
                      await proceedToRecovery(newKey);
                    } else {
                      onConfirm({
                        newKey,
                        recovery: null,
                      });
                      modalRef.current?.dismiss();
                    }
                  } else {
                    setModalState({ type: "signer", newKey });
                  }
                }}
              />
            );

          case "signer":
            return (
              <ChooseSignerView
                onConfirm={async (signer) => {
                  const newKey = modalState.newKey;
                  if (signer === "2fa") {
                    onConfirm({ newKey, recovery: null });
                    modalRef.current?.dismiss();
                    return;
                  }

                  if (signer === "recovery") {
                    await proceedToRecovery(newKey);
                    return;
                  }

                  return signer satisfies never;
                }}
              />
            );

          case "recovery":
            return (
              <ChooseRecoveryKeyView
                recoveryKeys={recoveryKeys}
                onConfirm={async (recoveryKey) => {
                  await confirmRecoveryKey(modalState.newKey, recoveryKey);
                }}
              />
            );

          default:
            return modalState satisfies never;
        }
      }}
      onDismiss={() => {
        setModalState(null);
      }}
    />
  );
}

export type LedgerAccount = {
  address: Address;
  derivationPath: string;
};

function ChooseLedgerAddressView({
  deviceId,
  onSelect,
}: {
  deviceId: string;
  onSelect: (ledgerAccount: LedgerAccount) => void;
}) {
  const { wallet } = useActiveWallet();
  const walletKey = wallet.walletKey;
  const recoveryKeys = wallet.keys.recoveryKeys;

  const { accounts, isLoading, isLoadingMore, loadMoreAccounts, error } =
    useLedgerAddresses(deviceId, walletKey);

  const [selectedAccount, setSelectedAccount] = useState<LedgerAccount | null>(
    null
  );

  useEffect(() => {
    if (selectedAccount === null && accounts?.length) {
      setSelectedAccount(
        accounts.find(
          (a) =>
            a.address !== wallet.keys.cloudKey.address &&
            !(
              recoveryKeys !== null &&
              recoveryKeys.some((key) => key.address === a.address)
            )
        ) ?? null
      );
    }
  }, [accounts, selectedAccount]);

  const loading = isLoading || accounts === null;

  const placeholderMessage =
    typeof error === "string"
      ? LedgerAccountError.toString(error)
      : error
        ? error.message
        : "Loading...";

  useEffect(() => {
    if (error) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  }, [error]);

  return (
    <View style={{ minHeight: 220, maxHeight: 280, gap: 20 }}>
      {error || loading ? (
        <Animated.View
          key={placeholderMessage}
          entering={FadeInDown.duration(DURATION_FAST).reduceMotion(
            ReduceMotion.Never
          )}
          exiting={FadeOutUp.duration(DURATION_FAST).reduceMotion(
            ReduceMotion.Never
          )}
          style={{ flex: 1, justifyContent: "center" }}
        >
          <Text variant="medium" style={{ fontSize: 18, textAlign: "center" }}>
            {placeholderMessage}
          </Text>
          <Text
            variant="medium"
            colorToken="textSecondary"
            style={{ fontSize: 14, textAlign: "center" }}
          >
            {error === "locked" || loading
              ? "Make sure the device is powered on and unlocked"
              : error === "unknown_app"
                ? "Open the Solana App on the Ledger"
                : ""}
          </Text>
        </Animated.View>
      ) : (
        <FlatList
          showsVerticalScrollIndicator={false}
          data={accounts}
          ItemSeparatorComponent={ListSeparator}
          ListFooterComponent={
            accounts.length < 9 ? (
              <Animated.View
                key="loadMore"
                style={{ paddingVertical: 20 }}
                exiting={FadeOut.duration(DURATION_FAST).reduceMotion(
                  ReduceMotion.Never
                )}
              >
                <TouchableScale
                  onPress={() => loadMoreAccounts()}
                  disabled={isLoadingMore}
                  style={{ borderBottomWidth: 1, alignSelf: "center" }}
                >
                  <Text
                    variant={"medium"}
                    colorToken={isLoadingMore ? "textTertiary" : "text"}
                    style={{
                      fontSize: 15,
                      textDecorationLine: "underline",
                    }}
                  >
                    Load more accounts
                  </Text>
                </TouchableScale>
              </Animated.View>
            ) : null
          }
          renderItem={({ item: account }) => {
            const isCurrentKey =
              account.address === wallet.keys.cloudKey.address;
            const isUsedAsRecovery = (recoveryKeys ?? []).some(
              (key) => key.address === account.address
            );
            return (
              <BottomModalMenuOption
                disabled={isCurrentKey || isUsedAsRecovery}
                key={account.address}
                selected={account.address === selectedAccount?.address}
                labelView={
                  <View
                    style={{
                      alignItems: "center",
                      flexDirection: "row",
                    }}
                  >
                    <View style={{ justifyContent: "center" }}>
                      <Text variant="medium" style={{ fontSize: 18 }}>
                        {abbreviateAddress(account.address)}
                      </Text>
                      {isUsedAsRecovery ? (
                        <Text
                          variant="medium"
                          colorToken="textToastWarning"
                          style={{ fontSize: 14 }}
                        >
                          Used for recovery
                        </Text>
                      ) : (
                        <Text
                          variant="medium"
                          colorToken="textSecondary"
                          style={{ fontSize: 14 }}
                        >
                          44'/501'/{account.derivationPath}
                        </Text>
                      )}
                    </View>

                    <View>
                      <Text
                        variant="medium"
                        colorToken={
                          account.balance > 0 ? "text" : "textSecondary"
                        }
                        style={{ fontSize: 16 }}
                      >
                        {"   •   "}
                        {isCurrentKey
                          ? "Current"
                          : formatSolAmount(account.balance)}
                      </Text>
                    </View>
                  </View>
                }
                onSelect={() => {
                  setSelectedAccount(account);
                }}
              />
            );
          }}
        />
      )}
      <Button
        variant="primary"
        disabled={selectedAccount === null || isLoading || error !== null}
        onPress={() => {
          Haptics.selectionAsync();
          onSelect(selectedAccount!);
        }}
      >
        Continue
      </Button>
    </View>
  );
}

function useLedgerAddresses(deviceId: string, walletKey: Address) {
  const {
    device,
    loading: deviceLoading,
    error: deviceError,
  } = useLedgerDevice(deviceId);

  const {
    data,
    isLoading,
    isFetchingNextPage,
    fetchNextPage: loadMoreAccounts,
    error: accountsError,
  } = useInfiniteQuery({
    queryKey: ["ledgerAddresses", deviceId],
    initialPageParam: 0,
    async queryFn({ pageParam: offset = 0 }) {
      invariant(device, "device must be defined");

      const accounts = await device
        .fetchAccounts(offset, 3)
        .catch((err: unknown) => {
          throw LedgerAccountError.fromError(err);
        });

      const addresses = accounts.map((account) => account.address);
      console.debug("found ledger accounts", accounts);
      const balances = await getBalancesBatch({ walletKey, addresses });

      return accounts.map((account) => ({
        ...account,
        balance: balances?.accounts[account.address]?.lamports ?? 0,
      }));
    },
    getNextPageParam(_, pages) {
      return pages.reduce((acc, page) => acc + page.length, 0);
    },
    enabled: !!device,
  });

  const loading = deviceLoading || isLoading;

  const accounts = data?.pages.flat() ?? [];

  const error = deviceError || accountsError;

  return {
    accounts,
    isLoading: loading,
    isLoadingMore: isFetchingNextPage,
    error,
    loadMoreAccounts,
  };
}

function ChooseSignerView({
  onConfirm,
}: {
  onConfirm: (signer: "2fa" | "recovery") => Promise<void>;
}) {
  const confirmMutation = useMutation({
    mutationFn: onConfirm,
  });

  return (
    <View style={{ gap: 8 }}>
      <SelectOption
        text={"Use existing 2FA Key"}
        disabled={confirmMutation.isPending}
        icon={<LedgerIcon colorToken="textSecondary" size={23} rectSize={28} />}
        onPress={() => {
          Haptics.selectionAsync();
          confirmMutation.mutate("2fa");
        }}
      />
      <SelectOption
        loading={confirmMutation.isPending}
        text={"Use Recovery Key"}
        icon={<LedgerIcon colorToken="textSecondary" size={23} rectSize={28} />}
        onPress={() => {
          Haptics.selectionAsync();
          confirmMutation.mutate("recovery");
        }}
      />
    </View>
  );
}

function ChooseRecoveryKeyView({
  recoveryKeys,
  onConfirm,
}: {
  recoveryKeys: Array<RecoveryKey>;
  onConfirm: (recoveryKey: RecoveryKey) => Promise<void>;
}) {
  const [selected, setSelected] = useState<RecoveryKey | null>(null);

  const onConfirmMutation = useMutation({
    mutationFn: onConfirm,
  });

  return (
    <View style={{ maxHeight: 260, gap: 20 }}>
      <FlatList
        data={recoveryKeys}
        ItemSeparatorComponent={ListSeparator}
        renderItem={({ item: key }) => {
          switch (key.recoveryKeyType) {
            case "selfCustody": {
              return (
                <BottomModalMenuOption
                  key={key.address}
                  selected={key === selected}
                  label={
                    <>
                      Wallet{" "}
                      <Text colorToken="textSecondary" style={{ fontSize: 15 }}>
                        {"  •  "}
                        {abbreviateAddress(key.address)}
                      </Text>
                    </>
                  }
                  onSelect={() => {
                    setSelected(key);
                  }}
                />
              );
            }
            case "keystone": {
              return (
                <BottomModalMenuOption
                  key={key.address}
                  selected={key === selected}
                  label={
                    <>
                      Keystone{" "}
                      <Text colorToken="textSecondary" style={{ fontSize: 15 }}>
                        {"  •  "}
                        {abbreviateAddress(key.address)}
                      </Text>
                    </>
                  }
                  onSelect={() => {
                    setSelected(key);
                  }}
                />
              );
            }

            case "turnkeyEmail":
            case "email": {
              return (
                <BottomModalMenuOption
                  key={key.email}
                  selected={key === selected}
                  label={
                    <>
                      Email{" "}
                      <Text colorToken="textSecondary" style={{ fontSize: 15 }}>
                        {"  •  "}
                        {key.email}
                      </Text>
                    </>
                  }
                  onSelect={() => {
                    setSelected(key);
                  }}
                />
              );
            }

            case "phone": {
              return null;
            }

            default:
              return key satisfies never;
          }
        }}
      />

      <Button
        variant="primary"
        loading={onConfirmMutation.isPending}
        disabled={!selected}
        onPress={async () => {
          selected && (await onConfirmMutation.mutateAsync(selected));
        }}
      >
        Continue
      </Button>
    </View>
  );
}
