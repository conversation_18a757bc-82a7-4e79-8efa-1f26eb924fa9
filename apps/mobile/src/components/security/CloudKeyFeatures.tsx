import { FeatureList, FeatureListItem } from "~/components/FeatureList";

export function CloudKeyFeatures() {
  return (
    <FeatureList>
      <FeatureListItem
        icon={"key.viewfinder"}
        label="Active Key"
        text="Used to approve transactions"
      />
      <FeatureListItem
        icon={"cloud.circle"}
        label="iCloud security"
        text="Stored in a secured container on iCloud that only Fuse app can access"
      />
      <FeatureListItem
        icon={"clock.arrow.2.circlepath"}
        label="Recovery"
        text="If lost, can be recovered by pairing your Device and Recovery Key"
      />
    </FeatureList>
  );
}
