import { prepareAction, RecoveryKey } from "~/services/wallets";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text } from "~/components/Themed";
import { Flex, Row } from "~/components/Grid";
import { Button } from "~/components/Button";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { refetchWallet, useActiveWallet } from "~/state/wallet";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { sendTransactionWithTrackingStatus } from "~/utils/sendTransactionWithTracking";
import { RefObject } from "react";
import { isClientErrorReasonType } from "~/services/utils";
import { showNotEnoughSolModal } from "~/components/modals/NotEnoughSolModal";

export function DeleteRecoveryKeyModal({
  modalRef,
  recoveryKey,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
  recoveryKey: RecoveryKey;
}) {
  const queryClient = useQueryClient();
  const { wallet } = useActiveWallet();

  const deleteRecoveryKeyMutation = useMutation({
    mutationFn: async () => {
      const action = await prepareAction({
        type: "updateRecoveryKeys",
        details: {
          type: "removeKey",
          oldKey: recoveryKey.address,
        },
      });

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );
      modalRef.current?.close();

      await sendTransactionWithTrackingStatus({
        walletKey: wallet.walletKey,
        transaction: { type: "action", signedTransactions },
        confirmationFailureMessage: `Failed to delete Recovery Key`,
      }).finally(() =>
        refetchWallet(queryClient, { walletKey: wallet.walletKey })
      );
    },
    onError: (error) => {
      if (isClientErrorReasonType(error, "notEnoughSol")) {
        showNotEnoughSolModal();
        return;
      }
    },
  });

  return (
    <BottomModal
      modalId="delete-recovery-key"
      modalRef={modalRef}
      title="Delete Recovery Key"
      iconName="trash"
      iconVariant="danger"
      body={
        <Text
          variant="medium"
          colorToken="textSecondary"
          style={{ fontSize: 18 }}
        >
          <Text variant="medium">
            {RecoveryKey.getStringValue(recoveryKey)}
          </Text>{" "}
          will no longer be available to restore access to your wallet
        </Text>
      }
      footer={
        <Row gap={12}>
          <Flex>
            <Button
              variant="secondary"
              onPress={() => {
                modalRef.current?.close();
              }}
            >
              Cancel
            </Button>
          </Flex>
          <Flex>
            <Button
              variant="danger"
              loading={deleteRecoveryKeyMutation.isPending}
              onPress={() => {
                deleteRecoveryKeyMutation.mutate();
              }}
            >
              Confirm
            </Button>
          </Flex>
        </Row>
      }
    />
  );
}
