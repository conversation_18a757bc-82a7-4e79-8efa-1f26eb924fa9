import { create } from "zustand";
import {
  newResolvablePromise,
  ResolvablePromise,
  sleep,
} from "~/utils/promise";

const useImproveSecurityModalState = create<{
  state?: ResolvablePromise<{ postpone: boolean }>;
  hide: (response: { postpone: boolean }) => Promise<void>;
}>((set) => ({
  state: undefined,
  hide: async (response: { postpone: boolean }) => {
    const state = useImproveSecurityModalState.getState().state;
    if (state !== undefined) {
      set({ state: undefined });

      //wait a bit for modal to close
      await sleep(400);
      state?.resolve(response);
    }
  },
}));

export function showImproveSecurityModal() {
  const callbackPromise = newResolvablePromise<{ postpone: boolean }>();
  useImproveSecurityModalState.setState({ state: callbackPromise });
  return callbackPromise;
}

export { useImproveSecurityModalState };
