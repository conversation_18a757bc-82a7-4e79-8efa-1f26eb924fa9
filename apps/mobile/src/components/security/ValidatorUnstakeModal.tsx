import { prepareAction } from "~/services/wallets";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { Row } from "~/components/Grid";
import { Button } from "~/components/Button";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { useActiveWallet } from "~/state/wallet";
import { Address } from "@squads/models/solana";
import { useEpochRemainingTime, useRewards } from "~/state/nativeStake";
import { ReactNode, RefObject, Suspense } from "react";
import {
  formatEpoch,
  formatTokenAmount,
  formatUsdValue,
} from "@squads/utils/numberFormats";
import { LAMPORTS_PER_SOL } from "@solana/web3.js";
import { refetchBalances, useSolBalance } from "~/state/balances";
import { tokenUsdValue } from "~/utils/tokens";
import { StakingStatus } from "~/components/StakingStatus";
import { Duration } from "luxon";
import { useLock } from "~/hooks/useLock";
import {
  InitializedStakeAccount,
  StakeAccount,
  StakeRewards,
  Validator,
} from "~/services/nativeStake";
import { ValidatorIcon } from "~/components/icons/ValidatorIcon";
import * as Haptics from "expo-haptics";
import { ListSeparator } from "~/components/ListSeparator";
import { FlatList } from "react-native";
import { useIsWalletInactive } from "~/hooks/useAssertWalletState";
import { sendTransactionWithTrackingStatus } from "~/utils/sendTransactionWithTracking";
import { clientErrorMessage } from "~/services/utils";
import { sleep } from "~/utils/promise";
import { useBalanceSettings } from "~/state/balanceSettings";
import { FuseErrorBoundary } from "~/components/errors/FuseErrorBoundary";
import { ContentSkeleton } from "~/components/Skeleton";

export function ValidatorUnstakeModal({
  validator,
  vaultKey,
  stakeAccounts,
  modalRef,
}: {
  validator: Validator;
  vaultKey: Address;
  stakeAccounts: InitializedStakeAccount[];
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  const { solBalance } = useSolBalance({
    address: vaultKey,
  });

  const stakedAmount = StakeAccount.totalStaked(stakeAccounts);
  const stakedUsdAmount = tokenUsdValue({
    amount: stakedAmount,
    usdcPrice: solBalance.usdcPrice,
    decimals: solBalance.decimals,
  });

  const apy = validator.apy;

  const { displayBalance } = useBalanceSettings();

  return (
    <BottomModal
      modalRef={modalRef}
      modalId={`stake-${validator.address}`}
      title={validator.name}
      icon={<ValidatorIcon validator={validator.address} />}
      body={() => (
        <View style={{ gap: 16 }}>
          <Row justify={"space-between"}>
            <View style={{ gap: 8 }}>
              <Text variant={"semibold"}>Total stake</Text>
              <Text variant={"semibold"} style={{ fontSize: 26 }}>
                {displayBalance(
                  formatTokenAmount(stakedAmount / LAMPORTS_PER_SOL, "SOL")
                )}
              </Text>
              <Text variant={"medium"} colorToken={"textSecondary"}>
                {displayBalance(formatUsdValue(stakedUsdAmount))}
              </Text>
            </View>
          </Row>
          <Row gap={12} justify={"space-between"}>
            <View style={{ gap: 8, flex: 1 }}>
              <Text variant={"semibold"}>Earned</Text>
              <EarnedValue vaultKey={vaultKey} />
            </View>

            <View style={{ gap: 8, flex: 1 }}>
              <Text variant={"semibold"}>APY</Text>
              <Text
                colorToken={"green"}
                variant={"medium"}
                style={{ fontSize: 18 }}
              >
                {apy}%
              </Text>
            </View>
          </Row>
          <Text variant="semibold" style={{ fontSize: 15 }}>
            Details
          </Text>
          <FlatList
            showsVerticalScrollIndicator={false}
            alwaysBounceVertical={false}
            style={{ maxHeight: 270 }}
            data={stakeAccounts}
            renderItem={({ item, index }) => (
              <StakingAccountRow
                key={index}
                account={item}
                onClose={() => modalRef.current?.close()}
              />
            )}
            ItemSeparatorComponent={() => (
              <ListSeparator style={{ marginVertical: 12 }} />
            )}
          />
        </View>
      )}
    />
  );
}

function EarnedValue({ vaultKey }: { vaultKey: Address }) {
  return (
    <FuseErrorBoundary FallbackComponent={EarnedValueSkeleton}>
      <Suspense fallback={<EarnedValueSkeleton />}>
        <EarnedValueInner vaultKey={vaultKey} />
      </Suspense>
    </FuseErrorBoundary>
  );
}

function EarnedValueInner({ vaultKey }: { vaultKey: Address }) {
  const { displayBalance } = useBalanceSettings();

  const rewards = useRewards({ vaultKey });
  const totalRewardsAmount =
    StakeRewards.totalRewards(rewards) / LAMPORTS_PER_SOL;

  return (
    <Text
      colorToken={totalRewardsAmount > 0 ? "green" : "textSecondary"}
      variant={"medium"}
      style={{ fontSize: 18 }}
    >
      {displayBalance(formatTokenAmount(totalRewardsAmount, "SOL"))}
    </Text>
  );
}

function EarnedValueSkeleton() {
  return (
    <View style={{ alignSelf: "flex-start" }}>
      <ContentSkeleton>
        <Text variant={"medium"} style={{ fontSize: 18 }}>
          1234 SOL
        </Text>
      </ContentSkeleton>
    </View>
  );
}

function StakingAccountRow({
  account,
  onClose,
}: {
  account: InitializedStakeAccount;
  onClose: () => void;
}) {
  const amount = formatTokenAmount(account.lamports / LAMPORTS_PER_SOL, "SOL");
  const epochRemainingTime = useEpochRemainingTime();
  const seconds = epochRemainingTime.seconds;
  const tillNextEpoch = Duration.fromObject({ seconds }).toFormat(
    formatEpoch(seconds)
  );

  const { displayBalance } = useBalanceSettings();
  return (
    <View style={{ gap: 8 }}>
      <Row gap={12} justify={"space-between"} style={{ height: 20 }}>
        <StakingStatus status={account.status} />
        <View style={{ minWidth: 100 }}>
          {account.status === "deactivating" && (
            <Text colorToken={"textSecondary"} variant={"medium"}>
              Inactive in {tillNextEpoch}
            </Text>
          )}
          {account.status === "activating" && (
            <Text colorToken={"textSecondary"} variant={"medium"}>
              Active in {tillNextEpoch}
            </Text>
          )}
        </View>
      </Row>
      <Row gap={12} justify={"space-between"} style={{ height: 32 }}>
        <Text variant={"medium"} style={{ fontSize: 18 }}>
          {displayBalance(amount)}
        </Text>
        <View style={{ minWidth: 100 }}>
          {account.status === "deactivated" && (
            <WithdrawButton account={account} onClose={() => onClose()} />
          )}
          {account.status === "active" && (
            <UnstakeButton account={account} onClose={() => onClose()}>
              Unstake
            </UnstakeButton>
          )}
          {account.status === "activating" && (
            <UnstakeButton account={account} onClose={() => onClose()}>
              Cancel
            </UnstakeButton>
          )}
        </View>
      </Row>
    </View>
  );
}

function Elipsis() {
  const size = 3;
  return (
    <View style={{ gap: 2, paddingHorizontal: 5 }}>
      <View
        background={"textTertiary"}
        style={{ width: size, height: size, borderRadius: 9999 }}
      />
      <View
        background={"textTertiary"}
        style={{ width: size, height: size, borderRadius: 9999 }}
      />
      <View
        background={"textTertiary"}
        style={{ width: size, height: size, borderRadius: 9999 }}
      />
    </View>
  );
}

function UnstakeButton({
  children,
  account,
  onClose,
}: {
  children?: ReactNode;
  account: { address: Address; lamports: number };
  onClose: () => void;
}) {
  const { wallet } = useActiveWallet();
  const queryClient = useQueryClient();

  const withLock = useLock();

  const unstakeMutation = useMutation({
    mutationFn: async () => {
      Haptics.selectionAsync();
      const action = await prepareAction({
        type: "stake",
        details: {
          type: "deactivate",
          vaultIndex: 0,
          stakeKey: account.address,
        },
      });

      //closing modal before signing transaction, since signing with 2FA requires another modal
      onClose();

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );

      await sendTransactionWithTrackingStatus({
        walletKey: wallet.walletKey,
        transaction: { type: "action", signedTransactions },
        confirmationFailureMessage: (e) =>
          clientErrorMessage("Unstake failed", e),
      }).finally(() =>
        refetchBalances({
          queryClient,
          address: wallet.defaultVault,
        })
      );
    },
  });

  const isWalletInactive = useIsWalletInactive();

  return (
    <Button
      variant="secondary"
      size={"small"}
      onPress={withLock(async () => {
        if (
          await isWalletInactive(async () => {
            onClose();
            //wait a bit for modal to close
            await sleep(300);
          })
        ) {
          return;
        }
        unstakeMutation.mutate();
      })}
    >
      {children}
    </Button>
  );
}

function WithdrawButton({
  account,
  onClose,
}: {
  account: { address: Address; lamports: number };
  onClose: () => void;
}) {
  const { wallet } = useActiveWallet();
  const queryClient = useQueryClient();

  const walletKey = wallet.walletKey;
  const withLock = useLock();
  const isWalletInactive = useIsWalletInactive();

  const withdrawMutation = useMutation({
    mutationFn: async () => {
      Haptics.selectionAsync();
      const action = await prepareAction({
        type: "stake",
        details: {
          type: "withdraw",
          vaultIndex: 0,
          lamports: account.lamports,
          stakeKey: account.address,
        },
      });

      //closing modal before signing transaction, since signing with 2FA requires another modal
      onClose();

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );

      await sendTransactionWithTrackingStatus({
        walletKey: wallet.walletKey,
        transaction: { type: "action", signedTransactions },
        confirmationFailureMessage: (e) =>
          clientErrorMessage("Withdrawing SOL failed", e),
      }).finally(() =>
        refetchBalances({
          queryClient,
          address: wallet.defaultVault,
        })
      );
    },
  });

  return (
    <Button
      variant={"secondary"}
      size={"small"}
      loading={withdrawMutation.isPending}
      onPress={withLock(async () => {
        if (
          await isWalletInactive(async () => {
            onClose();
            //wait a bit for modal to close
            await sleep(300);
          })
        ) {
          return;
        }

        withdrawMutation.mutate();
      })}
    >
      {"Withdraw"}
    </Button>
  );
}
