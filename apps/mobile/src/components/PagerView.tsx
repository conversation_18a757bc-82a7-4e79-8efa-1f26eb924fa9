import {
  Children,
  PropsWithChildren,
  RefObject,
  useImperativeHandle,
} from "react";
import { Dimensions, StyleSheet, ViewProps, ViewStyle } from "react-native";
import Animated, {
  clamp,
  Extrapolation,
  interpolate,
  SharedValue,
  useAnimatedProps,
  useAnimatedReaction,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from "react-native-reanimated";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import { SPRING } from "~/constants/animations";
import { useThemedValue } from "~/components/Themed";
import { AnimatedBlurView } from "./AnimatedBlurView";

export type PagerImperativeMethods = {
  setPage: (selectedPage: number) => void;
};

export function PagerView({
  children,
  pagerRef,
  initialPageIndex = 0,
  pagePosition,
  width = Dimensions.get("window").width,
  blurViewStyle,
  ...restProps
}: ViewProps & {
  pagerRef: RefObject<PagerImperativeMethods | null>;
  initialPageIndex?: number;
  pagePosition?: SharedValue<number>;
  blurViewStyle?: ViewStyle;
  width?: number;
}) {
  useImperativeHandle(pagerRef, () => ({
    setPage: (selectedPage: number) => {
      const currentPage = Math.round(offset.value / width);

      // Mark all pages between the current page and the selected page to skip
      // during the transition.
      skippedPages.value = Array.from(
        { length: Math.abs(currentPage - selectedPage) - 1 },
        (_, i) => {
          return currentPage < selectedPage
            ? currentPage + i + 1
            : currentPage - i - 1;
        }
      );

      offset.value = withSpring(selectedPage * width, SPRING, () => {
        // Reset the skipped pages after the transition is complete.
        skippedPages.value = [];
      });
    },
  }));

  // Shared value to keep track of the offset in pixels
  const offset = useSharedValue(initialPageIndex * width);

  // If `pagePosition` is passed, derive it from `offset`
  useAnimatedReaction(
    () => offset.value,
    (offsetValue) => {
      if (pagePosition) {
        pagePosition.value = offsetValue / width;
      }
    }
  );

  // Shared value to keep track of the pages that should be skipped (don't appear)
  // during the current offset change. This is to avoid intermediate pages flashing
  // when switching through multiple pages at once, e.g. from page 1 to page 3.
  const skippedPages = useSharedValue<number[]>([]);

  const numberOfPages = Children.count(children);

  const panCtx = useSharedValue({ startX: 0 });
  const pan = Gesture.Pan()
    .onStart(() => {
      panCtx.value = { startX: offset.value };
    })
    .onUpdate((event) => {
      offset.value = clamp(
        panCtx.value.startX - event.translationX,
        0,
        (numberOfPages - 1) * width
      );
    })
    .onEnd((event) => {
      // If the velocity is high enough, move to the next page,
      // but make sure we don't go over 1 page.
      const projectedOffset = clamp(
        offset.value - event.velocityX * 0.2,
        panCtx.value.startX - width,
        panCtx.value.startX + width
      );

      // Snap to the nearest page if the difference is
      // more than 15% of the page width.
      const relativeOffset = projectedOffset / width;
      const roundedOffset =
        relativeOffset % 1 > 0.15
          ? event.translationX > 0
            ? Math.floor(relativeOffset)
            : Math.ceil(relativeOffset)
          : Math.round(relativeOffset);

      // Snap the offset to the nearest page.
      const finalOffset = clamp(roundedOffset, 0, numberOfPages - 1) * width;

      offset.value = withSpring(finalOffset, SPRING);
    });

  return (
    <GestureDetector gesture={pan}>
      <Animated.View {...restProps}>
        {Children.map(children, (child, index) => {
          return (
            <Page
              key={index}
              index={index}
              offset={offset}
              skippedPages={skippedPages}
              width={width}
              blurViewStyle={blurViewStyle}
            >
              {child}
            </Page>
          );
        })}
      </Animated.View>
    </GestureDetector>
  );
}

function Page({
  index,
  width,
  offset,
  skippedPages,
  children,
  blurViewStyle,
}: PropsWithChildren<{
  index: number;
  width: number;
  offset: SharedValue<number>;
  skippedPages: SharedValue<number[]>;
  blurViewStyle?: ViewStyle;
}>) {
  const blurTint = useThemedValue({ light: "light", dark: "dark" });

  const animatedProps = useAnimatedProps(() => ({
    pointerEvents:
      offset.value === index * width ? ("auto" as const) : ("none" as const),
  }));

  const transitionAnim = useAnimatedStyle(() => ({
    opacity: skippedPages.value.includes(index)
      ? 0
      : interpolate(
          offset.value,
          [
            // Should start fading/appearing when the page swipe is half-way through
            (index - 1) * width + width / 2,
            index * width,
            // Should start fading/appearing when the page swipe is half-way through
            (index + 1) * width - width / 2,
          ],
          [0, 1, 0],
          Extrapolation.CLAMP
        ),
    transform: skippedPages.value.includes(index)
      ? []
      : [
          {
            translateX: interpolate(
              offset.value,
              [(index - 1) * width, index * width, (index + 1) * width],
              [width * 0.12, 0, -width * 0.12],
              Extrapolation.CLAMP
            ),
          },
          {
            scale: interpolate(
              offset.value,
              [
                // Should start fading/appearing when the page swipe is half-way through
                (index - 1) * width + width / 2,
                index * width,
                // Should start fading/appearing when the page swipe is half-way through
                (index + 1) * width - width / 2,
              ],
              [0.98, 1, 0.98],
              Extrapolation.CLAMP
            ),
          },
        ],
  }));

  const animatedBlurProps = useAnimatedProps(() => ({
    intensity: skippedPages.value.includes(index)
      ? 0
      : interpolate(
          offset.value,
          [
            // Should start fading/appearing when the page swipe is half-way through
            (index - 1) * width + width / 2,
            index * width,
            // Should start fading/appearing when the page swipe is half-way through
            (index + 1) * width - width / 2,
          ],
          [40, 0, 40],
          Extrapolation.CLAMP
        ),
  }));

  return (
    <Animated.View
      style={[
        StyleSheet.absoluteFill,
        { flex: 1, transformOrigin: "top" },
        transitionAnim,
      ]}
      animatedProps={animatedProps}
    >
      {children}

      <AnimatedBlurView
        pointerEvents="none"
        style={[StyleSheet.absoluteFill, blurViewStyle]}
        tint={blurTint}
        animatedProps={animatedBlurProps}
      />
    </Animated.View>
  );
}
