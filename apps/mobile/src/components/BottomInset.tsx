import { View } from "~/components/Themed";

import { useSafeAreaInsets } from "react-native-safe-area-context";

export function BottomInset({ offset = 0 }: { offset?: number }) {
  const insets = useSafeAreaInsets();

  return <View style={{ height: insets.bottom + offset }} />;
}

export function TopInset({ offset = 0 }: { offset?: number }) {
  const insets = useSafeAreaInsets();

  return <View style={{ height: insets.top + offset }} />;
}
