import KeystoneSDK, {
  KeystoneSolanaSDK,
  URDecoder,
} from "@keystonehq/keystone-sdk";
import { View, Text } from "./Themed";
import { CustomAnimatedQRCode } from "./CustomAnimatedQR";
import { VersionedTransaction } from "@solana/web3.js";
import { Address } from "~/vendor/squads/models/solana";
import { useEffect, useRef, useState } from "react";
import {
  addKeystoneSignatureToRecoveryTx,
  createRecoveryTransactionKeystone,
  urZ,
} from "~/services/keystone";
import { toast } from "./Toaster";
import * as Haptics from "expo-haptics";
import { QrScannerBody } from "./QrScannerModal";
import { useAppState } from "~/state/appState";
import {
  Camera,
  useCameraDevice,
  useCodeScanner,
} from "react-native-vision-camera";
import { BottomModal, BottomModalImperativeMethods } from "./BottomModal";
import { Button } from "./Button";
import { create } from "zustand";
import { Spinner } from "./Spinner";
import { Href, router } from "expo-router";
import { executeAction, RecoveryKey } from "~/services/wallets";
import invariant from "invariant";
import { useMutation } from "@tanstack/react-query";
import { v4 as uuidv4 } from "uuid";

const keystoneSDK = new KeystoneSDK();

export function SignTransactionWithKeystoneQR({
  requestId,
  path,
  masterFingerprint,
  address,
  transaction,
  options,
  logo,
  size,
  disabled = false,
}: {
  requestId: string;
  path: string;
  masterFingerprint: string;
  address: Address;
  transaction: VersionedTransaction;
  options?: {
    size?: number;
    capacity?: number;
    interval?: number;
  };
  logo?: "dark" | "light";
  size?: number;
  disabled?: boolean;
}) {
  const [ur] = useState(() => {
    return keystoneSDK.sol.generateSignRequest({
      requestId,
      signData: Buffer.from(
        transaction.message.serialize() as Uint8Array
      ).toString("hex"),
      dataType: KeystoneSolanaSDK.DataType.Transaction,
      path,
      xfp: masterFingerprint,
      address,
      origin: "Fuse",
    });
  });

  return (
    <View style={{ alignItems: "center" }}>
      <CustomAnimatedQRCode
        ur={ur}
        logo={logo}
        options={options}
        size={size}
        disabled={disabled}
      />
    </View>
  );
}

export function RecoveryByKeystone({
  recoveryKey,
  activityId,
  onSuccessRedirect,
  themeOverride,
}: {
  recoveryKey: RecoveryKey & { recoveryKeyType: "keystone" };
  activityId: string;
  onSuccessRedirect: Href;
  themeOverride?: "light" | "dark";
}) {
  const [requestId] = useState(uuidv4());
  const {
    getSignatureModalShown,
    hideGetSignatureModal,
    showGetSignatureModal,
  } = useGetSignatureModalState();

  const createTransactionMutation = useMutation({
    mutationFn: async () => {
      invariant(activityId, "activityId is not set");
      return await createRecoveryTransactionKeystone({
        activityId,
        recoveryKey,
      });
    },
    onError: (error: Error) => {
      console.error(
        "Create recovery transaction with Keystone failed:",
        error.message
      );
      toast.error("Couldn't generate transaction");
    },
  });

  useEffect(() => {
    createTransactionMutation.mutate();
  }, []);

  const approveKeystoneRecovery = useMutation({
    mutationFn: async (signature: string) => {
      invariant(
        createTransactionMutation.data,
        "Recovery transaction not created"
      );
      hideGetSignatureModal();
      const signedTransactions = await addKeystoneSignatureToRecoveryTx({
        recoveryTransactions:
          createTransactionMutation.data.recoveryTransactions,
        signer: recoveryKey.address,
        signature,
      });

      await executeAction({ signedTransactions });

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      router.replace(onSuccessRedirect);
    },
    onError: (error: Error) => {
      console.error("Approve recovery with Keystone failed:", error.message);
      toast.error("Failed to approve");
    },
  });

  if (!createTransactionMutation.data || approveKeystoneRecovery.isPending) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
        <Spinner size={24} colorToken={"text"} />
      </View>
    );
  }
  return (
    <View style={{ flex: 1 }} gap={24}>
      <View gap={24}>
        <Text
          variant="display-bold"
          style={{ fontSize: 45, letterSpacing: -1.5, lineHeight: 45 }}
        >
          Confirm Recovery
        </Text>
        <Text
          variant={"medium"}
          style={{
            fontSize: 15,
            lineHeight: 20,
          }}
        >
          Scan the following QR code from your keystone and sign the transaction
          to confirm the recovery.{"\n"}
          Once signed click "Get signature"
        </Text>
      </View>

      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
        }}
        gap={8}
      >
        <SignTransactionWithKeystoneQR
          requestId={requestId}
          path={recoveryKey.derivationPath}
          masterFingerprint={recoveryKey.masterFingerprint}
          address={recoveryKey.address}
          transaction={createTransactionMutation.data.transactionToSign}
          disabled={getSignatureModalShown}
        />
      </View>
      <View gap={8}>
        <Button
          disabled={false}
          variant="primary"
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            showGetSignatureModal();
          }}
        >
          Get signature
        </Button>
      </View>
      <GetSignaturesScanModal
        requestId={requestId}
        themeOverride={themeOverride}
        onScan={(signature) => {
          approveKeystoneRecovery.mutate(signature);
        }}
      />
    </View>
  );
}

enum ScanState {
  Scanning,
  Success,
  Failed,
}

export const useGetSignatureModalState = create<{
  getSignatureModalShown: boolean;
  hideGetSignatureModal: () => void;
  showGetSignatureModal: () => void;
}>((set) => ({
  getSignatureModalShown: false,
  hideGetSignatureModal: () => set({ getSignatureModalShown: false }),
  showGetSignatureModal: () => set({ getSignatureModalShown: true }),
}));

export function GetSignaturesScanModal({
  requestId,
  themeOverride,
  onScan,
}: {
  requestId: string;
  themeOverride?: "light" | "dark";
  onScan: (signature: string) => void;
}) {
  const { getSignatureModalShown, hideGetSignatureModal } =
    useGetSignatureModalState();
  const modalRef = useRef<BottomModalImperativeMethods>(null);
  useEffect(() => {
    if (getSignatureModalShown) {
      modalRef.current?.present();
    } else {
      modalRef.current?.close();
    }
  }, [getSignatureModalShown]);

  const [scanState, setScanState] = useState(ScanState.Scanning);

  const { appState } = useAppState();

  const device = useCameraDevice("back");
  const codeScanner = useCodeScanner({
    codeTypes: ["qr"],
    onCodeScanned: (codes) => {
      if (!codes[0].value) return;
      handleScan(codes[0].value);
    },
  });
  const handleScan = (data: string) => {
    if (scanState === ScanState.Success) return;
    const decoder = new URDecoder();

    const validationResult = urZ.safeParse(data);

    if (!validationResult.success) {
      if (scanState === ScanState.Failed) return;
      setScanState(ScanState.Failed);
      toast.error("Unsupported QR code", {
        opaque: true,
        duration: 2000,
        onClose: () => setScanState(ScanState.Scanning),
      });
      return;
    }

    decoder.receivePart(data);

    if (decoder.isSuccess()) {
      const ur = decoder.resultUR();
      const signatureObject = keystoneSDK.sol.parseSignature(ur);

      if (signatureObject.requestId !== requestId) {
        if (scanState === ScanState.Failed) return;
        setScanState(ScanState.Failed);
        toast.error("Invalid Request ID", {
          opaque: true,
          duration: 2000,
          onClose: () => setScanState(ScanState.Scanning),
        });
        return;
      }
      Haptics.selectionAsync();
      setScanState(ScanState.Success);

      setTimeout(() => {
        onScan(signatureObject.signature);
      }, 200);
    }

    if (decoder.isError()) {
      toast.error("Error reading QR");
    }
  };

  return (
    <BottomModal
      modalId={"get-signature-keystone"}
      title={"Scan QR"}
      modalRef={modalRef}
      themeOverride={themeOverride}
      onClose={hideGetSignatureModal}
      onDismiss={hideGetSignatureModal}
      body={
        <>
          <View style={{ gap: 15 }}>
            <Text
              colorToken={"textSecondary"}
              variant="medium"
              style={{ fontSize: 18 }}
            >
              Scan the QR code in your keystone device to continue
            </Text>

            <View
              style={{
                height: 400,
                borderRadius: 16,
                borderCurve: "continuous",
                overflow: "hidden",
                backgroundColor: "black",
              }}
            >
              {device && (
                <Camera
                  device={device}
                  isActive={
                    scanState === ScanState.Scanning && appState === "active"
                  }
                  codeScanner={codeScanner}
                  style={{
                    height: 400,
                    borderRadius: 16,
                    borderCurve: "continuous",
                    overflow: "hidden",
                  }}
                />
              )}
              <View style={{ bottom: 200 }}>
                <QrScannerBody />
              </View>
            </View>
          </View>
        </>
      }
      footer={
        <Button
          variant="secondary"
          onPress={() => {
            Haptics.selectionAsync();
            modalRef.current?.dismiss();
          }}
        >
          Close
        </Button>
      }
    />
  );
}
