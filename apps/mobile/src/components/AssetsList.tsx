import { Address } from "@squads/models/solana";
import { Text, useColor, View } from "~/components/Themed";
import { TouchableScale } from "~/components/TouchableScale";
import {
  formatPercent,
  formatTokenAmount,
  formatUsdValue,
} from "@squads/utils/numberFormats";
import { CoinImage } from "~/components/CoinLogo";
import { DashedListSeparator } from "~/components/ListSeparator";
import * as Haptics from "expo-haptics";
import Animated, {
  Easing,
  Extrapolation,
  FadeIn,
  FadeInLeft,
  FadeInUp,
  FadeOut,
  FadeOutUp,
  interpolate,
  measure,
  MeasuredDimensions,
  runOnJS,
  useAnimatedProps,
  useAnimatedRef,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withDelay,
  withRepeat,
  withSequence,
  withSpring,
  withTiming,
} from "react-native-reanimated";
import { DURATION_FAST, SPRING } from "~/constants/animations";
import { tokenUsdValue } from "~/utils/tokens";
import { H4 } from "~/components/typography/H4";
import { TokenBalance } from "~/services/balances";
import { P3 } from "~/components/typography/P3";
import {
  Canvas,
  Circle,
  Group,
  LinearGradient,
  Mask,
  RoundedRect,
  translate,
  vec,
} from "@shopify/react-native-skia";
import {
  ComponentProps,
  memo,
  ReactNode,
  Suspense,
  useEffect,
  useMemo,
  useState,
} from "react";
import { FlatList } from "~/components/FlatList";
import { Row } from "~/components/Grid";
import { Icon } from "~/components/Icon";
import { P2 } from "~/components/typography/P2";
import { LayoutRectangle, StyleSheet, useWindowDimensions } from "react-native";
import { SegmentedCircularBorder } from "~/components/SegmentedCircularBorder";
import { FullScreenView } from "~/components/stacking/FullScreenView";
import {
  Gesture,
  GestureDetector,
  State,
  TouchableWithoutFeedback,
} from "react-native-gesture-handler";
import { preloadRewards, useRewards } from "~/state/nativeStake";
import { useActiveWallet } from "~/state/wallet";
import { ContentSkeleton, TextSkeleton } from "~/components/Skeleton";
import { StakeRewards } from "~/services/nativeStake";
import { LAMPORTS_PER_SOL } from "@solana/web3.js";
import { useQueryClient } from "@tanstack/react-query";
import {
  HIDDEN_BALANCE_TEXT,
  useBalanceSettings,
} from "~/state/balanceSettings";
import { abbreviateAddress } from "~/vendor/squads/utils/abbreviateAddress";
import { mints } from "~/constants/tokens";
import { useTrustedTokenPriceByMint } from "~/state/tokens";
import { FuseErrorBoundary } from "./errors/FuseErrorBoundary";
import { AnimatedBlurView } from "./AnimatedBlurView";

type AssetListVariant = "tokenDetails" | "position";

export function AssetsList({
  tokens,
  variant,
  onSelect,
}: {
  tokens: Array<TokenBalance>;
  variant: AssetListVariant;
  onSelect?: (mint: Address | "SOL") => void;
}) {
  if (tokens.length === 0) {
    return (
      <Row gap={12} style={{ paddingHorizontal: 6, paddingVertical: 36 }}>
        <Icon
          name={"magnifyingglass"}
          size={12}
          colorToken={"textSecondary"}
          weight={"bold"}
        />
        <P2 colorToken={"textSecondary"}>No coins found</P2>
      </Row>
    );
  }

  return (
    <FlatList
      data={tokens}
      renderItem={({ item: token }) => (
        <AssetListItem
          key={token.mint}
          token={token}
          variant={variant}
          onPress={onSelect}
        />
      )}
    />
  );
}

const AssetListItem = memo(function AssetListItem({
  token,
  variant,
  onPress,
}: {
  token: TokenBalance;
  variant: AssetListVariant;
  onPress?: (mint: Address | "SOL") => void;
}) {
  return (
    <Animated.View
      key={token.mint}
      entering={FadeIn.delay(DURATION_FAST).duration(DURATION_FAST)}
      exiting={FadeOut.duration(DURATION_FAST)}
    >
      <TouchableScale
        onPress={
          onPress &&
          (() => {
            Haptics.selectionAsync();
            onPress(token.mint ?? "SOL");
          })
        }
        disabled={!onPress}
      >
        <Asset token={token} variant={variant} />
      </TouchableScale>
    </Animated.View>
  );
});

export function Asset({
  token,
  variant,
  includeStaked,
  includeEarn,
}: {
  token: TokenBalance;
  variant: AssetListVariant;
  includeStaked?: boolean;
  includeEarn?: boolean;
}) {
  const {
    isBalanceHidden,
    displayBalance,
    displayChange,
    getBalanceChangeMode,
  } = useBalanceSettings();
  const isSol = token.mint === null;

  const totalAmount = TokenBalance.total(token, { includeEarn, includeStaked });

  const totalAmount24h = (() => {
    const available24h = token.amount24h ?? 0;

    if (isSol) {
      const staked24h = token.stakedAmount24h ?? 0;

      return includeStaked ? available24h + staked24h : available24h;
    } else {
      const earn24h =
        (token.driftEarn?.total24h ?? 0) +
        (token.luloProtectedEarn?.total24h ?? 0) +
        (token.kaminoEarn?.total24h ?? 0);

      return includeEarn ? available24h + earn24h : available24h;
    }
  })();

  const amount =
    totalAmount &&
    formatTokenAmount(totalAmount / 10 ** token.decimals, undefined, {
      filterSmallAmounts: true,
    });

  const metadata = token.metadata;

  const usdValue = tokenUsdValue({
    amount: totalAmount,
    decimals: token.decimals,
    usdcPrice: token.usdcPrice,
  });
  const usdAmountString = formatUsdValue(usdValue);

  const usdAmount24h = tokenUsdValue({
    amount: totalAmount24h,
    decimals: token.decimals,
    usdcPrice: token.usdcPrice24h,
  });

  const usdDiff = usdValue - usdAmount24h;
  const diffPercent =
    usdDiff === 0 || usdAmount24h === 0 ? 0 : usdDiff / usdAmount24h;
  const diffPercentString = `${usdDiff >= 0 ? "+" : ""}${formatPercent(diffPercent)}%`;
  const diffUsdString = `${usdDiff >= 0 ? "+" : ""}${formatUsdValue(usdDiff)}`;
  const showDiff =
    getBalanceChangeMode() === "usd"
      ? !!(token.usdcPrice24h && Math.abs(usdDiff) > 0.01)
      : !!(token.usdcPrice24h && Math.abs(diffPercent) > 0.0001);

  return (
    <View
      style={{
        flexDirection: "row",
        gap: 12,
        alignItems: "flex-start",
        backgroundColor: "transparent",
        paddingVertical: 12,
      }}
    >
      <CoinImage
        key={token.metadata.logoUri}
        logoUri={token.metadata.logoUri}
        symbol={token.metadata.symbol}
      />
      <View style={{ flex: 1, gap: 2 }}>
        <H4 numberOfLines={1}>{metadata.name}</H4>
        <AssetListItemLeftBottomText>
          {isBalanceHidden || amount === 0 || variant === "tokenDetails"
            ? ""
            : `${amount} `}
          {metadata.symbol}
        </AssetListItemLeftBottomText>
      </View>
      <View style={{ gap: 2, alignItems: "flex-end" }}>
        {variant == "position" ? (
          <>
            <AssetListItemRightTopText>
              {displayBalance(usdAmountString)}
            </AssetListItemRightTopText>
            {showDiff && (
              <AssetListItemRightBottomText
                colorToken={usdDiff >= 0 ? "green" : "red"}
              >
                {displayChange({
                  percentageChange: diffPercentString,
                  usdChange: diffUsdString,
                })}
              </AssetListItemRightBottomText>
            )}
          </>
        ) : (
          variant == "tokenDetails" &&
          token.mint && (
            <>
              <FuseErrorBoundary
                FallbackComponent={TrustedTokenPriceTextFallback}
              >
                <Suspense fallback={<TextSkeleton size={10} />}>
                  <TrustedTokenPriceText mint={token.mint} />
                </Suspense>
              </FuseErrorBoundary>
              <AssetListItemRightBottomText>
                {token.mint === mints.sol ? "" : abbreviateAddress(token.mint)}
              </AssetListItemRightBottomText>
            </>
          )
        )}
      </View>
    </View>
  );
}

function TrustedTokenPriceTextFallback() {
  return (
    <Text
      variant="semibold"
      colorToken="textSecondary"
      style={{ fontSize: 15, lineHeight: 21 }}
    >
      -
    </Text>
  );
}

function TrustedTokenPriceText({ mint }: { mint: Address }) {
  const price = useTrustedTokenPriceByMint({ mint });
  return (
    <AssetListItemRightTopText>
      {price
        ? price >= 0.00001
          ? formatUsdValue(price, {
              maximumFractionDigits: price < 1 ? 5 : 2,
            })
          : "< $0.00001"
        : ""}
    </AssetListItemRightTopText>
  );
}

export function AssetWithDetails({
  assetBalance,
  onPress,
}: {
  assetBalance: TokenBalance;
  onPress?: () => void;
}) {
  const { getBalanceChangeMode } = useBalanceSettings();
  const isSol = assetBalance.mint === null;

  const availableAmount = assetBalance.amount ?? 0;
  const unavailableAmount = isSol
    ? ({ type: "staked", amount: assetBalance.stakedAmount ?? 0 } as const)
    : ({
        type: "deposited",
        amount:
          (assetBalance.driftEarn?.total ?? 0) +
          (assetBalance.luloProtectedEarn?.total ?? 0) +
          (assetBalance.kaminoEarn?.total ?? 0),
      } as const);

  const availableAmount24h = assetBalance.amount24h ?? 0;
  const unavailableAmount24h = isSol
    ? ({ type: "staked", amount: assetBalance.stakedAmount24h ?? 0 } as const)
    : ({
        type: "deposited",
        amount:
          (assetBalance.driftEarn?.total24h ?? 0) +
          (assetBalance.luloProtectedEarn?.total24h ?? 0) +
          (assetBalance.kaminoEarn?.total24h ?? 0),
      } as const);

  const totalAmount = availableAmount + unavailableAmount.amount;
  const totalAmount24h = availableAmount24h + unavailableAmount24h.amount;

  const usdAmount = tokenUsdValue({
    amount: totalAmount,
    decimals: assetBalance.decimals,
    usdcPrice: assetBalance.usdcPrice,
  });

  const usdAmount24h = tokenUsdValue({
    amount: totalAmount24h,
    decimals: assetBalance.decimals,
    usdcPrice: assetBalance.usdcPrice24h,
  });
  const usdDiff = usdAmount - usdAmount24h;
  const percentageDiff =
    usdDiff === 0 || usdAmount24h === 0 ? 0 : usdDiff / usdAmount24h;
  const showDiff =
    getBalanceChangeMode() === "usd"
      ? !!(assetBalance.usdcPrice24h && Math.abs(usdDiff) > 0.01)
      : !!(assetBalance.usdcPrice24h && Math.abs(percentageDiff) > 0.0001);

  const [expanded, setExpanded] = useState<{
    pageY: number;
    reason: "force-touch" | "logo-tap";
  } | null>(null);
  useEffect(() => {
    if (expanded) {
      originalItemOpacity.value = 0;
    } else {
      originalItemOpacity.value = 1;
    }
  }, [expanded]);

  const [collapsing, setCollapsing] = useState(false);

  const originalItemOpacity = useSharedValue(1);
  const originalItemStyle = useAnimatedStyle(() => ({
    opacity: originalItemOpacity.value,
  }));

  return (
    <>
      <Animated.View style={originalItemStyle}>
        <AssetWithDetailsInner
          name={assetBalance.metadata.name}
          logoUri={assetBalance.metadata.logoUri}
          symbol={assetBalance.metadata.symbol}
          decimals={assetBalance.decimals}
          availableAmount={availableAmount}
          unavailableAmount={unavailableAmount}
          usdValue={usdAmount}
          usdDiff={usdDiff}
          percentageDiff={percentageDiff}
          showDiff={showDiff}
          onExpand={({ rowMeasurements, reason }) => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Rigid);
            setExpanded({ pageY: rowMeasurements.pageY, reason });
          }}
          onPress={onPress}
        />
      </Animated.View>

      {/* Details Overlay */}
      <FullScreenView zIndex={100}>
        {expanded && (
          <BlurredBackdrop
            onExit={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Soft);
              setCollapsing(true);
            }}
            onExited={() => {
              setExpanded(null);
              setCollapsing(false);
            }}
          >
            <View style={{ paddingHorizontal: 20 }}>
              <AssetWithDetailsInner
                expanded={expanded}
                collapsing={collapsing}
                name={assetBalance.metadata.name}
                logoUri={assetBalance.metadata.logoUri}
                symbol={assetBalance.metadata.symbol}
                decimals={assetBalance.decimals}
                availableAmount={availableAmount}
                unavailableAmount={unavailableAmount}
                usdValue={usdAmount}
                usdDiff={usdDiff}
                percentageDiff={percentageDiff}
                showDiff={showDiff}
              />
            </View>
          </BlurredBackdrop>
        )}
      </FullScreenView>
    </>
  );
}

function AssetWithDetailsInner({
  expanded,
  collapsing,
  name,
  logoUri,
  symbol,
  decimals,
  availableAmount,
  unavailableAmount,
  usdValue,
  usdDiff,
  percentageDiff,
  showDiff,
  onExpand,
  onPress,
}: {
  expanded?: { pageY: number; reason: "force-touch" | "logo-tap" };
  collapsing?: boolean;
  name: string;
  logoUri: string | null;
  symbol: string;
  decimals: number;
  availableAmount: number;
  unavailableAmount:
    | { type: "deposited"; amount: number }
    | { type: "staked"; amount: number };
  usdValue: number;
  usdDiff: number;
  percentageDiff: number;
  showDiff: boolean;
  onExpand?: (args: {
    rowMeasurements: MeasuredDimensions;
    reason: "force-touch" | "logo-tap";
  }) => void;
  onPress?: () => void;
}) {
  const { wallet } = useActiveWallet();
  const { isBalanceHidden, displayBalance, displayChange } =
    useBalanceSettings();
  const { height: windowHeight } = useWindowDimensions();

  const diffPercentString = `${usdDiff >= 0 ? "+" : ""}${formatPercent(percentageDiff)}%`;
  const diffUsdString = `${usdDiff >= 0 ? "+" : ""}${formatUsdValue(usdDiff)}`;

  const availableColor = useColor("blue");
  const unavailableColor = useColor("textTertiary");

  const availablePart =
    availableAmount / (availableAmount + unavailableAmount.amount);
  const unavailablePart = 1 - availablePart;

  const breakdownWidth =
    availableAmount === 0 || unavailableAmount.amount === 0 ? 0 : 7;

  const availableAmountString = formatTokenAmount(
    availableAmount / 10 ** decimals,
    symbol,
    { filterSmallAmounts: true }
  );
  const unavailableAmountString = formatTokenAmount(
    unavailableAmount.amount / 10 ** decimals,
    symbol,
    { filterSmallAmounts: true }
  );

  const totalAmount = availableAmount + unavailableAmount.amount;
  const formattedAmount = formatTokenAmount(
    totalAmount / 10 ** decimals,
    undefined,
    { filterSmallAmounts: true }
  );

  const usdValueString = formatUsdValue(usdValue);

  const rowAnimatedRef = useAnimatedRef<Animated.View>();
  const logoAnimatedRef = useAnimatedRef<Animated.View>();

  const logoScale = useSharedValue(0.78);
  const chartScale = useDerivedValue(() => {
    return interpolate(
      logoScale.value,
      [0.78, 1],
      [1, 0.78],
      Extrapolation.CLAMP
    );
  });

  const logoStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: logoScale.value }],
    };
  });
  const chartStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: chartScale.value }],
    };
  });

  const logoAndChartScale = useSharedValue(1);
  const logoAndChartStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: logoAndChartScale.value }],
    };
  });

  const rowScale = useSharedValue(1);
  const rowStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: rowScale.value }],
    };
  });

  const expandedTranslateY = useSharedValue(expanded?.pageY ?? 0);
  const expandedStyle = useAnimatedStyle(() => {
    const translateY = expandedTranslateY.value;

    return {
      transform: [{ translateY: translateY }],
    };
  });

  const [expandedLayout, setExpandedLayout] = useState<LayoutRectangle | null>(
    null
  );

  useEffect(() => {
    logoScale.value = withSpring(collapsing ? 0.78 : expanded ? 1 : 0.78, {
      ...SPRING,
      stiffness: 250,
      damping: 15,
    });

    if (expanded && expanded.reason === "force-touch" && !collapsing) {
      rowScale.value = withSequence(
        withTiming(0.97, { duration: 0 }),
        withSpring(1, {
          ...SPRING,
          stiffness: 250,
          damping: 15,
        })
      );
    }

    const BOTTOM_PADDING = 80;
    const expandedHeight = (expandedLayout?.height ?? 0) + BOTTOM_PADDING;

    if (expanded) {
      if (collapsing) {
        expandedTranslateY.value = withSpring(expanded.pageY, SPRING);
      } else if (expanded.pageY > windowHeight - expandedHeight) {
        expandedTranslateY.value = withSpring(
          windowHeight - expandedHeight,
          SPRING
        );
      }
    }
  }, [expanded, collapsing, expandedLayout]);

  const isSol = unavailableAmount.type === "staked";
  const queryClient = useQueryClient();
  useEffect(() => {
    if (isSol) {
      preloadRewards({ queryClient, vaultKey: wallet.defaultVault }).catch(
        (err) => {
          console.error("Failed to preload rewards", err);
        }
      );
    }
  }, [isSol]);

  const enteringDuration = 200;

  const BREAKDOWN_BARS_ENTERING = useMemo(
    () => FadeInLeft.duration(enteringDuration),
    [enteringDuration]
  );
  const BREAKDOWN_BARS_EXITING = useMemo(() => FadeOutUp.duration(150), []);

  const BREAKDOWN_FIRST_ROW_ENTERING = useMemo(
    () =>
      FadeInUp.withInitialValues({
        opacity: 0,
        transform: [{ translateY: -10 }],
      })
        .delay(30)
        .duration(enteringDuration),
    [enteringDuration]
  );
  const BREAKDOWN_FIRST_ROW_EXITING = useMemo(
    () => FadeOutUp.duration(150),
    []
  );

  const BREAKDOWN_SECOND_ROW_ENTERING = useMemo(
    () =>
      FadeInUp.withInitialValues({
        opacity: 0,
        transform: [{ translateY: -10 }],
      })
        .delay(75)
        .duration(enteringDuration),
    [enteringDuration]
  );
  const BREAKDOWN_SECOND_ROW_EXITING = useMemo(
    () => FadeOutUp.delay(25).duration(150),
    []
  );

  const BREAKDOWN_THIRD_ROW_ENTERING = useMemo(
    () =>
      FadeInUp.withInitialValues({
        opacity: 0,
        transform: [{ translateY: -10 }],
      })
        .delay(100)
        .duration(enteringDuration),
    [enteringDuration]
  );
  const BREAKDOWN_THIRD_ROW_EXITING = useMemo(
    () => FadeOutUp.delay(40).duration(150),
    []
  );

  const BREAKDOWN_FOURTH_ROW_ENTERING = useMemo(
    () =>
      FadeInUp.withInitialValues({
        opacity: 0,
        transform: [{ translateY: -10 }],
      })
        .delay(125)
        .duration(enteringDuration),
    [enteringDuration]
  );
  const BREAKDOWN_FOURTH_ROW_EXITING = useMemo(
    () => FadeOutUp.delay(50).duration(150),
    []
  );

  const longPressTriggerDelay = 350;

  const rowLongPressGesture = Gesture.LongPress()
    .enabled(!!onExpand)
    .minDuration(longPressTriggerDelay)
    .onBegin((event) => {
      const logoMeasurements = measure(logoAnimatedRef);
      if (!logoMeasurements) return; // Shouldn't happen

      // Check if the user tapped on the logo, in which case we should ignore the long press completely.
      const isLogoTap =
        event.absoluteX > logoMeasurements.pageX &&
        event.absoluteX < logoMeasurements.pageX + logoMeasurements.width &&
        event.absoluteY > logoMeasurements.pageY &&
        event.absoluteY < logoMeasurements.pageY + logoMeasurements.height;
      if (isLogoTap) return;

      rowScale.value = withSequence(
        withSpring(0.98, { ...SPRING, velocity: -1.2 }),
        withTiming(0.97, {
          duration: longPressTriggerDelay,
          easing: Easing.in(Easing.ease),
        })
      );
    })
    .onStart((event) => {
      const logoMeasurements = measure(logoAnimatedRef);
      if (!logoMeasurements) return; // Shouldn't happen

      // Check if the user tapped on the logo, in which case we should ignore the long press completely.
      const isLogoTap =
        event.absoluteX > logoMeasurements.pageX &&
        event.absoluteX < logoMeasurements.pageX + logoMeasurements.width &&
        event.absoluteY > logoMeasurements.pageY &&
        event.absoluteY < logoMeasurements.pageY + logoMeasurements.height;
      if (isLogoTap) return;

      const measurements = measure(rowAnimatedRef);
      if (!measurements) return; // Shouldn't happen

      if (onExpand) {
        runOnJS(onExpand)({
          rowMeasurements: measurements,
          reason: "force-touch",
        });
      }
    })
    .onFinalize(() => {
      rowScale.value = withSpring(1, SPRING);
    });

  const rowTapGesture = Gesture.Tap()
    .enabled(!!onPress)
    .onStart(() => {
      runOnJS(onPress!)();
    });

  const logoTapGesture = Gesture.Tap()
    .enabled(!!onExpand)
    .blocksExternalGesture(rowLongPressGesture)
    // .maxDuration(100_000) // Unlimited
    .onBegin(() => {
      logoAndChartScale.value = withSpring(0.95, SPRING);
    })
    .onStart(() => {
      logoAndChartScale.value = withSpring(1, SPRING);

      const measurements = measure(rowAnimatedRef);
      if (!measurements) return; // Shouldn't happen

      if (onExpand) {
        runOnJS(onExpand)({
          rowMeasurements: measurements,
          reason: "logo-tap",
        });
      }
    })
    .onFinalize((event) => {
      if (event.state === State.FAILED) {
        logoAndChartScale.value = withSpring(1);
      }
    });

  const rowGesture = Gesture.Exclusive(rowTapGesture, rowLongPressGesture);

  return (
    <View
      onLayout={
        expanded
          ? (ev) => {
              setExpandedLayout(ev.nativeEvent.layout);
            }
          : undefined
      }
    >
      <Animated.View ref={rowAnimatedRef} style={[expandedStyle, { gap: 16 }]}>
        <GestureDetector gesture={rowGesture}>
          <Animated.View
            style={[
              {
                flexDirection: "row",
                gap: 12,
                alignItems: "flex-start",
                backgroundColor: "transparent",
                paddingVertical: 12,
              },
              rowStyle,
            ]}
          >
            {/* Asset Logo + Chart */}
            <GestureDetector gesture={logoTapGesture}>
              <Animated.View ref={logoAnimatedRef} style={logoAndChartStyle}>
                <Animated.View style={[StyleSheet.absoluteFill, chartStyle]}>
                  <SegmentedCircularBorder
                    size={42}
                    strokeWidth={3}
                    gap={5}
                    segments={[
                      {
                        color: unavailableColor,
                        part: unavailablePart,
                      },
                      { color: availableColor, part: availablePart },
                    ].filter(({ part }) => part > 0)}
                  />
                </Animated.View>
                <Animated.View style={logoStyle}>
                  <CoinImage key={logoUri} logoUri={logoUri} symbol={symbol} />
                </Animated.View>
              </Animated.View>
            </GestureDetector>
            <View style={{ flex: 1, gap: 2 }}>
              <H4 numberOfLines={1}>{name}</H4>
              <AssetListItemLeftBottomText>
                {isBalanceHidden ? "" : `${formattedAmount} `}
                {symbol}
              </AssetListItemLeftBottomText>
            </View>
            <View style={{ gap: 2, alignItems: "flex-end" }}>
              <>
                {usdValue && (
                  <AssetListItemRightTopText>
                    {displayBalance(usdValueString)}
                  </AssetListItemRightTopText>
                )}

                {showDiff ? (
                  <AssetListItemRightBottomText
                    colorToken={usdDiff >= 0 ? "green" : "red"}
                  >
                    {displayChange({
                      percentageChange: diffPercentString,
                      usdChange: diffUsdString,
                    })}
                  </AssetListItemRightBottomText>
                ) : null}
              </>
            </View>
          </Animated.View>
        </GestureDetector>
        {/* Expanded details */}
        {expanded && !collapsing && (
          <View
            style={{
              gap: 24,
              marginLeft: 42 + 12,
              overflow: "hidden",
            }}
          >
            {/* Breakdown */}
            <Animated.View
              entering={BREAKDOWN_BARS_ENTERING}
              exiting={BREAKDOWN_BARS_EXITING}
              style={{ flexDirection: "row", gap: 4 }}
            >
              <View
                style={{
                  height: 4,
                  flexGrow: availablePart,
                  width: breakdownWidth,
                  backgroundColor: availableColor,
                  borderRadius: 999,
                }}
              />
              <View
                style={{
                  height: 4,
                  flexGrow: unavailablePart,
                  width: breakdownWidth,
                  backgroundColor: unavailableColor,
                  borderRadius: 999,
                }}
              />
            </Animated.View>
            <View gap={12}>
              {/* Available */}
              <Animated.View
                entering={BREAKDOWN_FIRST_ROW_ENTERING}
                exiting={BREAKDOWN_FIRST_ROW_EXITING}
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  gap: 8,
                }}
              >
                <View
                  style={{
                    width: 12,
                    aspectRatio: 1,
                    flexShrink: 0,
                    backgroundColor: availableColor,
                    borderRadius: 999,
                  }}
                />
                <View style={{ flex: 1 }}>
                  <P3>Available</P3>
                </View>
                <P3>
                  {isBalanceHidden
                    ? `${formatPercent(availableAmount / totalAmount)}%`
                    : availableAmountString}
                </P3>
              </Animated.View>
              {/* Unavailable */}
              <Animated.View
                entering={BREAKDOWN_SECOND_ROW_ENTERING}
                exiting={BREAKDOWN_SECOND_ROW_EXITING}
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  gap: 8,
                }}
              >
                <View
                  style={{
                    width: 12,
                    aspectRatio: 1,
                    flexShrink: 0,
                    backgroundColor: unavailableColor,
                    borderRadius: 999,
                  }}
                />
                <View style={{ flex: 1 }}>
                  <P3>
                    {unavailableAmount.type === "deposited"
                      ? "Deposited"
                      : "Total Staked"}
                  </P3>
                </View>
                <P3>
                  {isBalanceHidden
                    ? `${formatPercent(unavailableAmount.amount / totalAmount)}%`
                    : unavailableAmountString}
                </P3>
              </Animated.View>
              {unavailableAmount.type === "staked" && (
                <FuseErrorBoundary FallbackComponent={() => null}>
                  <Animated.View
                    entering={BREAKDOWN_SECOND_ROW_ENTERING}
                    exiting={BREAKDOWN_SECOND_ROW_EXITING}
                    style={{
                      marginLeft: 20,
                      gap: 8,
                    }}
                  >
                    <DashedListSeparator />
                  </Animated.View>
                  <Animated.View
                    entering={BREAKDOWN_THIRD_ROW_ENTERING}
                    exiting={BREAKDOWN_THIRD_ROW_EXITING}
                    style={{ flexDirection: "row", marginLeft: 20 }}
                  >
                    <View style={{ flexGrow: 1 }}>
                      <P3>Staked</P3>
                    </View>
                    <StakedAmount stakedAmount={unavailableAmount.amount} />
                  </Animated.View>

                  <Animated.View
                    entering={BREAKDOWN_FOURTH_ROW_ENTERING}
                    exiting={BREAKDOWN_FOURTH_ROW_EXITING}
                    style={{ flexDirection: "row", marginLeft: 20 }}
                  >
                    <View style={{ flexGrow: 1 }}>
                      <P3>Earned</P3>
                    </View>
                    <StakeRewardsAmount />
                  </Animated.View>
                </FuseErrorBoundary>
              )}
            </View>
          </View>
        )}
      </Animated.View>
    </View>
  );
}

function AssetListItemLeftBottomText(props: ComponentProps<typeof Text>) {
  return (
    <P3 colorToken={"textSecondary"} style={{ fontSize: 14 }} {...props} />
  );
}

function AssetListItemRightTopText(props: ComponentProps<typeof Text>) {
  return (
    <Text
      variant="semibold"
      colorToken="text"
      style={{ fontSize: 15, lineHeight: 21 }}
      {...props}
    />
  );
}

function AssetListItemRightBottomText(props: ComponentProps<typeof Text>) {
  return (
    <P3 colorToken={"textSecondary"} style={{ fontSize: 14 }} {...props} />
  );
}

/** Staked minus rewards */
function StakedAmount({
  stakedAmount,
}: {
  /** Staked amount with rewards */
  stakedAmount: number;
}) {
  return (
    <Suspense fallback={<AmountSkeleton />}>
      <StakedAmountInner stakedAmount={stakedAmount} />
    </Suspense>
  );
}

function StakedAmountInner({ stakedAmount }: { stakedAmount: number }) {
  const { wallet } = useActiveWallet();
  const rewards = useRewards({ vaultKey: wallet.defaultVault });

  const totalRewards = StakeRewards.totalRewards(rewards);
  const { displayBalance } = useBalanceSettings();

  return (
    <P3>
      {displayBalance(
        formatTokenAmount(
          (stakedAmount - totalRewards) / LAMPORTS_PER_SOL,
          "SOL",
          { filterSmallAmounts: true }
        )
      )}
    </P3>
  );
}

function StakeRewardsAmount() {
  return (
    <Suspense fallback={<AmountSkeleton />}>
      <StakeRewardsAmountInner />
    </Suspense>
  );
}

function StakeRewardsAmountInner() {
  const { wallet } = useActiveWallet();
  const rewards = useRewards({ vaultKey: wallet.defaultVault });

  const totalRewards = StakeRewards.totalRewards(rewards);

  const { isBalanceHidden } = useBalanceSettings();
  if (isBalanceHidden) return <P3>{HIDDEN_BALANCE_TEXT}</P3>;

  return (
    <P3 colorToken={totalRewards > 0 ? "green" : "textSecondary"}>
      {totalRewards > 0 ? "+ " : ""}
      {formatTokenAmount(totalRewards / LAMPORTS_PER_SOL, "SOL", {
        filterSmallAmounts: true,
      })}
    </P3>
  );
}

function AmountSkeleton() {
  return (
    <ContentSkeleton>
      <P3>10.00 SOL</P3>
    </ContentSkeleton>
  );
}

function BlurredBackdrop({
  children,
  onExit,
  onExited,
}: {
  children: ReactNode;
  onExit?: () => void;
  onExited: () => void;
}) {
  const enterDuration = 150;
  const exitDuration = 200;

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [exit, setExit] = useState(false);

  const intensity = useSharedValue(0);

  useEffect(() => {
    if (exit) {
      intensity.value = withTiming(
        0,
        { duration: exitDuration },
        (finished) => {
          if (finished && onExited) {
            runOnJS(onExited)();
          }
        }
      );
    } else {
      intensity.value = withTiming(80, {
        duration: enterDuration,
        easing: Easing.inOut(Easing.ease),
      });
    }
  }, [exit]);

  const animatedBlurProps = useAnimatedProps(() => ({
    intensity: intensity.value,
  }));

  return (
    <TouchableWithoutFeedback
      onPress={() => {
        // originalItemOpacity.value = 1;
        setExit(true);
        onExit?.();
      }}
    >
      <View style={[{ width: windowWidth, height: windowHeight }]}>
        <AnimatedBlurView
          animatedProps={animatedBlurProps}
          tint="light"
          style={{ flex: 1 }}
        >
          {children}
        </AnimatedBlurView>
      </View>
    </TouchableWithoutFeedback>
  );
}

export function AssetSkeleton() {
  const size = useSharedValue({ width: 0, height: 0 });

  const height = useDerivedValue(() => size.value.height, [size]);
  const width = useDerivedValue(() => size.value.width, [size]);

  const endPoint = useDerivedValue(() => ({ x: width.value, y: 0 }), [size]);

  const animationProgress = useSharedValue(0);
  useEffect(() => {
    animationProgress.value = withRepeat(
      withDelay(1000, withTiming(1, { duration: 700, easing: Easing.linear })),
      -1
    );
  }, []);

  const gradientTransform = useDerivedValue(() => {
    return translate(
      interpolate(animationProgress.value, [0, 1], [-width.value, width.value]),
      0
    );
  });

  const rowButtonWidth = 105;
  const rowButtonX = useDerivedValue(
    () => width.value - rowButtonWidth,
    [width]
  );

  const rowTextWidth = useDerivedValue(
    () => width.value - 44 - 10 - rowButtonWidth - 10,
    [width]
  );
  const rowTextHeight = 50;

  return (
    <View style={{ height: 80 }}>
      <Canvas
        style={{
          flex: 1,
          width: "100%",
        }}
        onSize={size}
      >
        <Mask
          mode="luminance"
          mask={
            <Group>
              <Circle r={21} cx={21} cy={40} color="white" />
              <RoundedRect
                x={44 + 10}
                y={15}
                width={rowTextWidth}
                height={rowTextHeight}
                r={12}
                color="white"
              />
              <RoundedRect
                x={rowButtonX}
                y={19}
                width={rowButtonWidth}
                height={42}
                r={21}
                color="white"
              />
            </Group>
          }
        >
          <RoundedRect
            x={0}
            y={0}
            width={width}
            height={height}
            r={12}
            color="rgb(224,224,224)"
          />
          <RoundedRect
            x={0}
            y={0}
            width={width}
            height={height}
            matrix={gradientTransform}
            r={12}
          >
            <LinearGradient
              start={vec(0, 0)}
              end={endPoint}
              colors={[
                "rgb(224,224,224)",
                "rgb(224,224,224)",
                "rgb(234,234,234)",
                "rgb(224,224,224)",
                "rgb(224,224,224)",
              ]}
              positions={[0, 0.2, 0.5, 0.8, 1]}
            />
          </RoundedRect>
        </Mask>
      </Canvas>
    </View>
  );
}
