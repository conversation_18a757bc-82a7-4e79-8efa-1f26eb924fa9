import Animated, {
  SharedValue,
  useAnimatedProps,
  useAnimatedStyle,
  useSharedValue,
} from "react-native-reanimated";
import { ComponentProps } from "react";
import { useColor, View } from "~/components/Themed";
import { TextInput } from "react-native";

const AnimatedInput = Animated.createAnimatedComponent(TextInput);
Animated.addWhitelistedNativeProps({ text: true });

export function AnimatedAmountDisplay({
  amount,
}: {
  amount: SharedValue<string>;
}) {
  const animatedProps = useAnimatedProps(() => {
    return {
      text: amount.value,
      // Here we use any because the text prop is not available in the type
    } as NonNullable<ComponentProps<typeof AnimatedInput>["animatedProps"]>;
  });

  const fontSize = 45;
  const characterWidth = fontSize * 0.65; // This is the worst case: all 0s or all 9s
  const width = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => {
    const charsWidth = characterWidth * amount.value.length;
    return {
      fontSize: charsWidth > width.value ? 45 * (width.value / charsWidth) : 45,
    };
  });

  const placeholderTextColor = useColor("textTertiary");

  return (
    <View
      style={{ flex: 1, justifyContent: "center" }}
      onLayout={(e) => (width.value = e.nativeEvent.layout.width)}
    >
      <AnimatedInput
        animatedProps={animatedProps}
        editable={false}
        value={amount.value}
        placeholder={"0"}
        placeholderTextColor={placeholderTextColor}
        style={[
          {
            fontFamily: "SFProDisplaySemibold",
          },
          animatedStyle,
        ]}
      />
    </View>
  );
}
