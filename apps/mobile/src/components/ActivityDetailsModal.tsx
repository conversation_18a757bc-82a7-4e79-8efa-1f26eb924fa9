import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { RefObject, useState } from "react";
import { Activity } from "~/services/activities";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import { formatSolAmount } from "~/utils/tokens";
import { useActiveWallet } from "~/state/wallet";
import {
  MARINADE_NATIVE_VALIDATOR,
  SQUADS_VALIDATOR,
} from "~/services/nativeStake";
import { LAMPORTS_PER_SOL } from "@solana/web3.js";
import { formatTokenAmount } from "@squads/utils/numberFormats";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import { OnchainTransactions } from "~/components/activities/OnchainTransactions";
import {
  ActivityDetailsTextRow,
  ActivityRowsContainer,
} from "~/components/activities/ActivityDetailsRow";
import { DefaultActivityHeader } from "~/components/activities/details/DefaultActivityHeader";
import { VirtualAccountReceive } from "~/components/activities/details/VirtualAccountReceive";
import { SendFiat } from "~/components/activities/details/SendFiat";
import { OnchainFees } from "~/components/activities/OnchainFees";
import { Swap } from "~/components/activities/details/Swap";
import { Address } from "~/vendor/squads/models/solana";
import { useSuspenseToken } from "~/state/tokens";
import { CardCreate } from "~/components/activities/details/CardCreate";
import { WalletCreated } from "~/components/activities/details/WalletCreated";
import { WalletRecovery } from "~/components/activities/details/WalletRecovery";
import { KycUpdate } from "~/components/activities/details/KycUpdate";
import { TokenFeeRow } from "~/components/activities/TokenFeeRow";

export type ActivityDetailsModalParams = { activity: Activity };

export function ActivityDetailsModal({
  modalRef,
}: {
  modalRef: RefObject<BottomModalImperativeMethods<ActivityDetailsModalParams> | null>;
}) {
  const [showDetails, setShowDetails] = useState(false);

  function hasFooter(activity: Activity) {
    return (
      activity.details.type === "virtualAccountReceive" ||
      activity.details.type === "sendFiat"
    );
  }

  return (
    <BottomModal<ActivityDetailsModalParams>
      modalRef={modalRef}
      modalId="activity-details"
      title=" "
      onDismiss={() => setShowDetails(false)}
      onClose={() => setShowDetails(false)}
      body={({ activity }) => {
        return (
          <View
            style={{
              marginTop: -20,
              paddingHorizontal: 12,
              paddingBottom: !hasFooter(activity) ? 12 : undefined,
            }}
          >
            <ActivityDetailsModalBody
              activity={activity}
              showDetails={showDetails}
            />
          </View>
        );
      }}
      footer={({ activity }) => {
        if (!hasFooter(activity)) {
          return null;
        }

        return (
          <AnimatedTouchableScale
            onPress={() => setShowDetails((show) => !show)}
            style={{
              alignSelf: "center",
              paddingHorizontal: 18,
            }}
          >
            <Text variant="medium" size={14}>
              {showDetails ? "Hide details" : "Show details"}
            </Text>
          </AnimatedTouchableScale>
        );
      }}
    />
  );
}

function ActivityDetailsModalBody({
  activity,
  showDetails,
}: {
  activity: Activity;
  showDetails: boolean;
}) {
  if (Activity.is(activity, "virtualAccountReceive")) {
    return (
      <VirtualAccountReceive activity={activity} showDetails={showDetails} />
    );
  }

  if (Activity.is(activity, "sendFiat")) {
    return <SendFiat activity={activity} showDetails={showDetails} />;
  }

  if (Activity.is(activity, "swap")) {
    return <Swap activity={activity} />;
  }

  if (Activity.is(activity, "cardCreate")) {
    return <CardCreate activity={activity} />;
  }

  if (Activity.is(activity, "walletCreated")) {
    return <WalletCreated activity={activity} />;
  }

  if (Activity.is(activity, "walletRecovery")) {
    return <WalletRecovery activity={activity} />;
  }

  if (
    Activity.is(activity, "kycStarted") ||
    Activity.is(activity, "kycRejected") ||
    Activity.is(activity, "kycApproved")
  ) {
    return <KycUpdate activity={activity} />;
  }

  return (
    <View gap={32}>
      <DefaultActivityHeader activity={activity} />

      <ActivityRowsContainer>
        <ActivitySpecificRows activity={activity} />

        {activity.details.type === "sendToken" && (
          <TokenFeeRow
            mint={activity.details.tokenMint}
            feeAmount={activity.details.tokenFee?.amount ?? 0}
          />
        )}

        <OnchainTransactions transactions={activity.transactions} />
        <OnchainFees activity={activity} />
      </ActivityRowsContainer>
    </View>
  );
}

function ActivitySpecificRows({ activity }: { activity: Activity }) {
  const { wallet } = useActiveWallet();

  if (activity.details.type === "depositLiquidStake") {
    const lstAmount = activity.details.lamports / activity.details.lstPrice;

    const lstAmountString = formatTokenAmount(lstAmount, "fuseSOL", {
      filterSmallAmounts: true,
    });
    return (
      <ActivityDetailsTextRow label={"Received"} value={lstAmountString} />
    );
  }

  if (activity.details.type === "liquidStakeWithdraw") {
    const solAmount =
      activity.details.amount * (activity.details.lstPrice / LAMPORTS_PER_SOL);
    const solAmountString = formatSolAmount(solAmount);

    return (
      <ActivityDetailsTextRow label={"Received"} value={solAmountString} />
    );
  }

  if (
    activity.details.type === "sendSol" ||
    activity.details.type === "sendToken"
  ) {
    return (
      <>
        <ActivityDetailsTextRow
          label={"To"}
          value={abbreviateAddress(activity.details.to)}
          copyValue={activity.details.to}
        />
      </>
    );
  }

  if (
    activity.details.type === "receiveSol" ||
    activity.details.type === "receiveToken" ||
    activity.details.type === "receiveNft"
  ) {
    const vaultIndex = activity.details.vaultIndex;
    const vault = wallet.vaults.find((vault) => vault.index === vaultIndex);

    return (
      <>
        {activity.details.from && (
          <ActivityDetailsTextRow
            copyValue={activity.details.from}
            label={"From"}
            value={abbreviateAddress(activity.details.from)}
          />
        )}
        <ActivityDetailsTextRow
          label={"To"}
          value={vault?.name ?? "Wallet 1"}
          copyValue={vault?.key}
        />
      </>
    );
  }

  if (activity.details.type === "stake") {
    const voteKey = activity.details.details.voteKey;
    const validator =
      voteKey === SQUADS_VALIDATOR
        ? "Squads Validator"
        : voteKey === MARINADE_NATIVE_VALIDATOR
          ? "Marinade Native"
          : abbreviateAddress(voteKey);

    return (
      <>
        <ActivityDetailsTextRow label={"Validator"} value={validator} />
      </>
    );
  }
}


