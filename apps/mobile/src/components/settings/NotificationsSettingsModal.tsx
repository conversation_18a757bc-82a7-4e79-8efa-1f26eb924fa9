import { useRef, useState } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Flex, Row } from "~/components/Grid";
import { Linking } from "react-native";
import { useActiveWallet } from "~/state/wallet";
import { H4 } from "~/components/typography/H4";
import * as Notifications from "expo-notifications";
import * as Haptics from "expo-haptics";
import {
  registerForPushNotifications,
  updatePushNotificationsSettings,
} from "~/utils/pushNotifications";
import { Switch } from "~/components/Switch";
import { Text, useColor, View } from "~/components/Themed";
import { H5 } from "~/components/typography/H5";
import { FuseLogo } from "~/components/icons/FuseLogo";
import { S2 } from "~/components/typography/S2";
import { Button } from "~/components/Button";
import { hexToRgba } from "~/constants/Colors";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useToast } from "~/components/Toaster";
import { useAppState } from "~/state/appState";
import { SettingRow } from "~/components/settings/SettingRow";
import { hapticSelect } from "~/utils/haptics";
import { reportError } from "~/utils/errors";
import { queryClient } from "~/state/queryClient";

export function NotificationsRow() {
  const modalRef = useRef<BottomModalImperativeMethods>(null);

  return (
    <>
      <SettingRow
        iconName="bell"
        label="Notifications"
        onPress={async () => {
          hapticSelect();
          modalRef.current?.present();
        }}
      />
      <NotificationsSettingsModal modalRef={modalRef} />
    </>
  );
}

export function NotificationsSettingsModal({
  modalRef,
}: {
  modalRef: React.RefObject<BottomModalImperativeMethods | null>;
}) {
  const { wallet } = useActiveWallet();

  const { lastTransition } = useAppState();
  const { data: permissions } = useQuery({
    queryKey: ["notifications-permissions"],
    queryFn: async () => {
      return await Notifications.getPermissionsAsync();
    },
    refetchOnMount: () => lastTransition === "background-to-active",
  });

  const initialValue =
    wallet.pushNotificationsEnabled &&
    wallet.pushNotificationsSetUp &&
    (permissions?.granted || false);

  const [switchValue, setSwitchValue] = useState<boolean>(initialValue);

  function onDismiss() {
    setSwitchValue(initialValue);
  }

  const { toast } = useToast();

  const saveMutation = useMutation({
    mutationFn: async (shouldEnable: boolean) => {
      if (!shouldEnable) {
        console.debug("Disabling notifications");
        if (wallet.pushNotificationsSetUp) {
          console.debug("Updating enabled notifications setting");
          await updatePushNotificationsSettings({
            walletKey: wallet.walletKey,
            enabled: shouldEnable,
          });
        }
        return;
      }

      console.debug("Enabling notifications");
      if (!wallet.pushNotificationsSetUp) {
        console.debug("Setting up push notifications token");
        await registerForPushNotifications({ walletKey: wallet.walletKey });
        return;
      }

      if (permissions?.status !== "granted") {
        console.debug("Notifications permissions not granted");
        if (!permissions?.canAskAgain) {
          await Linking.openURL("app-settings:");
          return;
        }

        const { status } = await Notifications.requestPermissionsAsync();
        if (status !== "granted") {
          return;
        }
      }

      console.debug("Updating notification settings to enabled");
      await updatePushNotificationsSettings({
        walletKey: wallet.walletKey,
        enabled: shouldEnable,
      });
    },
    onError: (e) => {
      toast.error("Failed to update notifications settings");
      setSwitchValue(initialValue);
      reportError(queryClient, e);
    },
  });

  return (
    <BottomModal
      modalRef={modalRef}
      modalId={"notifications-settings"}
      title={"Notifications"}
      iconName="bell.fill"
      onDismiss={onDismiss}
      body={
        <View gap={24}>
          <H5 colorToken={"textSecondary"}>
            Get notified about receiving assets, product updates and more
          </H5>
          <NotificationExample />
          <Row justify={"space-between"}>
            <H4>Allow notifications</H4>
            <Switch
              disabled={permissions?.canAskAgain === false}
              onValueChange={setSwitchValue}
              value={switchValue}
            />
          </Row>
        </View>
      }
      footer={() => {
        if (permissions?.canAskAgain === false) {
          return (
            <Button
              variant="secondary"
              onPress={async () => {
                await Linking.openURL("app-settings:");
                Haptics.selectionAsync();
              }}
            >
              Open App Settings
            </Button>
          );
        }

        return (
          <Button
            variant="primary"
            loading={saveMutation.isPending}
            onPress={async () => {
              Haptics.selectionAsync();
              if (initialValue !== switchValue) {
                await saveMutation.mutateAsync(switchValue);
                Haptics.notificationAsync(
                  Haptics.NotificationFeedbackType.Success
                );
              }
              modalRef.current?.close();
            }}
          >
            Save
          </Button>
        );
      }}
    />
  );
}

function NotificationExample() {
  const backgroundColor = useColor("backgroundSecondary");

  return (
    <View>
      <Row style={{ position: "absolute", bottom: -8 }}>
        <View
          style={{
            flex: 1,
            marginHorizontal: 8,
            height: 24,
            borderRadius: 16,
            borderCurve: "continuous",
            backgroundColor: hexToRgba(backgroundColor, 0.5),
          }}
        />
      </Row>
      <Row
        gap={12}
        background={"backgroundSecondary"}
        style={{
          borderRadius: 16,
          borderCurve: "continuous",
          paddingHorizontal: 16,
          paddingVertical: 12,
        }}
      >
        <View
          style={{ backgroundColor: "white", borderRadius: 12, padding: 12 }}
        >
          <FuseLogo size={20} />
        </View>
        <Flex gap={4}>
          <Text variant="semibold" style={{ fontSize: 13 }}>
            Received 100.00 USDC
          </Text>
          <Text variant="medium" style={{ fontSize: 13 }}>
            From es6J...9QaP
          </Text>
        </Flex>
        <View style={{ alignSelf: "flex-start" }}>
          <S2 colorToken={"textSecondary"}>now</S2>
        </View>
      </Row>
    </View>
  );
}
