import { RefObject, useEffect, useId, useMemo, useRef, useState } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { FontFamilyByVariant, Text, useColor, View } from "~/components/Themed";
import { FuseLogo } from "~/components/icons/FuseLogo";
import { IconWrapper } from "~/components/IconWrapper";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import { Button } from "~/components/Button";
import { TextInput } from "react-native";
import * as Haptics from "expo-haptics";
import { isOffline, showOfflineWarning } from "~/components/OnlineIndicator";
import { SettingRow } from "~/components/settings/SettingRow";
import { useActiveWallet, useUpdateVaultName } from "~/state/wallet";
import { useToast } from "~/components/Toaster";
import { useFormik } from "formik";
import { z } from "zod";
import { toFormikValidationSchema } from "zod-formik-adapter";
import { FuseWalletIcon } from "~/components/icons/FuseWalletIcon";

export function EditWalletRow() {
  const editWalletModalRef = useRef<BottomModalImperativeMethods>(null);

  const { toast } = useToast();

  return (
    <>
      <SettingRow
        iconName="wallet"
        label="Edit wallet"
        onPress={async () => {
          if (await isOffline()) {
            showOfflineWarning(toast);
            return;
          }

          Haptics.selectionAsync();
          editWalletModalRef.current?.present();
        }}
      />

      <EditWalletModal modalRef={editWalletModalRef} />
    </>
  );
}

function EditWalletModal({
  modalRef,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  const id = useId();

  const { toast } = useToast();
  const { wallet } = useActiveWallet();

  const walletName = wallet.vaults.find((v) => v.index === 0)?.name ?? "Wallet";

  const textColor = useColor("text");
  const logoColor = useColor("textSecondary");

  const updateVaultNameMutation = useUpdateVaultName();

  const FormValues = useMemo(() => {
    return z.object({
      walletName: z
        .string()
        .min(1)
        .max(16)
        .trim()
        .refine((val) => {
          return val !== walletName;
        }),
    });
  }, [walletName]);

  const formik = useFormik<z.infer<typeof FormValues>>({
    validationSchema: toFormikValidationSchema(FormValues),

    initialValues: {
      walletName: walletName,
    },

    async onSubmit(values) {
      modalRef.current?.close(() => {
        updateVaultNameMutation.mutate(
          {
            walletKey: wallet.walletKey,
            vaultKey: wallet.defaultVault,
            vaultName: values.walletName,
          },
          {
            onError: (e) => {
              console.error(e);
              toast.error("Failed to rename wallet");
            },
          }
        );
      });
    },
  });

  // Validate form on walletName change
  useEffect(() => {
    formik.validateForm();
  }, [formik.validateForm, walletName]);

  return (
    <BottomModal
      modalRef={modalRef}
      modalId={id}
      title=""
      body={
        <View style={{ alignItems: "center", gap: 32 }}>
          <FuseWalletIcon size="xlarge" />
          <View style={{ alignItems: "center", gap: 10 }}>
            <TextInput
              autoFocus
              defaultValue={walletName}
              onChangeText={(text) => {
                formik.setFieldValue("walletName", text);
              }}
              selectionColor={textColor}
              style={{
                fontSize: 26,
                fontFamily: FontFamilyByVariant["semibold"],
              }}
            />
            <Text
              colorToken={"textSecondary"}
              variant="medium"
              style={{ fontSize: 18 }}
            >
              {abbreviateAddress(wallet.defaultVault, 4)}
            </Text>
          </View>
        </View>
      }
      footer={
        <Button
          disabled={!formik.isValid || updateVaultNameMutation.isPending}
          variant="primary"
          onPress={() => {
            formik.handleSubmit();
          }}
        >
          Save
        </Button>
      }
      // onDismiss={onDismiss}
    />
  );
}
