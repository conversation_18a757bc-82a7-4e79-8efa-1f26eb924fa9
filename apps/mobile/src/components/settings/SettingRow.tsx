import { SFSymbol } from "expo-symbols";
import { Text, TextColor, View } from "~/components/Themed";
import { WalletIcon } from "~/components/icons/WalletIcon";
import { TwitterIcon } from "~/components/icons/TwitterIcon";
import { Icon } from "~/components/Icon";
import { Flex, Row } from "~/components/Grid";
import { ReactNode } from "react";
import { BoltIcon } from "~/components/icons/BoltIcon";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";

export function SettingRow({
  iconName,
  label,
  color,
  value,
  onPress,
}: {
  iconName?: SFSymbol | "wallet" | "twitter" | "custom.bolt";
  label: ReactNode;
  value?: ReactNode;
  color?: TextColor;
  onPress: () => void;
}) {
  const icon = iconName ? (
    iconName === "wallet" ? (
      <View
        style={{
          width: 34,
          height: 34,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <WalletIcon />
      </View>
    ) : iconName === "twitter" ? (
      <View
        style={{
          width: 34,
          height: 34,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <TwitterIcon />
      </View>
    ) : iconName === "custom.bolt" ? (
      <View
        style={{
          width: 34,
          height: 34,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <BoltIcon colorToken="textSecondary" size={20} />
      </View>
    ) : (
      <Icon
        name={iconName}
        size={13}
        colorToken={color ?? "textSecondary"}
        weight="semibold"
        rectSize={34}
      />
    )
  ) : null;

  return (
    <AnimatedTouchableScale pressedScale={0.98} onPress={onPress}>
      <Row
        style={{
          height: 34,
          //aligning icons with Header
          marginLeft: -6,
        }}
        gap={24}
        justify="space-between"
      >
        <Row gap={8}>
          {icon}
          {typeof label === "string" ? (
            <Text colorToken={color} variant="medium" style={{ fontSize: 16 }}>
              {label}
            </Text>
          ) : (
            label
          )}
        </Row>
        <Row gap={10}>
          {typeof value === "string" ? (
            <Text
              variant="medium"
              colorToken="textSecondary"
              style={{ fontSize: 14 }}
            >
              {value}
            </Text>
          ) : (
            value
          )}
          <Icon
            name="chevron.right"
            size={8}
            colorToken="textSecondary"
            weight="heavy"
            rectSize={24}
          />
        </Row>
      </Row>
    </AnimatedTouchableScale>
  );
}

export function SectionHeading({ children }: { children: string }) {
  return (
    <Text
      variant="semibold"
      style={{ fontSize: 15, marginBottom: 16 }}
      colorToken="textSecondary"
    >
      {children}
    </Text>
  );
}

export function Section({ children }: { children: ReactNode }) {
  return (
    <Flex
      gap={10}
      style={{
        marginBottom: 24,
      }}
    >
      {children}
    </Flex>
  );
}
