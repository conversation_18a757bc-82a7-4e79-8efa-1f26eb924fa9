import { useSubscription } from "~/state/subscription";
import { Text, useColor, View } from "~/components/Themed";
import { FusePlusLogo } from "~/components/icons/FusePlusLogo";
import { IconWrapper } from "~/components/IconWrapper";
import { Row } from "~/components/Grid";
import { Icon } from "~/components/Icon";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import { Tag } from "~/components/Tag";
import { hexToRgba } from "~/constants/Colors";
import { router } from "expo-router";
import { hapticOpenModalSheet, hapticSelect } from "~/utils/haptics";
import { Image } from "expo-image";
import circleBlur from "~assets/images/circle-blur.png";

export function FusePlusMembershipBanner() {
  const subscriptionQuery = useSubscription();

  const subscription = subscriptionQuery.data;
  if (!subscription) {
    return null;
  }

  if (subscription.status === "none") {
    return (
      <AnimatedTouchableScale
        pressedScale={0.99}
        onPress={() => {
          hapticOpenModalSheet();
          router.push("/unlocked/subscription/promo");
        }}
      >
        <View
          background={"backgroundCard"}
          style={{
            marginHorizontal: -16,
            paddingHorizontal: 16,
            borderRadius: 16,
            borderCurve: "continuous",
            overflow: "hidden",
          }}
        >
          <Row justify="space-between" style={{ paddingVertical: 12 }}>
            <Row gap={12}>
              <View
                style={{
                  aspectRatio: 1,
                  width: 24,
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <FusePlusLogo size={12} />
              </View>

              {/*<FusePlusIcon size={24} />*/}
              <View gap={2}>
                <Text colorToken={"textOpposite"} variant={"medium"} size={14}>
                  Get Fuse Plus
                </Text>
                <Text
                  colorToken={"textSecondaryOpposite"}
                  variant={"medium"}
                  size={14}
                >
                  Earn more, pay less
                </Text>
              </View>
            </Row>
            <IconWrapper
              size={24}
              backgroundColorToken={"backgroundCardSecondary"}
            >
              <Icon
                name="chevron.right"
                size={8}
                colorToken="textOpposite"
                weight="bold"
              />
            </IconWrapper>
          </Row>

          {/* Bottom background gradient */}
          <View
            style={{
              position: "absolute",
              left: -16,
              width: 100,
              height: "100%",
              flexDirection: "row",
              overflow: "hidden",
            }}
          >
            <Image
              source={circleBlur}
              style={{
                position: "absolute",
                right: 0,
                aspectRatio: 1,
                width: 260,
                opacity: 0.2,
                alignSelf: "center",
              }}
            ></Image>
          </View>
        </View>
      </AnimatedTouchableScale>
    );
  }

  return (
    <AnimatedTouchableScale
      pressedScale={0.99}
      onPress={() => {
        hapticSelect();
        router.push("/unlocked/settings/fuse-plus");
      }}
    >
      <Row justify={"space-between"}>
        <Row gap={12}>
          <IconWrapper
            size={24}
            background={"black"}
            variant={"square"}
            style={{ borderRadius: 8 }}
          >
            <FusePlusLogo size={11} />
          </IconWrapper>
          <View>
            <Text variant={"medium"} size={16}>
              Fuse Plus
            </Text>
            <Text variant={"medium"} size={14} colorToken={"textSecondary"}>
              Member
            </Text>
          </View>
        </Row>
        <ActiveTag />
      </Row>
    </AnimatedTouchableScale>
  );
}

function ActiveTag() {
  const green = useColor("green");

  return (
    <Tag style={{ backgroundColor: hexToRgba(green, 0.1) }}>
      <Text size={12} variant={"medium"} style={{ color: green }}>
        Active
      </Text>
    </Tag>
  );
}
