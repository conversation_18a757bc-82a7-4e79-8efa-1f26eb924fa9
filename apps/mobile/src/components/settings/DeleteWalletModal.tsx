import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { Flex, Row } from "~/components/Grid";
import { Button } from "~/components/Button";
import { useMutation } from "@tanstack/react-query";
import { PropsWithChildren, RefObject, useEffect, useState } from "react";
import { H5 } from "~/components/typography/H5";
import { Checkbox } from "~/components/Checkbox";
import { P3 } from "~/components/typography/P3";
import { deactivateWallet } from "~/services/wallets";
import { useActiveWallet } from "~/state/wallet";
import { useUsdBalance } from "~/state/balances";
import { formatUsdValue } from "@squads/utils/numberFormats";
import { toast } from "~/components/Toaster";
import * as Haptics from "expo-haptics";
import Animated, {
  FadeInDown,
  FadeOutUp,
  interpolateColor,
  LayoutAnimationConfig,
  ReduceMotion,
  useAnimatedProps,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";
import { Icon } from "~/components/Icon";
import { IconWrapper } from "~/components/IconWrapper";
import Svg, { G, Path, Rect } from "react-native-svg";
import * as LocalAuthentication from "expo-local-authentication";
import { replaceAll } from "~/utils/router";

type Screen = "info" | "balance-check" | "confirm";

export function DeleteWalletModal({
  modalRef,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  const { wallet } = useActiveWallet();
  const usdcBalance = useUsdBalance({ address: wallet.defaultVault });

  const [screen, setScreen] = useState<Screen>("info");

  const initialState = {
    loseAccess: false,
    loseAssets: false,
    notLiable: false,
    noAccess: false,
    agreeTerms: false,
  };
  const [agreements, setAgreements] = useState(initialState);

  const toggleAgreement = (
    agreement: keyof typeof agreements,
    value: boolean
  ) => {
    Haptics.selectionAsync();
    setAgreements({ ...agreements, [agreement]: value });
  };

  const termsAgreed = Object.values(agreements).every((value) => value);

  function onDismiss() {
    setAgreements(initialState);
    setScreen("info");
  }

  const deleteWalletMutation = useMutation({
    mutationFn: async () => {
      const result = await LocalAuthentication.authenticateAsync({
        disableDeviceFallback: true,
      });
      if (!result.success) {
        toast.error("Face ID check failed. Please try again.", {
          id: "face_id_failed",
          opaque: true,
        });
        throw new Error(`Authentication failed :${result.error}`);
      }

      const { deactivationId } = await deactivateWallet();
      console.debug("deactivated wallet:", deactivationId);
      modalRef.current?.close();
      replaceAll(`/locked/invalidate-wallet?deactivationId=${deactivationId}`);
    },
  });

  return (
    <BottomModal
      modalId="delete-wallet"
      modalRef={modalRef}
      title="Delete Wallet"
      iconVariant="danger"
      onDismiss={() => onDismiss()}
      body={() => {
        return (
          <View gap={20}>
            <WalletView
              variant={
                screen === "info"
                  ? "normal"
                  : screen === "balance-check" || screen === "confirm"
                    ? "danger"
                    : "plain"
              }
            >
              <LayoutAnimationConfig skipEntering skipExiting>
                <Animated.View
                  key={screen}
                  entering={FadeInDown.duration(150).reduceMotion(
                    ReduceMotion.Never
                  )}
                  exiting={FadeOutUp.duration(150).reduceMotion(
                    ReduceMotion.Never
                  )}
                >
                  {screen === "info" && (
                    <View style={{ alignItems: "center", paddingTop: 24 }}>
                      <IconWrapper size={42} backgroundColorToken="red">
                        <Icon
                          name="trash.fill"
                          colorToken={"backgroundSecondary"}
                          size={17}
                        />
                      </IconWrapper>
                    </View>
                  )}
                  {screen === "confirm" && (
                    <View style={{ alignItems: "center", paddingTop: 24 }}>
                      <IconWrapper size={42} background={"#e75555"}>
                        <Icon name="trash.fill" colorToken={"red"} size={17} />
                      </IconWrapper>
                    </View>
                  )}
                  {screen === "balance-check" && (
                    <View style={{ alignItems: "center", paddingTop: 12 }}>
                      <WalletBalance amount={usdcBalance} />
                    </View>
                  )}
                </Animated.View>
              </LayoutAnimationConfig>
            </WalletView>

            {screen === "info" && (
              <H5 colorToken="textSecondary">
                Deleting your wallet will permanently remove it from the device.
                If you continue, you will not be able to recover, access or
                perform any other action with this wallet in Fuse. Any assets
                left in this wallet will be lost.{"\n\n"}By continuing, you
                confirm that you have sent out all assets before deleting the
                wallet and any assets left in the wallet after deletion will be
                inaccessible.
              </H5>
            )}

            {screen === "balance-check" && (
              <View gap={20}>
                <Row gap={12} style={{ flex: 1 }}>
                  <View
                    background="listSeparator"
                    style={{ height: 1, flex: 1 }}
                  />
                  <Icon
                    colorToken={"red"}
                    name="exclamationmark.triangle.fill"
                    size={20}
                  />
                  <View
                    background="listSeparator"
                    style={{ height: 1, flex: 1 }}
                  />
                </Row>

                {usdcBalance > 10 ? (
                  <H5 colorToken="textSecondary">
                    In order to continue with wallet deletion, send out all of
                    your assets and return to this step. You will not be able to
                    access the funds after deleting your wallet.
                  </H5>
                ) : (
                  <H5 colorToken="textSecondary">
                    Make sure to send all of your remaining assets before
                    deleting the wallet. You will not be able to access the
                    funds again.
                  </H5>
                )}
              </View>
            )}

            {screen === "confirm" && (
              <View gap={12}>
                <Checkbox
                  value={agreements.loseAccess}
                  onChange={(value) => toggleAgreement("loseAccess", value)}
                >
                  <P3
                    colorToken={
                      agreements.loseAccess ? "text" : "textSecondary"
                    }
                  >
                    I understand that by confirming this action, I will lose
                    access to my wallet
                  </P3>
                </Checkbox>
                <Checkbox
                  value={agreements.loseAssets}
                  onChange={(value) => toggleAgreement("loseAssets", value)}
                >
                  <P3
                    colorToken={
                      agreements.loseAssets ? "text" : "textSecondary"
                    }
                  >
                    I understand that any assets I have in the wallet now will
                    be lost and inaccessible after deletion of the wallet
                  </P3>
                </Checkbox>
                <Checkbox
                  value={agreements.notLiable}
                  onChange={(value) => toggleAgreement("notLiable", value)}
                >
                  <P3
                    colorToken={agreements.notLiable ? "text" : "textSecondary"}
                  >
                    I understand Fuse team is not liable for any assets lost in
                    the result of deleting my wallet
                  </P3>
                </Checkbox>
                <Checkbox
                  value={agreements.noAccess}
                  onChange={(value) => toggleAgreement("noAccess", value)}
                >
                  <P3
                    colorToken={agreements.noAccess ? "text" : "textSecondary"}
                  >
                    I understand Fuse has no access to my wallet, funds or any
                    other way to restore my wallet after I complete this step
                  </P3>
                </Checkbox>
                <Checkbox
                  value={agreements.agreeTerms}
                  onChange={(value) => toggleAgreement("agreeTerms", value)}
                >
                  <P3
                    colorToken={
                      agreements.agreeTerms ? "text" : "textSecondary"
                    }
                  >
                    I agree to the terms by checking the boxes
                  </P3>
                </Checkbox>
              </View>
            )}
          </View>
        );
      }}
      footer={() => {
        return (
          <Row gap={12}>
            <Flex>
              <Button
                variant="secondary"
                onPress={() => {
                  Haptics.selectionAsync();
                  if (screen === "info") {
                    modalRef.current?.close();
                    return;
                  }

                  if (screen === "balance-check") {
                    setScreen("info");
                    return;
                  }

                  if (screen === "confirm") {
                    if (usdcBalance > 0) {
                      setScreen("balance-check");
                    } else {
                      setScreen("info");
                    }
                    return;
                  }
                }}
                animation={"left-right"}
              >
                {screen === "info" ? "Cancel" : "Back"}
              </Button>
            </Flex>
            <Flex>
              <Button
                iconName={screen === "confirm" ? "faceid" : undefined}
                iconWeight="medium"
                iconSize={14}
                variant={screen === "confirm" ? "danger" : "primary"}
                disabled={
                  (screen === "balance-check" && usdcBalance > 10) ||
                  (screen === "confirm" && !termsAgreed)
                }
                loading={deleteWalletMutation.isPending}
                onPress={async () => {
                  Haptics.selectionAsync();

                  if (screen === "info") {
                    if (usdcBalance > 0) {
                      setScreen("balance-check");
                    } else {
                      setScreen("confirm");
                    }
                    return;
                  }

                  if (screen === "balance-check") {
                    setScreen("confirm");
                    return;
                  }

                  if (screen === "confirm") {
                    if (termsAgreed) {
                      deleteWalletMutation.mutate();
                    }
                    return;
                  }
                }}
              >
                {screen === "confirm" ? "Confirm" : "Continue"}
              </Button>
            </Flex>
          </Row>
        );
      }}
    />
  );
}

function WalletBalance({ amount }: { amount: number }) {
  const formattedBalance = formatUsdValue(amount, {
    renderCurrency: false,
    minimumFractionDigits: 2,
  });

  return (
    <View gap={6} style={{ alignItems: "center" }}>
      <P3 style={{ opacity: 0.6 }} colorToken="textButtonPrimary">
        Your wallet balance
      </P3>
      <Text
        colorToken={"textButtonPrimary"}
        variant={"medium"}
        style={{ fontSize: 40 }}
      >
        ${formattedBalance}
      </Text>
    </View>
  );
}

const AnimatedRect = Animated.createAnimatedComponent(Rect);
const AnimatedPath = Animated.createAnimatedComponent(Path);

export function WalletView({
  variant,
  children,
}: PropsWithChildren<{
  variant: "normal" | "danger" | "plain";
}>) {
  const color = useSharedValue(0);

  useEffect(() => {
    color.value = withTiming(variant === "danger" ? 1 : 0, {
      duration: 150,
    });
  }, [variant]);

  const animatedProps = useAnimatedProps(() => {
    const fill = interpolateColor(color.value, [0, 1], ["#e2e2e2", "#fe7273"]);
    return { fill };
  });

  const secondaryAnimatedProps = useAnimatedProps(() => {
    const fill = interpolateColor(color.value, [0, 1], ["#ccc", "#e75555"]);
    return { fill };
  });

  return (
    <View>
      <Svg width={"100%"} height={182} viewBox="0 0 312 182" fill="none">
        <AnimatedRect
          width={312}
          height={181.554}
          rx={20}
          animatedProps={animatedProps}
        />
        {variant !== "plain" && (
          <>
            <G>
              <Path
                d="M12 18.554h288v142c0 5.523-4.477 10-10 10H22c-5.523 0-10-4.477-10-10v-142z"
                stroke={variant === "normal" ? "#ccc" : "#e75555"}
                strokeOpacity={0.4}
                strokeWidth={1.5}
                strokeLinecap="round"
                strokeDasharray="6 6"
              />
            </G>
            <G>
              <AnimatedPath
                d="M11 20c0-5.523 4.477-10 10-10h270c5.523 0 10 4.477 10 10v12.75a4 4 0 01-4 4h-90.697c-.815 0-1.627.09-2.423.264l-39.281 8.647a39.996 39.996 0 01-17.198 0l-39.281-8.647a11.27 11.27 0 00-2.423-.263H15a4 4 0 01-4-4V20z"
                animatedProps={secondaryAnimatedProps}
              />
            </G>
            <G>
              <AnimatedPath
                animatedProps={secondaryAnimatedProps}
                d="M289.296 160.98c1.494 0 2.704-1.226 2.704-2.739s-1.21-2.739-2.704-2.739c-1.493 0-2.703 1.226-2.703 2.739s1.21 2.739 2.703 2.739zM289.267 144.477a2.688 2.688 0 00-1.813.702c-3.954 2.962-6.777.507-7.403-.131a12.33 12.33 0 00-.131-.143 3.149 3.149 0 00-3.442-.651 3.182 3.182 0 00-1.406 1.184 3.242 3.242 0 00-.527 1.774c.005.437.094.868.262 1.269.204.508.685.983.944 1.385.485.742.745 1.4.745 2.342a4.757 4.757 0 01-1.272 3.209 54.23 54.23 0 01-.473.548 3.809 3.809 0 00-.361 3.948 3.746 3.746 0 001.37 1.523 3.681 3.681 0 003.913 0 3.748 3.748 0 001.371-1.523 3.809 3.809 0 00-.361-3.948l-.336-.388a4.823 4.823 0 01-.436-6.348 4.596 4.596 0 01.554-.605 4.678 4.678 0 013.321-1.241 4.679 4.679 0 013.235 1.454c.087.093.352.379.394.415.614.539 1.12.768 1.849.768.726 0 1.422-.292 1.935-.812a2.788 2.788 0 00.801-1.96c0-.735-.288-1.44-.801-1.96a2.72 2.72 0 00-1.935-.811h.003z"
              />
            </G>
          </>
        )}
      </Svg>
      <View
        style={{
          height: "100%",
          width: "100%",
          position: "absolute",
          paddingTop: variant === "plain" ? 8 : 50,
          paddingHorizontal: 12,
        }}
      >
        {children}
      </View>
    </View>
  );
}
