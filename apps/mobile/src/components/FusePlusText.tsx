import { Row } from "~/components/Grid";
import { Text, TextColor, View } from "~/components/Themed";
import { Icon } from "~/components/Icon";
import { PlusBlur } from "~/components/icons/PlusBlur";

export function FusePlusText({
  size,
  colorToken,
  prefix,
}: {
  size?: number;
  colorToken?: TextColor;
  prefix?: string;
}) {
  return (
    <Row>
      <Text variant="medium" colorToken={colorToken} size={size}>
        {prefix}Fuse
      </Text>
      <Icon
        name={"plus"}
        size={7}
        colorToken={colorToken}
        weight={"heavy"}
        style={{ top: -5.5, left: -2 }}
      />
      <View style={{ top: -2, right: 0, position: "absolute" }}>
        <PlusBlur size={11} />
      </View>
    </Row>
  );
}
