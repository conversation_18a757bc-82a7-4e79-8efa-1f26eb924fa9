import { NativeStackHeaderProps } from "@react-navigation/native-stack";
import { Text, View } from "~/components/Themed";
import { DragIndicator } from "~/components/DragIndicator";
import { TouchableScale } from "~/components/TouchableScale";
import { router } from "expo-router";
import { Icon } from "~/components/Icon";
import { StyleSheet } from "react-native";
import { hapticGoBack } from "~/utils/haptics";

export function ModalHeader(props: NativeStackHeaderProps) {
  const isInitialRoute = props.navigation.getState().index === 0;

  return (
    <View background="background">
      <DragIndicator />
      <View style={headerStyles.header}>
        <View style={headerStyles.left}>
          {isInitialRoute ? (
            <View style={{ width: 32, height: 32 }} />
          ) : (
            <TouchableScale
              hitSlop={10}
              onPress={() => {
                hapticGoBack();
                router.back();
              }}
              style={{
                marginLeft: -8,
                alignItems: "flex-start",
                justifyContent: "center",
              }}
            >
              <Icon
                name={"chevron.left"}
                size={12}
                rectSize={32}
                weight="semibold"
              />
            </TouchableScale>
          )}
        </View>
        <View style={headerStyles.titleWrapper}>
          <Text variant="semibold" size={24}>
            {props.options.title}
          </Text>
        </View>
        <View style={headerStyles.right}></View>
      </View>
    </View>
  );
}

const headerStyles = StyleSheet.create({
  header: {
    flexDirection: "row",
    alignItems: "flex-end",
    height: 60,
    paddingHorizontal: 20,
    paddingBottom: 12,
    justifyContent: "space-between",
  },
  left: {
    width: 32,
  },
  titleWrapper: {
    alignItems: "center",
  },
  right: {
    width: 32,
    height: 32,
    alignItems: "flex-end",
    justifyContent: "center",
  },
});
