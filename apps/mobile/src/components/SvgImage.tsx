import { Image, ImageProps } from "expo-image";
import { debugColor, DebugProps } from "~/types/debug";

type SvgImageProps = ImageProps &
  (
    | { size: number; width?: number; height?: number }
    | { size?: never; width: number; height: number }
  ) &
  DebugProps;

export function SvgImage({
  style,
  size,
  width,
  height,
  debug,
  ...restProps
}: SvgImageProps) {
  return (
    <Image
      style={[
        { width: width ?? size, height: height ?? size },
        style,
        debug && { backgroundColor: debugColor },
      ]}
      contentFit="contain"
      {...restProps}
    />
  );
}
