import { Wallet } from "~/services/wallets";
import { Text, View } from "~/components/Themed";
import { Balance<PERSON>hart, useSelectedPoint } from "~/components/BalanceChart";
import { DateTime } from "luxon";
import { Flex, Row } from "~/components/Grid";
import { Balance } from "~/components/Balance";
import { SwapButton } from "~/components/SwapButton";
import { SendButton } from "~/components/SendButton";
import { HOME_TAB_BAR_HEIGHT } from "~/constants/sizes";
import { BottomInset } from "~/components/BottomInset";
import { FuseSuspense } from "~/components/FuseSuspense";
import { DepositButton } from "~/components/DepositButton";
import Animated, {
  FadeInLeft,
  FadeInRight,
  FadeOutLeft,
  FadeOutRight,
  LayoutAnimationConfig,
  ReduceMotion,
} from "react-native-reanimated";
import { DURATION_FAST } from "~/constants/animations";
import { ActionCards } from "~/components/home/<USER>";
import { BalanceChangeSkeleton, DailyChange } from "./DailyChange";
import { CardBanner } from "~/components/home/<USER>";

export function Dashboard({ wallet }: { wallet: Wallet }) {
  return (
    <Flex gap={20}>
      <Flex gap={16} style={{ justifyContent: "space-between" }}>
        <View style={{ gap: 20, paddingHorizontal: 20 }}>
          <BalanceView wallet={wallet} />

          <Row gap={6}>
            <DepositButton />
            <SwapButton vault={wallet.defaultVault} />
            <SendButton />
          </Row>
        </View>

        <BalanceChart vault={wallet.defaultVault} />
      </Flex>

      <View gap={16}>
        <ActionCards />
        <CardBanner />
      </View>

      <BottomInset offset={HOME_TAB_BAR_HEIGHT} />
    </Flex>
  );
}

const AnimatedText = Animated.createAnimatedComponent(Text);

function BalanceView({ wallet }: { wallet: Wallet }) {
  const selectedPoint = useSelectedPoint();
  const point = selectedPoint.active ? selectedPoint.point : null;

  return (
    <View>
      <LayoutAnimationConfig skipEntering skipExiting>
        <AnimatedText
          key={point?.date ? "point" : "balance"}
          variant="medium"
          colorToken="textSecondary"
          style={{ fontSize: 15 }}
          entering={(point?.date ? FadeInRight : FadeInLeft)
            .duration(DURATION_FAST)
            .reduceMotion(ReduceMotion.Never)}
          exiting={(point?.date ? FadeOutLeft : FadeOutRight)
            .duration(DURATION_FAST)
            .reduceMotion(ReduceMotion.Never)}
        >
          {point?.date
            ? DateTime.fromJSDate(point?.date).toLocaleString(
                DateTime.DATETIME_MED
              )
            : "Balance"}
        </AnimatedText>

        <Row justify="space-between">
          <Balance
            size={"large"}
            address={wallet.defaultVault}
            valueOverride={point?.value ?? null}
          />
          <FuseSuspense fallback={<BalanceChangeSkeleton />}>
            <DailyChange wallet={wallet} />
          </FuseSuspense>
        </Row>
      </LayoutAnimationConfig>
    </View>
  );
}
