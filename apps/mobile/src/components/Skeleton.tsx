import { PropsWithChildren, useEffect } from "react";
import { Text, View } from "~/components/Themed";
import { LinearGradient } from "expo-linear-gradient";
import Animated, {
  Easing,
  LayoutAnimationConfig,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withRepeat,
  withSequence,
  withTiming,
} from "react-native-reanimated";
import MaskedView from "@react-native-masked-view/masked-view";
import { CrossfadeView } from "./animations/CrossfadeView";

const AnimatedLinearGradient = Animated.createAnimatedComponent(LinearGradient);

export type SkeletonVariant = "default" | "small";

export function ContentSkeleton({
  theme = "light",
  children,
  borderRadius = 6,
  speed = 1,
  variant = "default",
}: PropsWithChildren<{
  theme?: "light" | "dark";
  borderRadius?: number;
  speed?: 1 | 2;
  variant?: SkeletonVariant;
}>) {
  const ViewForSize = <View style={{ opacity: 0 }}>{children}</View>;

  const gradientTransformX = useSharedValue(-100);
  const delayBetweenLoops = speed === 1 ? 1000 : 150;

  useEffect(() => {
    gradientTransformX.value = withDelay(
      1000,
      withRepeat(
        withSequence(
          withTiming(100, { duration: 700, easing: Easing.linear }),
          withDelay(delayBetweenLoops, withTiming(100, { duration: 0 }))
        ),
        -1
      )
    );
  }, []);

  const animatedGradientStyle = useAnimatedStyle(() => {
    return {
      left: `${gradientTransformX.value}%`,
    };
  });

  const color = theme === "light" ? "#E8E8E8" : "#444444";
  const alpha = theme === "light" ? 0.3 : 0.15;

  return (
    <View
      style={{ borderRadius, borderCurve: "continuous", overflow: "hidden" }}
    >
      {ViewForSize}
      <View
        style={{
          backgroundColor: color,
          position: "absolute",
          left: 0,
          right: 0,
          top: variant === "small" ? "15%" : 0,
          bottom: variant === "small" ? "15%" : 0,
          borderRadius,
        }}
      >
        <AnimatedLinearGradient
          start={[0, 1]}
          end={[1, 1]}
          colors={[
            "rgba(255,255,255,0)",
            `rgba(255,255,255,${alpha})`,
            "rgba(255,255,255,0)",
          ]}
          style={[
            {
              width: "100%",
              height: "100%",
              position: "absolute",
            },
            animatedGradientStyle,
          ]}
        />
      </View>
    </View>
  );
}

export function TextSkeleton({
  size = 16,
  variant,
}: {
  size?: number;
  variant?: SkeletonVariant;
}) {
  return (
    <ContentSkeleton variant={variant}>
      <Text>{Array.from({ length: size }).map((_, i) => i)}</Text>
    </ContentSkeleton>
  );
}

export function ViewSkeleton({
  children,
  disabled = false,
}: PropsWithChildren<{ disabled?: boolean }>) {
  const ViewForSize = (
    <View style={{ opacity: 0 }} pointerEvents="none">
      {children}
    </View>
  );

  return (
    <View>
      {ViewForSize}
      <View
        style={{ position: "absolute", top: 0, left: 0, right: 0, bottom: 0 }}
      >
        <MaskedView
          style={{ flex: 1 }}
          maskElement={
            <View
              style={{
                backgroundColor: "transparent",
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              {children}
            </View>
          }
        >
          {disabled ? (
            <LayoutAnimationConfig skipExiting>
              <CrossfadeView key="content">{children}</CrossfadeView>
            </LayoutAnimationConfig>
          ) : (
            <LayoutAnimationConfig skipEntering>
              <CrossfadeView key="skeleton">
                <ContentSkeleton>{ViewForSize}</ContentSkeleton>
              </CrossfadeView>
            </LayoutAnimationConfig>
          )}
        </MaskedView>
      </View>
    </View>
  );
}
