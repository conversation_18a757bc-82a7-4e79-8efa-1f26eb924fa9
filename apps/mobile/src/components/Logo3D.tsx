import invariant from "invariant";
import { Suspense } from "react";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import { Canvas, ThreeElement, useFrame } from "@react-three/fiber/native";
import { useGLTF } from "@react-three/drei/native";
import {
  SharedValue,
  useSharedValue,
  withDecay,
} from "react-native-reanimated";
import logoModelUri from "../../assets/3d-models/squads-logo.glb";

function Model({
  url,
  rotationValue,
  ...rest
}: { rotationValue: SharedValue<number> } & Omit<ThreeElement<any>, "object">) {
  const model = useGLTF(url);

  invariant(!Array.isArray(model), "expected single model");

  useFrame(() => (model.scene.rotation.y = rotationValue.value));

  return <primitive {...rest} object={model.scene} />;
}

export default function Logo3D() {
  const rotation = useSharedValue(0);
  let initialRotation = 0;

  const gesture = Gesture.Pan()
    .onBegin(() => {
      initialRotation = rotation.value;
    })
    .onUpdate((e) => {
      rotation.value = initialRotation + e.translationX * 0.01;
    })
    .onEnd((e) => {
      rotation.value = withDecay({
        velocityFactor: 0.01,
        velocity: e.velocityX,
      });
    });

  return (
    <GestureDetector gesture={gesture}>
      <Canvas
        style={{
          borderStyle: "solid",
          borderColor: "black",
          width: "100%",
          borderWidth: 1,
        }}
      >
        <ambientLight />
        <pointLight position={[10, 20, 10]} />
        <Suspense fallback={null}>
          <Model
            url={logoModelUri}
            rotationValue={rotation}
            position={[0, 0, -5]}
          />
        </Suspense>
      </Canvas>
    </GestureDetector>
  );
}
