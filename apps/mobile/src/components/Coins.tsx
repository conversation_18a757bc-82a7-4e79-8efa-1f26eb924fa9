import { Asset, AssetWithDetails } from "~/components/AssetsList";
import { Wallet } from "~/services/wallets";
import { refetchBalances, useSuspenseVerifiedBalances } from "~/state/balances";
import { RefreshControl } from "react-native";
import { Text, useColor } from "~/components/Themed";
import { router } from "expo-router";
import { mints } from "~/constants/tokens";
import * as Haptics from "expo-haptics";
import { memo, useCallback, useMemo, useState } from "react";
import { FuseErrorBoundary } from "~/components/errors/FuseErrorBoundary";
import { ErrorFallbackView } from "~/components/ErrorFallbackView";
import { useQueryClient } from "@tanstack/react-query";
import { FuseSuspense } from "~/components/FuseSuspense";
import Animated, {
  Extrapolation,
  FadeIn,
  FadeOut,
  interpolate,
  LinearTransition,
  ReduceMotion,
  runOnJS,
  SharedValue,
  useAnimatedS<PERSON>roll<PERSON><PERSON><PERSON>,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from "react-native-reanimated";
import { TokenBalance } from "~/services/balances";
import { DURATION_FAST, DURATION_FASTER, SPRING } from "~/constants/animations";
import { timeout } from "~/utils/promise";
import { reportError } from "~/utils/errors";
import { Icon } from "./Icon";
import { AnimatedTouchableScale } from "./AnimatedTouchableScale";
import { Address } from "~/vendor/squads/models/solana";
import { hapticConceal, hapticGoForward, hapticReveal } from "~/utils/haptics";
import { HOME_TAB_BAR_HEIGHT } from "~/constants/sizes";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const LIST_ITEM_LAYOUT_ANIMATION = LinearTransition.springify()
  .mass(1)
  .damping(SPRING.damping)
  .stiffness(SPRING.stiffness);

type CoinsBalances = {
  balances: TokenBalance[];
  isShowingAllBalances: boolean;
  showAllBalances: () => void;
  hideUnverifiedBalances: () => void;
  totalVerifiedBalances: number;
  totalUnverifiedBalances: number;
};

function useCoinsBalances({ address }: { address: Address }): CoinsBalances {
  const { allBalances, verifiedBalances, unverifiedBalances } =
    useSuspenseVerifiedBalances({ address });

  const [isShowingAllBalances, setIsShowingAllBalances] =
    useState<boolean>(false);

  const showAllBalances = useCallback(() => {
    setIsShowingAllBalances(true);
  }, []);
  const hideUnverifiedBalances = useCallback(() => {
    setIsShowingAllBalances(false);
  }, []);

  const balances = useMemo(
    () => (isShowingAllBalances ? allBalances : verifiedBalances),
    [isShowingAllBalances, allBalances, verifiedBalances]
  );

  return {
    balances,
    isShowingAllBalances,
    showAllBalances,
    hideUnverifiedBalances,
    totalVerifiedBalances: verifiedBalances.length,
    totalUnverifiedBalances: unverifiedBalances.length,
  };
}

type ListItem =
  | {
      type: "coin";
      balance: TokenBalance;
    }
  | {
      type: "unverified-coins-separator";
      overdragY: SharedValue<number>;
      isShowingAllBalances: boolean;
    };

export function Coins({ wallet }: { wallet: Wallet }) {
  return (
    <FuseErrorBoundary
      FallbackComponent={({ error, resetErrorBoundary, errorId }) => {
        return (
          <ErrorFallbackView
            error={error}
            errorId={errorId}
            resetErrorBoundary={resetErrorBoundary}
            label={"coins"}
          />
        );
      }}
    >
      <FuseSuspense fallback={null}>
        <CoinsInner wallet={wallet} />
      </FuseSuspense>
    </FuseErrorBoundary>
  );
}

function keyExtractor(item: ListItem) {
  switch (item.type) {
    case "unverified-coins-separator":
      return item.type;

    case "coin":
      return item.balance.mint === null ? "SOL" : item.balance.mint;

    default:
      item satisfies never;
      return "never";
  }
}

function coinItem(balance: TokenBalance): ListItem {
  return { type: "coin", balance };
}

const OVERDRAG_THRESHOLD = 80;
const TOGGLE_TEXT_REVEAL_TRANSLATE_Y = 31;

export function CoinsInner({ wallet }: { wallet: Wallet }) {
  const insets = useSafeAreaInsets();
  const queryClient = useQueryClient();

  const textTertiaryColor = useColor("textTertiary");

  const {
    balances,
    isShowingAllBalances,
    totalVerifiedBalances,
    totalUnverifiedBalances,
    showAllBalances,
    hideUnverifiedBalances,
  } = useCoinsBalances({ address: wallet.defaultVault });

  const handleConcealUnverifiedBalances = useCallback(() => {
    hapticConceal();
    hideUnverifiedBalances();
  }, [hideUnverifiedBalances]);

  const [refreshing, setRefreshing] = useState<boolean>(false);

  const overdragY = useSharedValue(0);
  const isDragging = useSharedValue(false);

  const handleScroll = useAnimatedScrollHandler({
    onScroll: (event) => {
      // only overscroll to reveal if the user is dragging. just momentum scrolling should not reveal.
      if (isDragging.value) {
        const maxOffsetY = Math.max(
          0,
          event.contentSize.height - event.layoutMeasurement.height
        );
        overdragY.value = Math.max(0, event.contentOffset.y - maxOffsetY);
      } else {
        overdragY.value = withSpring(0, SPRING);
      }

      if (
        isDragging.value &&
        !isShowingAllBalances &&
        overdragY.value >= OVERDRAG_THRESHOLD
      ) {
        runOnJS(hapticReveal)();
        runOnJS(showAllBalances)();
      }
    },
    onBeginDrag: () => {
      isDragging.value = true;
    },
    onEndDrag: (event) => {
      isDragging.value = false;
    },
  });

  const verifiedCoins = balances.slice(0, totalVerifiedBalances).map(coinItem);

  const balancesWithSeparator =
    totalUnverifiedBalances === 0
      ? verifiedCoins
      : [
          ...verifiedCoins,
          {
            type: "unverified-coins-separator" as const,
            overdragY,
            isShowingAllBalances,
          },
          ...balances.slice(totalVerifiedBalances).map(coinItem),
        ];

  return (
    <Animated.FlatList
      style={{
        flex: 1,
        marginBottom: insets.bottom + HOME_TAB_BAR_HEIGHT + 4,
        overflow: "visible",
      }}
      onScroll={handleScroll}
      refreshControl={
        <RefreshControl
          tintColor={textTertiaryColor}
          refreshing={refreshing}
          onRefresh={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            setRefreshing(true);

            Promise.race([
              refetchBalances({
                queryClient,
                address: wallet.defaultVault,
              }),
              timeout(10_000, "Refetch balances timeout."),
            ])
              .catch((e) => {
                reportError(queryClient, e, {
                  vaultAddress: wallet.defaultVault,
                });
              })
              .finally(() => {
                setRefreshing(false);
              });
          }}
        />
      }
      showsVerticalScrollIndicator={false}
      data={balancesWithSeparator}
      keyExtractor={keyExtractor}
      itemLayoutAnimation={LIST_ITEM_LAYOUT_ANIMATION}
      renderItem={({ item }: { item: ListItem }) => {
        if (item.type === "coin") {
          return <CoinsListItem assetBalance={item.balance} />;
        }

        return (
          <UnverifiedCoinsSeparator
            overdragY={item.overdragY}
            isShowingAllBalances={item.isShowingAllBalances}
            onConcealUnverifiedBalances={handleConcealUnverifiedBalances}
          />
        );
      }}
    />
  );
}

const CoinsListItem = memo(function CoinsListItem({
  assetBalance,
}: {
  assetBalance: TokenBalance;
}) {
  function handlePress() {
    hapticGoForward();
    router.push(
      `/unlocked/token/${assetBalance.mint === null ? mints.sol : assetBalance.mint}`
    );
  }

  return (
    <Animated.View
      entering={FadeIn.delay(DURATION_FASTER)
        .duration(DURATION_FAST)
        .reduceMotion(ReduceMotion.Never)}
      exiting={FadeOut.duration(DURATION_FASTER)}
    >
      {assetBalance.driftEarn?.total ||
      assetBalance.luloProtectedEarn?.total ||
      assetBalance.kaminoEarn?.total ||
      assetBalance.stakedAmount ? (
        <AssetWithDetails assetBalance={assetBalance} onPress={handlePress} />
      ) : (
        <AnimatedTouchableScale pressedScale={0.99} onPress={handlePress}>
          <Asset
            variant="position"
            token={assetBalance}
            includeStaked
            includeEarn
          />
        </AnimatedTouchableScale>
      )}
    </Animated.View>
  );
});

const UnverifiedCoinsSeparator = memo(function UnverifiedCoinsSeparator({
  overdragY,
  isShowingAllBalances,
  onConcealUnverifiedBalances,
}: {
  overdragY: SharedValue<number>;
  isShowingAllBalances: boolean;
  onConcealUnverifiedBalances: () => void;
}) {
  const dashedListSeparatorColor = useColor("dashedListSeparator");

  const animatedDragStyle = useAnimatedStyle(() => ({
    opacity: interpolate(
      overdragY.value,
      [0, OVERDRAG_THRESHOLD - 10],
      [1, 0],
      Extrapolation.CLAMP
    ),
    transform: [
      {
        scale: interpolate(
          overdragY.value,
          [0, OVERDRAG_THRESHOLD - 10],
          [1, 0.9],
          Extrapolation.CLAMP
        ),
      },
    ],
  }));

  const animatedTextStyle = useAnimatedStyle(() => ({
    opacity: isShowingAllBalances
      ? 1
      : interpolate(
          overdragY.value,
          [0, OVERDRAG_THRESHOLD - 10],
          [0, 1],
          Extrapolation.CLAMP
        ),
    transform: isShowingAllBalances
      ? []
      : [
          {
            translateY: interpolate(
              overdragY.value,
              [0, OVERDRAG_THRESHOLD],
              [TOGGLE_TEXT_REVEAL_TRANSLATE_Y, 0],
              Extrapolation.CLAMP
            ),
          },
        ],
  }));

  return (
    <AnimatedTouchableScale
      disabled={!isShowingAllBalances}
      onPress={onConcealUnverifiedBalances}
      pressedScale={0.95}
    >
      <Animated.View
        style={[
          {
            height: 40,
            alignItems: "center",
            justifyContent: "center",
            position: "relative",
          },
        ]}
      >
        {isShowingAllBalances ? null : (
          <Animated.View
            entering={FadeIn.duration(DURATION_FAST)}
            exiting={FadeOut.duration(DURATION_FASTER)}
          >
            <Animated.View
              style={[
                {
                  width: 36,
                  height: 5,
                  borderRadius: 2.5,
                  backgroundColor: dashedListSeparatorColor,
                },
                animatedDragStyle,
              ]}
            />
          </Animated.View>
        )}

        <Animated.View
          style={[
            {
              gap: 7,
              display: "flex",
              position: "absolute",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
            },
            animatedTextStyle,
          ]}
        >
          <Animated.View
            style={{
              // we need to manually adjust the centering of the label because "Show/Hide"
              // is absolutely positioned and doesn't affect the layout
              left: 18,
              flexDirection: "row",
              alignItems: "center",
              gap: 8,
            }}
          >
            <Animated.View
              key={isShowingAllBalances ? "hide" : "show"}
              entering={FadeIn.duration(DURATION_FAST)}
              exiting={FadeOut.duration(DURATION_FAST)}
              style={{
                position: "absolute",
                left: -40 - 3,
                width: 40,
              }}
            >
              <Text
                size={14}
                variant="medium"
                colorToken="textTertiary"
                style={{
                  textAlign: "right",
                }}
              >
                {isShowingAllBalances ? "Hide" : "Show"}
              </Text>
            </Animated.View>
            <Text size={14} variant="medium" colorToken="textTertiary">
              unverified coins
            </Text>
            <Animated.View
              key={isShowingAllBalances ? "eye-slash" : "eye"}
              entering={FadeIn.duration(DURATION_FAST)}
              exiting={FadeOut.duration(DURATION_FAST)}
            >
              <Icon
                name={isShowingAllBalances ? "eye.slash" : "eye"}
                colorToken={"textTertiary"}
                size={11}
              />
            </Animated.View>
          </Animated.View>
        </Animated.View>
      </Animated.View>
    </AnimatedTouchableScale>
  );
});
