import Animated, {
  Easing,
  Extrapolation,
  FadeInDown,
  FadeOutDown,
  interpolate,
  LayoutAnimationConfig,
  ReduceMotion,
  runOnJS,
  SharedValue,
  useAnimatedProps,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withSpring,
  ZoomIn,
  ZoomOut,
} from "react-native-reanimated";
import {
  ComponentProps,
  ReactComponentElement,
  ReactNode,
  RefObject,
  useCallback,
  useEffect,
  useId,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import {
  Text,
  ThemeOverrideContext,
  useColor,
  View,
} from "~/components/Themed";
import { Icon } from "~/components/Icon";
import { IconWrapper } from "~/components/IconWrapper";
import { Keyboard, LayoutChangeEvent, StyleSheet } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import {
  DURATION_FAST,
  DURATION_FASTER,
  smooth,
  smoothWorklet,
  snappyWorklet,
  ZoomAndFadeIn,
  ZoomAndFadeOut,
} from "~/constants/animations";
import {
  FlatList,
  Gesture,
  GestureDetector,
  TouchableWithoutFeedback,
} from "react-native-gesture-handler";
import { snapPoint } from "~/utils/animations";
import * as Haptics from "expo-haptics";
import { ListSeparator } from "~/components/ListSeparator";
import { SFSymbol } from "expo-symbols";
import { FullScreenView } from "~/components/stacking/FullScreenView";
import { useAnimatedKeyboard } from "~/hooks/useAnimatedKeyboard";
import { hapticDismissBottomTray } from "~/utils/haptics";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import { AnimatedBlurView } from "./AnimatedBlurView";
import { ColorName } from "~/constants/Colors";

const CONTENT_TRANSITION_DURATION = 150;
const CONTENT_EXITING_DURATION = 100;

const HEADER_AND_BODY_ENTERING = ZoomAndFadeIn.withInitialValues({
  transform: [{ scale: 0.97 }],
  opacity: 0,
})
  .duration(CONTENT_TRANSITION_DURATION)
  .reduceMotion(ReduceMotion.Never);

const HEADER_AND_BODY_EXITING = ZoomAndFadeOut.duration(
  CONTENT_EXITING_DURATION
)
  .withEndValues({
    scale: 0.95,
    opacity: 0,
  })
  .reduceMotion(ReduceMotion.Never);

const FOOTER_ENTERING = FadeInDown.duration(
  CONTENT_TRANSITION_DURATION
).reduceMotion(ReduceMotion.Never);

const FOOTER_EXITING = FadeOutDown.duration(
  CONTENT_EXITING_DURATION
).reduceMotion(ReduceMotion.Never);

export type BottomModalImperativeMethods<T = never> = {
  present: (data?: T) => void;
  /** Close the modal with an intent of keeping the state or changes made in it. */
  close: (callback?: () => void) => void;
  /** Close the modal with an intent of discarding the state or changes made in it. */
  dismiss: (callback?: () => void) => void;
};

export function BottomModal<T>({
  modalRef,
  modalId,
  icon,
  iconName,
  iconStyle,
  iconVariant = "default",
  title,
  body,
  footer,
  themeOverride,
  backgroundColor: backgroundColorOverride,
  closeButtonColor: closeButtonColorOverride,
  onDismissButtonPress,
  onClose,
  onDismiss,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
  /** Used to animate between different content of the modal */
  modalId: string;
  icon?: ReactNode;
  iconName?: SFSymbol;
  iconVariant?: "default" | "danger" | "warning";
  iconStyle?: ComponentProps<typeof Icon>["style"];
  title?: string;
  body?: ReactNode | ((data: T) => ReactNode);
  footer?: ReactNode | ((data: T) => ReactNode);
  themeOverride?: "light" | "dark";
  backgroundColor?: ColorName;
  closeButtonColor?: ColorName;
  onDismissButtonPress?: () => void;
  onClose?: () => void;
  /** Called when the modal is dismissed - closed with the close button, swiped away or dismissed via ref.dismiss() */
  onDismiss?: () => void;
}) {
  const isCollapsed = useSharedValue(true);

  const insets = useSafeAreaInsets();

  const keyboard = useAnimatedKeyboard();

  const modalBottom = useDerivedValue(() => {
    return insets.bottom + keyboard.height.value;
  });

  const backgroundColor = useColor(
    backgroundColorOverride ?? "backgroundBottomSheet",
    themeOverride
  );

  const handleDismiss = useCallback(() => {
    if (onDismissButtonPress) {
      onDismissButtonPress();
    } else {
      hapticDismissBottomTray();
      modalRef.current?.dismiss();
    }
  }, [modalRef, onDismissButtonPress]);

  // Progress of mounting of the modal.
  const mount = useSharedValue(0);

  const dataRef = useRef<T>(undefined);
  const [mounted, setMounted] = useState(false);

  const footerElement = mounted
    ? typeof footer === "function"
      ? footer(dataRef.current ?? ({} as T))
      : footer
    : null;

  const portalId = useId();

  const present = useCallback(
    (data?: T) => {
      if (mounted) return;
      dataRef.current = data;
      setMounted(true);
      isCollapsed.value = true;
      mount.value = withSpring(1, smooth(), () => {
        isCollapsed.value = false;
      });
    },
    [mounted, setMounted, mount, isCollapsed]
  );

  const close = useCallback(
    (callback?: () => void) => {
      if (!mounted) return;
      dataRef.current = undefined;
      Keyboard.dismiss();
      isCollapsed.value = false;
      mount.value = withSpring(0, smooth(), () => {
        isCollapsed.value = true;
        runOnJS(setMounted)(false);
        if (callback) runOnJS(callback)();
        if (onClose) runOnJS(onClose)();
      });
    },
    [mounted, onClose, setMounted, mount]
  );

  const dismiss = useCallback(
    (callback?: () => void) => {
      if (!mounted) return;
      Keyboard.dismiss();
      isCollapsed.value = false;
      mount.value = withSpring(0, smooth(), () => {
        isCollapsed.value = true;
        runOnJS(setMounted)(false);
        if (callback) runOnJS(callback)();
        if (onDismiss) runOnJS(onDismiss)();
      });
    },
    [mounted, onDismiss, setMounted, mount]
  );

  useImperativeHandle(modalRef, () => ({
    present,
    close,
    dismiss,
  }));

  const contentHeight = useSharedValue(0);
  const handleContentLayout = useCallback(
    (event: LayoutChangeEvent) => {
      contentHeight.value = event.nativeEvent.layout.height;
    },
    [contentHeight]
  );

  const footerHeight = useSharedValue(0);
  const handleFooterLayout = useCallback(
    (event: LayoutChangeEvent) => {
      footerHeight.value = event.nativeEvent.layout.height;
    },
    [footerHeight]
  );
  // Reset footerHeight when footer disappears
  useEffect(() => {
    if (!footerElement) {
      footerHeight.value = 0;
    }
  }, [footerElement]);

  const modalHeight = useDerivedValue(() => {
    return contentHeight.value + footerHeight.value;
  });

  const panOffsetY = useSharedValue(0);
  const pan = Gesture.Pan()
    .onUpdate((event) => {
      // Move the modal down freely, but apply overdrag of 0.05x when moving up.
      panOffsetY.value = interpolate(
        event.translationY,
        [-2000, 0],
        [-100, 0],
        Extrapolation.IDENTITY
      );
    })
    .onEnd((event) => {
      const destinationPoint = snapPoint(event.translationY, event.velocityY, [
        0,
        modalHeight.value + modalBottom.value,
      ]);

      if (destinationPoint !== 0) {
        isCollapsed.value = false;
        panOffsetY.value = withSpring(
          destinationPoint,
          smoothWorklet({ velocity: event.velocityY }),
          () => {
            isCollapsed.value = true;
            mount.value = 0;
            panOffsetY.value = 0;
            runOnJS(setMounted)(false);
            if (onDismiss) runOnJS(onDismiss)();
          }
        );
      } else {
        panOffsetY.value = withSpring(
          0,
          snappyWorklet({ velocity: event.velocityY })
        );
      }
    });

  const modalOffsetY = useDerivedValue(() => {
    return (
      interpolate(
        mount.value,
        [0, 1],
        [modalHeight.value + modalBottom.value, 0],
        Extrapolation.CLAMP
      ) + panOffsetY.value
    );
  });

  // Progress of the modal appearance/visibility
  const modalAppearance = useDerivedValue(() => {
    return interpolate(
      modalOffsetY.value,
      [0, modalHeight.value + modalBottom.value],
      [1, 0],
      Extrapolation.CLAMP
    );
  });

  const contentWrapperAnimatedStyles = useAnimatedStyle(() => {
    return {
      height: isCollapsed.value
        ? modalHeight.value
        : withSpring(modalHeight.value, snappyWorklet()),
    };
  });

  const comeUpAnim = useAnimatedStyle(() => ({
    bottom: modalBottom.value,
    transform: [{ translateY: modalOffsetY.value }],
  }));

  const hasHeader = Boolean(title || icon || iconName);

  if (!mounted) return null;

  return (
    <FullScreenView key={portalId} zIndex={100}>
      <BottomModalBackdrop
        modalAppearanceProgress={modalAppearance}
        onTap={dismiss}
      />
      <ThemeOverrideContext.Provider value={themeOverride ?? null}>
        <GestureDetector gesture={pan}>
          <LayoutAnimationConfig skipEntering>
            <Animated.View
              style={[
                {
                  position: "absolute",
                  width: "100%",
                  overflow: "hidden",
                },
                comeUpAnim,
              ]}
            >
              <Animated.View
                style={[
                  {
                    backgroundColor,
                    borderRadius: 32,
                    borderCurve: "continuous",
                    marginHorizontal: 20,
                  },
                  contentWrapperAnimatedStyles,
                ]}
              />

              {/* Content */}
              <View
                style={{
                  position: "absolute",
                  top: 0,
                  left: 20,
                  right: 20,
                }}
              >
                <View
                  style={[{ padding: 24, gap: 16 }]}
                  onLayout={handleContentLayout}
                >
                  {/* Header */}
                  {hasHeader && (
                    <Animated.View
                      key={modalId + "header"}
                      entering={HEADER_AND_BODY_ENTERING}
                      exiting={HEADER_AND_BODY_EXITING}
                      style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        gap: 12,
                      }}
                    >
                      <View
                        style={{
                          flex: 1,
                          gap: 16,
                          alignItems: "baseline",
                        }}
                      >
                        {icon ??
                          (iconName && (
                            <IconWrapper
                              size={48}
                              backgroundColorToken={
                                iconVariant === "danger"
                                  ? "backgroundIconDanger"
                                  : iconVariant === "warning"
                                    ? "backgroundIconWarning"
                                    : "backgroundSecondary"
                              }
                            >
                              <Icon
                                name={iconName}
                                weight="semibold"
                                size={15}
                                colorToken={
                                  iconVariant === "danger"
                                    ? "iconDanger"
                                    : iconVariant === "warning"
                                      ? "iconWarning"
                                      : "textSecondary"
                                }
                                style={iconStyle}
                              />
                            </IconWrapper>
                          ))}
                        {title && (
                          <Text
                            variant={"semibold"}
                            style={{
                              flex: 1,
                              fontSize: 20,
                              letterSpacing: -0.4,
                            }}
                          >
                            {title}
                          </Text>
                        )}
                      </View>
                    </Animated.View>
                  )}

                  {/* Body */}
                  <Animated.View
                    key={modalId + "body"}
                    entering={HEADER_AND_BODY_ENTERING}
                    exiting={HEADER_AND_BODY_EXITING}
                  >
                    {typeof body === "function"
                      ? body(dataRef.current ?? ({} as T))
                      : body}
                  </Animated.View>
                </View>
              </View>

              {/* Footer */}
              {!!footerElement && (
                <View
                  style={{
                    position: "absolute",
                    bottom: 0,
                    left: 20,
                    right: 20,
                    borderCurve: "continuous",
                    // Need a bit more radius otherwise there's visible artifact on the bottom radius curve
                    borderBottomLeftRadius: 40,
                    borderBottomRightRadius: 40,
                    backgroundColor,
                  }}
                >
                  <Animated.View
                    onLayout={handleFooterLayout}
                    entering={FOOTER_ENTERING}
                    exiting={FOOTER_EXITING}
                    style={{
                      paddingHorizontal: 24,
                      paddingBottom: 24,
                    }}
                  >
                    {footerElement}
                  </Animated.View>
                </View>
              )}

              {/* Close Button */}
              {title && (
                <AnimatedTouchableScale
                  style={{
                    position: "absolute",
                    top: 16,
                    right: 20 + 16,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  pressedScale={0.95}
                  hitSlop={8}
                  onPress={handleDismiss}
                >
                  <Icon
                    name={"xmark"}
                    weight="bold"
                    size={12}
                    rectSize={32}
                    colorToken={closeButtonColorOverride ?? "textTertiary"}
                  />
                </AnimatedTouchableScale>
              )}
            </Animated.View>
          </LayoutAnimationConfig>
        </GestureDetector>
      </ThemeOverrideContext.Provider>
    </FullScreenView>
  );
}

function BottomModalBackdrop({
  modalAppearanceProgress,
  onTap,
}: {
  modalAppearanceProgress: SharedValue<number>;
  onTap?: () => void;
}) {
  const animatedBlurProps = useAnimatedProps(() => ({
    intensity: interpolate(
      modalAppearanceProgress.value,
      [0, 1],
      [0, 40],
      Extrapolation.CLAMP
    ),
  }));

  const tap = Gesture.Tap().onEnd(() => {
    if (onTap) {
      runOnJS(onTap)();
    }
  });

  return (
    <GestureDetector gesture={tap}>
      <AnimatedBlurView
        style={StyleSheet.absoluteFill}
        animatedProps={animatedBlurProps}
        tint="dark"
      />
    </GestureDetector>
  );
}

export function BottomModalMenuOption({
  label,
  labelView,
  disabled,
  selected,
  onSelect,
}: {
  label?: ReactNode;
  labelView?: ReactNode;
  disabled?: boolean;
  selected?: boolean;
  onSelect?: () => void;
}) {
  return (
    <LayoutAnimationConfig skipEntering skipExiting>
      <TouchableWithoutFeedback
        disabled={disabled}
        onPress={() => {
          Haptics.selectionAsync();
          onSelect?.();
        }}
      >
        <View
          style={{
            height: 50,
            alignItems: "center",
            justifyContent: "space-between",
            flexDirection: "row",
            opacity: disabled ? 0.5 : 1,
          }}
        >
          {label ? (
            <Text variant="medium" style={{ fontSize: 18 }}>
              {label}
            </Text>
          ) : (
            labelView
          )}
          {selected && (
            <Animated.View
              entering={ZoomIn.duration(DURATION_FAST).easing(Easing.ease)}
              exiting={ZoomOut.duration(DURATION_FASTER)}
            >
              <Icon name="checkmark.circle" size={13} rectSize={20} />
            </Animated.View>
          )}
        </View>
      </TouchableWithoutFeedback>
    </LayoutAnimationConfig>
  );
}
