import { LineGraph } from "react-native-graph";
import { GraphPoint } from "react-native-graph/src/LineGraphProps";
import { Text, useColor, View } from "~/components/Themed";
import { DateTime } from "luxon";
import * as Haptics from "expo-haptics";
import { Dispatch, ReactNode, SetStateAction, useMemo } from "react";
import { useLazyHistoricalBalances } from "~/state/balances";
import { TouchableScale } from "~/components/TouchableScale";
import { Period, PeriodsZ } from "~/services/balances";
import { create } from "zustand";
import { Settings, StyleSheet } from "react-native";
import Animated from "react-native-reanimated";
import { Address } from "@squads/models/solana";
import { useLoadingAnimation } from "~/hooks/useLoadingAnimation";
import { Flex, Row } from "~/components/Grid";
import { useReportError } from "~/utils/errors";

export const useSelectedPoint = create<{
  point: GraphPoint | null;
  active: boolean;
}>(() => ({ active: false, point: null }));

export const useBalanceChartPeriod = create<{
  period: Period;
  setPeriod: (newPeriod: Period) => void;
}>((set) => {
  const savedPeriod = PeriodsZ.safeParse(
    Settings.get("DASHBOARD_CHART_PERIOD")
  );

  return {
    period: savedPeriod.success ? savedPeriod.data : "day",
    setPeriod: (newPeriod: Period) => {
      Settings.set({ DASHBOARD_CHART_PERIOD: newPeriod });
      set({ period: newPeriod });
    },
  };
});

const staticPoints = Array.from({ length: 100 }).map((_, i, array) => {
  return {
    date: DateTime.now().plus({ days: i }).toJSDate(),
    value: i === 0 ? 0 : i === array.length - 1 ? 2 : 1,
  };
});

export function BalanceChart({ vault }: { vault: Address }) {
  const { period, setPeriod } = useBalanceChartPeriod();

  const { data, isLoading } = useLazyHistoricalBalances({
    vault,
    period,
  });

  const reportError = useReportError();

  const balances = useMemo(() => {
    if (data && !data.map) {
      // todo: remove this when we figure out why data.map is undefined for some users/conditions
      reportError(new Error("BalanceChart.tsx: data.map is undefined"), {
        data,
      });
      return [];
    }

    const points = data?.map((balance) => {
      return {
        date: DateTime.fromSeconds(balance.timestamp).toJSDate(),
        value: balance.usdcBalance,
      };
    });

    if (points?.length === 101) {
      //remove extra element as temporal fix
      //fixme: fix the chart to handle 101 elements
      points?.pop();
    }

    return points;
  }, [data]);

  return (
    <Flex style={{ maxHeight: 320 }}>
      <Chart
        points={balances}
        isLoading={isLoading}
        onPointSelected={useSelectedPoint.setState}
      />
      <ChartPeriods selected={period} onSelect={setPeriod} />
    </Flex>
  );
}

export function ChartPeriods({
  selected: period,
  onSelect: setPeriod,
}: {
  selected: Period;
  onSelect: (period: Period) => void;
}) {
  return (
    <Row gap={12} style={{ justifyContent: "center" }}>
      <PeriodButton
        selected={period === "day"}
        onSelect={() => {
          Haptics.selectionAsync();
          setPeriod("day");
        }}
      >
        1D
      </PeriodButton>
      <PeriodButton
        selected={period === "week"}
        onSelect={() => {
          Haptics.selectionAsync();
          setPeriod("week");
        }}
      >
        1W
      </PeriodButton>
      <PeriodButton
        selected={period === "month"}
        onSelect={() => {
          Haptics.selectionAsync();
          setPeriod("month");
        }}
      >
        1M
      </PeriodButton>
      <PeriodButton
        selected={period === "halfYear"}
        onSelect={() => {
          Haptics.selectionAsync();
          setPeriod("halfYear");
        }}
      >
        6M
      </PeriodButton>
      <PeriodButton
        selected={period === "year"}
        onSelect={() => {
          Haptics.selectionAsync();
          setPeriod("year");
        }}
      >
        1Y
      </PeriodButton>
    </Row>
  );
}

export type SelectedChartPoint = {
  point: GraphPoint | null;
  active: boolean;
};

export function Chart({
  onPointSelected,
  points: inputPoints,
  isLoading,
  height,
}: {
  onPointSelected?: Dispatch<SetStateAction<SelectedChartPoint>>;
  points: GraphPoint[] | undefined;
  isLoading: boolean;
  height?: number;
}) {
  const isEmptyChart =
    !inputPoints ||
    inputPoints.length < 3 ||
    isLoading ||
    inputPoints.every((point) => point.value === inputPoints[0].value);

  const points: GraphPoint[] = isEmptyChart ? staticPoints : inputPoints;

  const chartColor = useColor("text");
  const gradientColor = useColor("textTertiary");

  const animatedOpacityStyle = useLoadingAnimation();

  return (
    <View style={styles.chartWrapper}>
      <Animated.View
        style={[
          isLoading ? [animatedOpacityStyle] : { opacity: 1 },
          height ? { height } : { height: "100%" },
        ]}
      >
        <LineGraph
          gradientFillColors={[gradientColor, "transparent"]}
          style={[
            { transform: [{ scaleX: isEmptyChart ? 1.1 : 1 }] },
            height ? { height } : { height: "100%" },
          ]}
          verticalPadding={30}
          animated={true}
          points={points}
          color={chartColor}
          lineThickness={2}
          enablePanGesture={!isEmptyChart}
          onGestureStart={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            onPointSelected?.((value) =>
              value.active ? value : { ...value, active: true }
            );
          }}
          onPointSelected={(point) => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            onPointSelected?.((value) =>
              value.point?.date.getTime() === point.date.getTime() &&
              value.point?.value === point.value
                ? value
                : { ...value, point }
            );
          }}
          onGestureEnd={() => {
            onPointSelected?.((value) =>
              !value.active ? value : { ...value, active: false }
            );
          }}
        />
      </Animated.View>
    </View>
  );
}

export function PeriodButton({
  children,
  selected,
  onSelect,
}: {
  children?: ReactNode;
  selected?: boolean;
  onSelect?: () => void;
}) {
  const disabled = onSelect === undefined;

  return (
    <TouchableScale
      aria-disabled={disabled}
      hitSlop={4}
      onPress={onSelect}
      activeScale={0.9}
    >
      <View
        background={selected ? "backgroundSecondary" : "background"}
        style={{
          borderRadius: 42,
          justifyContent: "center",
          alignItems: "center",
          paddingHorizontal: 12,
          paddingVertical: 6,
        }}
      >
        <Text
          style={{ fontSize: 12 }}
          variant="medium"
          colorToken={
            disabled ? "textTertiary" : selected ? "text" : "textSecondary"
          }
        >
          {children}
        </Text>
      </View>
    </TouchableScale>
  );
}

const styles = StyleSheet.create({
  chartWrapper: {
    flex: 1,
    gap: 12,
    overflow: "hidden",
  },
});
