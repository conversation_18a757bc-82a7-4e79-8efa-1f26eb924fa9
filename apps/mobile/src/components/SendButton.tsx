import { Address } from "@squads/models/solana";
import { router } from "expo-router";
import { mints } from "~/constants/tokens";
import { useLock } from "~/hooks/useLock";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { RefObject, Suspense, useCallback, useEffect, useRef } from "react";
import { Text, View } from "~/components/Themed";
import { TouchableScale } from "~/components/TouchableScale";
import { IconWrapper } from "~/components/IconWrapper";
import { ActionButton } from "./ActionButton";
import { useIsWalletInactive } from "~/hooks/useAssertWalletState";
import { ModalRow, ModalRowsList } from "~/components/ModalRow";
import { CoinIcon } from "~/components/icons/home/<USER>";
import { BankIcon } from "~/components/icons/home/<USER>";
import {
  hapticOpenBottomTray,
  hapticOpenModalSheet,
  hapticSelect,
} from "~/utils/haptics";
import { create } from "zustand";
import { DashedBorder } from "~/components/DashedBorder";
import { Button } from "~/components/Button";
import { Image } from "expo-image";
import {
  deleteExternalAccount,
  getKycStatus,
  isThirdPartyPayoutAvailable,
  loadBridgeAccount,
  loadCountriesList,
  markKycCompleted,
  useBridgeAccount,
  useExternalAccounts,
} from "~/state/bridge";
import { ContentSkeleton } from "~/components/Skeleton";
import { FuseErrorBoundary } from "~/components/errors/FuseErrorBoundary";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { routeToKyc } from "~/components/DepositButton";
import { Flex, Row } from "~/components/Grid";
import { Icon } from "~/components/Icon";
import { sleep } from "~/utils/promise";
import { showImproveSecurityModal } from "~/components/security/useImproveSecurityModalState";
import { useActiveWallet } from "~/state/wallet";
import { BridgeAccount, openKycFlow, updateKycLink } from "~/services/bridge";
import { useToast } from "~/components/Toaster";
import { StartKycRouteParams } from "~/app/unlocked/bridge/onboarding/start-kyc";
import { ScrollView } from "~/components/ScrollView";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";

export function SendButton({ mint }: { mint?: Address }) {
  const modalRef = useRef<BottomModalImperativeMethods>(null);

  const withLock = useLock();
  const isWalletInactive = useIsWalletInactive();

  const openSendCryptoFlowModal = useCallback(() => {
    hapticOpenModalSheet();

    const mintOrSol = mint === mints.sol ? "SOL" : mint;
    mint
      ? router.push(`/unlocked/send/${mintOrSol}/choose-recipient`)
      : router.push(`/unlocked/send/select-token`);
  }, [mint]);

  function openBankAccountFlow(accountType: "us" | "iban") {
    router.push(
      `/unlocked/bridge/add-external-account/account-details?accountType=${accountType}`
    );
  }

  function openSendFiatFlowModal(externalAccountId: string) {
    router.push(
      `/unlocked/bridge/send-fiat/enter-amount?externalAccountId=${externalAccountId}`
    );
  }

  return (
    <>
      <ActionButton
        hitSlop={4}
        iconName="arrow.up.circle.fill"
        iconWeight="semibold"
        onPress={withLock(async () => {
          if (await isWalletInactive()) return;

          if (mint && mint !== mints.usdc) {
            openSendCryptoFlowModal();
          } else {
            hapticOpenBottomTray();
            modalRef.current?.present();
          }
        })}
      >
        Send
      </ActionButton>

      <SendModal
        modalRef={modalRef}
        openSendCryptoFlowModal={openSendCryptoFlowModal}
        openSendFiatFlowModal={openSendFiatFlowModal}
        openBankAccountFlow={openBankAccountFlow}
      />
    </>
  );
}

type SendModalState =
  | { view: "sendOptions" }
  | { view: "bankAccounts" }
  | { view: "chooseFiatCurrency" }
  | { view: "deleteBankAccount"; externalAccountId: string };

const useSendModalState = create<{
  state: SendModalState;
  hide: () => void;
  goTo: (view: SendModalState) => void;
}>((set) => ({
  state: { view: "sendOptions" as const },
  hide: () => set({ state: { view: "sendOptions" } }),
  goTo: (state: SendModalState) => set({ state }),
}));

function SendModal({
  modalRef,
  openSendCryptoFlowModal,
  openBankAccountFlow,
  openSendFiatFlowModal,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
  openSendCryptoFlowModal: () => void;
  openSendFiatFlowModal: (externalAccountId: string) => void;
  openBankAccountFlow: (accountType: "us" | "iban") => void;
}) {
  const { wallet } = useActiveWallet();
  const { toast } = useToast();

  const queryClient = useQueryClient();
  const { state, hide, goTo } = useSendModalState();

  const deleteExternalAccountMutation = useMutation({
    mutationFn: async (externalAccountId: string) => {
      await deleteExternalAccount(queryClient, { externalAccountId });
      goTo({ view: "bankAccounts" });
    },
  });

  const title = (() => {
    switch (state.view) {
      case "sendOptions":
        return "Send";

      case "bankAccounts":
      case "deleteBankAccount":
        return "Bank accounts";

      case "chooseFiatCurrency":
        return "Choose currency";

      default:
        state satisfies never;
    }
  })();

  const body = (() => {
    switch (state.view) {
      case "sendOptions":
        return (
          <SendOptionsModalBody
            onSendCryptoSelected={() => {
              modalRef.current?.close();
              openSendCryptoFlowModal();
            }}
            onSendFiatSelected={async () => {
              if (wallet.keys.recoveryKeys === null) {
                modalRef.current?.close();
                await sleep(300);

                showImproveSecurityModal();
                return;
              }

              const bridgeAccount = await loadBridgeAccount({
                queryClient,
              });

              if (bridgeAccount) {
                const kycStatus = getKycStatus(bridgeAccount);
                if (kycStatus !== "approved") {
                  modalRef.current?.close();
                  routeToKyc(kycStatus);
                  return;
                }
              }

              goTo({ view: "bankAccounts" });
            }}
          />
        );

      case "bankAccounts":
        return (
          <OfframpBankAccountsModalBody
            onSelect={(externalAccountId) => {
              modalRef.current?.close();
              openSendFiatFlowModal(externalAccountId);
            }}
            onDelete={(externalAccountId) => {
              goTo({ view: "deleteBankAccount", externalAccountId });
            }}
          />
        );

      case "chooseFiatCurrency":
        return (
          <ChooseFiatCurrencyModalBody
            onSelect={async (accountType) => {
              modalRef.current?.close();

              const bridgeAccount = await loadBridgeAccount({
                queryClient,
              });

              if (!bridgeAccount) {
                modalRef.current?.close();
                router.push({
                  pathname: "/unlocked/bridge/onboarding/start-kyc",
                  params: {
                    endorsements: accountType === "iban" ? ["sepa"] : undefined,
                  } satisfies StartKycRouteParams,
                });
                return;
              }

              const kycStatus = getKycStatus(bridgeAccount);
              if (kycStatus !== "approved") {
                modalRef.current?.close();
                routeToKyc(kycStatus);
                return;
              }

              const sepaEndorsement = BridgeAccount.endorsement(
                bridgeAccount,
                "sepa"
              );
              if (
                accountType === "iban" &&
                sepaEndorsement?.status !== "approved"
              ) {
                if (
                  sepaEndorsement &&
                  sepaEndorsement.requirements.pending.length > 0
                ) {
                  toast.info("Verification for EU features is still pending", {
                    id: "kyc_pending",
                  });
                  return;
                }

                const { kycLink } = await updateKycLink({
                  endorsement: "sepa",
                });
                const result = await openKycFlow({ kycLink });
                if (result.status !== "completed") {
                  toast.info("Verification has been cancelled");
                } else {
                  markKycCompleted(bridgeAccount.customer_id);
                  router.dismissTo("/unlocked/root/home");
                  router.push("/unlocked/bridge/onboarding/kyc-status");
                }

                return;
              }

              openBankAccountFlow(accountType);
            }}
          />
        );

      case "deleteBankAccount":
        return (
          <DeleteBankAccountModalBody
            externalAccountId={state.externalAccountId}
          />
        );

      default:
        state satisfies never;
    }
  })();

  const footer = (() => {
    switch (state.view) {
      case "bankAccounts":
        return (
          <View gap={14} style={{ marginTop: -8 }}>
            <FuseErrorBoundary FallbackComponent={() => null}>
              <Suspense fallback={null}>
                <UnavailableStatesWarning />
              </Suspense>
            </FuseErrorBoundary>
            <Button
              variant="secondary"
              iconName="plus"
              iconWeight="semibold"
              onPress={() => {
                hapticSelect();
                goTo({ view: "chooseFiatCurrency" });
              }}
            >
              Add bank account
            </Button>
          </View>
        );

      case "sendOptions":
      case "chooseFiatCurrency":
        return null;

      case "deleteBankAccount":
        return (
          <Row gap={12}>
            <Flex>
              <Button
                variant="secondary"
                size="medium"
                onPress={() => {
                  hapticSelect();
                  goTo({ view: "bankAccounts" });
                }}
              >
                Cancel
              </Button>
            </Flex>
            <Flex>
              <Button
                size="medium"
                variant="danger"
                loading={deleteExternalAccountMutation.isPending}
                onPress={() => {
                  hapticSelect();
                  deleteExternalAccountMutation.mutate(state.externalAccountId);
                }}
              >
                Confirm
              </Button>
            </Flex>
          </Row>
        );

      default:
        state satisfies never;
    }
  })();

  return (
    <BottomModal
      modalRef={modalRef}
      modalId={"send-options" + state.view}
      title={title}
      onClose={hide}
      onDismiss={hide}
      body={body}
      footer={footer}
    />
  );
}

function SendOptionsModalBody({
  onSendCryptoSelected,
  onSendFiatSelected,
}: {
  onSendCryptoSelected: () => void;
  onSendFiatSelected: () => Promise<void>;
}) {
  return (
    <ModalRowsList>
      <TouchableScale
        onPress={async () => {
          hapticSelect();
          onSendCryptoSelected();
        }}
      >
        <ModalRow
          icon={
            <IconWrapper
              backgroundColorToken="backgroundTertiary"
              size={46}
              variant={"square"}
            >
              <CoinIcon />
            </IconWrapper>
          }
          title="To wallet"
          description="Send assets to crypto wallet"
        />
      </TouchableScale>
      <TouchableScale
        onPress={() => {
          hapticSelect();
          onSendFiatSelected();
        }}
      >
        <ModalRow
          icon={
            <IconWrapper
              backgroundColorToken="backgroundTertiary"
              size={46}
              variant={"square"}
            >
              <BankIcon />
            </IconWrapper>
          }
          title="To bank account"
          description="Send USDC to bank account"
        />
      </TouchableScale>
    </ModalRowsList>
  );
}

function OfframpBankAccountsModalBody({
  onSelect,
  onDelete,
}: {
  onSelect: (externalAccountId: string) => void;
  onDelete: (externalAccountId: string) => void;
}) {
  return (
    <FuseErrorBoundary FallbackComponent={OfframpBankAccountsModalBodySkeleton}>
      <Suspense fallback={<OfframpBankAccountsModalBodySkeleton />}>
        <OfframpBankAccountsModalBodyInner
          onSelect={onSelect}
          onDelete={onDelete}
        />
      </Suspense>
    </FuseErrorBoundary>
  );
}

function OfframpBankAccountsModalBodySkeleton() {
  return (
    <ModalRowsList>
      <ContentSkeleton>
        <ModalRow
          icon={
            <IconWrapper
              backgroundColorToken="backgroundSecondary"
              size={46}
              variant={"square"}
            >
              <BankIcon />
            </IconWrapper>
          }
          title={"John Doe"}
          description={"USD"}
        />
      </ContentSkeleton>
    </ModalRowsList>
  );
}

function OfframpBankAccountsModalBodyInner({
  onSelect,
  onDelete,
}: {
  onSelect: (externalAccountId: string) => void;
  onDelete: (externalAccountId: string) => void;
}) {
  const bankAccounts = useExternalAccounts();

  if (bankAccounts.length === 0)
    return (
      <View
        style={{
          height: 160,
          justifyContent: "center",
          alignItems: "center",
          gap: 12,
        }}
      >
        <DashedBorder
          borderWidth={1.5}
          borderColorToken={"dashedListSeparator"}
        />
        <IconWrapper
          backgroundColorToken="backgroundSecondary"
          size={46}
          variant={"square"}
        >
          <BankIcon colorToken={"textTertiary"} />
        </IconWrapper>
        <View gap={4} style={{ alignItems: "center" }}>
          <Text variant="semibold" size={15}>
            Add bank account
          </Text>
          <Text variant="medium" size={14} colorToken={"textSecondary"}>
            You have no added bank account yet
          </Text>
        </View>
      </View>
    );

  return (
    <ScrollView showsVerticalScrollIndicator={false} style={{ maxHeight: 270 }}>
      <ModalRowsList>
        {bankAccounts
          .sort((a, b) => a.accountOwnerName.localeCompare(b.accountOwnerName))
          .map((account) => (
            <AnimatedTouchableScale
              key={account.id}
              onPress={() => {
                hapticSelect();
                onSelect(account.id);
              }}
            >
              <View>
                <ModalRow
                  icon={
                    <IconWrapper
                      backgroundColorToken="backgroundTertiary"
                      size={46}
                      variant={"square"}
                    >
                      <BankIcon />
                    </IconWrapper>
                  }
                  title={account.accountOwnerName}
                  description={`${account.bankName} ${account.accountNumber}`}
                />
                <AnimatedTouchableScale
                  onPress={() => onDelete(account.id)}
                  hitSlop={8}
                  style={{
                    position: "absolute",
                    right: 24,
                    top: 28,
                  }}
                >
                  <Icon name={"trash"} size={12} colorToken="textSecondary" />
                </AnimatedTouchableScale>
              </View>
            </AnimatedTouchableScale>
          ))}
      </ModalRowsList>
    </ScrollView>
  );
}

function UnavailableStatesWarning() {
  const bridgeAccount = useBridgeAccount();

  if (!bridgeAccount || !bridgeAccount.country) {
    return null;
  }

  if (isThirdPartyPayoutAvailable(bridgeAccount.country)) {
    return null;
  }

  return (
    <Text
      variant="medium"
      colorToken="textSecondary"
      size={13}
      align={"center"}
    >
      Bank transfers to other individuals{"\n"}
      are not available for residents of TX and PA.
    </Text>
  );
}

function ChooseFiatCurrencyModalBody({
  onSelect,
}: {
  onSelect: (accountType: "us" | "iban") => void;
}) {
  const queryClient = useQueryClient();
  useEffect(() => {
    loadCountriesList(queryClient).then(() =>
      console.debug(`Preloaded countries list`)
    );
  }, []);

  return (
    <ModalRowsList>
      <TouchableScale
        onPress={() => {
          hapticSelect();
          onSelect("us");
        }}
      >
        <ModalRow
          icon={
            <Image
              source={require("../../assets/images/flag-us.png")}
              style={{
                width: 40,
                aspectRatio: 1,
                borderRadius: 9999,
              }}
            />
          }
          title="US Dollar"
          description="USD"
        />
      </TouchableScale>
      <TouchableScale
        onPress={() => {
          hapticSelect();
          onSelect("iban");
        }}
      >
        <ModalRow
          icon={
            <Image
              source={require("../../assets/images/flag-eu.png")}
              style={{
                width: 40,
                aspectRatio: 1,
                borderRadius: 9999,
              }}
            />
          }
          title="Euro"
          description="EUR"
        />
      </TouchableScale>
    </ModalRowsList>
  );
}

export function DeleteBankAccountModalBody({
  externalAccountId,
}: {
  externalAccountId: string;
}) {
  const bankAccounts = useExternalAccounts();
  const bankAccount = bankAccounts.find(
    (account) => account.id === externalAccountId
  );

  if (!bankAccount) {
    return null;
  }

  return (
    <View gap={22} style={{ alignItems: "center", paddingTop: 12 }}>
      <IconWrapper background={"white"} size={46} variant={"square"}>
        <BankIcon />
      </IconWrapper>
      <Text
        variant="medium"
        colorToken="textSecondary"
        style={{ fontSize: 18, textAlign: "center" }}
      >
        Bank account{" "}
        <Text variant="medium" colorToken="text" style={{ fontSize: 18 }}>
          {bankAccount.bankName} {bankAccount.accountNumber}
        </Text>
        {"\n"}
        will be removed
      </Text>
    </View>
  );
}
