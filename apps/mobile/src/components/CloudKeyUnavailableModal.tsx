import { create } from "zustand";
import { Text, View } from "~/components/Themed";
import { Flex, Row } from "~/components/Grid";
import { Button } from "~/components/Button";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { useEffect, useId, useRef } from "react";
import { Linking } from "react-native";
import { router } from "expo-router";
import { Wallet } from "~/services/wallets";
import { loadCloudKey } from "~/state/cloudKey";
import { queryClient } from "~/state/queryClient";
import { IconWrapper } from "~/components/IconWrapper";
import { Icon } from "~/components/Icon";

const useModalState = create<{
  shown: boolean;
  hide: () => void;
}>((set) => ({ shown: false, hide: () => set({ shown: false }) }));

export function CloudKeyUnavailableModal() {
  const id = useId();
  const modalRef = useRef<BottomModalImperativeMethods>(null);
  const { shown, hide } = useModalState();

  useEffect(() => {
    if (shown) {
      modalRef.current?.present();
    } else {
      modalRef.current?.close();
    }
  }, [shown]);

  return (
    <BottomModal
      modalId={id}
      modalRef={modalRef}
      title=" "
      onClose={hide}
      onDismiss={hide}
      body={
        <View gap={24} style={{ paddingHorizontal: 14 }}>
          <View gap={24} style={{ alignItems: "center" }}>
            <IconWrapper
              backgroundColorToken={"red"}
              size={56}
              variant={"square"}
            >
              <Icon
                name="icloud.slash"
                size={20}
                color={"white"}
                style={{ top: 2 }}
              />
            </IconWrapper>

            <View gap={4} style={{ alignItems: "center" }}>
              <Text variant="semibold" size={20}>
                iCloud is unavailable
              </Text>
            </View>
          </View>
          <Text size={16} variant="medium" colorToken="textSecondary">
            Make sure you're logged into iCloud{"\n\n"}Alternatively you can
            switch the Cloud Key to another external 2FA Key to access your
            wallet
          </Text>
        </View>
      }
      footer={
        <Row gap={12}>
          <Flex>
            <Button
              size={"medium"}
              variant="secondary"
              onPress={() => {
                hide();
                router.push("/unlocked/change-cloud-key/options");
              }}
            >
              Switch 2FA Key
            </Button>
          </Flex>
          <Flex>
            <Button
              size={"medium"}
              variant="primary"
              onPress={() => {
                hide();
                Linking.openURL("App-prefs:APPLE_ACCOUNT");
              }}
            >
              Open Settings
            </Button>
          </Flex>
        </Row>
      }
    />
  );
}

export function showCloudKeyUnavailableModal() {
  useModalState.setState({ shown: true });
}

export async function isICloudKeyUnavailable(wallet: Wallet) {
  if (wallet.keys.cloudKey.details.type !== "iCloud") {
    return false;
  }

  return loadCloudKey({ queryClient })
    .then((cloudKeyData) => {
      return cloudKeyData.type !== "success";
    })
    .catch(() => true);
}
