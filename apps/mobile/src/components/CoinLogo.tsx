import { Address } from "@squads/models/solana";
import { Text, View } from "~/components/Themed";
import { StyleProp, StyleSheet, ViewStyle } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { mints, sortMints } from "~/constants/tokens";
import { useToken } from "~/state/tokens";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import { Image } from "expo-image";
import { Icon } from "~/components/Icon";
import { IconWrapper } from "~/components/IconWrapper";
import { Spinner } from "~/components/Spinner";
import { useState } from "react";
import { SFSymbol } from "expo-symbols";
import { Row } from "~/components/Grid";
import { CoinsIcon } from "~/components/icons/CoinsIcon";
import { ContentSkeleton } from "~/components/Skeleton";
import { UsdcIcon } from "./icons/coins/UsdcIcon";

export type CoinLogoVariant = "error" | "loading" | "default";

export function CoinPairLogo({
  mint1,
  mint2,
  size = 42,
  variant = "default",
}: {
  mint1: Address;
  mint2: Address;
  size?: number;
  variant?: CoinLogoVariant;
}) {
  const coinSize = size / 1.5;
  const modifierWrapperSize = size / 2.1;
  const modifierIconSize = size / 3.5;

  return (
    <View
      style={{
        position: "relative",
        width: size,
        height: size,
      }}
    >
      <CoinIcon mint={mint1} size={coinSize} />
      <View
        style={{ position: "absolute", top: coinSize / 2, left: coinSize / 2 }}
      >
        <CoinIcon mint={mint2} size={coinSize} />
      </View>
      {variant === "error" && (
        <View style={{ position: "absolute", top: -3, right: -3 }}>
          <IconWrapper
            size={modifierWrapperSize}
            backgroundColorToken={"background"}
          >
            <Icon
              name={"xmark.circle.fill"}
              colorToken={"red"}
              size={modifierIconSize}
            />
          </IconWrapper>
        </View>
      )}
      {variant === "loading" && (
        <View style={{ position: "absolute", top: -3, right: -3 }}>
          <IconWrapper
            size={modifierWrapperSize}
            backgroundColorToken={"background"}
          >
            <Spinner size={modifierIconSize} colorToken={"text"} />
          </IconWrapper>
        </View>
      )}
    </View>
  );
}

export function CoinLogo({
  mint,
  size = 42,
  variant = "default",
  subIcon,
  style,
}: {
  mint: Address | "SOL";
  size?: number;
  variant?: CoinLogoVariant;
  subIcon?: SFSymbol;
  style?: StyleProp<ViewStyle>;
}) {
  return (
    <View style={[{ position: "relative" }, style]}>
      <CoinIcon mint={mint} size={size} />
      {variant === "error" && (
        <View style={{ position: "absolute", top: -5, right: -5 }}>
          <IconWrapper size={18} backgroundColorToken={"background"}>
            <Icon name={"xmark.circle.fill"} colorToken={"red"} size={12} />
          </IconWrapper>
        </View>
      )}
      {variant === "loading" && (
        <View style={{ position: "absolute", top: -5, right: -5 }}>
          <IconWrapper size={18} backgroundColorToken={"background"}>
            <Spinner size={12} colorToken={"text"} />
          </IconWrapper>
        </View>
      )}
      {subIcon && (
        <View style={{ position: "absolute", bottom: -5, right: -5 }}>
          <IconWrapper size={18} backgroundColorToken={"background"}>
            <Icon name={subIcon} colorToken={"textSecondary"} size={10} />
          </IconWrapper>
        </View>
      )}
    </View>
  );
}

export function MultiCoinLogo({
  mints,
  size,
}: {
  mints: Address[];
  size: number;
}) {
  const [first, ...rest] = sortMints(mints);
  const borderSize = size / 8;
  const offset = borderSize / 2;

  const width = size + (rest.length * size - 4);
  return (
    <Row style={{ alignSelf: "flex-start", width }}>
      <CoinLogo mint={first} size={size} />
      {rest.map((mint, index) => (
        <View key={mint} style={{ left: -4 * (index + 1) }}>
          <IconWrapper
            background={"#fff"}
            size={size + borderSize}
            style={{ position: "absolute", top: -offset, left: -offset }}
          />
          <CoinLogo mint={mint as Address} size={size} />
        </View>
      ))}
    </Row>
  );
}

function CoinIcon({
  mint: mintOrSol,
  size = 42,
}: {
  mint: Address | "SOL";
  size?: number;
}) {
  const mint = mintOrSol === "SOL" ? mints.sol : mintOrSol;
  const { data: token, isPending } = useToken({ mint });

  if (mint === mints.usdc) {
    return <UsdcIcon size={size} />;
  }

  if (isPending) {
    return <LoadingCoinIcon size={size} />;
  }

  const symbol =
    token?.symbol ?? (mintOrSol === "SOL" ? "SOL" : abbreviateAddress(mint, 2));

  if (!token && mintOrSol !== "SOL") {
    return <FallbackCoinIcon mint={mint} symbol={symbol} size={size} />;
  }

  if (token?.logoUri === null) {
    return (
      <IconWrapper
        key={mint}
        backgroundColorToken="backgroundSecondary"
        size={size}
      >
        <CoinsIcon size={size / 2} />
      </IconWrapper>
    );
  }

  return (
    <CoinImage
      key={symbol}
      symbol={symbol}
      logoUri={token?.logoUri ?? null}
      size={size}
    />
  );
}

export function CoinImage({
  logoUri,
  symbol,
  size = 42,
}: {
  logoUri: string | null;
  symbol: string;
  size?: number;
}) {
  const [hasError, setHasError] = useState(false);

  if (logoUri === null || hasError) {
    return (
      <IconWrapper backgroundColorToken="backgroundSecondary" size={size}>
        <CoinsIcon size={size / 2} />
      </IconWrapper>
    );
  }

  return (
    <Image
      source={logoUri}
      alt={symbol}
      style={{ width: size, height: size, borderRadius: 9999 }}
      onError={(e) => {
        console.error(`Failed loading image for ${symbol}: ${logoUri}`, e);
        setHasError(true);
      }}
    />
  );
}

const colors = [
  ["#ADA6F0", "#978FE7"],
  ["#A7D9FF", "#80BBE8"],
  ["#6AD3D4", "#42ACAD"],
] as const;

/** Deterministically pick a color from a predefined set */
function getColorForMint(mint: Address): readonly [string, string] {
  const index = hashcode(mint);
  return colors[index % colors.length];
}

/**
 * Naïve "hashcode" function for strings.
 */
function hashcode(str: string) {
  return str
    .split("")
    .map((char) => char.charCodeAt(0))
    .reduce((acc, charCode) => acc + charCode, 0);
}

function LoadingCoinIcon({ size }: { size: number }) {
  return (
    <ContentSkeleton borderRadius={9999}>
      <View style={{ width: size, height: size, borderRadius: 9999 }} />
    </ContentSkeleton>
  );
}

export function FallbackCoinIcon({
  mint,
  symbol,
  size,
}: {
  mint: Address;
  symbol: string;
  size: number;
}) {
  return (
    <LinearGradient
      style={[styles.wrapper, { width: size }]}
      colors={getColorForMint(mint)}
      start={[0, 0]}
      end={[1, 1]}
    >
      <Text
        lightColor={"white"}
        darkColor={"white"}
        variant="bold"
        style={styles.symbol}
      >
        {symbol}
      </Text>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  wrapper: {
    aspectRatio: 1,
    flexShrink: 0,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 9999,
  },
  symbol: {
    opacity: 0.6,
    fontSize: 10,
  },
});
