import { useSubscription } from "~/state/subscription";
import { router } from "expo-router";
import { Row } from "~/components/Grid";
import { IconWrapper } from "~/components/IconWrapper";
import { FusePlusLogo } from "~/components/icons/FusePlusLogo";
import { Icon } from "~/components/Icon";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import { FusePlusText } from "~/components/FusePlusText";
import { hapticOpenModalSheet } from "~/utils/haptics";

export function FusePlusActionBanner() {
  const subscriptionQuery = useSubscription();

  const subscription = subscriptionQuery.data;
  if (!subscription || subscription.status === "active") {
    return null;
  }

  return (
    <AnimatedTouchableScale
      pressedScale={0.99}
      onPress={() => {
        hapticOpenModalSheet();
        router.push("/unlocked/subscription/promo");
      }}
    >
      <Row
        background={"backgroundBanner"}
        style={{
          paddingHorizontal: 16,
          paddingVertical: 12,
          borderRadius: 16,
          borderWidth: 1,
          borderColor: "rgba(0,0,0,0.05)",
          borderCurve: "continuous",
          justifyContent: "space-between",
        }}
      >
        <Row gap={12}>
          <IconWrapper
            size={24}
            background={"black"}
            variant={"square"}
            style={{ borderRadius: 8 }}
          >
            <FusePlusLogo size={11} />
          </IconWrapper>

          <FusePlusText prefix={"Save on fees with "} />
        </Row>
        <IconWrapper size={24} backgroundColorToken={"backgroundSecondary"}>
          <Icon name="chevron.right" size={8} colorToken="text" weight="bold" />
        </IconWrapper>
      </Row>
    </AnimatedTouchableScale>
  );
}
