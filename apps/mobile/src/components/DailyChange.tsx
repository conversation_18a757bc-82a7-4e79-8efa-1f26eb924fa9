import { Wallet } from "~/services/wallets";
import { Text, useColor, View } from "./Themed";
import Animated, {
  Extrapolation,
  interpolate,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from "react-native-reanimated";
import { FuseErrorBoundary } from "./errors/FuseErrorBoundary";
import { FuseSuspense } from "./FuseSuspense";
import { SPRING } from "~/constants/animations";
import * as Haptics from "expo-haptics";
import { Icon } from "./Icon";
import { abbreviateAmount } from "~/vendor/squads/utils/numberFormats";
import { TouchableScale } from "~/components/TouchableScale";
import { Row } from "./Grid";
import { ContentSkeleton } from "./Skeleton";
import Ticker from "./Ticker";
import { useBalanceSettings } from "~/state/balanceSettings";
import { useCallback } from "react";
import { LayoutChangeEvent } from "react-native";
import {
  useSuspenseVerifiedBalances,
  usePeriodBalances,
} from "~/state/balances";
import { calculatePeriodChange } from "~/services/balances";
import { useBalanceChartPeriod } from "./BalanceChart";

const paddingHorizontal = 10;
const gap = 4;
const iconSize = 8;
const initialContainerWidth = 60;
const height = 28;
const borderRadius = 10;
const hiddenTextOffsetY = 15;

export function DailyChange({ wallet }: { wallet: Wallet }) {
  return (
    <FuseErrorBoundary FallbackComponent={() => <BalanceChangeFallback />}>
      <FuseSuspense fallback={<BalanceChangeSkeleton />}>
        <DailyChangeView wallet={wallet} />
      </FuseSuspense>
    </FuseErrorBoundary>
  );
}

function DailyChangeView({ wallet }: { wallet: Wallet }) {
  const { verifiedBalances } = useSuspenseVerifiedBalances({
    address: wallet.defaultVault,
  });
  const { period } = useBalanceChartPeriod();
  const periodBalances = usePeriodBalances({
    address: wallet.defaultVault,
    period: period,
  });
  const { totalUsdDiff: usdChange, percentageDiff: percentageChange } =
    calculatePeriodChange(verifiedBalances, periodBalances);

  const {
    toggleBalanceChangeMode,
    hidingBalance,
    getBalanceChangeMode,
    balanceChangeMode,
  } = useBalanceSettings();

  const usdChangeString = abbreviateAmount(usdChange, { type: "usd" });
  const percentageChangeString = abbreviateAmount(percentageChange, {
    type: "percentage",
  });

  const isPositive = usdChange >= 0;

  const red = useColor("red");
  const green = useColor("green");
  const containerBorder = `${useColor("border")}80`;

  const isGestureInProgress = useSharedValue(false);

  const usdChangeTextWidth = useSharedValue(initialContainerWidth);
  const percentageChangeTextWidth = useSharedValue(initialContainerWidth);

  const showPercentage = useSharedValue(
    balanceChangeMode === "percentage" ? 1 : 0
  );

  const handlePercentageLayout = useCallback((event: LayoutChangeEvent) => {
    const textWidth = event.nativeEvent.layout.width;
    const calculatedWidth = textWidth + paddingHorizontal * 2 + iconSize + gap;
    percentageChangeTextWidth.value = calculatedWidth;
  }, []);

  const handleUsdLayout = useCallback((event: LayoutChangeEvent) => {
    const textWidth = event.nativeEvent.layout.width;
    const calculatedWidth = textWidth + paddingHorizontal * 2 + iconSize + gap;
    usdChangeTextWidth.value = calculatedWidth;
  }, []);

  const currentMode = getBalanceChangeMode();
  const containerSize = useAnimatedStyle(() => {
    let widthValue: number;

    if (isGestureInProgress.value) {
      widthValue = withSpring(
        currentMode === "usd"
          ? percentageChangeTextWidth.value
          : usdChangeTextWidth.value,
        SPRING
      );
    } else {
      widthValue = interpolate(
        Math.max(hidingBalance.value, showPercentage.value),
        [0, 1],
        [usdChangeTextWidth.value, percentageChangeTextWidth.value]
      );
    }

    return {
      width: widthValue,
      height: height,
    };
  });

  const priceChangeAnimatedStyle = useAnimatedStyle(() => {
    const interpolateValue = Math.max(
      hidingBalance.value,
      showPercentage.value
    );

    return {
      transform: [
        { perspective: 100 },
        {
          translateY: interpolate(
            interpolateValue,
            [0, 1],
            [0, hiddenTextOffsetY]
          ),
        },
        {
          rotateX: `${interpolate(
            interpolateValue,
            [0.05, 0.95],
            [0, -45],
            Extrapolation.CLAMP
          )}deg`,
        },
      ],
      opacity: interpolate(
        interpolateValue,
        [0.1, 0.9],
        [1, 0],
        Extrapolation.CLAMP
      ),
    };
  });

  const percentageChangeAnimatedStyle = useAnimatedStyle(() => {
    const interpolateValue = Math.max(
      hidingBalance.value,
      showPercentage.value
    );

    return {
      transform: [
        { perspective: 100 },
        {
          translateY: interpolate(
            interpolateValue,
            [0, 1],
            [-hiddenTextOffsetY, 0]
          ),
        },
        {
          rotateX: `${interpolate(
            interpolateValue,
            [0.05, 0.95],
            [45, 0],
            Extrapolation.CLAMP
          )}deg`,
        },
      ],
      opacity: interpolate(
        interpolateValue,
        [0.1, 0.9],
        [0, 1],
        Extrapolation.CLAMP
      ),
    };
  });

  const onPress = () => {
    if (hidingBalance.value === 1) return;
    isGestureInProgress.value = true;
    runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Soft);

    showPercentage.value = withSpring(
      currentMode === "percentage" ? 0 : 1,
      SPRING,
      () => {
        isGestureInProgress.value = false;
        runOnJS(toggleBalanceChangeMode)();
      }
    );
  };

  const usdChangeElement = (
    <Text
      variant={"bold"}
      colorToken={isPositive ? "green" : "red"}
      style={{ fontSize: 14 }}
    >
      ${usdChangeString}
    </Text>
  );

  const percentageChangeElement = (
    <Text
      numberOfLines={1}
      variant={"bold"}
      colorToken={isPositive ? "green" : "red"}
      style={{ fontSize: 14 }}
    >
      {percentageChangeString}%
    </Text>
  );

  return (
    <>
      {/*Hidden elements to provide measurement */}
      <View
        onLayout={handleUsdLayout}
        style={{ position: "absolute", opacity: 0 }}
      >
        {usdChangeElement}
      </View>
      {/*Hidden elements to provide measurement */}
      <View
        onLayout={handlePercentageLayout}
        style={{ position: "absolute", opacity: 0 }}
      >
        {percentageChangeElement}
      </View>

      <TouchableScale
        style={{ position: "absolute", height: "100%", right: 0, top: 13 }}
        activeScale={0.95}
        onPress={onPress}
      >
        <Animated.View
          style={[
            {
              flexDirection: "row",
              gap: gap,
              alignItems: "center",
              borderRadius: borderRadius,
              borderWidth: 1.5,
              borderCurve: "continuous",
              borderColor: containerBorder,
              overflow: "hidden",
            },
            containerSize,
          ]}
        >
          <Icon
            name={isPositive ? "arrow.up.right" : "arrow.down.right"}
            size={iconSize}
            color={isPositive ? green : red}
            weight={"heavy"}
            style={{ marginLeft: paddingHorizontal }}
          />
          <View style={{ flex: 1 }}>
            <Animated.View style={[percentageChangeAnimatedStyle]}>
              {percentageChangeElement}
            </Animated.View>

            <Animated.View
              style={[{ position: "absolute" }, priceChangeAnimatedStyle]}
            >
              {usdChangeElement}
            </Animated.View>
          </View>
        </Animated.View>
      </TouchableScale>
    </>
  );
}

export function BalanceChangeFallback() {
  const containerBorder = `${useColor("border")}80`;
  return (
    <View
      style={{
        width: initialContainerWidth,
        height: height,
        borderRadius: borderRadius,
        flexDirection: "row",
        borderWidth: 1.5,
        borderColor: containerBorder,
        alignSelf: "center",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Ticker
        value={"--"}
        textVariant={"bold"}
        fontSize={14}
        wholeNumbersColorToken={"textSecondary"}
        decimalsColorToken={"textSecondary"}
      />
    </View>
  );
}

export function BalanceChangeSkeleton() {
  return (
    <Row>
      <ContentSkeleton borderRadius={10}>
        <View
          style={{
            width: initialContainerWidth,
            height: height,
          }}
        />
      </ContentSkeleton>
    </Row>
  );
}
