import Animated, {
  FadeInDown,
  FadeOutUp,
  interpolateColor,
  LayoutAnimationConfig,
  ReduceMotion,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";
import { memo, ReactNode, useEffect } from "react";
import { Text, useColor, View } from "~/components/Themed";
import { Row } from "~/components/Grid";
import { AnimatedAmountDisplay } from "~/components/AnimatedAmountDisplay";
import { hapticMaxButtonPressed } from "~/utils/haptics";
import {
  formatAmountInputWithSeparators,
  formatTokenAmount,
  formatUsdValue,
} from "@squads/utils/numberFormats";
import { ContentSkeleton } from "~/components/Skeleton";
import { DURATION_MEDIUM } from "~/constants/animations";
import { TouchableScale } from "~/components/TouchableScale";
import { AmountScreenSecondaryText } from "~/components/AmountScreenSecondaryText";
import { AmountSlider } from "~/components/AmountSlider";
import { CoinImage } from "~/components/CoinLogo";
import { TokenMetadata } from "~/services/balances";
import { Switch } from "~/components/Switch";

const TEXT_FADE_IN_DOWN = FadeInDown.duration(DURATION_MEDIUM).reduceMotion(
  ReduceMotion.Never
);
const TEXT_FADE_OUT_UP = FadeOutUp.duration(DURATION_MEDIUM).reduceMotion(
  ReduceMotion.Never
);

export const AssetAmountInputBlock = memo(function AssetAmountInputBlock({
  amount,
  usdPrice,
  onInstantChange,
  maxAmountQuery,
  showSlider = false,
}: {
  amount: string;
  usdPrice: number | null;
  maxAmountQuery:
    | { status: "error" }
    | { status: "pending" }
    | { status: "success"; maxAmount: number };
  onInstantChange: (amount: number) => void;
  showSlider?: boolean;
}) {
  const formattedAmount = amount ? formatAmountInputWithSeparators(amount) : "";

  const animatedAmount = useSharedValue(formattedAmount);

  useEffect(() => {
    animatedAmount.value = formattedAmount;
  }, [formattedAmount]);

  const amountNumber = parseFloat((formattedAmount || "0").replace(/,/g, ""));

  const maxDisabled =
    maxAmountQuery.status !== "success" ||
    amountNumber === maxAmountQuery.maxAmount;

  return (
    <View>
      <Row
        gap={10}
        justify={"space-between"}
        style={{ width: "100%", height: 52 }}
      >
        <AnimatedAmountDisplay amount={animatedAmount} />
        <MaxButton
          disabled={maxDisabled}
          onPress={() => {
            if (maxAmountQuery.status === "success") {
              hapticMaxButtonPressed();
              onInstantChange(maxAmountQuery.maxAmount);
            }
          }}
        />
      </Row>
      <Row
        gap={10}
        justify={"space-between"}
        style={{ paddingHorizontal: 2, overflow: "hidden" }}
      >
        <View>
          {usdPrice != null ? (
            <AmountScreenSecondaryText>
              {formatUsdValue(usdPrice * amountNumber)}
            </AmountScreenSecondaryText>
          ) : null}
        </View>

        {maxAmountQuery.status === "pending" ? (
          <ContentSkeleton>
            <AmountScreenSecondaryText>10,000.00</AmountScreenSecondaryText>
          </ContentSkeleton>
        ) : maxAmountQuery.status === "success" ? (
          <LayoutAnimationConfig skipEntering skipExiting>
            <Animated.View
              key={String(maxAmountQuery.maxAmount)}
              entering={TEXT_FADE_IN_DOWN}
              exiting={TEXT_FADE_OUT_UP}
            >
              <AmountScreenSecondaryText>
                {formatTokenAmount(maxAmountQuery.maxAmount, undefined)}
              </AmountScreenSecondaryText>
            </Animated.View>
          </LayoutAnimationConfig>
        ) : (
          <AmountScreenSecondaryText>{""}</AmountScreenSecondaryText>
        )}
      </Row>
      {showSlider ? (
        <View style={{ marginTop: 32 }}>
          <AmountSlider
            amount={animatedAmount}
            maxAmount={
              maxAmountQuery.status === "success" ? maxAmountQuery.maxAmount : 0
            }
            onInstantChange={onInstantChange}
          />
        </View>
      ) : null}
    </View>
  );
});

export function TokenLabel({ token }: { token: TokenMetadata }) {
  return (
    <Row gap={6} style={{ paddingHorizontal: 2 }}>
      <CoinImage
        key={token.logoUri}
        logoUri={token.logoUri}
        symbol={token.symbol}
        size={24}
      />
      <Text variant={"medium"} size={16}>
        {token.symbol}
      </Text>
    </Row>
  );
}

function MaxButton({
  disabled,
  onPress,
}: {
  disabled?: boolean;
  onPress?: () => void;
}) {
  const borderColor = useColor("border");
  const borderColorDisabled = useColor("borderHard");

  const backgroundEnabledColor = useColor("background");
  const backgroundDisabledColor = useColor("backgroundSecondary");

  const disabledValue = useSharedValue(0);
  useEffect(() => {
    disabledValue.value = withTiming(disabled ? 1 : 0, {
      duration: 100,
    });
  }, [disabled]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      backgroundColor: interpolateColor(
        disabledValue.value,
        [1, 0],
        [backgroundDisabledColor, backgroundEnabledColor]
      ),
    };
  });

  const textColorEnabled = useColor("text");
  const textColorDisabled = useColor("textSecondary");
  const textAnimatedStyle = useAnimatedStyle(() => {
    return {
      color: interpolateColor(
        disabledValue.value,
        [1, 0],
        [textColorDisabled, textColorEnabled]
      ),
    };
  });

  return (
    <TouchableScale disabled={disabled} hitSlop={6} onPress={onPress}>
      <Animated.View
        style={[
          {
            height: 28,
            paddingHorizontal: 10,
            borderRadius: 999,
            borderWidth: 1,
            borderColor: disabled ? borderColorDisabled : borderColor,
            justifyContent: "center",
            alignItems: "center",
          },
          animatedStyle,
        ]}
      >
        <Animated.Text
          // variant="heavy"
          // size={16}
          // colorToken={disabled ? "textSecondary" : "text"}
          style={[
            {
              fontSize: 16,
              fontFamily: "SFProDisplayHeavy",
            },
            textAnimatedStyle,
          ]}
        >
          MAX
        </Animated.Text>
      </Animated.View>
    </TouchableScale>
  );
}

export function UseSpendingLimitRow({
  enabled,
  onSwitchChange,
}: {
  enabled: boolean;
  onSwitchChange?: (enabled: boolean) => void;
}) {
  return (
    <SwitchRow
      title="Use Spending Limit"
      enabled={enabled}
      onSwitchChange={onSwitchChange}
    />
  );
}

export function SwitchRow({
  title,
  subtitle,
  enabled,
  onSwitchChange,
}: {
  title: string | ReactNode;
  subtitle?: string | ReactNode;
  enabled: boolean;
  onSwitchChange?: (enabled: boolean) => void;
}) {
  return (
    <Row justify="space-between" style={{ height: 24 }}>
      <AmountScreenSecondaryText>{title}</AmountScreenSecondaryText>
      <Row>
        {subtitle && (
          <AmountScreenSecondaryText>{subtitle}</AmountScreenSecondaryText>
        )}
        <Switch
          onValueChange={onSwitchChange}
          value={enabled}
          style={{ transform: [{ scale: 0.75 }], transformOrigin: "right" }}
        />
      </Row>
    </Row>
  );
}
