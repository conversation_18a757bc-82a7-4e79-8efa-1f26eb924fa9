import { StyleSheet, View as RNView, ViewStyle } from "react-native";
import { useColor, View } from "~/components/Themed";
import Colors from "~/constants/Colors";
import Svg, { Circle } from "react-native-svg";
import { ForwardedRef, forwardRef, ReactNode } from "react";

export const IconWrapper = forwardRef(function IconWrapper(
  {
    backgroundColorToken = "backgroundSecondary",
    background,
    size,
    children,
    style,
    variant = "circle",
    borderRadius = 12,
  }: {
    backgroundColorToken?: keyof typeof Colors.light & keyof typeof Colors.dark;
    background?: string;
    size: number;
    children?: ReactNode;
    style?: ViewStyle;
    variant?: "circle" | "square";
    borderRadius?: number;
  },
  ref: ForwardedRef<RNView>
) {
  const backgroundColor = useColor(backgroundColorToken);

  return (
    <View
      ref={ref}
      style={[
        styles.wrapper,
        {
          width: size,
          backgroundColor: background ?? backgroundColor,
          borderRadius: variant === "circle" ? 9999 : borderRadius,
        },
        style,
      ]}
    >
      {children}
    </View>
  );
});

const styles = StyleSheet.create({
  wrapper: {
    aspectRatio: 1,
    flexShrink: 0,
    justifyContent: "center",
    alignItems: "center",
  },
});

export function IconBorderWrapper({
  size = 44,
  color,
  children,
}: {
  size?: number;
  color?: string;
  children?: ReactNode;
}) {
  return (
    <View
      style={{
        width: size,
        aspectRatio: 1,
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Svg width={size} height={size} viewBox="0 0 44 44" fill="none">
        <Circle
          cx={22}
          cy={22}
          r={21}
          stroke={color ?? "#AAA"}
          strokeWidth={2}
          strokeLinecap="round"
          strokeDasharray="7 7"
        />
      </Svg>
      <View style={{ position: "absolute" }}>{children}</View>
    </View>
  );
}
