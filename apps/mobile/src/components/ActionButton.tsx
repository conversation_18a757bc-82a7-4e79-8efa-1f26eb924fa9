import {
  ComponentProps,
  ForwardedRef,
  forwardRef,
  ReactNode,
  useMemo,
} from "react";
import { SFSymbol as SFSymbolName } from "sf-symbols-typescript";
import { Icon } from "./Icon";
import { Insets, View } from "react-native";
import Colors, { ColorName } from "~/constants/Colors";
import { Text, useColor } from "./Themed";
import { TouchableScale } from "./TouchableScale";
import { BaseButton } from "react-native-gesture-handler";
import Animated, {
  FadeInDown,
  FadeInRight,
  FadeOutLeft,
  FadeOutUp,
  LayoutAnimationConfig,
  ReduceMotion,
} from "react-native-reanimated";
import { DURATION_FAST } from "~/constants/animations";

const AnimatedText = Animated.createAnimatedComponent(Text);

type Variant = "primary" | "secondary";

const backgroundByVariant: Record<Variant, ColorName> = {
  primary: "backgroundButtonPrimary",
  secondary: "backgroundButtonSecondary",
} as const;

const textColorByVariant = {
  primary: "textButtonPrimary",
  secondary: "textButtonSecondary",
} as const;

export const ActionButton = forwardRef(function ActionButton(
  {
    iconName,
    iconWeight = "regular",
    children,
    variant = "primary",
    disabled = false,
    onPress,
    hitSlop,
    animation = "top-down",
    backgroundColorToken,
  }: {
    iconName?: SFSymbolName;
    iconWeight?: ComponentProps<typeof Icon>["weight"];
    children: ReactNode;
    variant?: Variant;
    disabled?: boolean;
    onPress?: () => void;
    hitSlop?: Insets | number;
    animation?: "top-down" | "left-right" | false;
    backgroundColorToken?: keyof (typeof Colors)["light" | "dark"];
  },
  ref: ForwardedRef<typeof BaseButton>
) {
  const textColorToken = textColorByVariant[variant];

  const background = useColor(
    backgroundColorToken ??
      backgroundByVariant[variant] ??
      "backgroundButtonPrimary"
  );

  const iconColor = useColor(textColorToken);

  const animations = useMemo(() => {
    return animation === false
      ? null
      : animation === "top-down"
        ? {
            entering: FadeInDown.duration(DURATION_FAST).reduceMotion(
              ReduceMotion.Never
            ),
            exiting: FadeOutUp.duration(DURATION_FAST).reduceMotion(
              ReduceMotion.Never
            ),
          }
        : {
            entering: FadeInRight.duration(DURATION_FAST).reduceMotion(
              ReduceMotion.Never
            ),
            exiting: FadeOutLeft.duration(DURATION_FAST).reduceMotion(
              ReduceMotion.Never
            ),
          };
  }, [animation]);

  return (
    <TouchableScale
      ref={ref}
      disabled={disabled}
      activeScale={0.95}
      onPress={onPress}
      hitSlop={hitSlop}
    >
      <View
        style={[
          {
            backgroundColor: background,
            paddingHorizontal: 12,
            paddingVertical: 8,
            gap: 10,
            alignItems: "center",
            justifyContent: "center",
            flexDirection: "row",
            borderRadius: 20,
            borderCurve: "continuous",
            opacity: disabled ? 0.5 : 1,
          },
        ]}
      >
        <LayoutAnimationConfig skipEntering skipExiting>
          {iconName ? (
            <Icon
              name={iconName}
              size={12}
              color={iconColor}
              weight={iconWeight}
            />
          ) : null}
          {children && (
            <AnimatedText
              key={String(children)}
              entering={animations?.entering}
              exiting={animations?.exiting}
              style={{ fontSize: 15 }}
              variant={"semibold"}
              colorToken={textColorToken}
            >
              {children}
            </AnimatedText>
          )}
        </LayoutAnimationConfig>
      </View>
    </TouchableScale>
  );
});
