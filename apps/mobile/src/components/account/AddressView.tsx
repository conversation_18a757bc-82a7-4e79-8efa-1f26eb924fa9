import { ReactElement } from "react";
import { Row } from "~/components/Grid";
import { View } from "~/components/Themed";
import { H4 } from "~/components/typography/H4";
import { P3 } from "~/components/typography/P3";

export function AddressView({
  icon,
  title,
  subtitle,
  right,
}: {
  icon: ReactElement<any>;
  title: string;
  subtitle: string | ReactElement<any>;
  right?: ReactElement<any>;
}) {
  return (
    <Row gap={12}>
      {icon}

      <View gap={2} style={{ flexGrow: 1 }}>
        <H4>{title}</H4>
        <View style={{ alignSelf: "flex-start" }}>
          {typeof subtitle === "string" ? (
            <P3 colorToken="textSecondary">{subtitle}</P3>
          ) : (
            subtitle
          )}
        </View>
      </View>

      {right}
    </Row>
  );
}
