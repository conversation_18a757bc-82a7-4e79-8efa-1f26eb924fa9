import { useRef, useState } from "react";
import { BottomModalImperativeMethods } from "~/components/BottomModal";
import { useActiveWallet } from "~/state/wallet";
import { EarnBalance, useEarnBalances } from "~/state/earn";
import { BalanceNumbers } from "~/components/earn/BalanceNumbers";
import { DashedListSeparator } from "~/components/ListSeparator";
import { Text, useColor, View } from "~/components/Themed";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import {
  hapticDismissBottomTray,
  hapticOpenBottomTray,
  hapticOpenModalSheet,
} from "~/utils/haptics";
import { router } from "expo-router";
import { EarnWithdrawRouteParams } from "~/app/unlocked/earn/deposit/withdraw-amount";
import { Icon } from "~/components/Icon";
import {
  ProviderChooserDepositModal,
  ProviderChooserWithdrawModal,
} from "~/components/earn/ProviderChooserModal";
import { ContentSkeleton } from "~/components/Skeleton";
import { formatUsdValue } from "@squads/utils/numberFormats";
import { useLazyHistoricalBalances } from "~/state/balances";
import { calculateEarnedAtPointInTime } from "~/services/balances";
import { DURATION_FAST, ZoomAndFadeOut } from "~/constants/animations";
import { useSubscription } from "~/state/subscription";
import { Row } from "~/components/Grid";
import { FusePlusLogo } from "~/components/icons/FusePlusLogo";
import { IconWrapper } from "~/components/IconWrapper";
import { useSettings } from "~/state/userPreferences";
import { z } from "zod";
import { Image } from "expo-image";
import circleBlur from "~assets/images/circle-blur.png";
import { EARN_PROVIDERS } from "~/services/earn";
import { Address } from "@squads/models/solana";

export function EarnDashboard() {
  return <EarnDashboardInner />;
}

function useEarnings({
  vault,
  totalEarnBalance,
}: {
  vault: Address;
  totalEarnBalance: number;
}) {
  const historicalBalances = useLazyHistoricalBalances({
    vault,
    period: "week",
  });
  if (historicalBalances.status === "pending") {
    return { lifetimeEarned: undefined, last7dEarned: undefined };
  }

  const lifetimeEarned = EARN_PROVIDERS.reduce((acc, provider) => {
    const earned = calculateEarnedAtPointInTime(
      provider,
      "now",
      totalEarnBalance,
      historicalBalances.data ?? []
    );
    return acc + (earned ?? 0);
  }, 0);

  const historical7dEarned = EARN_PROVIDERS.reduce((acc, provider) => {
    const earned = calculateEarnedAtPointInTime(
      provider,
      "7dAgo",
      totalEarnBalance,
      historicalBalances.data ?? []
    );
    return acc + (earned ?? 0);
  }, 0);

  const last7dEarned = lifetimeEarned - historical7dEarned;
  return { lifetimeEarned, last7dEarned };
}

export function EarnDashboardInner() {
  const { wallet } = useActiveWallet();
  const earnBalances = useEarnBalances({ address: wallet.defaultVault });

  const totalEarnBalance = earnBalances.reduce((acc, cur) => {
    return acc + EarnBalance.totalBalanceUsd(cur.assets);
  }, 0);

  const weightedApy = EarnBalance.weightedApy(
    earnBalances.flatMap((b) => b.assets)
  );

  const { lifetimeEarned, last7dEarned } = useEarnings({
    totalEarnBalance,
    vault: wallet.defaultVault,
  });

  const providerChooserWithdrawModalRef =
    useRef<BottomModalImperativeMethods>(null);
  const providerChooserDepositModalRef =
    useRef<BottomModalImperativeMethods>(null);
  const [showProviderOrTokenPicker, setShowProviderOrTokenPicker] = useState<
    "provider" | "token"
  >("provider");

  return (
    <>
      <View
        gap={18}
        background="backgroundBanner"
        style={{
          padding: 24,
          borderRadius: 24,
          borderCurve: "continuous",
          overflow: "hidden",
        }}
      >
        {totalEarnBalance > 0 && (
          <View gap={14}>
            <BalanceNumbers
              balanceSize={"medium"}
              totalEarnBalance={totalEarnBalance}
              weightedApy={weightedApy}
            />
            <DashedListSeparator />
          </View>
        )}
        <View>
          <EarnedPill
            lifetimeEarned={lifetimeEarned}
            last7dEarned={last7dEarned}
          />
        </View>
        <View
          style={{
            backgroundColor: "rgba(243,243,243,0.8)",
            borderRadius: 16,
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
            marginHorizontal: -8,
            marginBottom: -8,
          }}
        >
          <AnimatedTouchableScale
            style={{
              flex: 1,
              paddingVertical: 15,
              paddingHorizontal: 4,
            }}
            onPress={() => {
              hapticOpenBottomTray();
              providerChooserDepositModalRef.current?.present();
            }}
          >
            <View
              style={{
                flexDirection: "row",
                justifyContent: "center",
                gap: 4,
              }}
            >
              <Text variant="medium">􀁍</Text>
              <Text variant="medium">Deposit</Text>
            </View>
          </AnimatedTouchableScale>
          <VerticalSeparator />
          <AnimatedTouchableScale
            style={{
              flex: 1,
              paddingVertical: 15,
              paddingHorizontal: 4,
            }}
            onPress={() => {
              hapticOpenBottomTray();

              if (earnBalances.length !== 1) {
                setShowProviderOrTokenPicker("provider");
                providerChooserWithdrawModalRef.current?.present();
                return;
              }

              const oneProvider = earnBalances[0];
              if (oneProvider.assets.length !== 1) {
                setShowProviderOrTokenPicker("token");
                providerChooserWithdrawModalRef.current?.present();
                return;
              }

              const oneMint = oneProvider.assets[0].mint;
              router.push({
                pathname: "/unlocked/earn/deposit/withdraw-amount",
                params: {
                  provider: oneProvider.provider,
                  earnMint: oneMint,
                } satisfies EarnWithdrawRouteParams,
              });
            }}
          >
            <View
              style={{
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "baseline",
                gap: 8,
              }}
            >
              <Icon name="arrow.up.circle.fill" size={11} />
              <Text variant="medium">Withdraw</Text>
            </View>
          </AnimatedTouchableScale>
        </View>

        <EarnFusePlusBanner />
      </View>

      <ProviderChooserWithdrawModal
        modalRef={providerChooserWithdrawModalRef}
        initialStep={
          showProviderOrTokenPicker === "provider"
            ? "provider-picker"
            : "drift-token-picker"
        }
        onDismiss={() => setShowProviderOrTokenPicker("provider")}
      />
      <ProviderChooserDepositModal modalRef={providerChooserDepositModalRef} />
    </>
  );
}

function EarnedPill({
  lifetimeEarned,
  last7dEarned,
}: {
  lifetimeEarned: number | undefined;
  last7dEarned: number | undefined;
}) {
  return (
    <View style={{ flexDirection: "row", alignItems: "center", gap: 12 }}>
      <View style={[{ gap: 4, flex: 1 }]}>
        <Text colorToken="textSecondaryV2" variant="medium" size={14}>
          Lifetime Earned
        </Text>
        {lifetimeEarned === undefined ? (
          <ContentSkeleton>
            <Text colorToken="text" variant="semibold" size={14}>
              $123.45
            </Text>
          </ContentSkeleton>
        ) : (
          <Text colorToken="text" variant="semibold" size={14}>
            {formatUsdValue(lifetimeEarned < 0.01 ? 0 : lifetimeEarned)}
          </Text>
        )}
      </View>

      <View style={[{ gap: 4, flex: 1 }, { alignItems: "flex-end" }]}>
        <Text colorToken="textSecondaryV2" variant="medium" size={14}>
          Last 7D
        </Text>
        {last7dEarned === undefined ? (
          <ContentSkeleton>
            <Text colorToken="text" variant="semibold" size={14}>
              $123.45
            </Text>
          </ContentSkeleton>
        ) : (
          <Text colorToken="text" variant="semibold" size={14}>
            {formatUsdValue(last7dEarned < 0.01 ? 0 : last7dEarned)}
          </Text>
        )}
      </View>
    </View>
  );
}

function VerticalSeparator() {
  return (
    <View
      style={{
        height: 20,
        width: 1,
        backgroundColor: "#141414",
        opacity: 0.1,
      }}
    />
  );
}

const ExitAnimation = ZoomAndFadeOut.duration(DURATION_FAST).withEndValues({
  opacity: 0,
  scale: 0.7,
});

export function EarnFusePlusBanner() {
  const backgroundButtonPrimary = useColor("backgroundButtonPrimary");
  const [dismissed, setDismissed] = useSettings(
    "EARN_FUSE_PLUS_BANNER_DISMISSED",
    z.coerce.boolean(),
    false
  );

  const subscriptionQuery = useSubscription();

  if (
    !subscriptionQuery.data ||
    subscriptionQuery.data.status === "active" ||
    dismissed
  ) {
    return null;
  }

  return (
    <AnimatedTouchableScale
      pressedScale={1}
      exiting={ExitAnimation}
      onPress={() => {
        hapticOpenModalSheet();
        router.push("/unlocked/subscription/promo");
      }}
      style={{
        backgroundColor: backgroundButtonPrimary,
        paddingHorizontal: 24,
        paddingVertical: 20,

        marginTop: 4,
        marginHorizontal: -24,
        marginBottom: -24,

        borderCurve: "continuous",
        gap: 14,
      }}
    >
      <Row gap={12}>
        <FusePlusLogo size={12} />
        <Text variant={"medium"} size={14} colorToken={"textButtonPrimary"}>
          Get Fuse Plus{" "}
        </Text>
      </Row>
      <DashedListSeparator colorToken={"dashedListSeparatorOpposite"} />
      <Row justify={"space-between"}>
        <Text variant={"medium"} size={14} colorToken={"textSecondaryOpposite"}>
          Deposit $100+ in Earn{"\n"}
          to activate Fuse Plus.
        </Text>
        <IconWrapper size={24} background={"#181718"}>
          <Icon name={"info"} colorToken={"textButtonPrimary"} size={12} />
        </IconWrapper>
      </Row>
      <AnimatedTouchableScale
        hitSlop={8}
        onPress={() => {
          hapticDismissBottomTray();
          setDismissed(true);
        }}
        style={{
          position: "absolute",
          top: 16,
          right: 24,
        }}
      >
        <Icon
          name={"xmark"}
          weight="bold"
          size={10}
          rectSize={24}
          colorToken="textSecondaryOpposite"
        />
      </AnimatedTouchableScale>

      {/* Bottom background gradient */}
      <View
        style={{
          position: "absolute",
          bottom: 0,
          width: "100%",
          height: 80,
          alignSelf: "center",
          overflow: "hidden",
        }}
      >
        <Image
          source={circleBlur}
          style={{
            aspectRatio: 1,
            width: "100%",
            opacity: 0.28,
          }}
        ></Image>
      </View>
    </AnimatedTouchableScale>
  );
}
