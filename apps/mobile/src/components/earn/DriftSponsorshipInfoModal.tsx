import { RefObject } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import * as Haptics from "expo-haptics";
import { Text, View } from "~/components/Themed";
import { P3 } from "~/components/typography/P3";
import { Button } from "~/components/Button";
import { ImageBackground } from "expo-image";
import { FuseLogo } from "~/components/icons/FuseLogo";
import { IconWrapper } from "~/components/IconWrapper";
import { DriftCircleIcon } from "~/components/icons/earn/DriftCircleIcon";
import { PairOfIcons } from "~/components/icons/PairOfIcons";

export function DriftSponsorshipInfoModal({
  modalRef,
  onAccept,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
  onAccept: () => void;
}) {
  function hide() {
    modalRef.current?.close();
  }

  return (
    <BottomModal
      modalRef={modalRef}
      onClose={hide}
      onDismiss={hide}
      modalId="Drift-Sponsorship-Info"
      body={() => {
        return (
          <View gap={16}>
            <DriftFuseImage />
            <View gap={8}>
              <Text
                variant={"semibold"}
                style={{ fontSize: 20, textAlign: "center" }}
              >
                Free Drift Account Creation
              </Text>
              <P3 colorToken="textSecondary" style={{ textAlign: "center" }}>
                We've partnered with Drift to sponsor account creation for the
                first 1,000 deposits with Fuse Earn. Secure your free account
                and start earning yield today.
              </P3>
            </View>
          </View>
        );
      }}
      footer={
        <Button
          variant="secondary"
          onPress={() => {
            Haptics.selectionAsync();
            modalRef.current?.close();
            onAccept();
          }}
        >
          Got it
        </Button>
      }
    />
  );
}

export function DriftFuseImage() {
  return (
    <View>
      <ImageBackground
        source={require("../../../assets/images/earn/drift-sponsorship-bg.png")}
        style={{
          flex: 1,
          aspectRatio: 1.8,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <PairOfIcons
          animated
          icon1={(size) => (
            <IconWrapper
              size={size}
              background={"#000"}
              style={{ borderWidth: 2, borderColor: "#fff" }}
            >
              <DriftCircleIcon size={size} />
            </IconWrapper>
          )}
          icon2={(size) => (
            <IconWrapper
              size={size}
              background={"#000"}
              style={{ borderWidth: 2, borderColor: "#fff" }}
            >
              <FuseLogo size={size / 3} color={"#fff"} />
            </IconWrapper>
          )}
        />
      </ImageBackground>
    </View>
  );
}
