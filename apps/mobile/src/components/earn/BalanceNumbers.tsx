import { Text, View } from "~/components/Themed";
import { useActiveWallet } from "~/state/wallet";
import { formatPercent } from "~/vendor/squads/utils/numberFormats";
import { Balance, BalanceProps } from "../Balance";

export function BalanceNumbers({
  totalEarnBalance,
  weightedApy,
  balanceSize = "large",
}: {
  totalEarnBalance: number;
  weightedApy: number;
  balanceSize?: BalanceProps["size"];
}) {
  const { wallet } = useActiveWallet();

  return (
    <View style={{ gap: 8 }}>
      <View style={{ flexDirection: "row", alignItems: "center" }}>
        <Text colorToken="textSecondaryV2" variant="medium">
          Balance ⋅{" "}
        </Text>
        <Text colorToken="systemGreen" variant="semibold" size={12}>
          APY {formatPercent(weightedApy)}%
        </Text>
      </View>
      <View style={{ flexDirection: "row", alignItems: "center" }}>
        <Balance
          size={balanceSize}
          address={wallet.defaultVault}
          valueOverride={totalEarnBalance}
        />
      </View>
    </View>
  );
}
