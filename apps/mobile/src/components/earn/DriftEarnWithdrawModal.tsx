import * as Haptics from "expo-haptics";
import { router } from "expo-router";
import { Address } from "@squads/models/solana";
import { RefObject } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { Row } from "~/components/Grid";
import { mints } from "~/constants/tokens";
import { CoinLogo } from "~/components/CoinLogo";
import { EarnWithdrawRouteParams } from "~/app/unlocked/earn/deposit/withdraw-amount";
import { AnimatedTouchableScale } from "../AnimatedTouchableScale";
import {
  formatPercent,
  formatUsdValue,
} from "~/vendor/squads/utils/numberFormats";
import { LendingApy } from "~/services/earn";
import { useSuspenseDriftSpotMarkets } from "~/state/earn";
import { EarnProviderIcon } from "./EarnProviderIcon";
import Colors from "~/constants/Colors";
import { TextFadeCents } from "../typography/TextFadeCents";
import { useBalanceSettings } from "~/state/balanceSettings";

export function DriftWithdrawModal({
  balances,
  modalRef,
}: {
  balances: Record<Address, number>;
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  function hide() {
    modalRef.current?.close();
  }

  async function onSelect(earnMint: Address) {
    modalRef.current?.close();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    router.push({
      pathname: "/unlocked/earn/deposit/withdraw-amount",
      params: { earnMint, provider: "drift" } satisfies EarnWithdrawRouteParams,
    });
  }

  const options = [
    { mint: mints.usdc, symbol: "USDC" },
    { mint: mints.pyusd, symbol: "PYUSD" },
    { mint: mints.usds, symbol: "USDS" },
  ];

  return (
    <BottomModal
      modalRef={modalRef}
      onClose={hide}
      onDismiss={hide}
      modalId="withdraw-drift"
      title=" "
      body={() => {
        return (
          <View gap={10}>
            <View style={{ alignItems: "center", marginBottom: 14 }} gap={4}>
              <View style={{ marginBottom: 16 }}>
                <EarnProviderIcon provider="drift" size={54} />
                <View
                  style={{
                    position: "absolute",
                    top: -6,
                    right: -6,
                    width: 20,
                    height: 20,
                    backgroundColor: Colors.light.backgroundSecondary,
                    borderRadius: 9999,
                    borderWidth: 2,
                    borderColor: Colors.light.backgroundBottomSheet,
                    alignItems: "center",
                    justifyContent: "center",
                    transform: [{ rotate: "180deg" }],
                  }}
                >
                  <Text variant="bold" size={7.4}>
                    􀄩
                  </Text>
                </View>
              </View>
              <Text variant="semibold" size={16}>
                Lending Yield Withdrawal
              </Text>
              <Text variant="semibold" style={{ opacity: 0.3 }}>
                Choose token
              </Text>
            </View>
            {options.map(({ mint, symbol }) => {
              return (
                <TokenTile
                  key={mint}
                  mint={mint}
                  balance={balances[mint]}
                  symbol={symbol}
                  onPress={() => onSelect(mint)}
                />
              );
            })}
          </View>
        );
      }}
    />
  );
}

export function TokenTile({
  mint,
  symbol,
  balance,
  onPress,
}: {
  mint: Address;
  symbol: string;
  balance: number;
  onPress: () => void;
}) {
  const { spotMarkets } = useSuspenseDriftSpotMarkets();
  const { displayBalance } = useBalanceSettings();

  const disabled = (balance ?? 0) === 0;
  const spotMarket = spotMarkets.find((spotMarket) => spotMarket.mint === mint);

  const totalAmount = (balance ?? 0) / 10 ** 6;
  if (totalAmount === 0) return null;

  return (
    <AnimatedTouchableScale disabled={disabled} onPress={onPress} key={mint}>
      <Row
        style={{
          justifyContent: "space-between",
          backgroundColor: "white",
          padding: 16,
          borderRadius: 18,
        }}
      >
        <Row gap={10} style={{ opacity: disabled ? 0.5 : 1 }}>
          <CoinLogo mint={mint} size={24} />
          <View>
            <Row>
              <Text colorToken="systemGreen" size={12}>
                {spotMarket
                  ? formatPercent(LendingApy.total(spotMarket.apy)) + "%"
                  : "N/A"}
              </Text>
              <Text size={12} colorToken="textSecondary">
                {" "}
                APY
              </Text>
            </Row>
            <Text variant="semibold">{symbol}</Text>
          </View>
        </Row>
        <TextFadeCents size={14} variant="semibold">
          {displayBalance(formatUsdValue(totalAmount))}
        </TextFadeCents>
      </Row>
    </AnimatedTouchableScale>
  );
}
