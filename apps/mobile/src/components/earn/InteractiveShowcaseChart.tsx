import { View, Text } from "~/components/Themed";
import { Row } from "~/components/Grid";
import { S2 } from "~/components/typography/S2";
import { P2 } from "~/components/typography/P2";
import { formatPercent, formatUsdValue } from "@squads/utils/numberFormats";
import { IconWrapper } from "~/components/IconWrapper";
import { Icon } from "~/components/Icon";
import { useState } from "react";
import { LinearGradient } from "expo-linear-gradient";
import { hexToRgba } from "~/constants/Colors";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import { clamp, runOnJS, useSharedValue } from "react-native-reanimated";
import * as Haptics from "expo-haptics";
import { DateTime } from "luxon";
import { useBalanceSettings } from "~/state/balanceSettings";
import { H4 } from "../typography/H4";
import { mints } from "~/constants/tokens";
import { CoinLogo } from "../CoinLogo";
import { TextFadeCents } from "../typography/TextFadeCents";

const heights = [6, 13, 23, 32, 42, 51, 61, 70, 79, 88, 97, 107];
const lastBarIndex = heights.length - 1;

type EarnChartData = { apy: number; usdBalance: number };

export function InteractiveShowcaseChart({ data }: { data: EarnChartData }) {
  const [selectedIndex, setSelectedIndex] = useState(lastBarIndex);
  const [width, setWidth] = useState(0);

  const barWidth = (width - 11 * 4) / 12;

  const now = DateTime.now();

  //a = p(1+r/n)^nt
  //A  is the amount of money accumulated after  t  years, including interest.
  //P  is the principal amount (initial investment).
  //r  is the annual interest rate (decimal).
  //n  is the number of times that interest is compounded per year.
  //t  is the time the money is invested for in years.
  const currentBalance = data.usdBalance;
  const days = 30.4 * (selectedIndex + 1);
  const projectedBalance =
    currentBalance * (1 + data.apy / (365 * 24)) ** (days * 24);

  const currentDate = now.year;
  const projectedDate =
    selectedIndex === lastBarIndex
      ? now.year + 1
      : now.plus({ months: selectedIndex + 1 }).toFormat("MMM yyyy");

  const spacerWidth = (11 - selectedIndex) * (barWidth + 4);

  const { displayBalance } = useBalanceSettings();

  return (
    <View
      gap={16}
      onLayout={(e) => setWidth(e.nativeEvent.layout.width)}
      style={{
        borderWidth: 1,
        borderRadius: 24,
        borderStyle: "dashed",
        borderColor: "rgba(0, 0, 0, 0.1)",
        padding: 24,
      }}
    >
      <H4 style={{ textAlign: "center" }}>See how it works</H4>

      <View gap={8} style={{ alignItems: "center" }}>
        <Text
          colorToken="textSecondary"
          style={{ textAlign: "center" }}
          variant="medium"
        >
          You Deposit
        </Text>
        <Row gap={4} style={{ justifyContent: "center" }}>
          <CoinLogo mint={mints.usdc} size={20} />
          <TextFadeCents style={{ fontSize: 24 }} variant="bold">
            {formatUsdValue(data.usdBalance)}
          </TextFadeCents>
        </Row>
        <View
          style={{
            gap: 4,
            flexDirection: "row",
            alignItems: "center",
            backgroundColor: "rgba(0, 122, 255, 0.10)",
            borderRadius: 421,
            borderColor: "rgba(0, 122, 255, 0.20)",
            borderWidth: 1,
            paddingHorizontal: 10,
            paddingVertical: 4,
          }}
        >
          <Text style={{ color: "#007AFF" }} variant="medium">
            Simulated Yield
          </Text>
          <Text style={{ color: "#007AFF" }} variant="semibold">
            {formatPercent(data.apy)}%
          </Text>
        </View>
      </View>

      <Row gap={20} style={{ marginVertical: 8 }}>
        <View
          style={{
            height: 1,
            width: 3,
            backgroundColor: "rgba(20, 20, 20, 0.06)",
            flex: 1,
          }}
        />
        <Text variant="medium" colorToken="textSecondary">
          Your Potential Earn
        </Text>
        <View
          style={{
            height: 1,
            width: 3,
            backgroundColor: "rgba(20, 20, 20, 0.06)",
            flex: 1,
          }}
        />
      </Row>

      <Row>
        <View gap={4}>
          <S2 colorToken={"textSecondary"}>{currentDate}</S2>
          <P2 style={{ opacity: 0.4 }}>
            {displayBalance(formatUsdValue(currentBalance))}
          </P2>
        </View>
        <View
          gap={4}
          style={{ flexGrow: 1, alignItems: "flex-end", marginLeft: 12 }}
        >
          <S2 colorToken="textSecondary">{projectedDate}</S2>
          <Row gap={4}>
            <P2 style={{ color: "#007AFF" }}>
              {displayBalance(formatUsdValue(projectedBalance))}
            </P2>
            <IconWrapper
              size={16}
              style={{ borderRadius: 6 }}
              background="#007AFF"
            >
              <Icon
                name="arrow.up"
                size={7}
                weight="bold"
                colorToken="textButtonPrimary"
              />
            </IconWrapper>
          </Row>
        </View>
        <View style={{ width: spacerWidth }} />
      </Row>

      <EarnChartBars
        selected={selectedIndex}
        onSelect={(index) => {
          Haptics.selectionAsync();
          setSelectedIndex(index);
        }}
      />
    </View>
  );
}

export function EarnChartBars({
  selected,
  onSelect,
}: {
  selected: number;
  onSelect: (index: number) => void;
}) {
  const width = useSharedValue(0);

  const pan = Gesture.Pan()
    .onBegin((event) => {
      runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Medium);
      const index = clamp(
        Math.floor((event.x / width.value) * heights.length),
        0,
        lastBarIndex
      );
      if (index === selected) return;
      runOnJS(onSelect)(index);
    })
    .onUpdate((event) => {
      const index = clamp(
        Math.floor((event.x / width.value) * heights.length),
        0,
        lastBarIndex
      );
      if (index === selected) return;
      runOnJS(onSelect)(index);
    })
    .onFinalize(() => {
      runOnJS(onSelect)(heights.length - 1);
    });

  return (
    <View onLayout={(e) => (width.value = e.nativeEvent.layout.width)}>
      <GestureDetector gesture={pan}>
        <Row collapsable={false} gap={4} style={{ alignItems: "flex-end" }}>
          {Array.from({ length: heights.length }).map((_, index) => (
            <ChartBar
              key={index}
              index={index}
              selected={selected === index}
              empty={index > selected}
            />
          ))}
        </Row>
      </GestureDetector>
    </View>
  );
}

function ChartBar({
  index,
  selected,
  empty,
}: {
  index: number;
  selected: boolean;
  empty: boolean;
}) {
  const height = heights[index];

  const style = {
    flex: 1,
    borderRadius: 7,
    height,
  };

  if (selected) {
    return (
      <View style={[style]}>
        <LinearGradient
          style={style}
          colors={[hexToRgba("#007AFF", 0.9), hexToRgba("#007AFF", 0.7)]}
        />
      </View>
    );
  }

  if (empty) {
    return (
      <View
        style={[
          style,
          {
            borderWidth: 1,
            borderColor: hexToRgba("#1F1E1E", 0.1),
            borderStyle: "dashed",
          },
        ]}
      />
    );
  }

  return (
    <View
      style={[
        style,
        { padding: 1, backgroundColor: hexToRgba("#007AFF", 0.1) },
      ]}
    />
  );
}
