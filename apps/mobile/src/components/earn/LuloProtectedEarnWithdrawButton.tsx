import { router } from "expo-router";
import { Button } from "~/components/Button";
import { useLock } from "~/hooks/useLock";
import { mints } from "~/constants/tokens";
import { EarnWithdrawRouteParams } from "~/app/unlocked/earn/deposit/withdraw-amount";
import { useIsWalletInactive } from "~/hooks/useAssertWalletState";
import { hapticOpenModalSheet } from "~/utils/haptics";
import { useActiveWallet } from "~/state/wallet";
import { useBalances } from "~/state/balances";

export function LuloProtectedEarnWithdrawButton() {
  const withLock = useLock();

  const isWalletInactive = useIsWalletInactive();

  const { wallet } = useActiveWallet();
  const balances = useBalances({ address: wallet.defaultVault });
  const protectedEarnUsdcBalance =
    balances.find((balance) => balance.mint === mints.usdc)?.luloProtectedEarn
      ?.total ?? 0;

  return (
    <>
      <Button
        disabled={protectedEarnUsdcBalance === 0}
        variant="secondary"
        size={"medium-new"}
        onPress={withLock(async () => {
          if (await isWalletInactive()) {
            return;
          }

          hapticOpenModalSheet();
          router.push({
            pathname: "/unlocked/earn/deposit/withdraw-amount",
            params: {
              earnMint: mints.usdc,
              provider: "lulo",
            } satisfies EarnWithdrawRouteParams,
          });
        })}
      >
        Withdraw
      </Button>
    </>
  );
}
