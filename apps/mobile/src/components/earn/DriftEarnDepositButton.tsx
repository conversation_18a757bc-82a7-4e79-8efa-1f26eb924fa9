import * as Haptics from "expo-haptics";
import { Link, router } from "expo-router";
import { Button } from "~/components/Button";
import { Address } from "@squads/models/solana";
import { RefObject, useRef, useState } from "react";
import { useLock } from "~/hooks/useLock";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { View, Text } from "~/components/Themed";
import { Row } from "~/components/Grid";
import { mints } from "~/constants/tokens";
import { CoinLogo } from "~/components/CoinLogo";
import { EarnDepositRouteParams } from "~/app/unlocked/earn/deposit/deposit-amount";
import { useBalances } from "~/state/balances";
import { useActiveWallet } from "~/state/wallet";
import { Settings } from "react-native";
import { Checkbox } from "~/components/Checkbox";
import { P3 } from "~/components/typography/P3";
import { useIsWalletInactive } from "~/hooks/useAssertWalletState";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import Colors from "~/constants/Colors";
import { useSuspenseDriftSpotMarkets } from "~/state/earn";
import { formatPercent } from "~/vendor/squads/utils/numberFormats";
import { LendingApy } from "~/services/earn";
import { EarnProviderIcon } from "~/components/earn/EarnProviderIcon";

export function DriftEarnDepositButton() {
  const withLock = useLock();
  const modalRef = useRef<BottomModalImperativeMethods>(null);

  const isWalletInactive = useIsWalletInactive();

  const { wallet } = useActiveWallet();
  const balances = useBalances({ address: wallet.defaultVault });
  const usdcBalance =
    balances.find((balance) => balance.mint === mints.usdc)?.amount ?? 0;
  const pyusdBalance =
    balances.find((balance) => balance.mint === mints.pyusd)?.amount ?? 0;
  const usdsBalance =
    balances.find((balance) => balance.mint === mints.usds)?.amount ?? 0;

  return (
    <>
      <Button
        size={"medium-new"}
        disabled={usdcBalance === 0 && pyusdBalance === 0 && usdsBalance === 0}
        onPress={withLock(async () => {
          if (await isWalletInactive()) {
            return;
          }

          Haptics.selectionAsync();
          modalRef.current?.present();
        })}
      >
        Deposit
      </Button>

      <DriftDepositModal
        modalRef={modalRef}
        balances={{
          [mints.usdc]: usdcBalance,
          [mints.pyusd]: pyusdBalance,
          [mints.usds]: usdsBalance,
        }}
      />
    </>
  );
}

export function DriftDepositModal({
  balances,
  modalRef,
}: {
  balances: Record<string, number>;
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  const { spotMarkets } = useSuspenseDriftSpotMarkets();

  function hide() {
    modalRef.current?.close();
  }

  const [fuseTermsChecked, setFuseTermsChecked] = useState(
    Settings.get("EARN_FUSE_TERMS_ACCEPTED") ?? false
  );
  const [driftTermsChecked, setDriftTermsChecked] = useState(
    Settings.get("EARN_DRIFT_TERMS_ACCEPTED") ?? false
  );
  const [termsAccepted, setTermsAccepted] = useState(
    fuseTermsChecked && driftTermsChecked
  );

  function acceptTerms() {
    Settings.set({ EARN_FUSE_TERMS_ACCEPTED: true });
    Settings.set({ EARN_DRIFT_TERMS_ACCEPTED: true });

    setFuseTermsChecked(true);
    setDriftTermsChecked(true);
    setTermsAccepted(true);
  }

  async function onSelect(earnMint: Address) {
    modalRef.current?.close();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    router.push({
      pathname: "/unlocked/earn/deposit/deposit-amount",
      params: {
        provider: "drift",
        earnMint,
      } satisfies EarnDepositRouteParams,
    });
  }

  const options = [
    { mint: mints.usdc, symbol: "USDC" },
    { mint: mints.pyusd, symbol: "PYUSD" },
    { mint: mints.usds, symbol: "USDS" },
  ];

  return (
    <BottomModal
      modalRef={modalRef}
      onClose={hide}
      onDismiss={hide}
      modalId="deposit-drift-terms"
      title=" "
      body={() => {
        if (!termsAccepted) {
          return (
            <View gap={12}>
              <View style={{ alignItems: "center", marginBottom: 14 }} gap={4}>
                <View style={{ marginBottom: 16 }}>
                  <EarnProviderIcon provider="drift" size={54} />
                  <View
                    style={{
                      position: "absolute",
                      top: -6,
                      right: -6,
                      width: 20,
                      height: 20,
                      backgroundColor: Colors.light.backgroundSecondary,
                      borderRadius: 9999,
                      borderWidth: 2,
                      borderColor: Colors.light.backgroundBottomSheet,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <Text variant="bold" size={7.4}>
                      􀄩
                    </Text>
                  </View>
                </View>
                <Text variant="semibold" size={16}>
                  Lending Yield Deposit
                </Text>
              </View>
              <Checkbox
                value={fuseTermsChecked}
                onChange={(checked) => {
                  if (checked) {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Rigid);
                  } else {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Soft);
                  }

                  setFuseTermsChecked(checked);
                }}
              >
                <P3 colorToken="textSecondary">
                  I agree to{" "}
                  <Link
                    href="https://fusewallet.com/legal/terms-of-service"
                    target="_blank"
                  >
                    <P3 style={{ textDecorationLine: "underline" }}>
                      Fuse Terms of Service
                    </P3>
                  </Link>
                </P3>
              </Checkbox>
              <Checkbox
                value={driftTermsChecked}
                onChange={(checked) => {
                  if (checked) {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Rigid);
                  } else {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Soft);
                  }
                  setDriftTermsChecked(checked);
                }}
              >
                <P3 colorToken="textSecondary">
                  I understand that this is a third party integration subject to
                  the applicable{" "}
                  <Link
                    href="https://docs.drift.trade/legal-and-regulations/terms-of-use"
                    target="_blank"
                  >
                    <P3 style={{ textDecorationLine: "underline" }}>
                      third party terms
                    </P3>
                  </Link>
                </P3>
              </Checkbox>
            </View>
          );
        }

        return (
          <View gap={10}>
            <View style={{ alignItems: "center", marginBottom: 14 }} gap={4}>
              <View style={{ marginBottom: 16 }}>
                <EarnProviderIcon provider="drift" size={54} />
                <View
                  style={{
                    position: "absolute",
                    top: -6,
                    right: -6,
                    width: 20,
                    height: 20,
                    backgroundColor: Colors.light.backgroundSecondary,
                    borderRadius: 9999,
                    borderWidth: 2,
                    borderColor: Colors.light.backgroundBottomSheet,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Text variant="bold" size={7.4}>
                    􀄩
                  </Text>
                </View>
              </View>
              <Text variant="semibold" size={16}>
                Lending Yield Deposit
              </Text>
              <Text variant="semibold" style={{ opacity: 0.3 }}>
                Choose token
              </Text>
            </View>
            {options.map(({ mint, symbol }) => {
              const disabled = (balances[mint] ?? 0) === 0;
              const spotMarket = spotMarkets.find(
                (spotMarket) => spotMarket.mint === mint
              );
              return (
                <AnimatedTouchableScale
                  disabled={disabled}
                  onPress={() => onSelect(mint)}
                  key={mint}
                >
                  <Row
                    style={{
                      justifyContent: "space-between",
                      backgroundColor: "white",
                      padding: 16,
                      borderRadius: 18,
                    }}
                  >
                    <Row gap={10} style={{ opacity: disabled ? 0.5 : 1 }}>
                      <CoinLogo mint={mint} size={24} />
                      <View>
                        <Row>
                          <Text colorToken="systemGreen" size={12}>
                            {spotMarket
                              ? formatPercent(
                                  LendingApy.total(spotMarket.apy)
                                ) + "%"
                              : "N/A"}
                          </Text>
                          <Text size={12} colorToken="textSecondary">
                            {" "}
                            APY
                          </Text>
                        </Row>
                        <Text variant="semibold">{symbol}</Text>
                      </View>
                    </Row>
                  </Row>
                </AnimatedTouchableScale>
              );
            })}
          </View>
        );
      }}
      footer={
        !termsAccepted && (
          <Button
            disabled={!fuseTermsChecked || !driftTermsChecked}
            onPress={() => {
              Haptics.selectionAsync();
              acceptTerms();
            }}
          >
            Agree and continue
          </Button>
        )
      }
    />
  );
}
