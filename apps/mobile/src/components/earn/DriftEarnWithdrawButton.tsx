import * as Haptics from "expo-haptics";
import { Button } from "~/components/Button";
import { useRef } from "react";
import { useLock } from "~/hooks/useLock";
import { BottomModalImperativeMethods } from "~/components/BottomModal";
import { mints } from "~/constants/tokens";
import { useActiveWallet } from "~/state/wallet";
import { useBalances } from "~/state/balances";
import { useIsWalletInactive } from "~/hooks/useAssertWalletState";
import { DriftWithdrawModal } from "./DriftEarnWithdrawModal";
import { useRouter } from "expo-router";
import { EarnWithdrawRouteParams } from "~/app/unlocked/earn/deposit/withdraw-amount";
import { Address } from "~/vendor/squads/models/solana";

export function DriftEarnWithdrawButton() {
  const withLock = useLock();
  const modalRef = useRef<BottomModalImperativeMethods>(null);
  const router = useRouter();

  const isWalletInactive = useIsWalletInactive();

  const { wallet } = useActiveWallet();
  const balances = useBalances({ address: wallet.defaultVault });
  const earnUsdcBalance =
    balances.find((balance) => balance.mint === mints.usdc)?.driftEarn?.total ??
    0;
  const earnPyusdBalance =
    balances.find((balance) => balance.mint === mints.pyusd)?.driftEarn
      ?.total ?? 0;
  const earnUsdsBalance =
    balances.find((balance) => balance.mint === mints.usds)?.driftEarn?.total ??
    0;

  return (
    <>
      <Button
        disabled={
          earnUsdcBalance === 0 &&
          earnPyusdBalance === 0 &&
          earnUsdsBalance === 0
        }
        variant="secondary"
        size="medium-new"
        onPress={withLock(async () => {
          if (await isWalletInactive()) {
            return;
          }

          Haptics.selectionAsync();

          const driftEarnBalances = {
            [mints.usdc]: earnUsdcBalance,
            [mints.pyusd]: earnPyusdBalance,
            [mints.usds]: earnUsdsBalance,
          };

          const tokensWithBalance = (
            Object.entries(driftEarnBalances) as [Address, number][]
          ).filter(([, balance]) => balance > 0);

          if (tokensWithBalance.length === 1) {
            const [mint] = tokensWithBalance[0];
            router.push({
              pathname: "/unlocked/earn/deposit/withdraw-amount",
              params: {
                provider: "drift",
                earnMint: mint,
              } satisfies EarnWithdrawRouteParams,
            });
          } else {
            modalRef.current?.present();
          }
        })}
      >
        Withdraw
      </Button>

      <DriftWithdrawModal
        modalRef={modalRef}
        balances={{
          [mints.usdc]: earnUsdcBalance,
          [mints.pyusd]: earnPyusdBalance,
          [mints.usds]: earnUsdsBalance,
        }}
      />
    </>
  );
}
