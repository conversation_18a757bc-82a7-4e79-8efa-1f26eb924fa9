import { router } from "expo-router";
import { Button } from "~/components/Button";
import { useLock } from "~/hooks/useLock";
import { mints } from "~/constants/tokens";
import { EarnWithdrawRouteParams } from "~/app/unlocked/earn/deposit/withdraw-amount";
import { useIsWalletInactive } from "~/hooks/useAssertWalletState";
import { hapticOpenModalSheet } from "~/utils/haptics";
import { useActiveWallet } from "~/state/wallet";
import { useBalances } from "~/state/balances";

export function KaminoEarnWithdrawButton() {
  const withLock = useLock();

  const isWalletInactive = useIsWalletInactive();

  const { wallet } = useActiveWallet();
  const balances = useBalances({ address: wallet.defaultVault });
  const usdcBalance = balances.find((balance) => balance.mint === mints.usdc);

  const earnUsdcBalance = usdcBalance?.kaminoEarn?.total ?? 0;

  return (
    <>
      <Button
        disabled={earnUsdcBalance === 0}
        variant="secondary"
        size={"medium-new"}
        onPress={withLock(async () => {
          if (await isWalletInactive()) {
            return;
          }

          hapticOpenModalSheet();
          router.push({
            pathname: "/unlocked/earn/deposit/withdraw-amount",
            params: {
              earnMint: mints.usdc,
              provider: "kamino",
            } satisfies EarnWithdrawRouteParams,
          });
        })}
      >
        Withdraw
      </Button>
    </>
  );
}
