import { RefObject, useEffect, useState } from "react";
import * as Haptics from "expo-haptics";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { Row } from "~/components/Grid";
import { mints } from "~/constants/tokens";
import { CoinLogo } from "~/components/CoinLogo";
import { useActiveWallet } from "~/state/wallet";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import Colors from "~/constants/Colors";
import {
  EarnBalance,
  useEarnBalances,
  useSuspenseDriftSpotMarkets,
  useSuspenseKaminoVault,
  useSuspenseLuloPools,
} from "~/state/earn";
import {
  formatPercent,
  formatUsdValue,
} from "~/vendor/squads/utils/numberFormats";
import {
  EARN_PROVIDERS,
  EarnProvider,
  earnProviderName,
  LendingApy,
} from "~/services/earn";
import { EarnProviderIcon } from "~/components/earn/EarnProviderIcon";
import { TextFadeCents } from "../typography/TextFadeCents";
import { useBalanceSettings } from "~/state/balanceSettings";
import { TokenTile } from "./DriftEarnWithdrawModal";
import { EarnWithdrawRouteParams } from "~/app/unlocked/earn/deposit/withdraw-amount";
import { Link, useRouter } from "expo-router";
import { useBalances } from "~/state/balances";
import { Settings } from "react-native";
import { hapticOpenModalSheet, hapticSelect } from "~/utils/haptics";
import { EarnDepositRouteParams } from "~/app/unlocked/earn/deposit/deposit-amount";
import { assertNever } from "~/utils/assertNever";
import { Address } from "~/vendor/squads/models/solana";
import { Checkbox } from "../Checkbox";
import { P3 } from "../typography/P3";
import { Button } from "../Button";

type WithdrawStep = "provider-picker" | "drift-token-picker";

type ProviderChooserWithdrawModalProps = {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
  initialStep?: WithdrawStep;
  onDismiss?: () => void;
};

export function ProviderChooserWithdrawModal({
  modalRef,
  initialStep = "provider-picker",
  onDismiss,
}: ProviderChooserWithdrawModalProps) {
  const [modalState, setModalState] = useState<{
    step: WithdrawStep;
  }>({
    step: initialStep,
  });
  useEffect(() => {
    if (initialStep !== modalState.step) setModalState({ step: initialStep });
  }, [initialStep]);

  const router = useRouter();

  const onPress = async (provider: EarnProvider) => {
    if (provider === "drift") {
      hapticOpenModalSheet();

      const driftEarnBalances = {
        [mints.usdc]: driftEarnUsdcBalance,
        [mints.pyusd]: driftEarnPyusdBalance,
        [mints.usds]: driftEarnUsdsBalance,
      };

      const tokensWithBalance = (
        Object.entries(driftEarnBalances) as [Address, number][]
      ).filter(([, balance]) => balance > 0);

      if (tokensWithBalance.length === 1) {
        const [mint] = tokensWithBalance[0];
        onTokenSelect(mint as Address);
      } else {
        setModalState({ step: "drift-token-picker" });
      }
    } else if (provider === "lulo") {
      modalRef.current?.dismiss();
      router.push({
        pathname: "/unlocked/earn/deposit/withdraw-amount",
        params: {
          earnMint: mints.usdc,
          provider: "lulo",
        } satisfies EarnWithdrawRouteParams,
      });
    } else {
      modalRef.current?.dismiss();
      router.push({
        pathname: "/unlocked/earn/deposit/withdraw-amount",
        params: {
          earnMint: mints.usdc,
          provider: "kamino",
        } satisfies EarnWithdrawRouteParams,
      });
    }
  };

  const options = EARN_PROVIDERS;

  const { wallet } = useActiveWallet();
  const balances = useBalances({ address: wallet.defaultVault });
  const driftEarnUsdcBalance =
    balances.find((balance) => balance.mint === mints.usdc)?.driftEarn?.total ??
    0;
  const driftEarnPyusdBalance =
    balances.find((balance) => balance.mint === mints.pyusd)?.driftEarn
      ?.total ?? 0;
  const driftEarnUsdsBalance =
    balances.find((balance) => balance.mint === mints.usds)?.driftEarn?.total ??
    0;

  async function onTokenSelect(earnMint: Address) {
    modalRef.current?.dismiss();
    hapticSelect();
    router.push({
      pathname: "/unlocked/earn/deposit/withdraw-amount",
      params: { earnMint, provider: "drift" } satisfies EarnWithdrawRouteParams,
    });
  }

  return (
    <BottomModal
      modalRef={modalRef}
      modalId="provider-chooser-withdraw-modal"
      title=" "
      onDismiss={() => {
        setModalState({ step: "provider-picker" });
        onDismiss?.();
      }}
      body={() => {
        if (modalState.step === "provider-picker") {
          return (
            <View gap={10}>
              <View
                style={{
                  alignItems: "center",
                  marginBottom: 14,
                  marginTop: -18,
                }}
                gap={4}
              >
                <View
                  style={{
                    width: 54,
                    height: 54,
                    backgroundColor: Colors.light.backgroundSecondary,
                    borderRadius: 9999,
                    alignItems: "center",
                    justifyContent: "center",
                    transform: [{ rotate: "180deg" }],
                  }}
                >
                  <Text variant="bold" size={24}>
                    􀄩
                  </Text>
                </View>
                <Text variant="semibold" size={18}>
                  Withdraw
                </Text>
                <Text variant="semibold" style={{ opacity: 0.3 }}>
                  Choose product
                </Text>
              </View>
              {options.map((provider) => (
                <EarnTileWithdraw
                  key={provider}
                  provider={provider}
                  onPress={() => onPress(provider)}
                />
              ))}
            </View>
          );
        } else if (modalState.step === "drift-token-picker") {
          const options = [
            { mint: mints.usdc, symbol: "USDC" },
            { mint: mints.pyusd, symbol: "PYUSD" },
            { mint: mints.usds, symbol: "USDS" },
          ];

          const balances: Record<Address, number> = {
            [mints.usdc]: driftEarnUsdcBalance,
            [mints.pyusd]: driftEarnPyusdBalance,
            [mints.usds]: driftEarnUsdsBalance,
          };

          return (
            <View gap={10}>
              <View style={{ alignItems: "center", marginBottom: 14 }} gap={4}>
                <View style={{ marginBottom: 16 }}>
                  <EarnProviderIcon provider="drift" size={54} />
                  <View
                    style={{
                      position: "absolute",
                      top: -6,
                      right: -6,
                      width: 20,
                      height: 20,
                      backgroundColor: Colors.light.backgroundSecondary,
                      borderRadius: 9999,
                      borderWidth: 2,
                      borderColor: Colors.light.backgroundBottomSheet,
                      alignItems: "center",
                      justifyContent: "center",
                      transform: [{ rotate: "180deg" }],
                    }}
                  >
                    <Text variant="bold" size={7.4}>
                      􀄩
                    </Text>
                  </View>
                </View>
                <Text variant="semibold" size={16}>
                  Lending Yield Withdrawal
                </Text>
                <Text variant="semibold" style={{ opacity: 0.3 }}>
                  Choose token
                </Text>
              </View>
              {options.map(({ mint, symbol }) => {
                return (
                  <TokenTile
                    key={mint}
                    mint={mint as Address}
                    balance={balances[mint as Address]}
                    symbol={symbol}
                    onPress={() => onTokenSelect(mint)}
                  />
                );
              })}
            </View>
          );
        } else assertNever(modalState.step);
      }}
    />
  );
}

type ProviderChooserDepositModalProps = {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
};

export function ProviderChooserDepositModal({
  modalRef,
}: ProviderChooserDepositModalProps) {
  const [modalState, setModalState] = useState<{
    step:
      | "picker"
      | "drift-terms"
      | "lulo-terms"
      | "kamino-terms"
      | "drift-token-selection";
  }>({
    step: "picker",
  });
  const router = useRouter();
  const { spotMarkets } = useSuspenseDriftSpotMarkets();

  const [fuseTermsChecked, setFuseTermsChecked] = useState(
    Settings.get("EARN_FUSE_TERMS_ACCEPTED") ?? false
  );
  const [driftTermsChecked, setDriftTermsChecked] = useState(
    Settings.get("EARN_DRIFT_TERMS_ACCEPTED") ?? false
  );
  const [luloTermsChecked, setLuloTermsChecked] = useState(
    Settings.get("EARN_LULO_TERMS_ACCEPTED") ?? false
  );
  const [kaminoTermsChecked, setKaminoTermsChecked] = useState(
    Settings.get("EARN_KAMINO_TERMS_ACCEPTED") ?? false
  );
  const [fuseAndDriftTermsAccepted, setFuseAndDriftTermsAccepted] = useState(
    fuseTermsChecked && driftTermsChecked
  );
  const [fuseAndLuloTermsAccepted, setFuseAndLuloTermsAccepted] = useState(
    fuseTermsChecked && luloTermsChecked
  );
  const [fuseAndKaminoTermsAccepted, setFuseAndKaminoTermsAccepted] = useState(
    fuseTermsChecked && kaminoTermsChecked
  );

  function driftAcceptTerms() {
    Settings.set({ EARN_FUSE_TERMS_ACCEPTED: true });
    Settings.set({ EARN_DRIFT_TERMS_ACCEPTED: true });

    setFuseTermsChecked(true);
    setDriftTermsChecked(true);
    setFuseAndDriftTermsAccepted(true);

    setModalState({ step: "drift-token-selection" });
  }

  function luloAcceptTerms() {
    Settings.set({ EARN_FUSE_TERMS_ACCEPTED: true });
    Settings.set({ EARN_LULO_TERMS_ACCEPTED: true });

    setFuseTermsChecked(true);
    setLuloTermsChecked(true);
    setFuseAndLuloTermsAccepted(true);

    modalRef.current?.dismiss();

    openDepositSheet("lulo", mints.usdc);
  }

  function kaminoAcceptTerms() {
    Settings.set({ EARN_FUSE_TERMS_ACCEPTED: true });
    Settings.set({ EARN_KAMINO_TERMS_ACCEPTED: true });

    setFuseTermsChecked(true);
    setKaminoTermsChecked(true);
    setFuseAndKaminoTermsAccepted(true);

    modalRef.current?.dismiss();

    openDepositSheet("kamino", mints.usdc);
  }

  const openDepositSheet = (provider: EarnProvider, earnMint: Address) => {
    modalRef.current?.dismiss();
    hapticOpenModalSheet();
    router.push({
      pathname: "/unlocked/earn/deposit/deposit-amount",
      params: {
        provider,
        earnMint,
      } satisfies EarnDepositRouteParams,
    });
  };

  const onPress = async (provider: EarnProvider) => {
    if (modalState.step === "picker") {
      if (provider === "drift") {
        if (fuseAndDriftTermsAccepted) {
          hapticOpenModalSheet();
          setModalState({ step: "drift-token-selection" });
        } else {
          setModalState({ step: "drift-terms" });
        }
      } else if (provider === "lulo") {
        if (fuseAndLuloTermsAccepted) {
          openDepositSheet("lulo", mints.usdc);
        } else {
          setModalState({ step: "lulo-terms" });
        }
      } else if (provider === "kamino") {
        if (fuseAndKaminoTermsAccepted) {
          openDepositSheet("kamino", mints.usdc);
        } else {
          setModalState({ step: "kamino-terms" });
        }
      } else {
        provider satisfies never;
      }
    }
  };

  const options: Array<EarnProvider> = EARN_PROVIDERS;

  const { wallet } = useActiveWallet();
  const balances = useBalances({ address: wallet.defaultVault });
  const usdcBalance =
    balances.find((balance) => balance.mint === mints.usdc)?.amount ?? 0;
  const pyusdBalance =
    balances.find((balance) => balance.mint === mints.pyusd)?.amount ?? 0;
  const usdsBalance =
    balances.find((balance) => balance.mint === mints.usds)?.amount ?? 0;

  async function onTokenSelect(earnMint: Address) {
    openDepositSheet("drift", earnMint);
  }

  return (
    <BottomModal
      modalRef={modalRef}
      modalId="provider-chooser-deposit-modal"
      title=" "
      onDismiss={() => setModalState({ step: "picker" })}
      body={() => {
        if (modalState.step === "picker") {
          return (
            <View gap={10}>
              <View
                style={{
                  alignItems: "center",
                  marginBottom: 14,
                  marginTop: -18,
                }}
                gap={4}
              >
                <View
                  style={{
                    width: 54,
                    height: 54,
                    backgroundColor: Colors.light.backgroundSecondary,
                    borderRadius: 9999,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Text variant="bold" size={24}>
                    􀄩
                  </Text>
                </View>
                <Text variant="semibold" size={18}>
                  Deposit
                </Text>
                <Text variant="semibold" style={{ opacity: 0.3 }}>
                  Choose product
                </Text>
              </View>
              {options.map((provider) => (
                <EarnTileDeposit
                  key={provider}
                  provider={provider}
                  onPress={() => onPress(provider)}
                />
              ))}
            </View>
          );
        } else if (modalState.step === "drift-terms") {
          return (
            <View gap={12}>
              <View style={{ alignItems: "center", marginBottom: 14 }} gap={4}>
                <View style={{ marginBottom: 16 }}>
                  <EarnProviderIcon provider="drift" size={54} />
                  <View
                    style={{
                      position: "absolute",
                      top: -6,
                      right: -6,
                      width: 20,
                      height: 20,
                      backgroundColor: Colors.light.backgroundSecondary,
                      borderRadius: 9999,
                      borderWidth: 2,
                      borderColor: Colors.light.backgroundBottomSheet,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <Text variant="bold" size={7.4}>
                      􀄩
                    </Text>
                  </View>
                </View>
                <Text variant="semibold" size={16}>
                  Lending Yield Deposit
                </Text>
              </View>
              <Checkbox
                value={fuseTermsChecked}
                onChange={(checked) => {
                  if (checked) {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Rigid);
                  } else {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Soft);
                  }

                  setFuseTermsChecked(checked);
                }}
              >
                <P3 colorToken="textSecondary">
                  I agree to{" "}
                  <Link
                    href="https://fusewallet.com/legal/terms-of-service"
                    target="_blank"
                  >
                    <P3 style={{ textDecorationLine: "underline" }}>
                      Fuse Terms of Service
                    </P3>
                  </Link>
                </P3>
              </Checkbox>
              <Checkbox
                value={driftTermsChecked}
                onChange={(checked) => {
                  if (checked) {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Rigid);
                  } else {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Soft);
                  }
                  setDriftTermsChecked(checked);
                }}
              >
                <P3 colorToken="textSecondary">
                  I understand that this is a third party integration subject to
                  the applicable{" "}
                  <Link
                    href="https://docs.drift.trade/legal-and-regulations/terms-of-use"
                    target="_blank"
                  >
                    <P3 style={{ textDecorationLine: "underline" }}>
                      third party terms
                    </P3>
                  </Link>
                </P3>
              </Checkbox>
            </View>
          );
        } else if (modalState.step === "lulo-terms") {
          return (
            <View gap={12}>
              <View style={{ alignItems: "center", marginBottom: 14 }} gap={4}>
                <View style={{ marginBottom: 16 }}>
                  <EarnProviderIcon provider="lulo" size={54} />
                  <View
                    style={{
                      position: "absolute",
                      top: -6,
                      right: -6,
                      width: 20,
                      height: 20,
                      backgroundColor: Colors.light.backgroundSecondary,
                      borderRadius: 9999,
                      borderWidth: 2,
                      borderColor: Colors.light.backgroundBottomSheet,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <Text variant="bold" size={7.4}>
                      􀄩
                    </Text>
                  </View>
                </View>
                <Text variant="semibold" size={16}>
                  Rebalancing Yield Deposit
                </Text>
              </View>

              <Checkbox
                value={fuseTermsChecked}
                onChange={(checked) => {
                  if (checked) {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Rigid);
                  } else {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Soft);
                  }

                  setFuseTermsChecked(checked);
                }}
              >
                <P3 colorToken="textSecondary">
                  I agree to{" "}
                  <Link
                    href="https://fusewallet.com/legal/terms-of-service"
                    target="_blank"
                  >
                    <P3 style={{ textDecorationLine: "underline" }}>
                      Fuse Terms of Service
                    </P3>
                  </Link>
                </P3>
              </Checkbox>
              <Checkbox
                value={luloTermsChecked}
                onChange={(checked) => {
                  if (checked) {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Rigid);
                  } else {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Soft);
                  }
                  setLuloTermsChecked(checked);
                }}
              >
                <P3 colorToken="textSecondary">
                  I understand that this is a third party integration subject to
                  the applicable{" "}
                  <Link href="https://lulo.fi/terms-of-service" target="_blank">
                    <P3 style={{ textDecorationLine: "underline" }}>
                      third party terms
                    </P3>
                  </Link>
                </P3>
              </Checkbox>
            </View>
          );
        } else if (modalState.step === "kamino-terms") {
          return (
            <View gap={12}>
              <View style={{ alignItems: "center", marginBottom: 14 }} gap={4}>
                <View style={{ marginBottom: 16 }}>
                  <EarnProviderIcon provider="kamino" size={54} />
                  <View
                    style={{
                      position: "absolute",
                      top: -6,
                      right: -6,
                      width: 20,
                      height: 20,
                      backgroundColor: Colors.light.backgroundSecondary,
                      borderRadius: 9999,
                      borderWidth: 2,
                      borderColor: Colors.light.backgroundBottomSheet,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <Text variant="bold" size={7.4}>
                      􀄩
                    </Text>
                  </View>
                </View>
                <Text variant="semibold" size={16}>
                  Lending Yield Deposit
                </Text>
              </View>

              <Checkbox
                value={fuseTermsChecked}
                onChange={(checked) => {
                  if (checked) {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Rigid);
                  } else {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Soft);
                  }

                  setFuseTermsChecked(checked);
                }}
              >
                <P3 colorToken="textSecondary">
                  I agree to{" "}
                  <Link
                    href="https://fusewallet.com/legal/terms-of-service"
                    target="_blank"
                  >
                    <P3 style={{ textDecorationLine: "underline" }}>
                      Fuse Terms of Service
                    </P3>
                  </Link>
                </P3>
              </Checkbox>
              <Checkbox
                value={kaminoTermsChecked}
                onChange={(checked) => {
                  if (checked) {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Rigid);
                  } else {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Soft);
                  }
                  setKaminoTermsChecked(checked);
                }}
              >
                <P3 colorToken="textSecondary">
                  I understand that this is a third party integration subject to
                  the applicable{" "}
                  <Link href="https://app.kamino.finance/terms" target="_blank">
                    {/* kaminomock is this the right link?*/}
                    <P3 style={{ textDecorationLine: "underline" }}>
                      third party terms
                    </P3>
                  </Link>
                </P3>
              </Checkbox>
            </View>
          );
        } else if (modalState.step === "drift-token-selection") {
          const options = [
            { mint: mints.usdc, symbol: "USDC" },
            { mint: mints.pyusd, symbol: "PYUSD" },
            { mint: mints.usds, symbol: "USDS" },
          ];
          const balances: Record<Address, number> = {
            [mints.usdc]: usdcBalance,
            [mints.pyusd]: pyusdBalance,
            [mints.usds]: usdsBalance,
          };

          return (
            <View gap={10}>
              <View style={{ alignItems: "center", marginBottom: 14 }} gap={4}>
                <View style={{ marginBottom: 16 }}>
                  <EarnProviderIcon provider="drift" size={54} />
                  <View
                    style={{
                      position: "absolute",
                      top: -6,
                      right: -6,
                      width: 20,
                      height: 20,
                      backgroundColor: Colors.light.backgroundSecondary,
                      borderRadius: 9999,
                      borderWidth: 2,
                      borderColor: Colors.light.backgroundBottomSheet,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <Text variant="bold" size={7.4}>
                      􀄩
                    </Text>
                  </View>
                </View>
                <Text variant="semibold" size={16}>
                  Lending Yield Deposit
                </Text>
                <Text variant="semibold" style={{ opacity: 0.3 }}>
                  Choose token
                </Text>
              </View>
              {options.map(({ mint, symbol }) => {
                const disabled = (balances[mint] ?? 0) === 0;
                const spotMarket = spotMarkets.find(
                  (spotMarket) => spotMarket.mint === mint
                );
                return (
                  <AnimatedTouchableScale
                    disabled={disabled}
                    onPress={() => onTokenSelect(mint)}
                    key={mint}
                  >
                    <Row
                      style={{
                        justifyContent: "space-between",
                        backgroundColor: "white",
                        padding: 16,
                        borderRadius: 18,
                      }}
                    >
                      <Row gap={10} style={{ opacity: disabled ? 0.5 : 1 }}>
                        <CoinLogo mint={mint} size={24} />
                        <View>
                          <Row>
                            <Text colorToken="systemGreen" size={12}>
                              {spotMarket
                                ? formatPercent(
                                    LendingApy.total(spotMarket.apy)
                                  ) + "%"
                                : "N/A"}
                            </Text>
                            <Text size={12} colorToken="textSecondary">
                              {" "}
                              APY
                            </Text>
                          </Row>
                          <Text variant="semibold">{symbol}</Text>
                        </View>
                      </Row>
                    </Row>
                  </AnimatedTouchableScale>
                );
              })}
            </View>
          );
        } else assertNever(modalState.step);
      }}
      footer={() => {
        if (modalState.step === "drift-terms") {
          if (!fuseAndDriftTermsAccepted) {
            return (
              <Button
                disabled={!fuseTermsChecked || !driftTermsChecked}
                onPress={() => {
                  Haptics.selectionAsync();
                  driftAcceptTerms();
                }}
              >
                Agree and continue
              </Button>
            );
          }
        } else if (modalState.step === "lulo-terms") {
          if (!fuseAndLuloTermsAccepted) {
            return (
              <Button
                disabled={!fuseTermsChecked || !luloTermsChecked}
                onPress={() => {
                  Haptics.selectionAsync();
                  luloAcceptTerms();
                }}
              >
                Agree and continue
              </Button>
            );
          }
        } else if (modalState.step === "kamino-terms") {
          if (!fuseAndKaminoTermsAccepted) {
            return (
              <Button
                disabled={!fuseTermsChecked || !kaminoTermsChecked}
                onPress={() => {
                  Haptics.selectionAsync();
                  kaminoAcceptTerms();
                }}
              >
                Agree and continue
              </Button>
            );
          }
        }
      }}
    />
  );
}

function EarnTileWithdraw({
  provider,
  onPress,
}: {
  provider: EarnProvider;
  onPress: () => void;
}) {
  const { wallet } = useActiveWallet();
  const earnBalances = useEarnBalances({ address: wallet.defaultVault });
  const { displayBalance } = useBalanceSettings();

  const providerEarnBalances =
    earnBalances.find((b) => b.provider === provider)?.assets ?? [];

  const hasEarnBalance = EarnBalance.totalBalanceUsd(providerEarnBalances) > 0;

  const { spotMarkets } = useSuspenseDriftSpotMarkets();
  const luloPools = useSuspenseLuloPools();
  const kaminoVault = useSuspenseKaminoVault();

  const totalProviderEarnBalance = EarnBalance.totalBalanceUsd(
    providerEarnBalances ?? []
  );

  const apy = (() => {
    if (provider === "drift") {
      const maxApy = spotMarkets.reduce(
        (max, market) => Math.max(max, LendingApy.total(market.apy)),
        0
      );
      const weightedApy = EarnBalance.weightedApy(providerEarnBalances);
      return totalProviderEarnBalance !== 0 ? weightedApy : maxApy;
    } else if (provider === "lulo") {
      return luloPools.protected.apy;
    } else if (provider === "kamino") {
      return kaminoVault.apy;
    } else {
      provider satisfies never;
      return 0;
    }
  })();

  const disabled = !hasEarnBalance;

  return (
    <AnimatedTouchableScale
      key={provider}
      disabled={disabled}
      onPress={onPress}
    >
      <View
        style={[
          {
            justifyContent: "space-between",
            backgroundColor: "white",
            padding: 16,
            borderRadius: 18,
          },
          disabled && { opacity: 0.5 },
        ]}
      >
        <Row gap={10}>
          <EarnProviderIcon provider={provider} />
          <View style={{ flex: 1 }}>
            <Row>
              <Text variant="medium" size={12} style={{ opacity: 0.3 }}>
                {earnProviderName(provider)} ⋅{" "}
              </Text>
              <Text colorToken="systemGreen" size={12}>
                {formatPercent(apy)}%
              </Text>
              <Text size={12} colorToken="textSecondary">
                {" "}
                APY
              </Text>
            </Row>
            <Text variant="semibold">
              {provider === "lulo" ? "Rebalancing Yield" : "Lending Yield"}
            </Text>
          </View>

          {hasEarnBalance && (
            <TextFadeCents size={14}>
              {displayBalance(formatUsdValue(totalProviderEarnBalance))}
            </TextFadeCents>
          )}
        </Row>
      </View>
    </AnimatedTouchableScale>
  );
}

function EarnTileDeposit({
  provider,
  onPress,
}: {
  provider: EarnProvider;
  onPress: () => void;
}) {
  const { spotMarkets } = useSuspenseDriftSpotMarkets();
  const luloPools = useSuspenseLuloPools();
  const kaminoVault = useSuspenseKaminoVault();

  const { wallet } = useActiveWallet();
  const balances = useBalances({ address: wallet.defaultVault });
  const usdcBalance =
    balances.find((balance) => balance.mint === mints.usdc)?.amount ?? 0;
  const pyusdBalance =
    balances.find((balance) => balance.mint === mints.pyusd)?.amount ?? 0;
  const usdsBalance =
    balances.find((balance) => balance.mint === mints.usds)?.amount ?? 0;

  const apy = (() => {
    if (provider === "drift") {
      return spotMarkets.reduce(
        (max, market) => Math.max(max, LendingApy.total(market.apy)),
        0
      );
    } else if (provider === "lulo") {
      return luloPools.protected.apy;
    } else if (provider === "kamino") {
      return kaminoVault.apy;
    } else {
      provider satisfies never;
      return 0;
    }
  })();

  const disabled =
    provider === "lulo" || provider === "kamino"
      ? usdcBalance === 0
      : provider === "drift"
        ? usdcBalance === 0 && pyusdBalance === 0 && usdsBalance === 0
        : false;

  return (
    <AnimatedTouchableScale
      key={provider}
      disabled={disabled}
      onPress={onPress}
    >
      <View
        style={[
          {
            justifyContent: "space-between",
            backgroundColor: "white",
            padding: 16,
            borderRadius: 18,
          },
          disabled && { opacity: 0.5 },
        ]}
      >
        <Row gap={10}>
          <EarnProviderIcon provider={provider} />
          <View style={{ flex: 1 }}>
            <Row>
              <Text variant="medium" size={12} style={{ opacity: 0.3 }}>
                {earnProviderName(provider)} ⋅{" "}
              </Text>
              <Text colorToken="systemGreen" size={12}>
                {formatPercent(apy)}%
              </Text>
              <Text size={12} colorToken="textSecondary">
                {" "}
                APY
              </Text>
            </Row>
            <Text variant="semibold">
              {provider === "lulo" ? "Rebalancing Yield" : "Lending Yield"}
            </Text>
          </View>

          <Row>
            <CoinLogo mint={mints.usdc} size={20} />
            {provider === "drift" ? (
              <>
                <CoinLogo
                  mint={mints.pyusd}
                  size={20}
                  style={{
                    marginLeft: -6,
                    borderWidth: 1,
                    borderColor: "white",
                    borderRadius: 9999,
                  }}
                />
                <CoinLogo
                  mint={mints.usds}
                  size={20}
                  style={{
                    marginLeft: -6,
                    borderWidth: 1,
                    borderColor: "white",
                    borderRadius: 9999,
                  }}
                />
              </>
            ) : null}
          </Row>
        </Row>
      </View>
    </AnimatedTouchableScale>
  );
}
