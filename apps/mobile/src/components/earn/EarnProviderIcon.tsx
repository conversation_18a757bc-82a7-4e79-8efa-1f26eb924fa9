import { View } from "~/components/Themed";
import { DriftLogo } from "~/components/icons/earn/DriftLogo";
import { DriftGradient } from "~/components/icons/earn/DriftGradient";
import { StyleSheet, Image } from "react-native";
import { EarnProvider } from "~/services/earn";
import LuloIcon from "~assets/images/earn/lulo-icon.png";
import { SvgImage } from "~/components/SvgImage";
import KaminoIconSvg from "~assets/images/earn/kamino-icon.svg";

const DEFAULT_SIZE = 34;

export function EarnProviderIcon({
  provider,
  size = DEFAULT_SIZE,
}: {
  provider: EarnProvider;
  size?: number;
}) {
  return (
    <View
      style={{
        width: size,
        aspectRatio: 1,
        flexShrink: 0,
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      {provider === "drift" ? (
        <DriftIcon size={size} />
      ) : provider === "lulo" ? (
        <Image
          source={LuloIcon}
          style={{
            width: size,
            height: size,
            borderRadius: 7.385 * (size / DEFAULT_SIZE),
          }}
        />
      ) : provider === "kamino" ? (
        <View
          style={{
            width: size,
            height: size,
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "#091326",
            borderRadius: 7.385 * (size / DEFAULT_SIZE),
          }}
        >
          <SvgImage source={KaminoIconSvg} width={size - 8} height={size - 8} />
        </View>
      ) : null}
    </View>
  );
}

export function DriftIcon({ size = 34 }: { size?: number }) {
  return (
    <View>
      <DriftGradient size={size} />
      <View
        style={[
          StyleSheet.absoluteFill,
          { alignItems: "center", justifyContent: "center" },
        ]}
      >
        <DriftLogo size={size / 2.5} />
      </View>
    </View>
  );
}
