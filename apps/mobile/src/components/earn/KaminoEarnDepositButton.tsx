import { Link, router } from "expo-router";
import { Button } from "~/components/Button";
import { useLock } from "~/hooks/useLock";
import * as Haptics from "expo-haptics";
import { mints } from "~/constants/tokens";
import { EarnDepositRouteParams } from "~/app/unlocked/earn/deposit/deposit-amount";
import { useBalances } from "~/state/balances";
import { useActiveWallet } from "~/state/wallet";
import { useIsWalletInactive } from "~/hooks/useAssertWalletState";
import { hapticOpenModalSheet } from "~/utils/haptics";
import { BottomModal, BottomModalImperativeMethods } from "../BottomModal";
import { RefObject, useRef, useState } from "react";
import { Settings } from "react-native";
import { P3 } from "../typography/P3";
import { Checkbox } from "../Checkbox";
import { Text, View } from "../Themed";
import Colors from "~/constants/Colors";
import { EarnProviderIcon } from "./EarnProviderIcon";

export function KaminoEarnDepositButton() {
  const withLock = useLock();
  const modalRef = useRef<BottomModalImperativeMethods>(null);

  const [fuseTermsChecked] = useState(
    Settings.get("EARN_FUSE_TERMS_ACCEPTED") ?? false
  );
  const [kaminoTermsChecked] = useState(
    Settings.get("EARN_KAMINO_TERMS_ACCEPTED") ?? false
  );
  const [termsAccepted, setTermsAccepted] = useState(
    fuseTermsChecked && kaminoTermsChecked
  );

  const isWalletInactive = useIsWalletInactive();

  const { wallet } = useActiveWallet();
  const balances = useBalances({ address: wallet.defaultVault });
  const usdcBalance =
    balances.find((balance) => balance.mint === mints.usdc)?.amount ?? 0;

  return (
    <>
      <Button
        size={"medium-new"}
        disabled={usdcBalance === 0}
        onPress={withLock(async () => {
          if (await isWalletInactive()) {
            return;
          }

          if (!termsAccepted) {
            modalRef.current?.present();
            return;
          }

          hapticOpenModalSheet();
          router.push({
            pathname: "/unlocked/earn/deposit/deposit-amount",
            params: {
              provider: "kamino",
              earnMint: mints.usdc,
            } satisfies EarnDepositRouteParams,
          });
        })}
      >
        Deposit
      </Button>

      <KaminoTermsModal
        modalRef={modalRef}
        onAccept={() => setTermsAccepted(true)}
      />
    </>
  );
}

export function KaminoTermsModal({
  modalRef,
  onAccept,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
  onAccept: () => void;
}) {
  function hide() {
    modalRef.current?.close();
  }

  const [fuseTermsChecked, setFuseTermsChecked] = useState<boolean>(
    Settings.get("EARN_FUSE_TERMS_ACCEPTED") ?? false
  );
  const [kaminoTermsChecked, setKaminoTermsChecked] = useState<boolean>(
    Settings.get("EARN_KAMINO_TERMS_ACCEPTED") ?? false
  );
  const [termsAccepted, setTermsAccepted] = useState(
    fuseTermsChecked && kaminoTermsChecked
  );

  function acceptTerms() {
    Settings.set({ EARN_FUSE_TERMS_ACCEPTED: true });
    Settings.set({ EARN_KAMINO_TERMS_ACCEPTED: true });

    setFuseTermsChecked(true);
    setKaminoTermsChecked(true);
    setTermsAccepted(true);

    modalRef.current?.dismiss();
    onAccept();

    hapticOpenModalSheet();
    router.push({
      pathname: "/unlocked/earn/deposit/deposit-amount",
      params: {
        provider: "kamino",
        earnMint: mints.usdc,
      } satisfies EarnDepositRouteParams,
    });
  }

  return (
    <BottomModal
      modalRef={modalRef}
      onClose={hide}
      onDismiss={hide}
      modalId="deposit-kamino-terms"
      title=" "
      body={() => {
        return (
          <View gap={12}>
            <View style={{ alignItems: "center", marginBottom: 14 }} gap={4}>
              <View style={{ marginBottom: 16 }}>
                <EarnProviderIcon provider="kamino" size={54} />
                <View
                  style={{
                    position: "absolute",
                    top: -6,
                    right: -6,
                    width: 20,
                    height: 20,
                    backgroundColor: Colors.light.backgroundSecondary,
                    borderRadius: 9999,
                    borderWidth: 2,
                    borderColor: Colors.light.backgroundBottomSheet,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Text variant="bold" size={7.4}>
                    􀄩
                  </Text>
                </View>
              </View>
              <Text variant="semibold" size={16}>
                Lending Yield Deposit
              </Text>
            </View>

            <Checkbox
              value={fuseTermsChecked}
              onChange={(checked) => {
                if (checked) {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Rigid);
                } else {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Soft);
                }

                setFuseTermsChecked(checked);
              }}
            >
              <P3 colorToken="textSecondary">
                I agree to{" "}
                <Link
                  href="https://fusewallet.com/legal/terms-of-service"
                  target="_blank"
                >
                  <P3 style={{ textDecorationLine: "underline" }}>
                    Fuse Terms of Service
                  </P3>
                </Link>
              </P3>
            </Checkbox>
            <Checkbox
              value={kaminoTermsChecked}
              onChange={(checked) => {
                if (checked) {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Rigid);
                } else {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Soft);
                }
                setKaminoTermsChecked(checked);
              }}
            >
              <P3 colorToken="textSecondary">
                I understand that this is a third party integration subject to
                the applicable{" "}
                <Link href="https://app.kamino.finance/terms" target="_blank">
                  {/* kaminomock is this the right link?*/}
                  <P3 style={{ textDecorationLine: "underline" }}>
                    third party terms
                  </P3>
                </Link>
              </P3>
            </Checkbox>
          </View>
        );
      }}
      footer={
        !termsAccepted && (
          <Button
            disabled={!fuseTermsChecked || !kaminoTermsChecked}
            onPress={() => {
              Haptics.selectionAsync();
              acceptTerms();
            }}
          >
            Agree and continue
          </Button>
        )
      }
    />
  );
}
