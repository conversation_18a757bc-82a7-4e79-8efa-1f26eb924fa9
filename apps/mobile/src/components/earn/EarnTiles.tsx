import { FuseErrorBoundary } from "~/components/errors/FuseErrorBoundary";
import { PropsWithChildren, Suspense, useRef, useState } from "react";
import { Text, View } from "~/components/Themed";
import { EarnProvider, earnProviderName, LendingApy } from "~/services/earn";
import { useActiveWallet } from "~/state/wallet";
import {
  EarnBalance,
  useEarnBalances,
  useSuspenseDriftSpotMarkets,
  useSuspenseKaminoVault,
  useSuspenseLuloPools,
} from "~/state/earn";
import { BottomModalImperativeMethods } from "~/components/BottomModal";
import { Settings } from "react-native";
import * as Haptics from "expo-haptics";
import { EarnProviderRouteParams } from "~/app/unlocked/earn";
import { router } from "expo-router";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import { DriftSponsorshipInfoModal } from "~/components/earn/DriftSponsorshipInfoModal";
import { DashedListSeparator } from "~/components/ListSeparator";
import { Row } from "~/components/Grid";
import { CoinLogo } from "~/components/CoinLogo";
import { mints } from "~/constants/tokens";
import { useBalances } from "~/state/balances";
import { useBalanceSettings } from "~/state/balanceSettings";
import { EarnProviderIcon } from "~/components/earn/EarnProviderIcon";
import { ContentSkeleton } from "~/components/Skeleton";
import { formatPercent, formatUsdValue } from "@squads/utils/numberFormats";
import { TextFadeCents } from "~/components/typography/TextFadeCents";
import { ChevronIcon } from "~/components/icons/earn/ChevronIcon";

export function EarnTiles() {
  return (
    <FuseErrorBoundary FallbackComponent={EarnTileSkeleton}>
      <Suspense fallback={<EarnTileSkeleton />}>
        <EarnTilesInner />
      </Suspense>
    </FuseErrorBoundary>
  );
}

export function EarnTilesInner() {
  const { wallet } = useActiveWallet();
  const earnBalances = useEarnBalances({ address: wallet.defaultVault });

  const driftAssets =
    earnBalances.find((b) => b.provider === "drift")?.assets ?? [];
  const luloAssets =
    earnBalances.find((b) => b.provider === "lulo")?.assets ?? [];
  const kaminoAssets: EarnBalance[] =
    earnBalances.find((b) => b.provider === "kamino")?.assets ?? [];

  const driftEarnBalance = EarnBalance.totalBalanceUsd(driftAssets);
  const luloEarnBalance = EarnBalance.totalBalanceUsd(luloAssets);
  const kaminoEarnBalance = EarnBalance.totalBalanceUsd(kaminoAssets);

  const minEarnBalance = EarnBalance.MIN_EARN_BALANCE_TO_SHOW;
  const showOtherOptions =
    driftEarnBalance >= minEarnBalance !== luloEarnBalance >= minEarnBalance;

  return (
    <View gap={10}>
      {kaminoEarnBalance >= minEarnBalance && <EarnTile provider="kamino" />}
      {driftEarnBalance >= minEarnBalance && <EarnTile provider="drift" />}
      {luloEarnBalance >= minEarnBalance && <EarnTile provider="lulo" />}

      {showOtherOptions && (
        <Text
          variant="medium"
          align="center"
          colorToken="textSecondary"
          style={{ marginTop: 10 }}
        >
          Available products
        </Text>
      )}
      {kaminoEarnBalance < minEarnBalance && <EarnTile provider="kamino" />}
      {driftEarnBalance < minEarnBalance && <EarnTile provider="drift" />}
      {luloEarnBalance < minEarnBalance && <EarnTile provider="lulo" />}
    </View>
  );
}

function EarnTileSkeleton() {
  return (
    <EarnTileContainer>
      <EarnTileHeader provider="drift" providerEarnBalances={undefined} />
    </EarnTileContainer>
  );
}

function EarnTile({ provider }: { provider: EarnProvider }) {
  const { wallet } = useActiveWallet();
  const earnBalances = useEarnBalances({ address: wallet.defaultVault });
  const providerEarnBalances =
    provider === "drift"
      ? (earnBalances.find((b) => b.provider === "drift")?.assets ?? [])
      : provider === "lulo"
        ? (earnBalances.find((b) => b.provider === "lulo")?.assets ?? [])
        : (earnBalances.find((b) => b.provider === "kamino")?.assets ?? []);

  const modalRef = useRef<BottomModalImperativeMethods>(null);

  const [isDriftSponsorshipAcknowledged, setIsDriftSponsorshipAcknowledged] =
    useState(Settings.get("DRIFT_SPONSORSHIP_ACKNOWLEDGED") ?? false);

  function navigateToEarn(provider: EarnProvider) {
    Haptics.selectionAsync();

    if (provider === "drift" && !isDriftSponsorshipAcknowledged) {
      modalRef.current?.present();
      return;
    }

    const params = new URLSearchParams({
      provider,
    } satisfies EarnProviderRouteParams);
    router.push(`/unlocked/earn?${params.toString()}`);
  }

  return (
    <>
      <AnimatedTouchableScale
        pressedScale={0.99}
        onPress={() => navigateToEarn(provider)}
      >
        <EarnTileView
          provider={provider}
          providerEarnBalances={providerEarnBalances}
        />
      </AnimatedTouchableScale>

      <DriftSponsorshipInfoModal
        modalRef={modalRef}
        onAccept={() => {
          Settings.set({ DRIFT_SPONSORSHIP_ACKNOWLEDGED: true });
          setIsDriftSponsorshipAcknowledged(true);
          const params = new URLSearchParams({
            provider: "drift",
          } satisfies EarnProviderRouteParams);
          router.push(`/unlocked/earn?${params.toString()}`);
        }}
      />
    </>
  );
}

function EarnTileView({
  provider,
  providerEarnBalances,
}: {
  provider: EarnProvider;
  providerEarnBalances: EarnBalance[];
}) {
  const hasEarnBalance = EarnBalance.totalBalanceUsd(providerEarnBalances) > 0;

  return (
    <EarnTileContainer>
      <EarnTileHeader
        provider={provider}
        providerEarnBalances={providerEarnBalances}
      />

      {!hasEarnBalance && <EarnTileCTA provider={provider} />}
    </EarnTileContainer>
  );
}

function EarnTileCTA({ provider }: { provider: EarnProvider }) {
  return (
    <View gap={16}>
      <DashedListSeparator />

      <Row gap={8} style={{ alignItems: "flex-end" }}>
        <Text
          colorToken="textSecondary"
          variant="medium"
          style={{ flex: 1, flexShrink: 1 }}
        >
          {provider === "drift" &&
            "Deposit your stablecoins with Drift\nto earn yield from lending markets."}
          {provider === "lulo" &&
            "Lulo dynamically allocates your deposits\nacross four integrated DeFi apps."}
          {provider === "kamino" &&
            "Deposit your stablecoins with Kamino\nto earn yield from optimized lending vaults."}
        </Text>
        <Row>
          <CoinLogo mint={mints.usdc} size={20} />
          {provider === "drift" ? (
            <>
              <CoinLogo
                mint={mints.pyusd}
                size={20}
                style={{
                  marginLeft: -6,
                  borderWidth: 1,
                  borderColor: "white",
                  borderRadius: 9999,
                }}
              />
              <CoinLogo
                mint={mints.usds}
                size={20}
                style={{
                  marginLeft: -6,
                  borderWidth: 1,
                  borderColor: "white",
                  borderRadius: 9999,
                }}
              />
            </>
          ) : null}
        </Row>
      </Row>
    </View>
  );
}

function EarnTileContainer({ children }: PropsWithChildren) {
  return (
    <View
      gap={24}
      background="backgroundBanner"
      style={{
        padding: 24,
        borderRadius: 24,
        borderCurve: "continuous",
      }}
    >
      {children}
    </View>
  );
}

function EarnTileHeader({
  provider,
  providerEarnBalances,
}: {
  provider: EarnProvider;
  providerEarnBalances: EarnBalance[] | undefined;
}) {
  const { wallet } = useActiveWallet();
  const balances = useBalances({ address: wallet.defaultVault });
  const { spotMarkets } = useSuspenseDriftSpotMarkets();
  const luloPools = useSuspenseLuloPools();
  const kaminoVault = useSuspenseKaminoVault();
  const { displayBalance } = useBalanceSettings();

  const totalProviderEarnBalance = EarnBalance.totalBalanceUsd(
    providerEarnBalances ?? []
  );

  const apy = (() => {
    if (provider === "drift") {
      const maxApy = spotMarkets.reduce(
        (max, market) => Math.max(max, LendingApy.total(market.apy)),
        0
      );
      const weightedApy = EarnBalance.weightedApy(providerEarnBalances ?? []);
      return totalProviderEarnBalance !== 0 ? weightedApy : maxApy;
    } else if (provider === "lulo") {
      return luloPools.protected.apy;
    } else if (provider === "kamino") {
      return kaminoVault.apy;
    } else {
      provider satisfies never;
      return 0;
    }
  })();

  const estimatedYearEarn = (() => {
    const usdcBalance = balances.find((b) => b.mint === mints.usdc);
    if (!usdcBalance) return 0;
    const usdBalance = usdcBalance.amount / 10 ** usdcBalance.decimals;

    if (provider === "drift") {
      const usdcApy = spotMarkets.find((m) => m.mint === mints.usdc)?.apy;
      return (
        usdBalance * LendingApy.total(usdcApy ?? { lending: 0, rewards: 0 })
      );
    } else if (provider === "lulo") {
      return usdBalance * luloPools.protected.apy;
    } else if (provider === "kamino") {
      return usdBalance * kaminoVault.apy;
    } else {
      provider satisfies never;
      return 0;
    }
  })();

  const showPotentialEarn = totalProviderEarnBalance === 0;
  const enoughBalanceToShowPotentialEarn = estimatedYearEarn > 50;

  return (
    <Row
      gap={8}
      style={{ alignItems: "stretch", justifyContent: "space-between" }}
    >
      <Row gap={14}>
        <EarnProviderIcon provider={provider} />
        <View>
          <Row>
            <Text variant="medium" size={12} colorToken="textSecondaryV2">
              {earnProviderName(provider)} ⋅{" "}
            </Text>
            {providerEarnBalances === undefined ? (
              <ContentSkeleton variant="small">
                <Text colorToken="systemGreen" size={12}>
                  {formatPercent(0.05)}%
                </Text>
              </ContentSkeleton>
            ) : (
              <Text colorToken="systemGreen" size={12}>
                {formatPercent(apy)}%
              </Text>
            )}
            <Text variant="medium" size={12} colorToken="textSecondaryV2">
              {" "}
              APY
            </Text>
          </Row>

          <Text variant="semibold" size={16}>
            {provider === "lulo" ? "Rebalancing Yield" : "Lending Yield"}
          </Text>
        </View>
      </Row>

      {showPotentialEarn ? (
        enoughBalanceToShowPotentialEarn ? (
          <View
            style={{
              alignItems: "flex-end",
              flex: 1,
              justifyContent: "space-between",
            }}
          >
            <Text colorToken="textSecondaryV2" variant="medium" size={12}>
              Potential annual earn
            </Text>
            <Text style={{ color: "#0073F1" }} variant="semibold">
              {formatUsdValue(estimatedYearEarn)}
            </Text>
          </View>
        ) : null
      ) : (
        <Row>
          {providerEarnBalances === undefined ? (
            <ContentSkeleton>
              <TextFadeCents variant="medium" size={14}>
                {displayBalance(formatUsdValue(123.0))}
              </TextFadeCents>
            </ContentSkeleton>
          ) : (
            <TextFadeCents variant="medium" size={14}>
              {displayBalance(formatUsdValue(totalProviderEarnBalance))}
            </TextFadeCents>
          )}
          <ChevronIcon />
        </Row>
      )}
    </Row>
  );
}
