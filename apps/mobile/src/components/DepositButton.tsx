import * as Clipboard from "expo-clipboard";
import { RefObject, useEffect, useRef } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { create } from "zustand";
import { WalletQrCode } from "~/components/WalletQrCode";
import { ActionButton } from "~/components/ActionButton";
import { useActiveWallet } from "~/state/wallet";
import { Button } from "~/components/Button";
import { TouchableScale } from "~/components/TouchableScale";
import { ExtendedKycStatus, reloadBridgeAccount } from "~/state/bridge";
import { useQueryClient } from "@tanstack/react-query";
import { router } from "expo-router";
import { S2 } from "~/components/typography/S2";
import { CoinIcon } from "~/components/icons/home/<USER>";
import { BankIcon } from "~/components/icons/home/<USER>";
import { useToast } from "~/components/Toaster";
import { showImproveSecurityModal } from "~/components/security/useImproveSecurityModalState";
import { useLazySecurityWarning } from "~/hooks/useSecurityWarning";
import { ModalRow, ModalRowsList } from "~/components/ModalRow";
import {
  hapticOpenBottomTray,
  hapticSelect,
  hapticSuccess,
} from "~/utils/haptics";
import { IconWrapper } from "~/components/IconWrapper";
import { sleep } from "~/utils/promise";

export function DepositButton() {
  const { showSecurityWarning } = useLazySecurityWarning();

  return (
    <>
      <ActionButton
        hitSlop={4}
        iconName="plus.circle.fill"
        iconWeight="semibold"
        onPress={async () => {
          hapticOpenBottomTray();

          if (showSecurityWarning) {
            const { postpone } = await showImproveSecurityModal();
            if (!postpone) {
              return true;
            }
          }

          useModalState.setState({ view: "options" });
        }}
      >
        Receive
      </ActionButton>
    </>
  );
}

const useModalState = create<{
  view: "options" | "crypto" | null;
  hide: () => void;
}>((set) => ({
  view: null,
  hide: () => set({ view: null }),
}));

export function showDepositModal() {
  useModalState.setState({ view: "options" });
}

export function routeToKyc(kycStatus: Exclude<ExtendedKycStatus, "approved">) {
  switch (kycStatus) {
    case "not_started":
    case "incomplete":
      router.push(`/unlocked/bridge/onboarding/start-kyc`);
      return;

    case "under_review":
    case "just_completed":
    case "rejected":
      router.push(`/unlocked/bridge/onboarding/kyc-status`);
      return;
    default:
      kycStatus satisfies never;
      return;
  }
}

export function DepositModal() {
  const queryClient = useQueryClient();
  const { wallet } = useActiveWallet();

  const modalRef = useRef<BottomModalImperativeMethods>(null);

  const { view, hide } = useModalState();
  useEffect(() => {
    if (view) {
      reloadBridgeAccount(queryClient).then((account) => {
        console.debug(`reloaded bridge account`, account);
      });
      modalRef.current?.present();
    } else {
      modalRef.current?.close();
    }
  }, [view]);

  return (
    <BottomModal
      modalId={"receive-modal"}
      modalRef={modalRef}
      title={view === "options" ? "Receive" : undefined}
      onClose={hide}
      onDismiss={hide}
      body={() => {
        switch (view) {
          case "options":
            return (
              <ReceiveOptionsView
                onSelect={async (option) => {
                  hapticSelect();

                  if (option === "crypto") {
                    useModalState.setState({ view: "crypto" });
                    return;
                  }

                  if (wallet.keys.recoveryKeys === null) {
                    hide();
                    await sleep(300);

                    showImproveSecurityModal();
                    return;
                  }

                  hide();

                  router.push(`/unlocked/bridge/virtual-account`);
                }}
              />
            );
          case "crypto":
            return <ReceiveCryptoView modalRef={modalRef} />;
          default:
            return null;
        }
      }}
    />
  );
}

function ReceiveOptionsView({
  onSelect,
}: {
  onSelect: (option: "crypto" | "fiat") => void;
}) {
  return (
    <ModalRowsList>
      <TouchableScale onPress={() => onSelect("crypto")}>
        <ModalRow
          icon={
            <IconWrapper
              backgroundColorToken="backgroundTertiary"
              size={46}
              variant={"square"}
            >
              <CoinIcon />
            </IconWrapper>
          }
          title="Crypto"
          description="Receive via wallet address"
        />
      </TouchableScale>
      <TouchableScale onPress={() => onSelect("fiat")}>
        <ModalRow
          icon={
            <IconWrapper
              backgroundColorToken="backgroundTertiary"
              size={46}
              variant={"square"}
            >
              <BankIcon />
            </IconWrapper>
          }
          title="Fiat"
          description="Receive via bank transfer"
        />
      </TouchableScale>
    </ModalRowsList>
  );
}

function ReceiveCryptoView({
  modalRef,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  const { wallet } = useActiveWallet();
  const walletName = wallet.vaults.find((v) => v.index === 0)?.name ?? "Wallet";

  const { toast } = useToast();

  return (
    <View
      style={{
        alignItems: "center",
        justifyContent: "center",
        gap: 18,
      }}
    >
      <Text variant="display-bold" style={{ fontSize: 24 }}>
        {walletName}
      </Text>

      <View style={{ alignItems: "center" }}>
        <WalletQrCode value={wallet.defaultVault} size={7.5} />
      </View>

      <Text
        variant="medium"
        style={{ fontSize: 14, textAlign: "center", maxWidth: "75%" }}
      >
        {wallet.defaultVault}
      </Text>

      <S2 colorToken={"textSecondary"} align={"center"}>
        􀁞 Do not send NFTs to this address. Not all of them are supported.
        Contact support for more information.
      </S2>

      <View style={{ width: "100%" }}>
        <Button
          iconName="doc.on.doc"
          iconWeight="bold"
          iconSize={10}
          variant={"secondary"}
          // size={"medium"}
          onPress={() => {
            Clipboard.setStringAsync(wallet.defaultVault);
            hapticSuccess();
            toast.info("Copied");
            modalRef.current?.close();
          }}
        >
          Copy address
        </Button>
      </View>
    </View>
  );
}
