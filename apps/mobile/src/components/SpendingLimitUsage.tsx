import { useTokenAmountString } from "~/state/tokens";
import { Text } from "~/components/Themed";
import { ElementType } from "react";
import { ComponentProps } from "react";

export function SpendingLimitUsage<C extends ElementType>({
  spendingLimit,
  as,
  secondaryColorToken,
}: {
  spendingLimit: {
    amount: number;
    remainingAmount: number;
    mint: string | null;
  };
  as?: C;
  secondaryColorToken?: string;
} & ComponentProps<C>) {
  const mintOrSol = spendingLimit.mint ?? "SOL";
  const amountString = useTokenAmountString({
    mint: mintOrSol,
    amount: spendingLimit.amount,
  });

  const usedAmount = spendingLimit.amount - spendingLimit.remainingAmount;
  const usedAmountString = useTokenAmountString({
    mint: mintOrSol,
    amount: usedAmount,
    showSymbol: false,
  });

  const Component = as || Text;

  return (
    <Component>
      {usedAmountString}{" "}
      <Component colorToken={secondaryColorToken}>/ {amountString}</Component>
    </Component>
  );
}
