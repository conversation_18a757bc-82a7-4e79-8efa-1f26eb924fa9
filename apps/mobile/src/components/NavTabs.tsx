import Animated, {
  interpolate,
  SharedValue,
  useAnimatedStyle,
  useDerivedValue,
} from "react-native-reanimated";
import { Text, useColor, View } from "~/components/Themed";
import { useState } from "react";
import { TouchableScale } from "~/components/TouchableScale";
import * as Haptics from "expo-haptics";

const AnimatedText = Animated.createAnimatedComponent(Text);

export function NavTabs({
  onSelectView,
  pagePosition,
}: {
  onSelectView: (index: number) => void;
  pagePosition: SharedValue<number>;
}) {
  const underscoreColor = useColor("text");

  const [tabBoxes, setTabBoxes] = useState<{ x: number; width: number }[]>([]);

  const setLayoutBox = (index: number, value: { x: number; width: number }) =>
    setTabBoxes((boxes) => {
      const newBoxes = [...boxes];
      newBoxes[index] = value;
      return newBoxes;
    });

  const tabsNumber = 2;
  const isAllTabsMeasured = tabBoxes.filter(Boolean).length === tabsNumber;

  const animatedStyles = useAnimatedStyle(() => {
    const width = isAllTabsMeasured
      ? interpolate(
          pagePosition.value,
          Array.from({ length: tabBoxes.length }, (_, index) => index),
          tabBoxes.map((box) => box.width / 2)
        )
      : 0;

    const translateX = isAllTabsMeasured
      ? interpolate(
          pagePosition.value,
          Array.from({ length: tabBoxes.length }, (_, index) => index),
          Array.from(
            { length: tabBoxes.length },
            (_, i) => tabBoxes[i].x + width / 2
          )
        )
      : 0;

    return { transform: [{ translateX }], width: width };
  });

  const dashboardOpacity = useDerivedValue(() => {
    const index = pagePosition.value;
    return interpolate(index, [0, 1], [1, 0.3]);
  });

  const coinsOpacity = useDerivedValue(() => {
    const index = pagePosition.value;
    return interpolate(index, [0, 1], [0.3, 1]);
  });

  return (
    <View style={{ gap: 6 }}>
      <View style={{ flexDirection: "row", gap: 24 }}>
        <NavTab
          name={"Dashboard"}
          onSelect={() => {
            Haptics.selectionAsync();
            onSelectView(0);
          }}
          onLayoutMeasured={(value) => setLayoutBox(0, value)}
          opacity={dashboardOpacity}
        />
        <NavTab
          name={"Coins"}
          onSelect={() => {
            Haptics.selectionAsync();
            onSelectView(1);
          }}
          onLayoutMeasured={(value) => setLayoutBox(1, value)}
          opacity={coinsOpacity}
        />
      </View>

      <Animated.View
        style={[
          {
            height: 2,
            backgroundColor: underscoreColor,
          },
          animatedStyles,
        ]}
      />
    </View>
  );
}

export function NavTab({
  name,
  onSelect,
  onLayoutMeasured,
  opacity,
}: {
  name: string;
  opacity: SharedValue<number>;
  onSelect: () => void;
  onLayoutMeasured: (value: { x: number; width: number }) => void;
}) {
  const animatesStyles = useAnimatedStyle(() => {
    return { opacity: opacity.value };
  });

  return (
    <TouchableScale
      hitSlop={12}
      onPress={onSelect}
      onLayout={(e) => {
        e.target.measure((x, _y, width) => onLayoutMeasured({ x, width }));
      }}
    >
      <AnimatedText
        style={[{ fontSize: 18 }, animatesStyles]}
        variant={"semibold"}
      >
        {name}
      </AnimatedText>
    </TouchableScale>
  );
}
