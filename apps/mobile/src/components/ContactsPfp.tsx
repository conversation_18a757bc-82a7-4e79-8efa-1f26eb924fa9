import { Contact } from "~/state/contacts";
import { IconWrapper } from "~/components/IconWrapper";
import { Text } from "~/components/Themed";
import { PfpTagIcon } from "~/components/icons/wallets/PfpTagIcon";

import { WalletIcon } from "~/components/icons/WalletIcon";

export function ContactPfpIcon({
  pfp,
  size,
}: {
  pfp: Contact["pfp"];
  size: number;
}) {
  const iconSize = Math.round(size / 1.7);
  if (pfp.icon.type === "emoji") {
    return (
      <IconWrapper background={pfp.icon.background} size={size}>
        <Text
          style={{
            fontSize: iconSize,
            lineHeight: size,
          }}
        >
          {pfp.icon.value}
        </Text>
      </IconWrapper>
    );
  }

  if (pfp.icon.type === "tag") {
    return <PfpTagIcon name={pfp.icon.value} size={size} />;
  }

  if (pfp.icon.type === "default") {
    return (
      <IconWrapper backgroundColorToken={"backgroundSecondary"} size={size}>
        <WalletIcon size={size / 2.5} />
      </IconWrapper>
    );
  }

  pfp.icon satisfies never;
}
