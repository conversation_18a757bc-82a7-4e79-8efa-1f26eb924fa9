import { StatusView } from "~/components/StatusView";

export function StakingStatus({
  status,
}: {
  status: "activating" | "active" | "deactivating" | "deactivated";
}) {
  switch (status) {
    case "activating":
      return <StatusView variant={"warning"} title={"Activating"} />;
    case "active":
      return <StatusView variant={"success"} title={"Active"} />;
    case "deactivating":
      return <StatusView variant={"danger"} title={"Deactivating"} />;
    case "deactivated":
      return <StatusView variant={"info"} title={"Inactive"} />;
  }
}
