import * as Haptics from "expo-haptics";
import { router } from "expo-router";
import { Address } from "@squads/models/solana";
import { SwapRouteParams } from "~/app/unlocked/swap/_layout";
import { useLock } from "~/hooks/useLock";
import { ActionButton } from "./ActionButton";
import { useIsWalletInactive } from "~/hooks/useAssertWalletState";

export function SwapButton({
  vault,
  inputMint,
}: {
  vault: Address;
  inputMint?: Address;
}) {
  const withLock = useLock();
  const swapAction = useSwapAction();

  return (
    <>
      <ActionButton
        hitSlop={4}
        iconName="arrow.up.left.arrow.down.right.circle.fill"
        iconWeight="bold"
        onPress={withLock(async () => {
          await swapAction(inputMint);
        })}
      >
        Swap
      </ActionButton>
    </>
  );
}

export function useSwapAction() {
  const isWalletInactive = useIsWalletInactive();

  return async (inputMint?: Address) => {
    if (await isWalletInactive()) {
      return;
    }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    inputMint
      ? router.push({
          pathname: "/unlocked/swap/main",
          params: { inputMint } satisfies SwapRouteParams,
        })
      : router.push("/unlocked/swap/choose-input");
  };
}
