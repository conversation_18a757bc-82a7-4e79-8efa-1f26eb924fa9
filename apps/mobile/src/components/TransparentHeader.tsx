import { styles } from "~/components/ProgressHeader.css";

import { useSafeAreaInsets } from "react-native-safe-area-context";
import Animated, {
  FadeInDown,
  FadeInUp,
  FadeOut,
  ZoomIn,
} from "react-native-reanimated";
import { router } from "expo-router";
import { Text, View } from "~/components/Themed";
import { StepProgress } from "~/components/StepProgress";
import { DURATION_FAST } from "~/constants/animations";
import { usePrevious } from "~/hooks/usePrevious";
import { useIsFirstRender } from "~/hooks/useIsFirstRender";
import { Icon } from "~/components/Icon";
import { TouchableScale } from "~/components/TouchableScale";
import { ReactNode } from "react";

const AnimatedText = Animated.createAnimatedComponent(Text);

export function TransparentHeader({
  title,
  steps,
  right,
  color,
  colorInactive,
  allowBack = false,
}: {
  title?: string;
  steps?: { total: number; active: number };
  right?: ReactNode;
  color?: string;
  colorInactive?: string;
  allowBack?: boolean;
}) {
  const insets = useSafeAreaInsets();

  const firstRender = useIsFirstRender();

  const prevActiveStep = usePrevious(steps?.active);
  const movingForward =
    steps?.active != null &&
    (prevActiveStep == undefined || prevActiveStep < steps.active);

  return (
    <View style={[styles.header, { paddingTop: insets.top + 20 }]}>
      <View style={styles.controls}>
        <View style={styles.left}>
          {router.canGoBack() && allowBack && (
            <Animated.View
              entering={ZoomIn.duration(DURATION_FAST)}
              exiting={FadeOut.duration(DURATION_FAST)}
              style={{ alignSelf: "flex-start" }}
            >
              <TouchableScale
                onPress={() => router.back()}
                style={{
                  marginLeft: -7,
                  alignItems: "flex-start",
                  justifyContent: "center",
                }}
              >
                <Icon
                  name={"arrow.backward"}
                  size={16}
                  rectSize={32}
                  weight="medium"
                  color={color}
                />
              </TouchableScale>
            </Animated.View>
          )}
        </View>
        <View style={styles.right}>{right}</View>
      </View>
      {steps && (
        <StepProgress
          activeStep={steps.active}
          steps={steps.total}
          color={color}
          colorInactive={colorInactive}
        />
      )}
      {title && (
        <AnimatedText
          key={title}
          entering={
            !firstRender
              ? movingForward
                ? FadeInUp.duration(DURATION_FAST)
                : FadeInDown.duration(DURATION_FAST)
              : undefined
          }
          exiting={FadeOut.duration(DURATION_FAST)}
          variant="medium"
          style={styles.title}
          darkColor={color}
          lightColor={color}
        >
          {title}
        </AnimatedText>
      )}
    </View>
  );
}
