import * as Updates from "expo-updates";
import { useUpdates } from "expo-updates";
import { useToast } from "~/components/Toaster";
import { useEffect } from "react";

export function AppUpdatesIndicator() {
  const toastContext = useToast();
  const { isUpdatePending } = useUpdates();

  useEffect(() => {
    if (!toastContext.isReady || !isUpdatePending) {
      return;
    }

    const id = setTimeout(() => {
      const { toast } = toastContext;
      toast.info("App update available. Tap to apply", {
        id: "update-available",
        iconName: "arrow.triangle.2.circlepath.circle.fill",
        duration: 0,
        async onPress(id: string) {
          try {
            toastContext.api.hideAll();
            await new Promise((resolve) => setTimeout(resolve, 500));
            await Updates.reloadAsync();
          } catch (error) {
            console.error(error);
            toast.error("App update failed", { id, duration: 5_000 });
          }
        },
      });
    }, 3_000);

    return () => clearTimeout(id);
  }, [isUpdatePending, toastContext]);

  return null;
}
