import {
  Canvas,
  CornerPathEffect,
  DashPathEffect,
  Path,
} from "@shopify/react-native-skia";
import { useDerivedValue, useSharedValue } from "react-native-reanimated";
import { ColorName } from "~/constants/Colors";
import { useColor } from "~/components/Themed";
import { StyleSheet } from "react-native";

export function DashedBorder({
  borderWidth,
  borderColor,
  borderColorToken = "text",
  borderRadius = 20,
}: {
  borderWidth: number;
  borderColor?: string;
  borderColorToken?: ColorName;
  borderRadius?: number;
}) {
  const borderColorFromToken = useColor(borderColorToken);
  const color = borderColor ?? borderColorFromToken;

  const size = useSharedValue({ width: 0, height: 0 });

  const path = useDerivedValue(() => {
    return `M ${borderWidth} ${borderWidth} L ${size.value.width - borderWidth} ${borderWidth} L ${size.value.width - borderWidth} ${size.value.height - borderWidth} L ${borderWidth} ${size.value.height - borderWidth} Z`;
  });

  return (
    <Canvas style={StyleSheet.absoluteFill} onSize={size}>
      <Path path={path} strokeWidth={borderWidth} style="stroke" color={color}>
        <DashPathEffect intervals={[10, 5]} />
        <CornerPathEffect r={borderRadius} />
      </Path>
    </Canvas>
  );
}
