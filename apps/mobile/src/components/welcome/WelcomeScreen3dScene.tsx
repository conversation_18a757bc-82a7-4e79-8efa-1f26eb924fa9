import fuseBlobsUri from "../../../assets/3d-models/fuse-blobs.glb";

import Animated, {
  Extrapolation,
  interpolate,
  SharedValue,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";
import { useEffect, useRef } from "react";
import { Canvas, ThreeElement, useFrame } from "@react-three/fiber/native";
import { Float, useGLTF, useProgress } from "@react-three/drei/native";
import invariant from "invariant";
import { Group } from "three";
import { EffectComposer, Noise } from "@react-three/postprocessing";
import { useAppState } from "~/state/appState";

useGLTF.preload(fuseBlobsUri);

export function WelcomeScreen3dScene({ step }: { step: SharedValue<number> }) {
  const { appState } = useAppState();

  const modelsLoadingProgress = useProgress((state) => state.progress);

  const appear = useSharedValue(0);

  useEffect(() => {
    if (modelsLoadingProgress < 100) return;

    appear.value = withTiming(1, { duration: 3000 });
  }, [modelsLoadingProgress]);

  const position = useDerivedValue(() => {
    return [
      interpolate(step.value, [1, 2], [-3, -1.2], Extrapolation.CLAMP),
      // interpolate(step.value, [1, 2], [-1.2, -3], Extrapolation.CLAMP),
      interpolate(step.value, [1, 2], [-4.5, -4.5], Extrapolation.CLAMP),
      // interpolate(step.value, [1, 2], [-4.5, -4.5], Extrapolation.CLAMP),
      interpolate(
        appear.value * step.value,
        [0, 1, 2],
        [-6, -4, 5],
        Extrapolation.CLAMP
      ),
      // interpolate(
      //   appear.value * step.value,
      //   [0, 1, 2],
      //   [3.5, 5, -4],
      //   Extrapolation.CLAMP
      // ),
    ];
  });

  const rotation = useDerivedValue(() => {
    return [
      interpolate(step.value, [1, 2], [0, -1.5], Extrapolation.CLAMP),
      // interpolate(step.value, [1, 2], [-1.5, 0], Extrapolation.CLAMP),
      interpolate(step.value, [1, 2], [-0.5, -0.1], Extrapolation.CLAMP),
      // interpolate(step.value, [1, 2], [-0.1, -0.5], Extrapolation.CLAMP),
      0,
    ];
  });

  return (
    <Animated.View
      style={{
        flex: 1,
        opacity: appear,
      }}
    >
      <Canvas
        frameloop={appState === "active" ? "always" : "never"}
        style={{
          flex: 1,
        }}
      >
        <ambientLight intensity={0.15} />
        <directionalLight position={[2, 5, 7]} intensity={0.6} />
        <directionalLight position={[-1, -5, -7]} intensity={0.7} />

        <Float
          speed={2} // Animation speed, defaults to 1
          rotationIntensity={1} // XYZ rotation intensity, defaults to 1
          floatIntensity={1} // Up/down float intensity, works like a multiplier with floatingRange,defaults to 1
          floatingRange={[-0.2, 0.2]} // Range of y-axis values the object will float within, defaults to [-0.1,0.1]
        >
          <FuseBlobs position={position} rotation={rotation} scale={3} />
        </Float>
        {/* DISABLE NOISE FOR NOW DUE TO IT THROWING: "EXGL: renderbufferStorageMultisample() isn't implemented yet!" */}
        {/*<EffectComposer>*/}
        {/*  <Noise opacity={0.1} />*/}
        {/*</EffectComposer>*/}
      </Canvas>
    </Animated.View>
  );
}

function FuseBlobs({
  position,
  rotation,
  ...props
}: Omit<ThreeElement<any>, "object">) {
  const groupRef = useRef<Group>(null!);

  const model = useGLTF(fuseBlobsUri);
  invariant(!Array.isArray(model), "expected single model");

  const { nodes } = model;

  useFrame(() => {
    groupRef.current.position.x = position.value[0];
    groupRef.current.position.y = position.value[1];
    groupRef.current.position.z = position.value[2];

    groupRef.current.rotation.x = rotation.value[0];
    groupRef.current.rotation.y = rotation.value[1];
    groupRef.current.rotation.z = rotation.value[2];
  });

  return (
    <group ref={groupRef} {...props} dispose={null}>
      <group scale={0.03}>
        <group scale={13.692}>
          <mesh
            castShadow
            receiveShadow
            geometry={(nodes.Volume_Mesher1 as any).geometry}
            position={[3.618, 0.973, 1.101]}
            rotation={[1.072, -1.215, 1.028]}
          >
            <meshPhysicalMaterial
              reflectivity={2.9}
              transmission={0}
              roughness={0}
              metalness={0.2}
              ior={2}
            />
            {/*<meshStandardMaterial />*/}
          </mesh>
          <mesh
            castShadow
            receiveShadow
            geometry={(nodes.Sphere1 as any).geometry}
            position={[3.984, 1.06, 2.801]}
          >
            <meshPhysicalMaterial
              reflectivity={0.9}
              transmission={0}
              roughness={0}
              ior={2}
              metalness={0.2}
            />
          </mesh>
          <mesh
            castShadow
            receiveShadow
            geometry={(nodes.Sphere as any).geometry}
            position={[5.959, 5.513, 2.835]}
          >
            <meshPhysicalMaterial
              reflectivity={0.9}
              transmission={0}
              roughness={0}
              ior={2}
              metalness={0.2}
            />
          </mesh>
        </group>
      </group>
    </group>
  );
}
