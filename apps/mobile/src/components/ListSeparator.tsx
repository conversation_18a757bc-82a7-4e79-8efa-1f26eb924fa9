import { useColor, View } from "~/components/Themed";
import { memo } from "react";
import Svg, { Line } from "react-native-svg";
import { ColorName } from "~/constants/Colors";
import { StyleProp, ViewStyle } from "react-native";

export function ListSeparator({ style }: { style?: StyleProp<ViewStyle> }) {
  return <View background="listSeparator" style={[{ height: 1 }, style]} />;
}

export const DashedListSeparator = memo(function DashedListSeparator({
  colorToken = "dashedListSeparator",
}: {
  colorToken?: ColorName;
}) {
  const color = useColor(colorToken);

  return (
    <Svg height="1.4" width="100%">
      <Line
        x1="0"
        y1="1"
        x2="100%"
        y2="1"
        stroke={color}
        strokeWidth="1"
        strokeDasharray="8 8"
      />
    </Svg>
  );
});
