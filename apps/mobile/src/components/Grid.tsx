import { View, ViewProps } from "~/components/Themed";
import { ViewStyle } from "react-native";
import Colors, { ColorName } from "~/constants/Colors";
import { PropsWithChildren, ReactNode } from "react";
import { DebugProps } from "~/types/debug";

export function Grid<T>({
  items,
  renderItem,
  columns,
  gap,
  cellStyle,
  rowStyle,
  style,
}: {
  items: T[];
  renderItem: (item: T, index: number) => ReactNode;
  columns: number;
  gap: number;
  cellStyle?: ViewStyle;
  rowStyle?: ViewStyle;
  style?: ViewStyle;
}) {
  const rows = splitIntoRows(items, columns);

  return (
    <View style={[{ flex: 1, gap }, style]}>
      {rows.map((row, index) => (
        <Row key={index} gap={gap} style={rowStyle}>
          {row.map((item, index) => (
            <Cell key={index} style={cellStyle}>
              {renderItem(item, index)}
            </Cell>
          ))}
          {row.length < columns && <Cell />}
        </Row>
      ))}
    </View>
  );
}

function Cell({ children, style }: PropsWithChildren<{ style?: ViewStyle }>) {
  return <View style={[{ flex: 1 }, style]}>{children}</View>;
}

export function Row({
  children,
  flex,
  gap,
  justify = "flex-start",
  style,
  background,
  debug,
  ...restProps
}: PropsWithChildren<{
  flex?: true;
  gap?: number;
  justify?:
    | "flex-start"
    | "space-between"
    | "space-around"
    | "center"
    | "flex-end";
  background?: keyof typeof Colors.light & keyof typeof Colors.dark;
  style?: ViewStyle;
  debug?: true;
  collapsable?: ViewProps["collapsable"];
}>) {
  return (
    <View
      {...restProps}
      debug={debug}
      background={background}
      style={[
        {
          flex: flex ? 1 : undefined,
          flexDirection: "row",
          justifyContent: justify,
          alignItems: "center",
          gap,
        },
        style,
      ]}
    >
      {children}
    </View>
  );
}

export function Flex({
  gap,
  style,
  background,
  children,
  justify,
  ...rest
}: {
  gap?: number;
  justify?:
    | "flex-start"
    | "space-between"
    | "space-around"
    | "center"
    | "flex-end";
  style?: ViewStyle;
  background?: ColorName;
  children?: ReactNode;
} & ViewProps &
  DebugProps) {
  return (
    <View
      {...rest}
      background={background}
      style={[{ flex: 1, gap, justifyContent: justify }, style]}
    >
      {children}
    </View>
  );
}

function splitIntoRows<T>(items: T[], columns: number) {
  const pairs = [];
  for (let i = 0; i < items.length; i += columns) {
    pairs.push(items.slice(i, i + columns));
  }

  return pairs;
}
