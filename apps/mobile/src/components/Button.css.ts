import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
  button: {
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
    gap: 8,
    borderRadius: 27,
    borderCurve: "continuous",
  },
  sizeXLarge: {
    paddingHorizontal: 24,
    height: 56,
  },
  sizeLarge: {
    paddingHorizontal: 24,
    height: 54,
  },
  sizeMediumNew: {
    paddingHorizontal: 30,
    height: 48,
  },
  sizeMiddle: {
    paddingHorizontal: 18,
    height: 42,
  },
  sizeSmall: {
    paddingHorizontal: 16,
    height: 34,
  },
  circle: {
    aspectRatio: 1,
    width: 54,
    borderRadius: 9999,
  },
  disabled: {
    opacity: 0.5,
  },
  textXLarge: {
    fontSize: 18,
  },
  textRegular: {
    fontSize: 18,
  },
  textMediumNew: {
    fontSize: 16,
  },
  textMedium: {
    fontSize: 15,
  },
  textSmall: {
    fontSize: 15,
  },
});
