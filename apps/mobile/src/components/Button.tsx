import { styles } from "./Button.css";

import {
  ComponentProps,
  ForwardedRef,
  forwardRef,
  ReactNode,
  useMemo,
} from "react";
import { Insets, TextStyle, View, ViewStyle } from "react-native";
import { Text, useColor } from "~/components/Themed";
import { ColorName } from "~/constants/Colors";
import { TouchableScale } from "~/components/TouchableScale";
import { Icon } from "~/components/Icon";
import { Spinner } from "~/components/Spinner";
import { BaseButton } from "react-native-gesture-handler";
import Animated, {
  FadeInDown,
  FadeInRight,
  FadeOutLeft,
  FadeOutUp,
  LayoutAnimationConfig,
  ReduceMotion,
} from "react-native-reanimated";
import { DURATION_FAST } from "~/constants/animations";
import { SFSymbol as SFSymbolName } from "sf-symbols-typescript";

type Variant =
  | "primary"
  | "secondary"
  | "danger"
  | "success"
  | "primary-opposite"
  | "secondary-opposite";

const backgroundByVariant: Record<Variant, ColorName> = {
  primary: "backgroundButtonPrimary",
  secondary: "backgroundButtonSecondary",
  "primary-opposite": "backgroundButtonPrimaryOpposite",
  danger: "backgroundButtonDanger",
  success: "backgroundButtonSuccess",
  "secondary-opposite": "backgroundButtonSecondaryOpposite",
} as const;

const textColorByVariant = {
  primary: "textButtonPrimary",
  secondary: "textButtonSecondary",
  danger: "textButtonDanger",
  success: "textButtonSuccess",
  "primary-opposite": "textButtonPrimaryOpposite",
  "secondary-opposite": "textButtonSecondaryOpposite",
} as const;

const AnimatedText = Animated.createAnimatedComponent(Text);

export const Button = forwardRef(function Button(
  {
    icon,
    iconName,
    iconSize = 12,
    iconWeight = "regular",
    children,
    variant = "primary",
    size = "large",
    circle = false,
    loading = false,
    loadingText,
    disabled = false,
    error,
    warning,
    onPress,
    hitSlop,
    style,
    animation = "top-down",
    animationDuration = DURATION_FAST,
    activeScale = 0.95,
  }: {
    icon?: ReactNode;
    iconName?: SFSymbolName;
    iconSize?: number;
    iconWeight?: ComponentProps<typeof Icon>["weight"];
    children?: ReactNode;
    variant?: Variant;
    size?: "xlarge" | "large" | "medium-new" | "medium" | "small";
    circle?: boolean;
    loading?: boolean;
    loadingText?: string;
    disabled?: boolean;
    error?: ReactNode;
    warning?: ReactNode;
    onPress?: () => void;
    hitSlop?: Insets | number;
    style?: {
      view?: ViewStyle;
      text?: TextStyle;
    };
    animation?: "top-down" | "left-right" | false;
    animationDuration?: number;
    activeScale?: number;
  },
  ref: ForwardedRef<typeof BaseButton>
) {
  const textColorToken = error
    ? "textToastDanger"
    : warning
      ? "textToastWarning"
      : textColorByVariant[variant];

  const background = useColor(
    error
      ? "backgroundToastDanger"
      : warning
        ? "backgroundToastWarning"
        : (backgroundByVariant[variant] ?? "backgroundButtonPrimary")
  );

  const iconColor = useColor(textColorToken);

  const animations = useMemo(() => {
    return animation === false
      ? null
      : animation === "top-down"
        ? {
            entering: FadeInDown.duration(animationDuration).reduceMotion(
              ReduceMotion.Never
            ),
            exiting: FadeOutUp.duration(animationDuration).reduceMotion(
              ReduceMotion.Never
            ),
          }
        : {
            entering: FadeInRight.duration(animationDuration).reduceMotion(
              ReduceMotion.Never
            ),
            exiting: FadeOutLeft.duration(animationDuration).reduceMotion(
              ReduceMotion.Never
            ),
          };
  }, [animation, animationDuration]);

  const textLabelStyle =
    size === "small"
      ? styles.textSmall
      : size === "medium"
        ? styles.textMedium
        : size === "medium-new"
          ? styles.textMediumNew
          : size === "large"
            ? styles.textRegular
            : styles.textXLarge;

  return (
    <TouchableScale
      ref={ref}
      disabled={loading || disabled || !!error || !!warning}
      activeScale={activeScale}
      onPress={onPress}
      hitSlop={hitSlop}
    >
      <View
        style={[
          { backgroundColor: background },
          styles.button,
          size === "small"
            ? styles.sizeSmall
            : size === "medium"
              ? styles.sizeMiddle
              : size === "medium-new"
                ? styles.sizeMediumNew
                : size === "large"
                  ? styles.sizeLarge
                  : styles.sizeXLarge,
          circle && styles.circle,
          !error && !warning && disabled && styles.disabled,
          style?.view,
        ]}
      >
        {loading ? (
          <>
            <Spinner
              size={size == "large" ? 20 : 16}
              colorToken={textColorToken}
            />
            {loadingText && (
              <Text
                variant="semibold"
                colorToken={textColorToken}
                style={[textLabelStyle, style?.text]}
                numberOfLines={1}
              >
                {loadingText}
              </Text>
            )}
          </>
        ) : (
          <LayoutAnimationConfig skipEntering skipExiting>
            {!(error || warning) &&
              (icon ??
                (iconName ? (
                  <Icon
                    name={iconName}
                    size={iconSize}
                    color={style?.text?.color?.toString() ?? iconColor}
                    weight={iconWeight}
                  />
                ) : null))}
            {(error || warning || children) && (
              <AnimatedText
                key={String(error || warning || children)}
                entering={animations?.entering}
                exiting={animations?.exiting}
                style={[textLabelStyle, style?.text]}
                variant={
                  size === "small" || size === "medium" ? "medium" : "semibold"
                }
                colorToken={textColorToken}
                numberOfLines={1}
              >
                {error ?? warning ?? children}
              </AnimatedText>
            )}
          </LayoutAnimationConfig>
        )}
      </View>
    </TouchableScale>
  );
});
