import { ReactNode } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Flex, Row } from "~/components/Grid";
import { Icon } from "~/components/Icon";
import { Text, useColor, View } from "~/components/Themed";
import { TouchableScale } from "~/components/TouchableScale";
import * as Haptics from "expo-haptics";
import { ListSeparator } from "~/components/ListSeparator";
import { confirmActivity } from "~/services/activities";
import { useActiveWallet } from "~/state/wallet";
import { useSuspenseToken } from "~/state/tokens";
import invariant from "invariant";
import { executeAction, prepareAction } from "~/services/wallets";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { formatUsdValue } from "@squads/utils/numberFormats";
import { showCardActivityInfoModal } from "~/components/card/CardActivityInfoModal";
import { showCancelCardActivityModal } from "~/components/card/CancelCardActivityModal";
import { refetchCard, refetchCardEvents } from "~/state/bridge";
import { Button } from "~/components/Button";
import Animated, { LinearTransition } from "react-native-reanimated";
import { SPRING } from "~/constants/animations";
import { CardPendingAction } from "~/services/bridge";
import { useCountdown } from "~/hooks/useCountdown";
import {
  AnimatedButtonGroup,
  BUTTON_GROUP_EXIT_ANIMATION_DURATION,
} from "~/components/AnimatedButtonGroup";
import { hapticSelect } from "~/utils/haptics";

export function PendingWithdraw({
  timeLockSeconds,
  pendingAction,
}: {
  timeLockSeconds: number;
  pendingAction: CardPendingAction & { details: { type: "cardWithdraw" } };
}) {
  const { wallet } = useActiveWallet();
  const queryClient = useQueryClient();

  const token = useSuspenseToken({ mint: pendingAction.details.mint });
  invariant(token, "Invalid token");
  const decimals = token.decimals;
  const amount = pendingAction.details.amount / 10 ** decimals;

  async function completeAction() {
    const action = await prepareAction({
      type: "cardWithdrawExecute",
      activityId: pendingAction.activityId,
    });

    const signedTransactions = await signTransactionsWithFuseKeys(
      wallet.keys,
      action.transactions
    );

    const { activityId } = await executeAction({ signedTransactions });
    await confirmActivity({ activityId });

    await Promise.all([
      refetchCard({ queryClient }),
      refetchCardEvents(queryClient),
    ]);
  }

  const name = wallet.vaults[0].name ?? "Main Account";

  return (
    <PendingActivityView
      key={pendingAction.activityId}
      labelVariant={"withdrawal"}
      timeLockStartedSeconds={pendingAction.approvedAt}
      timeLockDurationSeconds={timeLockSeconds}
      body={
        <Text variant={"semibold"}>
          {formatUsdValue(amount)}
          <Text variant={"semibold"} colorToken={"textSecondary"}>
            {"  →  "}
          </Text>
          <Text variant={"medium"}>{name}</Text>
        </Text>
      }
      onInfo={() => showCardActivityInfoModal({ actionType: "withdrawal" })}
      onCancel={() => showCancelCardActivityModal(pendingAction.activityId)}
      onConfirm={() => completeAction()}
    />
  );
}

export function PendingSLChange({
  timeLockSeconds,
  pendingAction,
}: {
  timeLockSeconds: number;
  pendingAction: CardPendingAction & { details: { type: "cardSLChange" } };
}) {
  const { wallet } = useActiveWallet();
  const queryClient = useQueryClient();

  const token = useSuspenseToken({ mint: pendingAction.details.mint });
  invariant(token, "Invalid token");
  const decimals = token.decimals;

  const oldAmount = pendingAction.details.oldAmount / 10 ** decimals;
  const newAmount = pendingAction.details.newAmount / 10 ** decimals;

  async function completeAction() {
    const action = await prepareAction({
      type: "cardSLChangeExecute",
      activityId: pendingAction.activityId,
    });

    const signedTransactions = await signTransactionsWithFuseKeys(
      wallet.keys,
      action.transactions
    );

    const { activityId } = await executeAction({ signedTransactions });
    await confirmActivity({ activityId });

    await refetchCard({ queryClient });
  }

  return (
    <PendingActivityView
      labelVariant={"cardSLChange"}
      timeLockStartedSeconds={pendingAction.approvedAt}
      timeLockDurationSeconds={timeLockSeconds}
      body={
        <Text variant={"medium"}>
          {formatUsdValue(oldAmount)}
          <Text variant={"semibold"} colorToken={"textSecondary"}>
            {"  →  "}
          </Text>
          <Text variant={"semibold"}>{formatUsdValue(newAmount)}</Text>
        </Text>
      }
      onInfo={() => showCardActivityInfoModal({ actionType: "cardSLChange" })}
      onCancel={() => showCancelCardActivityModal(pendingAction.activityId)}
      onConfirm={() => completeAction()}
    />
  );
}

const ViewLayoutAnimation = LinearTransition.springify()
  .stiffness(SPRING.stiffness)
  .damping(SPRING.damping)
  .mass(SPRING.mass);

function PendingActivityView({
  labelVariant,
  body,
  timeLockStartedSeconds,
  timeLockDurationSeconds,
  onInfo,
  onCancel,
  onConfirm,
}: {
  labelVariant: "withdrawal" | "cardSLChange";
  body: ReactNode;
  timeLockStartedSeconds: number;
  timeLockDurationSeconds: number;
  onInfo: () => void;
  onCancel: () => void;
  onConfirm: () => Promise<void>;
}) {
  const { remaining: timeLockRemaining, isReady } = useCountdown({
    timestamp: timeLockStartedSeconds,
    duration: timeLockDurationSeconds,
  });

  const confirmMutation = useMutation({
    mutationFn: onConfirm,
  });

  const background = useColor("backgroundBanner");

  return (
    <Animated.View
      layout={ViewLayoutAnimation}
      style={{
        padding: 16,
        gap: 8,
        flexDirection: "row",
        alignItems: "baseline",
        borderRadius: 16,
        borderCurve: "continuous",
        backgroundColor: background,
      }}
    >
      <Icon
        name={"arrow.up.square.fill"}
        size={12}
        colorToken={"textSecondary"}
      />
      <Flex gap={16}>
        <View gap={10}>
          <Row justify={"space-between"}>
            <Text variant={"medium"} size={15} colorToken={"textSecondary"}>
              Pending{" "}
              {labelVariant === "withdrawal"
                ? "withdrawal"
                : "Daily Limit change"}
            </Text>
            <TouchableScale
              hitSlop={8}
              onPress={() => {
                Haptics.selectionAsync();
                onInfo();
              }}
            >
              <Icon
                name={"info.circle"}
                colorToken={"textSecondary"}
                size={12}
              />
            </TouchableScale>
          </Row>
          {body}
          <ListSeparator />
        </View>
        <Row>
          <Text
            style={{ maxWidth: "80%" }}
            variant={"medium"}
            size={13}
            colorToken={"textSecondary"}
          >
            {labelVariant === "withdrawal"
              ? isReady
                ? `Press Complete to finalize the withdrawal.`
                : `Wait until the countdown is finished to complete the withdrawal.`
              : isReady
                ? `Press complete to finalize the daily spending limit change.`
                : `Wait for the countdown to finalize your new daily spending limit.`}
          </Text>
        </Row>
        <AnimatedButtonGroup
          distributeEvenly={false}
          left={
            confirmMutation.isPending ? null : (
              <Button
                size={"small"}
                variant={"secondary"}
                hitSlop={4}
                style={{
                  view: { minWidth: 84 },
                }}
                onPress={() => {
                  hapticSelect();
                  onCancel();
                }}
              >
                Cancel
              </Button>
            )
          }
          right={
            <Button
              size={"small"}
              variant={isReady ? "success" : "primary"}
              hitSlop={4}
              animation={isReady ? "left-right" : false}
              loading={confirmMutation.isPending}
              loadingText={"Confirming"}
              animationDuration={BUTTON_GROUP_EXIT_ANIMATION_DURATION}
              style={{
                view: { minWidth: 84 },
                text: { fontVariant: ["tabular-nums"] },
              }}
              onPress={() => {
                if (!isReady) return;
                if (confirmMutation.isPending) return;

                hapticSelect();
                confirmMutation.mutate();
              }}
            >
              {isReady ? "Complete" : timeLockRemaining.toFormat("mm:ss")}
            </Button>
          }
        />
      </Flex>
    </Animated.View>
  );
}
