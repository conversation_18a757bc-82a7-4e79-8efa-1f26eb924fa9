import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, useColor, View } from "~/components/Themed";
import { useEffect, useRef } from "react";
import { create } from "zustand";
import { BridgeAccount, CardAuthorization, CardEvent } from "~/services/bridge";
import {
  OnchainActivityIcon,
  TransactionIcon,
} from "~/components/card/CardIcons";
import { mints } from "~/constants/tokens";
import {
  formatCurrency,
  formatFiatValue,
  formatUsdValue,
} from "@squads/utils/numberFormats";
import { formatSPLAmount } from "~/utils/tokens";
import { DateTime } from "luxon";
import { Flex, Row } from "~/components/Grid";
import { TouchableScale } from "~/components/TouchableScale";
import * as Clipboard from "expo-clipboard";
import { Icon } from "~/components/Icon";
import { useActiveWallet } from "~/state/wallet";
import invariant from "invariant";
import { Activity } from "~/services/activities";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import { hapticSelect, hapticSuccess } from "~/utils/haptics";
import { Linking, StyleSheet } from "react-native";
import { useLazyBridgeAccount } from "~/state/bridge";
import { SFSymbol as SFSymbolName } from "sf-symbols-typescript";
import { CopyText } from "~/components/CopyText";

const useModalState = create<{
  event: CardEvent | null;
  hide: () => void;
}>((set) => ({ event: null, hide: () => set({ event: null }) }));

export function showCardEventModal(event: CardEvent) {
  useModalState.setState({ event });
}

export function CardEventModal() {
  const { data: bridgeAccount } = useLazyBridgeAccount();

  const modalRef = useRef<BottomModalImperativeMethods>(null);
  const { event, hide } = useModalState();

  useEffect(() => {
    if (event) {
      modalRef.current?.present();
    } else {
      modalRef.current?.close();
    }
  }, [event]);

  if (!event) return null;

  const hasFooter =
    event.type !== "onchainActivity" && event.type !== "decline";

  return (
    <BottomModal
      modalId="card-event-details"
      modalRef={modalRef}
      title={" "}
      onClose={hide}
      onDismiss={hide}
      body={
        <View
          gap={24}
          style={{
            marginTop: -20,
            paddingHorizontal: 12,
            paddingBottom: !hasFooter ? 12 : undefined,
          }}
        >
          <View gap={12} style={{ alignItems: "center" }}>
            <EventIcon event={event} />

            <View
              gap={4}
              style={{ alignItems: "center", paddingHorizontal: 20 }}
            >
              <AmountText event={event} />
              <Text
                variant="semibold"
                size={14}
                colorToken="textSecondary"
                style={{ textAlign: "center" }}
              >
                {CardEvent.description(event)}
              </Text>
            </View>
          </View>

          <View gap={20}>
            <View gap={8}>
              <EventStatusTag event={event} />

              {event.type === "authorization" && (
                <ReservedAmountWarning authorization={event.authorization} />
              )}
            </View>

            <View style={{ gap: 16 }}>
              <EventDetails event={event} />
            </View>
          </View>
        </View>
      }
      footer={
        hasFooter && (
          <>
            <DisputeTransactionButton
              onPress={() =>
                Linking.openURL(makeDisputeEmail(event, bridgeAccount ?? null))
              }
            />
          </>
        )
      }
    />
  );
}

function DisputeTransactionButton({ onPress }: { onPress: () => void }) {
  return (
    <TouchableScale
      hitSlop={8}
      style={{ paddingVertical: 4 }}
      onPress={() => {
        hapticSelect();
        onPress();
      }}
    >
      <Row gap={8} justify={"center"} style={{ alignItems: "baseline" }}>
        <Text variant={"medium"} size={14} colorToken={"textSecondary"}>
          Dispute transaction
        </Text>
        <Icon
          name="exclamationmark.bubble"
          size={10}
          colorToken={"textSecondary"}
        />
      </Row>
    </TouchableScale>
  );
}

function makeDisputeEmail(
  event: CardEvent & { type: "authorization" | "transaction" },
  bridgeAccount: BridgeAccount | null
) {
  const data =
    event.type === "transaction" ? event.transaction : event.authorization;

  const body = `
---
Customer id: ${bridgeAccount?.customer_id}
Transaction id: ${CardEvent.id(event)}
Transaction type: ${event.type}
Transaction date: ${DateTime.fromSeconds(CardEvent.timestamp(event)).toLocaleString(DateTime.DATETIME_MED)}
Transaction amount: ${data.amount}
Transaction description: ${data.description}
`.trim();
  return `mailto:<EMAIL>?subject=Transaction&body=${encodeURIComponent(body)}`;
}

function EventIcon({ event }: { event: CardEvent }) {
  switch (event.type) {
    case "authorization":
      return (
        <TransactionIcon size={54} category={event.authorization.category} />
      );

    case "onchainActivity":
      return (
        <OnchainActivityIcon
          mint={mints.usdc}
          size={54}
          type={
            event.activity.details.type === "cardWithdraw"
              ? "cardWithdraw"
              : "cardTopUp"
          }
        />
      );

    case "transaction":
      return (
        <TransactionIcon size={54} category={event.transaction.category} />
      );

    case "decline":
      return <TransactionIcon size={54} category={event.decline.category} />;

    default:
      event satisfies never;
      return null;
  }
}

function AmountText({ event }: { event: CardEvent }) {
  switch (event.type) {
    case "authorization":
      return (
        <Text variant="bold" size={28}>
          {formatUsdValue(Math.abs(event.authorization.amount))}
        </Text>
      );

    case "onchainActivity":
      const activityDetails = event.activity.details as Activity["details"] & {
        type: "cardWithdraw" | "cardTopUp" | "cardReferralReward";
      };

      const amount = activityDetails.amount;
      const decimals = activityDetails.decimals;

      return (
        <Text variant="bold" size={28}>
          {formatSPLAmount({ amount, decimals, symbol: "USDC" })}
        </Text>
      );

    case "transaction":
      return (
        <Text variant="bold" size={28}>
          {formatUsdValue(Math.abs(event.transaction.amount))}
        </Text>
      );

    case "decline":
      return (
        <Text variant="bold" size={28}>
          {formatUsdValue(Math.abs(event.decline.amount))}
        </Text>
      );

    default:
      event satisfies never;
      return null;
  }
}

function EventStatusTag({ event }: { event: CardEvent }) {
  let statusTag;
  switch (event.type) {
    case "authorization":
      statusTag = <StatusTag variant={"pending"} />;
      break;

    case "onchainActivity":
      statusTag = <StatusTag variant={"success"} />;
      break;

    case "transaction":
      if (event.transaction.type === "refund") {
        statusTag = <StatusTag variant={"refund"} />;
      } else {
        statusTag = <StatusTag variant={"success"} />;
      }
      break;

    case "decline":
      statusTag = <StatusTag variant={"decline"} />;
      break;

    default:
      event satisfies never;
      return null;
  }

  const border = useColor("border");

  return (
    <Row
      justify={"space-between"}
      style={{
        flex: 1,
        paddingVertical: 16,
        paddingHorizontal: 16,
        marginHorizontal: -16,
        borderColor: border,
        borderWidth: StyleSheet.hairlineWidth,
        borderRadius: 16,
        borderCurve: "continuous",
      }}
    >
      <Text size={14} variant="medium">
        Status
      </Text>
      {statusTag}
    </Row>
  );
}

function ReservedAmountWarning({
  authorization,
}: {
  authorization: CardAuthorization;
}) {
  const reservedAmount = authorization.amount - authorization.billingAmount;

  if (reservedAmount <= 0) return null;

  return (
    <View
      background={"backgroundSecondary"}
      style={{
        padding: 16,
        borderRadius: 16,
        borderCurve: "continuous",
        marginHorizontal: -16,
      }}
    >
      <Text size={14} colorToken={"textSecondary"} variant={"medium"}>
        We reserved amount of{" "}
        <Text colorToken={"text"} size={14} variant={"medium"}>
          {formatUsdValue(reservedAmount)}
        </Text>{" "}
        for this{"\n"}
        transaction. This amount will become available{"\n"}
        in 24 hours once transactions are cleared.
      </Text>
    </View>
  );
}

function StatusTag({
  variant,
}: {
  variant: "success" | "pending" | "refund" | "decline";
}) {
  const green = useColor("green");
  const blue = useColor("blue");
  const red = useColor("red");
  const textSecondary = useColor("textSecondary");

  const mapping: Record<
    typeof variant,
    { icon: SFSymbolName; text: string; color: string }
  > = {
    success: {
      icon: "checkmark.circle.fill",
      text: "Successful",
      color: green,
    },
    pending: {
      icon: "clock.fill",
      text: "Pending",
      color: textSecondary,
    },
    refund: {
      icon: "arrow.counterclockwise.circle.fill",
      text: "Refunded",
      color: blue,
    },
    decline: {
      icon: "xmark.circle.fill",
      text: "Declined",
      color: red,
    },
  };

  const tag = mapping[variant];

  return (
    <View
      style={{
        flexDirection: "row",
        alignItems: "baseline",
        gap: 6,
      }}
    >
      <Text size={13} variant="medium" style={{ color: tag.color }}>
        {tag.text}
      </Text>
      <Icon
        name={tag.icon}
        size={10}
        color={tag.color}
        style={{ marginRight: 4 }}
      />
    </View>
  );
}

function EventDetails({ event }: { event: CardEvent }) {
  const { wallet } = useActiveWallet();
  const card = wallet.card;
  invariant(card?.status === "issued", "card is not issued");

  const activityDate = DateTime.fromSeconds(
    CardEvent.timestamp(event)
  ).toLocaleString(DateTime.DATETIME_MED);

  switch (event.type) {
    case "authorization":
      return (
        <>
          <EventDetailsRow label={"Date"} value={activityDate} />
          <EventDetailsRow
            label={"Authorization ID"}
            value={event.authorization.id}
            copyValue={event.authorization.id}
          />
        </>
      );

    case "decline":
      return (
        <>
          <EventDetailsRow label={"Date"} value={activityDate} />
          <EventDetailsRow
            label={"Authorization ID"}
            value={event.decline.id}
            copyValue={event.decline.id}
          />
        </>
      );

    case "transaction":
      return (
        <>
          <EventDetailsRow label={"Date"} value={activityDate} />

          {event.transaction.localTransactionDetails && (
            <>
              <EventDetailsRow
                label={"Local Amount"}
                value={formatFiatValue(
                  Math.abs(
                    Number(event.transaction.localTransactionDetails.amount)
                  ),
                  {
                    currency:
                      event.transaction.localTransactionDetails.currency,
                  }
                )}
              />

              <EventDetailsRow
                label={"Exchange Rate"}
                value={
                  formatFiatValue(
                    Number(
                      event.transaction.localTransactionDetails.exchangeRate
                    )
                  ) +
                  ` USD / ${formatCurrency(event.transaction.localTransactionDetails.currency)}`
                }
              />
            </>
          )}

          <EventDetailsRow
            label={"Transaction ID"}
            value={event.transaction.id}
            copyValue={event.transaction.id}
          />
        </>
      );

    case "onchainActivity":
      const executeTx = event.activity.transactions.find(
        (tx) => tx.transactionType === "execute"
      );

      const executeTxTimestamp =
        executeTx &&
        DateTime.fromSeconds(executeTx.timestamp).toLocaleString(
          DateTime.DATETIME_MED
        );

      return (
        <>
          <EventDetailsRow
            label={"Date"}
            value={executeTxTimestamp ?? activityDate}
          />
          {event.activity.details.type === "cardTopUp" && (
            <EventDetailsRow
              label={"From"}
              value={wallet.vaults[0]?.name ?? "Main Account"}
            />
          )}
          {event.activity.details.type === "cardWithdraw" && (
            <EventDetailsRow
              label={"To"}
              value={wallet.vaults[0]?.name ?? "Main Account"}
            />
          )}
          {executeTx && (
            <EventDetailsRow
              key={executeTx.signature}
              label={"Onchain transaction"}
              value={abbreviateAddress(executeTx.signature)}
              copyValue={executeTx.signature}
            />
          )}
        </>
      );

    default:
      event satisfies never;
      return null;
  }
}

function EventDetailsRow({
  label,
  value,
  copyValue,
}: {
  label: string;
  value: string;
  copyValue?: string;
}) {
  return (
    <Row flex justify={"space-between"} gap={24}>
      <Text colorToken={"textSecondary"} variant={"medium"}>
        {label}
      </Text>
      <Flex style={{ alignItems: "flex-end" }}>
        {copyValue ? (
          <CopyText
            colorToken={"text"}
            variant={"medium"}
            numberOfLines={1}
            style={{ flexShrink: 1 }}
            onPress={() => {
              Clipboard.setStringAsync(copyValue);
              hapticSuccess();
            }}
          >
            {value}
          </CopyText>
        ) : (
          <Text numberOfLines={1} variant={"medium"}>
            {value}
          </Text>
        )}
      </Flex>
    </Row>
  );
}
