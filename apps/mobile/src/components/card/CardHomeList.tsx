import { Text, useColor, View } from "~/components/Themed";
import {
  refetchCardBalance,
  useCardEvents,
  useSuspenseCardBalance,
} from "~/state/bridge";
import { DateTime } from "luxon";
import {
  Card,
  CardEvent,
  CardPendingAction,
  CardTransactionCategory,
} from "~/services/bridge";
import Animated, {
  AnimatedRef,
  FadeOut,
  interpolate,
  LinearTransition,
  ReduceMotion,
  SharedValue,
  useAnimatedStyle,
} from "react-native-reanimated";
import {
  ComponentProps,
  ForwardedRef,
  ReactNode,
  useMemo,
  useRef,
  useState,
} from "react";
import { Flex, Row } from "~/components/Grid";
import { formatUsdValue } from "@squads/utils/numberFormats";
import { mints } from "~/constants/tokens";
import { useActiveWallet } from "~/state/wallet";
import { formatSPLAmount } from "~/utils/tokens";
import { Icon } from "~/components/Icon";
import { IconWrapper } from "~/components/IconWrapper";
import {
  Dimensions,
  type FlatList,
  RefreshControl,
  StyleSheet,
} from "react-native";
import {
  hapticOpenBottomTray,
  hapticOpenModalSheet,
  hapticSelect,
  hapticSuccess,
} from "~/utils/haptics";
import { CardImage } from "~/components/card/CardImage";
import { CancelCardActivityModal } from "~/components/card/CancelCardActivityModal";
import { CardActivityInfoModal } from "~/components/card/CardActivityInfoModal";
import {
  BalanceFallback,
  BalanceSkeleton,
  BalanceView,
} from "~/components/Balance";
import { ActionButton } from "~/components/ActionButton";
import { showCardFrozenModal } from "~/components/card/CardFrozenModal";
import { router, useRouter } from "expo-router";
import { showCardPendingActivityModal } from "~/components/card/CardPendingActivityModal";
import { VisaCard } from "~/components/card/VisaCard";
import { Button } from "~/components/Button";
import { showCardUnfreezeModal } from "~/components/card/CardUnfreezeModal";
import {
  PendingSLChange,
  PendingWithdraw,
} from "~/components/card/PendingActivityView";
import {
  DURATION_FAST,
  DURATION_MEDIUM,
  SPRING,
  ZoomAndFadeIn,
  ZoomAndFadeOut,
} from "~/constants/animations";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import { ActivitiesStackGraphic } from "~/components/card/ActivitiesStackGraphic";
import { useHeaderHeight } from "@react-navigation/elements";
import { showCardEventModal } from "~/components/card/CardEventModal";
import {
  OnchainActivityIcon,
  TransactionIcon,
} from "~/components/card/CardIcons";
import MaskedView from "@react-native-masked-view/masked-view";
import { ShiningGradientFill } from "~/components/ShiningGradientFill";
import { LinearGradient } from "expo-linear-gradient";
import { hexToRgba } from "~/constants/Colors";
import * as Haptics from "expo-haptics";
import { FuseSuspense } from "~/components/FuseSuspense";
import { FuseErrorBoundary } from "~/components/errors/FuseErrorBoundary";
import { AppleWalletIcon } from "~/components/icons/AppleWalletIcon";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import {
  CardAnimationPoints,
  startCardAnimation,
  useCardIntroAnimationState,
  useCardOpacityAnimationStyle,
} from "~/hooks/useCardAnimation";
import { isWalletOffline } from "~/hooks/useAssertWalletState";
import { useToast } from "~/components/Toaster";
import { useQueryClient } from "@tanstack/react-query";
import { OnboardingText } from "~/components/card/OnboardingText";
import { LowSpendingLimitWarning } from "~/components/card/LowSpendingLimitWarning";
import { addCardToAppleWallet } from "~/services/card";
import { BottomModalImperativeMethods } from "~/components/BottomModal";
import {
  CardBalanceModal,
  CardBalanceModalParams,
} from "~/components/card/CardBalanceModal";

const { height: SCREEN_HEIGHT } = Dimensions.get("screen");

const LIST_ITEM_LAYOUT_ANIMATION = LinearTransition.springify()
  .stiffness(SPRING.stiffness)
  .damping(SPRING.damping)
  .mass(SPRING.mass);

export type ListItem =
  | {
      rowType: "list-title";
      title: string;
      pageIndex: number;
      right?: ReactNode;
    }
  | {
      rowType: "section-header";
      date: string;
      pageIndex: number;
    }
  | {
      rowType: "event";
      event: CardEvent;
      pageIndex: number;
    };

const SCROLL_VIEW_PADDING_TOP = 16;
const SCROLL_VIEW_PADDING_BOTTOM = 20;

export const TRANSACTION_ITEM_ENTERING = ZoomAndFadeIn.withInitialValues({
  opacity: 0,
  transform: [{ scale: 0.97 }],
})
  .stiffness(SPRING.stiffness)
  .damping(SPRING.damping)
  .mass(SPRING.mass)
  .reduceMotion(ReduceMotion.Never);

export function CardHomeList({
  card,
  animatedScrollRef,
  animationProgress,
}: {
  card: Card;
  animatedScrollRef: AnimatedRef<Animated.ScrollView>;
  animationProgress: SharedValue<number>;
}) {
  const headerHeight = useHeaderHeight();
  const queryClient = useQueryClient();

  // NOTE: we can't use `isFetching` from react-query because it changes async
  // and causes refresh indicator to flicker weirdly. @see https://github.com/facebook/react-native/issues/32836
  const [refreshing, setRefreshing] = useState(false);
  const refreshControlColor = useColor("textTertiary");

  const cardEventsQueryResult = useCardEvents();

  const listItems = useMemo((): ListItem[] => {
    if (cardEventsQueryResult.status !== "success") {
      return [{ rowType: "list-title", title: "Transactions", pageIndex: 0 }];
    }

    const eventsByDate = new Map<string, ListItem[]>();
    const allEvents = cardEventsQueryResult.data.pages.flatMap((p) => p.events);
    if (allEvents.length === 0) {
      return [];
    }

    const titleRow = {
      rowType: "list-title",
      title: "Transactions",
      pageIndex: 0,
      right:
        allEvents.filter((e) => e.type === "transaction").length > 0 ? (
          <AnimatedTouchableScale
            style={{ padding: 15, margin: -15 }}
            onPress={() => {
              hapticSelect();
              router.push(`/unlocked/bridge/card/spending`);
            }}
          >
            <Icon
              name={"chart.pie"}
              size={12}
              weight={"semibold"}
              colorToken={"textSecondary"}
            />
          </AnimatedTouchableScale>
        ) : null,
    } as const;

    for (const page of cardEventsQueryResult.data.pages) {
      for (const [index, event] of page.events.entries()) {
        const dateTime = DateTime.fromSeconds(CardEvent.timestamp(event));
        const date = dateTime.equals(DateTime.now())
          ? "Today"
          : dateTime.toLocaleString(DateTime.DATE_MED);

        eventsByDate.set(date, [
          ...(eventsByDate.get(date) ?? []),
          { rowType: "event", event, pageIndex: index } as const,
        ]);
      }
    }

    const sectionAndEventRows = Array.from(eventsByDate.entries()).flatMap(
      ([date, events]) => {
        return [
          { rowType: "section-header", date, pageIndex: 0 } as const,
          ...events,
        ] satisfies ListItem[];
      }
    );

    return [titleRow, ...sectionAndEventRows];
  }, [cardEventsQueryResult]);

  const opacityAnimatedStyle = useCardOpacityAnimationStyle(animationProgress);
  const animationState = useCardIntroAnimationState(animationProgress);

  const introAnimationCompleted = animationState > 0;

  return (
    <>
      {introAnimationCompleted ? null : (
        <IntroductionFooter card={card} animationProgress={animationProgress} />
      )}

      <Animated.FlatList
        //todo fix disabling scroll causes jumping
        // scrollEnabled={introAnimationCompleted}
        ref={animatedScrollRef as unknown as ForwardedRef<FlatList>}
        contentInsetAdjustmentBehavior="automatic"
        style={{
          paddingHorizontal: 20,
        }}
        contentContainerStyle={{
          paddingTop: SCROLL_VIEW_PADDING_TOP,
          // calc(100% - headerHeight - paddingTop - paddingBottom)
          minHeight:
            SCREEN_HEIGHT -
            headerHeight -
            SCROLL_VIEW_PADDING_TOP -
            SCROLL_VIEW_PADDING_BOTTOM,
        }}
        data={listItems}
        refreshControl={
          <RefreshControl
            tintColor={refreshControlColor}
            refreshing={refreshing}
            progressViewOffset={100}
            onRefresh={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              setRefreshing(true);
              Promise.all([
                cardEventsQueryResult.refetch(),
                refetchCardBalance(queryClient),
              ]).finally(() => setRefreshing(false));
            }}
          />
        }
        ListHeaderComponent={
          <CardDetails
            card={card}
            introAnimationCompleted={introAnimationCompleted}
            animationProgress={animationProgress}
          />
        }
        ListHeaderComponentStyle={{ marginBottom: 32 }}
        itemLayoutAnimation={LIST_ITEM_LAYOUT_ANIMATION}
        // We use ListFooter for rendering bottom padding or ListEmptyComponent as a workaround,
        // because the list is never really empty, and because CellRendererComponent is not supported in Animated.FlatList
        ListFooterComponent={
          <Animated.View style={[opacityAnimatedStyle, { flex: 1 }]}>
            <ListFooter />
          </Animated.View>
        }
        ListFooterComponentStyle={{ flex: 1 }}
        keyExtractor={(item) => {
          return item.rowType === "list-title"
            ? "title"
            : item.rowType === "section-header"
              ? item.date
              : CardEvent.id(item.event);
        }}
        renderItem={({ item }) => {
          return (
            <Animated.View style={opacityAnimatedStyle}>
              <Animated.View entering={TRANSACTION_ITEM_ENTERING}>
                <ListItemView item={item} />
              </Animated.View>
            </Animated.View>
          );
        }}
        ItemSeparatorComponent={ListItemSpacer}
        onEndReached={() => cardEventsQueryResult.fetchNextPage()}
      />
    </>
  );
}

function IntroductionFooter({
  card,
  animationProgress,
}: {
  card: Card;
  animationProgress: SharedValue<number>;
}) {
  const insets = useSafeAreaInsets();
  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(animationProgress.value, [0, 1, 2], [1, 0, 0]),
    };
  });
  const queryClient = useQueryClient();

  return (
    <Animated.View
      style={[
        animatedStyle,
        {
          position: "absolute",
          bottom: 0,
          width: "100%",
          paddingHorizontal: 20,
          paddingBottom: insets.bottom,
          zIndex: 1,
          gap: 4,
        },
      ]}
    >
      <AnimatedTouchableScale
        onPress={() => {
          startCardAnimation(animationProgress);
        }}
        style={{
          alignSelf: "center",
          justifyContent: "center",
          paddingHorizontal: 18,
          height: 54,
        }}
      >
        <Text variant="semibold" size={16}>
          Skip
        </Text>
      </AnimatedTouchableScale>
      <Button
        icon={<AppleWalletIcon />}
        variant={"primary"}
        onPress={async () => {
          hapticOpenModalSheet();
          const response = await addCardToAppleWallet(queryClient, {
            cardholderName: "",
            primaryAccountSuffix: card.details.last4,
          });

          if (response.status === "success") {
            hapticSuccess();
            startCardAnimation(animationProgress);
          }
        }}
      >
        Add to Apple Wallet
      </Button>
    </Animated.View>
  );
}

export function ListItemView({ item }: { item: ListItem }) {
  if (item.rowType === "list-title") {
    return (
      <Row justify={"space-between"}>
        <Text variant="semibold" size={18}>
          {item.title}
        </Text>
        {item.right}
      </Row>
    );
  } else if (item.rowType === "section-header") {
    return <SectionHeader key={item.date} date={item.date} />;
  } else if (item.rowType === "event") {
    return (
      <AnimatedTouchableScale
        key={CardEvent.id(item.event)}
        pressedScale={0.99}
        onPress={() => {
          hapticOpenBottomTray();
          showCardEventModal(item.event);
        }}
      >
        <EventRow event={item.event} />
      </AnimatedTouchableScale>
    );
  } else {
    item satisfies never;
    return null;
  }
}

function ListFooter() {
  const cardEventsQueryResult = useCardEvents();

  const isEmptyTransactionList =
    cardEventsQueryResult.status === "success" &&
    (cardEventsQueryResult.data.pages[0]?.events?.length ?? 0) === 0;

  return cardEventsQueryResult.status === "pending" ? (
    <CardEventsSkeleton numberOfRows={4} />
  ) : cardEventsQueryResult.isFetchingNextPage ? (
    <CardEventsSkeleton numberOfRows={3} />
  ) : isEmptyTransactionList ? (
    <View
      style={{
        justifyContent: "center",
        alignItems: "center",
        paddingVertical: 20,
        marginTop: 60,
      }}
    >
      <ActivitiesStackGraphic />
      <Text variant="semibold" size={14} colorToken={"textTertiary"}>
        No transactions yet
      </Text>
    </View>
  ) : (
    <View style={{ height: SCROLL_VIEW_PADDING_BOTTOM }} />
  );
}

function CardDetails({
  card,
  introAnimationCompleted,
  animationProgress,
}: {
  card: Card;
  introAnimationCompleted: boolean;
  animationProgress: SharedValue<number>;
}) {
  const actionsAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(animationProgress.value, [0, 1, 2], [0, 0, 1]),
      transform: [
        {
          translateY: interpolate(
            animationProgress.value,
            [0, 1, 2],
            [16, 16, 0]
          ),
        },
        {
          scale: interpolate(
            animationProgress.value,
            [0, 1, 2],
            [0.99, 0.99, 1]
          ),
        },
      ],
    };
  });

  const cardAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          translateY: interpolate(
            animationProgress.value,
            [0, 1, 2] satisfies CardAnimationPoints,
            [-50, 0, 0]
          ),
        },
      ],
    };
  });

  return (
    <>
      <View gap={12}>
        <View gap={24}>
          <Animated.View style={[actionsAnimatedStyle, { gap: 16 }]}>
            <CardBalanceView />

            <Row gap={6}>
              <CardDepositButton card={card} />
              <CardWithdrawButton card={card} />
              {/*<CardSLChangeButton />*/}
            </Row>
          </Animated.View>

          <Animated.View style={[cardAnimatedStyle]}>
            {card.status === "frozen" ? (
              <CardFrozenView />
            ) : card.pendingAction ? (
              <CardLockedView type={card.pendingAction.details.type} />
            ) : (
              <CardImage
                url={card.cardImageUrl}
                showText={introAnimationCompleted}
              />
            )}

            {introAnimationCompleted ? null : (
              <CardIntroTextView animationProgress={animationProgress} />
            )}
          </Animated.View>
        </View>

        {card.pendingAction && (
          <PendingAction
            timeLockSeconds={card.timeLockSeconds}
            pendingAction={card.pendingAction}
          />
        )}

        <LowSpendingLimitWarning onchainSpendingLimits={card.spendingLimits} />
      </View>

      <CancelCardActivityModal />
      <CardActivityInfoModal />
    </>
  );
}

function CardIntroTextView({
  animationProgress,
}: {
  animationProgress: SharedValue<number>;
}) {
  const introTextAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(
        animationProgress.value,
        [0, 0.8, 2] satisfies CardAnimationPoints,
        [1, 0, 0]
      ),
      transform: [
        {
          translateY: interpolate(
            animationProgress.value,
            [0, 0.8, 2] satisfies CardAnimationPoints,
            [0, -20, -20]
          ),
        },
        {
          scale: interpolate(
            animationProgress.value,
            [0, 0.8, 2] satisfies CardAnimationPoints,
            [1, 0.9, 0.9]
          ),
        },
      ],
    };
  });

  return (
    <Animated.View
      style={[
        introTextAnimatedStyle,
        { position: "absolute", bottom: -84, width: "100%" },
      ]}
    >
      <OnboardingText
        title={"Your card is ready"}
        subtitle={"You can now add it to your Apple Wallet."}
      />
    </Animated.View>
  );
}

export function ListItemSpacer({ leadingItem }: { leadingItem: ListItem }) {
  const space = leadingItem.rowType === "list-title" ? 14 : 24;

  return <View style={{ height: space, width: "100%" }} />;
}

function CardBalanceView() {
  return (
    <FuseErrorBoundary
      FallbackComponent={() => (
        <View>
          <BalanceLabel />
          <BalanceFallback size={"large"} />
        </View>
      )}
    >
      <FuseSuspense
        fallback={
          <View>
            <BalanceLabel />
            <BalanceSkeleton size={"large"} />
          </View>
        }
      >
        <BalanceViewInner />
      </FuseSuspense>
    </FuseErrorBoundary>
  );
}

function BalanceLabel() {
  return (
    <Text variant="medium" colorToken="textSecondary" size={15}>
      Balance
    </Text>
  );
}

function BalanceViewInner() {
  const { balances } = useSuspenseCardBalance();

  const balance = balances[mints.usdc];
  const fiatAmount = balance?.total ?? 0;
  const reservedAmount = balance?.reserved ?? 0;

  const modalRef =
    useRef<BottomModalImperativeMethods<CardBalanceModalParams>>(null);

  return (
    <View>
      <CardBalanceModal modalRef={modalRef} />
      <AnimatedTouchableScale
        style={{
          alignSelf: "flex-start",
          //until hitSlop is fixed
          padding: 10,
          margin: -10,
        }}
        pressedScale={1}
        onPress={() => {
          if (reservedAmount > 0) {
            modalRef.current?.present({ reservedAmount });
          }
        }}
      >
        <Row gap={6}>
          <BalanceLabel />
          {reservedAmount > 0 && (
            <Icon
              name={"info.circle"}
              colorToken={"textSecondary"}
              size={9}
              weight={"semibold"}
            />
          )}
        </Row>
      </AnimatedTouchableScale>
      <BalanceView size={"large"} amount={fiatAmount} />
    </View>
  );
}

function CardDepositButton({ card }: { card: Card }) {
  const router = useRouter();
  const { toast } = useToast();
  const { wallet } = useActiveWallet();

  return (
    <ActionButton
      hitSlop={4}
      iconName="plus.circle.fill"
      iconWeight="semibold"
      onPress={async () => {
        if (await isWalletOffline(toast, wallet)) {
          return;
        }

        if (card.status === "frozen") {
          showCardFrozenModal();
          return;
        }

        hapticOpenModalSheet();
        router.push(`/unlocked/bridge/card/deposit/enter-amount`);
      }}
    >
      Top up
    </ActionButton>
  );
}

function CardWithdrawButton({ card }: { card: Card }) {
  const router = useRouter();
  const { toast } = useToast();
  const { wallet } = useActiveWallet();

  return (
    <ActionButton
      hitSlop={4}
      iconName="arrow.up.circle.fill"
      iconWeight="semibold"
      onPress={async () => {
        if (await isWalletOffline(toast, wallet)) {
          return;
        }

        const timeLock = Card.pendingActivityTimeLock(card);

        if (timeLock) {
          showCardPendingActivityModal({ isTimeLockReady: timeLock.isReady });
          return;
        }

        hapticOpenModalSheet();
        router.push(`/unlocked/bridge/card/withdraw/enter-amount`);
      }}
    >
      Withdraw
    </ActionButton>
  );
}

function CardFrozenView() {
  return (
    <View>
      <VisaCard showVisaLogo={false} showFuseLogo={false} showDigits={false} />
      <View
        style={{
          position: "absolute",
          top: "50%",
          width: "100%",
          flexDirection: "row",
        }}
      >
        <Flex style={{ height: 1, backgroundColor: "#2F2F2F" }} />
        <View style={{ bottom: 16 }}>
          <IconWrapper size={32} background={"#2F2F2F"}>
            <IconWrapper size={30} backgroundColorToken="blue">
              <Icon
                name={"snowflake"}
                color={"#fff"}
                size={14}
                weight={"bold"}
              />
            </IconWrapper>
          </IconWrapper>
        </View>
        <Flex style={{ height: 1, backgroundColor: "#2F2F2F" }} />
      </View>

      <View
        style={{
          position: "absolute",
          bottom: 0,
          padding: 16,
          width: "100%",
        }}
      >
        <Row justify={"space-between"}>
          <View gap={4}>
            <Text
              variant={"semibold"}
              colorToken={"textButtonPrimary"}
              size={14}
            >
              Your card is frozen
            </Text>
            <Text
              variant={"medium"}
              colorToken={"textButtonPrimary"}
              style={{ opacity: 0.4 }}
              size={14}
            >
              Unfreeze to continue spending
            </Text>
          </View>
          <Button
            size={"small"}
            variant="primary"
            style={{
              view: { backgroundColor: "#363636" },
            }}
            onPress={() => {
              hapticOpenBottomTray();
              showCardUnfreezeModal();
            }}
          >
            Unfreeze
          </Button>
        </Row>
      </View>
    </View>
  );
}

function CardLockedView({ type }: { type: "cardSLChange" | "cardWithdraw" }) {
  return (
    <View>
      <VisaCard showVisaLogo={false} showFuseLogo={false} showDigits={false} />
      <View
        style={{
          position: "absolute",
          top: "50%",
          width: "100%",
          flexDirection: "row",
        }}
      >
        <Flex style={{ height: 1, backgroundColor: "#2F2F2F" }} />
        <View style={{ bottom: 16 }}>
          <IconWrapper size={32} background={"#2F2F2F"}>
            <IconWrapper size={30} background={"#2F2F2F"}>
              <Icon name={"clock"} color={"#fff"} size={14} weight={"bold"} />
            </IconWrapper>
          </IconWrapper>
        </View>
        <Flex style={{ height: 1, backgroundColor: "#2F2F2F" }} />
      </View>

      <View
        style={{
          position: "absolute",
          bottom: 0,
          padding: 16,
          width: "100%",
        }}
      >
        <Row justify={"center"}>
          <Text
            variant={"medium"}
            colorToken={"textButtonPrimary"}
            style={{ opacity: 0.4 }}
            size={14}
          >
            Card is inactive until{" "}
            {type === "cardSLChange" ? "daily limit change" : "withdrawal"} is
            complete
          </Text>
        </Row>
      </View>
    </View>
  );
}

const PendingActionExitAnimation = ZoomAndFadeOut.duration(
  DURATION_FAST
).withEndValues({
  opacity: 0,
  scale: 0.7,
});

function PendingAction({
  timeLockSeconds,
  pendingAction,
}: {
  timeLockSeconds: number;
  pendingAction: CardPendingAction;
}) {
  return (
    <Animated.View exiting={PendingActionExitAnimation} style={{ gap: 12 }}>
      {CardPendingAction.isSLChange(pendingAction) && (
        <PendingSLChange
          key={pendingAction.activityId}
          timeLockSeconds={timeLockSeconds}
          pendingAction={pendingAction}
        />
      )}
      {CardPendingAction.isWithdrawal(pendingAction) && (
        <PendingWithdraw
          key={pendingAction.activityId}
          timeLockSeconds={timeLockSeconds}
          pendingAction={pendingAction}
        />
      )}
    </Animated.View>
  );
}

export function EventRow({ event }: { event: CardEvent }) {
  switch (event.type) {
    case "transaction":
      return <TransactionItem key={event.transaction.id} event={event} />;
    case "authorization":
      return <AuthorizationItem key={event.authorization.id} event={event} />;
    case "onchainActivity":
      return (
        <OnchainActivityItem key={event.activity.activityId} event={event} />
      );
    case "decline":
      return <DeclineItem key={event.decline.id} event={event} />;
    default:
      event satisfies never;
      return null;
  }
}

function SectionHeader({ date }: { date: string }) {
  return (
    <Text
      variant="medium"
      colorToken="textTertiary"
      style={{ fontSize: 14, marginTop: 6 }}
    >
      {date}
    </Text>
  );
}

export function CardEventsSkeleton({
  numberOfRows = 4,
}: {
  numberOfRows?: number;
}) {
  const backgroundColor = useColor("background");

  return (
    <Animated.View
      exiting={FadeOut.duration(DURATION_MEDIUM).reduceMotion(
        ReduceMotion.Never
      )}
      style={{ paddingTop: 24, gap: 24 }}
    >
      {Array.from({ length: numberOfRows }).map((_, i) => (
        <CardEventSkeleton key={i} />
      ))}
      <LinearGradient
        style={StyleSheet.absoluteFill}
        start={[0, 0]}
        end={[0, 1]}
        colors={[
          hexToRgba(backgroundColor, 0),
          hexToRgba(backgroundColor, 0.5),
        ]}
      />
    </Animated.View>
  );
}

const skeletonShiningGradientColorStops = {
  0: "rgba(255,255,255,0)",
  0.5: "rgba(255,255,255,0.3)",
  1: "rgba(255,255,255,0)",
};

function CardEventSkeleton() {
  return (
    <MaskedView
      style={{ height: 42 }}
      maskElement={
        <EventItem
          icon={
            <View
              style={{
                width: 42,
                height: 42,
                borderRadius: 999,
                backgroundColor: "black",
              }}
            />
          }
          leftTop={<TextSkeleton width={52} />}
          leftBottom={<TextSkeleton width={100} />}
          rightTop={<TextSkeleton width={32} />}
          rightBottom={<TextSkeleton width={23} />}
        />
      }
    >
      <View
        background="backgroundSecondary"
        style={[StyleSheet.absoluteFill]}
      />
      <ShiningGradientFill
        colorStops={skeletonShiningGradientColorStops}
        animationDuration={1000}
      />
    </MaskedView>
  );
}

function TextSkeleton({ width }: { width: number }) {
  return (
    <View
      style={{
        width,
        height: 11,
        borderRadius: 999,
        backgroundColor: "black",
      }}
    />
  );
}

function TransactionItem({
  event,
}: {
  event: CardEvent & { type: "transaction" };
}) {
  const transaction = event.transaction;
  const isRefund = transaction.type === "refund";

  return (
    <EventItem
      icon={
        <TransactionIcon
          type={isRefund ? "refund" : "processed"}
          size={42}
          category={transaction.category}
        />
      }
      leftTop={<Title>{CardEvent.description(event)}</Title>}
      leftBottom={
        <Subtitle>
          {isRefund
            ? "Refunded"
            : CardTransactionCategory.toString(transaction.category)}
        </Subtitle>
      }
      rightTop={
        <Amount colorToken={isRefund ? "green" : "text"}>
          {formatUsdValue(Math.abs(transaction.amount))}
        </Amount>
      }
      rightBottom={
        <Subtitle>
          {DateTime.fromSeconds(CardEvent.timestamp(event)).toLocaleString(
            DateTime.TIME_SIMPLE
          )}
        </Subtitle>
      }
    />
  );
}

function AuthorizationItem({
  event,
}: {
  event: CardEvent & { type: "authorization" };
}) {
  const authorization = event.authorization;

  return (
    <EventItem
      icon={
        <TransactionIcon
          type={"pending"}
          size={42}
          category={authorization.category}
        />
      }
      leftTop={<Title>{CardEvent.description(event)}</Title>}
      leftBottom={<Subtitle>Pending</Subtitle>}
      rightTop={
        <Amount>{formatUsdValue(Math.abs(authorization.amount))}</Amount>
      }
      rightBottom={
        <Subtitle>
          {DateTime.fromSeconds(CardEvent.timestamp(event)).toLocaleString(
            DateTime.TIME_SIMPLE
          )}
        </Subtitle>
      }
    />
  );
}

function DeclineItem({ event }: { event: CardEvent & { type: "decline" } }) {
  const decline = event.decline;

  return (
    <EventItem
      icon={
        <TransactionIcon
          type={"decline"}
          size={42}
          category={decline.category}
        />
      }
      leftTop={<Title>{CardEvent.description(event)}</Title>}
      leftBottom={<Subtitle>Declined</Subtitle>}
      rightTop={
        <Amount colorToken={"textSecondary"}>
          {formatUsdValue(Math.abs(decline.amount))}
        </Amount>
      }
      rightBottom={
        <Subtitle>
          {DateTime.fromSeconds(CardEvent.timestamp(event)).toLocaleString(
            DateTime.TIME_SIMPLE
          )}
        </Subtitle>
      }
    />
  );
}

function OnchainActivityItem({
  event,
}: {
  event: CardEvent & { type: "onchainActivity" };
}) {
  const activity = event.activity;
  const { wallet } = useActiveWallet();
  const name = wallet.vaults[0].name ?? "Main Account";

  if (activity.details.type === "cardReferralReward") {
    const amountUsd = formatSPLAmount({
      amount: activity.details.amount,
      decimals: activity.details.decimals,
    });

    return (
      <EventItem
        icon={
          <OnchainActivityIcon
            mint={mints.usdc}
            size={42}
            type="cardReferralReward"
          />
        }
        leftTop={<Title>Referral Bonus</Title>}
        leftBottom={<Subtitle>From: Fuse</Subtitle>}
        rightTop={<Amount colorToken={"green"}>+${amountUsd}</Amount>}
        rightBottom={
          <Subtitle>
            {DateTime.fromSeconds(CardEvent.timestamp(event)).toLocaleString(
              DateTime.TIME_SIMPLE
            )}
          </Subtitle>
        }
      />
    );
  }

  let title;
  let subtitle;
  let amount;
  let decimals;
  let sign;
  let type;
  switch (activity.details.type) {
    case "cardWithdraw":
      title = "Withdrawal";
      subtitle = `To: ${name}`;
      amount = activity.details.amount;
      decimals = activity.details.decimals;
      sign = "-";
      type = "cardWithdraw" as const;
      break;
    case "cardTopUp":
      title = "Deposit";
      subtitle = `From: ${name}`;
      amount = activity.details.amount;
      decimals = activity.details.decimals;
      sign = "+";
      type = "cardTopUp" as const;
      break;
    default:
      return null;
  }

  return (
    <EventItem
      icon={<OnchainActivityIcon mint={mints.usdc} size={42} type={type} />}
      leftTop={<Title>{title}</Title>}
      leftBottom={<Subtitle>{subtitle}</Subtitle>}
      rightTop={
        <Amount colorToken={sign === "+" ? "green" : "red"}>
          {formatSPLAmount({ amount, decimals, symbol: "USDC" })}
        </Amount>
      }
      rightBottom={
        <Subtitle>
          {DateTime.fromSeconds(CardEvent.timestamp(event)).toLocaleString(
            DateTime.TIME_SIMPLE
          )}
        </Subtitle>
      }
    />
  );
}

function EventItem({
  icon,
  leftTop,
  leftBottom,
  rightTop,
  rightBottom,
}: {
  icon: ReactNode;
  leftTop: ReactNode;
  leftBottom: ReactNode;
  rightTop: ReactNode;
  rightBottom: ReactNode;
}) {
  return (
    <Row gap={20}>
      <Row flex gap={16}>
        {icon}
        <View style={{ flexShrink: 1 }} gap={4}>
          {leftTop}
          {leftBottom}
        </View>
      </Row>
      <View gap={4} style={{ alignItems: "flex-end" }}>
        {rightTop}
        {rightBottom}
      </View>
    </Row>
  );
}

function Title(props: ComponentProps<typeof Text>) {
  return <Text variant={"semibold"} size={16} numberOfLines={1} {...props} />;
}

function Subtitle(props: ComponentProps<typeof Text>) {
  return (
    <Text
      variant={"medium"}
      size={13}
      colorToken={"textTertiary"}
      numberOfLines={1}
      {...props}
    />
  );
}

function Amount(props: ComponentProps<typeof Text>) {
  return <Text variant={"semibold"} size={14} numberOfLines={1} {...props} />;
}
