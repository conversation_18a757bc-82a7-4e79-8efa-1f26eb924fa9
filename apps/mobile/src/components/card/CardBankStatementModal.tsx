import { create } from "zustand";
import { useEffect, useId, useRef } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import { Flex, Row } from "~/components/Grid";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "~/components/Toaster";
import { TransparentGradient } from "~/components/TransparentGradient";
import { FuseTransparentIcon } from "~/components/icons/card/FuseTransparentIcon";
import { getBankStatement } from "~/services/bridge";
import * as FileSystem from "expo-file-system";
import * as Sharing from "expo-sharing";
import { DateTime } from "luxon";
import { AnimatedButtonGroup } from "~/components/AnimatedButtonGroup";

const useModalState = create<{
  shown: boolean;
  hide: () => void;
}>((set) => ({ shown: false, hide: () => set({ shown: false }) }));

export function showCardBankStatementModal() {
  useModalState.setState({ shown: true });
}

export function CardBankStatementModal() {
  const { toast } = useToast();

  const id = useId();
  const modalRef = useRef<BottomModalImperativeMethods>(null);
  const { shown, hide } = useModalState();

  useEffect(() => {
    if (shown) {
      modalRef.current?.present();
    } else {
      modalRef.current?.close();
    }
  }, [shown]);

  const exportMutation = useMutation({
    mutationFn: async (date: string) => {
      const data = await getBankStatement({ date });

      const uint8Array = new Uint8Array(data);
      const buff = Buffer.from(uint8Array);
      const base64 = buff.toString("base64");

      const fileUri =
        FileSystem.documentDirectory +
        `${encodeURI(`fuse-bank-statement-${date}`)}.pdf`;

      await FileSystem.writeAsStringAsync(fileUri, base64, {
        encoding: FileSystem.EncodingType.Base64,
      });

      void Sharing.shareAsync(fileUri);
    },
    onError: () => {
      toast.error("Bank statement is not available. Please try again later.", {
        opaque: true,
      });
    },
  });

  return (
    <BottomModal
      modalId={id}
      modalRef={modalRef}
      title={" "}
      onClose={hide}
      onDismiss={hide}
      body={
        <View gap={16}>
          <FreezeCardImage />
          <View gap={8} style={{ alignItems: "center" }}>
            <Text variant="semibold" size={20} align={"center"}>
              Bank statement
            </Text>
            <Text
              variant="medium"
              colorToken="textSecondary"
              size={15}
              align={"center"}
            >
              You can open or export your bank transaction summary statement.
            </Text>
          </View>
        </View>
      }
      footer={
        <AnimatedButtonGroup
          left={
            exportMutation.isPending ? null : (
              <Button
                size={"medium-new"}
                variant="secondary"
                onPress={() => {
                  hide();
                }}
              >
                Cancel
              </Button>
            )
          }
          right={
            <Button
              size={"medium-new"}
              variant="primary"
              loading={exportMutation.isPending}
              loadingText={"Exporting"}
              onPress={async () => {
                const date = DateTime.now()
                  .minus({ month: 1 })
                  .toFormat("yyyyMM");

                await exportMutation.mutateAsync(date);
                hide();
              }}
            >
              Export
            </Button>
          }
        />
      }
    />
  );
}

export function FreezeCardImage() {
  const height = 200;

  const color = "rgba(0,0,0,0.1)";
  const placeholder = (
    <View gap={4}>
      <View style={{ height: 6, width: 42, backgroundColor: color }} />
      <View style={{ height: 6, width: 60, backgroundColor: color }} />
    </View>
  );

  return (
    <View
      style={{
        alignItems: "center",
        justifyContent: "center",
        marginBottom: -64,
        height: height,
      }}
    >
      <View style={{ width: "90%" }}>
        <View
          style={{
            backgroundColor: "#fff",
            height,
            borderRadius: 16,
            borderCurve: "continuous",
            paddingVertical: 20,
            paddingHorizontal: 16,
          }}
          gap={24}
        >
          <Row gap={12}>
            <FuseTransparentIcon fillColor={color} style={{ width: 18 }} />
            <Text variant={"semibold"} style={{ color }} size={15}>
              Bank statement
            </Text>
          </Row>
          {placeholder}
          <Row gap={16}>
            {placeholder}
            {placeholder}
            {placeholder}
          </Row>
        </View>
        <TransparentGradient position={"bottom"} size={height + 42} />
      </View>
    </View>
  );
}
