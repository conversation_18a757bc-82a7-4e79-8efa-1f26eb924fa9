import { RefObject, useId } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import { formatUsdValue } from "@squads/utils/numberFormats";
import { BalanceLabelImage } from "~/components/icons/card/BalanceLabelImage";
import { DashedBorder } from "~/components/DashedBorder";
import { Row } from "~/components/Grid";
import { Icon } from "~/components/Icon";

export type CardBalanceModalParams = { reservedAmount: number };

export function CardBalanceModal({
  modalRef,
}: {
  modalRef: RefObject<BottomModalImperativeMethods<CardBalanceModalParams> | null>;
}) {
  const id = useId();

  return (
    <BottomModal
      modalId={id}
      title={" "}
      modalRef={modalRef}
      body={({ reservedAmount }: CardBalanceModalParams) => (
        <View gap={24}>
          <View gap={14}>
            <View style={{ alignItems: "center", paddingVertical: 14 }}>
              <BalanceLabelImage />
            </View>
            <View gap={10} style={{ alignItems: "center" }}>
              <Text variant="semibold" size={20} align={"center"}>
                Reserved amount
              </Text>
              <Text
                variant="medium"
                colorToken="textSecondary"
                size={14}
                align={"center"}
              >
                Your balance has a reserved{"\n"}
                amount of{" "}
                <Text variant="medium" colorToken="text" size={14}>
                  {formatUsdValue(reservedAmount)}
                </Text>{" "}
                for pending transactions.
                {"\n"}
                This amount will become available in 24 hours{"\n"}
                once transactions are cleared.
              </Text>
            </View>
          </View>
          <ReservedAmountWarning />
        </View>
      )}
      footer={
        <Button
          variant="secondary"
          onPress={() => {
            modalRef.current?.close();
          }}
        >
          Got it
        </Button>
      }
    />
  );
}

function ReservedAmountWarning() {
  return (
    <View>
      <DashedBorder
        borderWidth={1.5}
        borderColorToken={"dashedListSeparator"}
      />
      <Row flex gap={16} style={{ gap: 18, padding: 20 }}>
        <Icon
          name={"info.square.fill"}
          size={12}
          colorToken={"textSecondary"}
        />
        <Text
          variant="medium"
          colorToken="textSecondary"
          size={13}
          style={{ flexShrink: 1 }}
        >
          Reserved amount is necessary to account for potential tips and
          upcharges.
        </Text>
      </Row>
    </View>
  );
}
