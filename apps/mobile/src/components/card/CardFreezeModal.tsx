import { create } from "zustand";
import { useEffect, useId, useRef } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import { freezeCard } from "~/state/bridge";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "~/components/Toaster";
import { VisaCard } from "~/components/card/VisaCard";
import { BlurView } from "expo-blur";
import { IconWrapper } from "~/components/IconWrapper";
import { Icon } from "~/components/Icon";
import { router } from "expo-router";
import { AnimatedButtonGroup } from "~/components/AnimatedButtonGroup";
import { hapticDismissBottomTray, hapticSelect } from "~/utils/haptics";

const useModalState = create<{
  shown: boolean;
  hide: () => void;
}>((set) => ({ shown: false, hide: () => set({ shown: false }) }));

export function showCardFreezeModal() {
  useModalState.setState({ shown: true });
}

export function CardFreezeModal() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const id = useId();
  const modalRef = useRef<BottomModalImperativeMethods>(null);
  const { shown, hide } = useModalState();

  useEffect(() => {
    if (shown) {
      modalRef.current?.present();
    } else {
      modalRef.current?.close();
    }
  }, [shown]);

  const freezeCardMutation = useMutation({
    mutationFn: async () => {
      await freezeCard({ queryClient });
    },
    onSuccess: () => {
      toast.info("Card has been frozen");
    },
    onError: () => {
      toast.error("Failed to freeze card. Please try again");
    },
  });

  return (
    <BottomModal
      modalId={id}
      modalRef={modalRef}
      title={" "}
      onClose={hide}
      onDismiss={hide}
      body={
        <View gap={16}>
          <FreezeCardImage />
          <View gap={8} style={{ alignItems: "center" }}>
            <Text variant="semibold" size={20} align={"center"}>
              Freeze your card?
            </Text>
            <Text
              variant="medium"
              colorToken="textSecondary"
              size={15}
              align={"center"}
            >
              Your card will be temporarily disabled. You can unfreeze it
              anytime to continue using it.
            </Text>
          </View>
        </View>
      }
      footer={
        <AnimatedButtonGroup
          left={
            freezeCardMutation.isPending ? null : (
              <Button
                size={"medium-new"}
                variant="secondary"
                onPress={() => {
                  hapticDismissBottomTray();
                  hide();
                }}
              >
                Cancel
              </Button>
            )
          }
          right={
            <Button
              size={"medium-new"}
              loading={freezeCardMutation.isPending}
              loadingText={"Freezing"}
              variant="primary"
              onPress={async () => {
                hapticSelect();
                await freezeCardMutation.mutateAsync();
                hide();
                router.dismissTo("/unlocked/bridge/card/main");
              }}
            >
              Freeze
            </Button>
          }
        />
      }
    />
  );
}

export function FreezeCardImage() {
  const height = 164 + 80;

  return (
    <View
      style={{
        alignItems: "center",
        justifyContent: "center",
        marginHorizontal: -24,
        marginBottom: -24,
        height: height,
      }}
    >
      <View style={{ width: "80%" }}>
        <VisaCard />
      </View>
      <View style={[{ position: "absolute", left: 0, right: 0, bottom: 0 }]}>
        <View
          style={{
            alignItems: "center",
            position: "relative",
            top: 16,
            zIndex: 1,
          }}
        >
          <IconWrapper size={32} background={"#fff"}>
            <IconWrapper size={30} backgroundColorToken="blue">
              <Icon name={"snowflake"} color={"#fff"} size={14} />
            </IconWrapper>
          </IconWrapper>
        </View>
        <BlurView
          tint={"light"}
          intensity={50}
          style={{
            height: height / 2,
            borderTopColor: "#fff",
            borderTopWidth: 1,
          }}
        />
      </View>
    </View>
  );
}
