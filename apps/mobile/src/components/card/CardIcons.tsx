import { useColor, View } from "~/components/Themed";
import { Address } from "@squads/models/solana";
import { CardTransactionCategory } from "~/services/bridge";
import { SFSymbol } from "expo-symbols";
import { IconWrapper } from "~/components/IconWrapper";
import { CoinLogo } from "~/components/CoinLogo";
import { Icon } from "~/components/Icon";
import { hexToRgba } from "~/constants/Colors";

export function TransactionIcon({
  type = "processed",
  category,
  size,
}: {
  type?: "processed" | "pending" | "refund" | "decline";
  category: CardTransactionCategory;
  size: number;
}) {
  const { name, color, background } = getCategoryIconDetails({ category });

  const borderColor = useColor("background");
  const borderWidth = size * 0.09;

  const iconSymbolSize = size * 0.28;
  const iconBadgeSymbolSize = size * 0.27;

  const subIconMapping: Record<"pending" | "refund" | "decline", SFSymbol> = {
    pending: "clock.fill",
    refund: "arrow.counterclockwise.circle.fill",
    decline: "xmark.circle.fill",
  };

  return (
    <View>
      <IconWrapper size={size} background={background}>
        <Icon name={name} color={color} size={iconSymbolSize} />
      </IconWrapper>
      {type === "pending" || type === "refund" || type === "decline" ? (
        <View
          style={{
            position: "absolute",
            top: -borderWidth,
            right: -borderWidth,
            borderRadius: 999,
            borderWidth: borderWidth,
            borderColor,
          }}
        >
          <Icon
            name={subIconMapping[type]}
            size={iconBadgeSymbolSize}
            color={color}
          />
        </View>
      ) : null}
    </View>
  );
}

export function OnchainActivityIcon({
  mint,
  size,
  type,
}: {
  mint: Address;
  size: number;
  type: "cardWithdraw" | "cardTopUp" | "cardReferralReward";
}) {
  const iconSymbolSize = size * 0.28;

  const borderColor = useColor("background");
  const borderWidth = size * 0.05;

  const iconBadgeSymbolSize = size * 0.38;

  const green = useColor("green");
  const red = useColor("red");
  const color = {
    cardWithdraw: red,
    cardTopUp: green,
    cardReferralReward: green,
  }[type];

  const iconName = {
    cardWithdraw: "arrow.up" as const,
    cardTopUp: "arrow.down" as const,
    cardReferralReward: "person.2.fill" as const,
  }[type];

  const iconBadgeSymbol = {
    cardWithdraw: true,
    cardTopUp: true,
    cardReferralReward: false,
  }[type];

  return (
    <View>
      <IconWrapper size={size} background={hexToRgba(color, 0.08)}>
        <Icon
          name={iconName}
          color={color}
          size={iconSymbolSize}
          weight={"bold"}
        />
      </IconWrapper>
      {iconBadgeSymbol && (
        <View
          style={{
            position: "absolute",
            top: -borderWidth,
            right: -borderWidth,
            borderRadius: 999,
            borderWidth: borderWidth,
            borderColor,
          }}
        >
          <CoinLogo mint={mint} size={iconBadgeSymbolSize} />
        </View>
      )}
    </View>
  );
}

export function getCategoryIconDetails({
  category,
}: {
  category: CardTransactionCategory;
}): {
  name: SFSymbol;
  color: string;
  background: string;
} {
  switch (category) {
    case "other":
      return {
        name: "circle.hexagonpath.fill",
        color: "#0082FF",
        background: "#0082FF12",
      };
    case "shopping":
      return {
        name: "bag.fill",
        color: "#FF812E",
        background: "#FF812E12",
      };
    case "bills":
      return {
        name: "creditcard.fill",
        color: "#6740FF",
        background: "#6740FF12",
      };
    case "healthcare":
      return {
        name: "heart.fill",
        color: "#FF4A4A",
        background: "#FF4A4A12",
      };
    case "groceries":
      return {
        name: "leaf.fill",
        color: "#6740FF",
        background: "#6740FF12",
      };
    case "eatingOut":
      return {
        name: "fork.knife",
        color: "#FF4397",
        background: "#FF439712",
      };
    case "entertainment":
      return {
        name: "ticket.fill",
        color: "#29B8FF",
        background: "#29B8FF12",
      };
    case "personalCare":
      return {
        name: "scissors",
        color: "#4A4AFF",
        background: "#4A4AFF12",
      };
    case "fuel":
      return {
        name: "fuelpump.fill",
        color: "#FF6937",
        background: "#FF693712",
      };
    case "education":
      return {
        name: "graduationcap.fill",
        color: "#3187FF",
        background: "#3187FF12",
      };
    case "transport":
      return {
        name: "tram.fill",
        color: "#FF4AD2",
        background: "#FF4AD212",
      };
    case "trips":
      return {
        name: "beach.umbrella.fill",
        color: "#CF4AFF",
        background: "#CF4AFF12",
      };
    default:
      category satisfies never;
      return {
        name: "circle.hexagonpath.fill",
        color: "#0082FF",
        background: "#0082FF12",
      };
  }
}
