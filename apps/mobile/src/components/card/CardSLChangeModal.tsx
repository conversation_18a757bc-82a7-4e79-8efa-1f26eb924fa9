import { create } from "zustand";
import { useEffect, useRef } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import {
  FontFamilyByVariant,
  Text,
  TextColor,
  View,
} from "~/components/Themed";
import { Button } from "~/components/Button";
import { Row } from "~/components/Grid";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { executeAction, prepareAction } from "~/services/wallets";
import { mints } from "~/constants/tokens";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { useActiveWallet } from "~/state/wallet";
import { refetchCard } from "~/state/bridge";
import invariant from "invariant";
import { useSuspenseToken } from "~/state/tokens";
import { Picker } from "@react-native-picker/picker";
import { formatUsdValue } from "@squads/utils/numberFormats";
import { DashedListSeparator } from "~/components/ListSeparator";
import { awaitActivityStatus, confirmActivity } from "~/services/activities";
import { Card, CardPendingAction } from "~/services/bridge";
import {
  hapticError,
  hapticOpenBottomTray,
  hapticSelect,
} from "~/utils/haptics";
import { DashedBorder } from "~/components/DashedBorder";
import { useCountdown } from "~/hooks/useCountdown";
import { useToast } from "~/components/Toaster";
import { AnimatedButtonGroup } from "~/components/AnimatedButtonGroup";

const SL_OPTIONS = Array.from(
  new Set([
    ...range(25, 50, 25),
    ...range(100, 500, 50),
    ...range(500, 2000, 100),
  ])
);

function range(start: number, end: number, step: number): number[] {
  const result = [];
  for (let i = start; i <= end; i += step) {
    result.push(i);
  }
  return result;
}

type State =
  | { type: "options"; amount: number; defaultAmount: number }
  | {
      type: "pending";
      timeLockSeconds: number;
      pendingAction: CardPendingAction & { details: { type: "cardSLChange" } };
    }
  | {
      type: "cancelling";
      timeLockSeconds: number;
      pendingAction: CardPendingAction & { details: { type: "cardSLChange" } };
    };

const useModalState = create<{
  shown: boolean;
  state: State | null;
  hide: () => void;
}>((set) => ({
  shown: false,
  state: null,
  hide: () => set({ shown: false }),
}));

export function showCardSLChangeModal({ card }: { card: Card }) {
  hapticOpenBottomTray();

  const pendingSLChange =
    card.pendingAction && CardPendingAction.isSLChange(card.pendingAction)
      ? card.pendingAction
      : null;

  const timeLockSeconds = card.timeLockSeconds;

  if (pendingSLChange) {
    useModalState.setState({
      state: {
        type: "pending",
        timeLockSeconds: timeLockSeconds,
        pendingAction: pendingSLChange,
      },
      shown: true,
    });
    return;
  }

  const usdcSpendingLimit = card.spendingLimits[mints.usdc];
  invariant(usdcSpendingLimit, "usdc spending limit not set");

  const usdcDecimals = 6;

  const defaultAmount = usdcSpendingLimit.amount / 10 ** usdcDecimals;
  return useModalState.setState({
    state: { type: "options", amount: defaultAmount, defaultAmount },
    shown: true,
  });
}

export function CardSLChangeModal() {
  const modalRef = useRef<BottomModalImperativeMethods>(null);
  const { shown, state, hide } = useModalState();

  useEffect(() => {
    if (shown) {
      modalRef.current?.present();
    } else {
      modalRef.current?.close(() => {
        useModalState.setState({ state: null });
      });
    }
  }, [shown]);

  return (
    <BottomModal
      modalId={`card-sl-change-${state?.type}`}
      modalRef={modalRef}
      onClose={hide}
      onDismiss={hide}
      title=" "
      body={
        state &&
        (state.type === "options" ? (
          <OptionsBody state={state} />
        ) : state.type === "pending" ? (
          <PendingBody state={state} />
        ) : (
          <CancellingBody state={state} />
        ))
      }
      footer={
        state &&
        (state.type === "options" ? (
          <OptionsFooter state={state} />
        ) : state.type === "pending" ? (
          <PendingFooter state={state} />
        ) : (
          <CancellingFooter state={state} />
        ))
      }
    />
  );
}

function OptionsBody({ state }: { state: State & { type: "options" } }) {
  const slOptions: { label: string; value: number }[] = SL_OPTIONS.map(
    (value) => ({
      label: formatUsdValue(value, { maximumFractionDigits: 0 }),
      value: value,
    })
  );

  return (
    <View gap={24}>
      <View gap={10}>
        <Text variant="semibold" size={20} align={"center"}>
          Daily Spending Limit
        </Text>
        <Text
          variant="medium"
          colorToken="textSecondary"
          size={14}
          align={"center"}
        >
          Set a maximum amount for daily card purchases. This protects your card
          balance from unauthorized spending. You can adjust your daily limit
          anytime.
        </Text>
      </View>

      <DashedListSeparator />

      <Picker
        itemStyle={{
          fontFamily: FontFamilyByVariant["semibold"],
        }}
        selectedValue={state.amount}
        onValueChange={(value) => {
          useModalState.setState(({ state }) => {
            if (state?.type !== "options") {
              return { state };
            }
            return { state: { ...state, amount: value } };
          });
        }}
      >
        {slOptions.map((option) => (
          <Picker.Item
            key={option.value}
            label={option.label}
            value={option.value}
          />
        ))}
      </Picker>
    </View>
  );
}

function PendingBody({ state }: { state: State & { type: "pending" } }) {
  const { isReady } = useCountdown({
    timestamp: state.pendingAction.approvedAt,
    duration: state.timeLockSeconds,
  });

  return (
    <View gap={24} style={{ alignItems: "center" }}>
      <View gap={10}>
        <Text variant="semibold" size={20} align={"center"}>
          Daily Spending Limit
        </Text>
        <Text
          variant="medium"
          colorToken="textSecondary"
          size={14}
          align={"center"}
        >
          {isReady
            ? "Press complete to finalize the daily\nlimit change"
            : `Wait for the countdown to confirm\nyour new daily limit.`}
        </Text>
      </View>

      <PendingSLChangeDetails pendingAction={state.pendingAction} />

      <Text size={14} variant="medium" colorToken="textSecondary">
        You can close this and return to it later.
      </Text>
    </View>
  );
}

function CancellingBody({ state }: { state: State & { type: "cancelling" } }) {
  return (
    <View gap={24} style={{ alignItems: "center" }}>
      <View gap={10}>
        <Text variant="semibold" size={20} align={"center"}>
          Cancel change?
        </Text>
        <Text
          variant="medium"
          colorToken="textSecondary"
          size={14}
          align={"center"}
        >
          Your daily limit change will be canceled.
        </Text>
      </View>

      <PendingSLChangeDetails
        pendingAction={state.pendingAction}
        colorToken="textTertiary"
      />
    </View>
  );
}

function OptionsFooter({ state }: { state: State & { type: "options" } }) {
  const { hide } = useModalState();

  const queryClient = useQueryClient();
  const { wallet } = useActiveWallet();

  const token = useSuspenseToken({ mint: mints.usdc });
  invariant(token, "usdc token not found");

  const changeSLMutation = useMutation({
    mutationFn: async () => {
      const action = await prepareAction({
        type: "cardSLChangeInit",
        vaultIndex: 0,
        amount: state.amount * 10 ** token.decimals, // 10 usd
        mint: token.mint,
        period: "daily",
      });

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );

      const { activityId } = await executeAction({
        signedTransactions,
      });

      await awaitActivityStatus({
        activityId,
        status: { type: "pending", state: "awaitsExecution" },
      });

      const card = await refetchCard({ queryClient });
      invariant(card, "card is not set");

      const pendingSLChange =
        card.pendingAction && CardPendingAction.isSLChange(card.pendingAction)
          ? card.pendingAction
          : null;
      invariant(pendingSLChange, "pendingSLChange is not set");

      const timeLockSeconds = card.timeLockSeconds;

      useModalState.setState({
        state: {
          type: "pending",
          timeLockSeconds: timeLockSeconds,
          pendingAction: pendingSLChange,
        },
      });
    },
  });

  return (
    <AnimatedButtonGroup
      left={
        changeSLMutation.isPending ? null : (
          <Button
            size={"medium-new"}
            variant="secondary"
            disabled={changeSLMutation.isPending}
            onPress={() => {
              hide();
            }}
          >
            Back
          </Button>
        )
      }
      right={
        <Button
          size={"medium-new"}
          loading={changeSLMutation.isPending}
          loadingText={"Saving"}
          variant="primary"
          disabled={state.amount === state.defaultAmount}
          onPress={async () => {
            await changeSLMutation.mutateAsync();
          }}
        >
          Save
        </Button>
      }
    />
  );
}

function PendingSLChangeDetails({
  colorToken,
  pendingAction,
}: {
  colorToken?: TextColor;
  pendingAction: CardPendingAction & { details: { type: "cardSLChange" } };
}) {
  const token = useSuspenseToken({ mint: pendingAction.details.mint });
  invariant(token, "Invalid token");
  const decimals = token.decimals;

  const oldAmount = pendingAction.details.oldAmount / 10 ** decimals;
  const newAmount = pendingAction.details.newAmount / 10 ** decimals;

  return (
    <View style={{ padding: 16 }}>
      <DashedBorder
        borderWidth={1.5}
        borderColorToken={"dashedListSeparator"}
      />
      <Row justify={"center"}>
        <Text variant={"semibold"} size={32} colorToken={colorToken ?? "text"}>
          {formatUsdValue(oldAmount, { maximumFractionDigits: 0 })}
        </Text>
        <Text
          variant={"bold"}
          colorToken={colorToken ?? "textSecondary"}
          size={16}
        >
          {"  →  "}
        </Text>
        <Text variant={"semibold"} size={32} colorToken={colorToken ?? "text"}>
          {formatUsdValue(newAmount, { maximumFractionDigits: 0 })}
        </Text>
      </Row>
    </View>
  );
}

function PendingFooter({ state }: { state: State & { type: "pending" } }) {
  const { hide } = useModalState();
  const { timeLockSeconds, pendingAction } = state;
  const { wallet } = useActiveWallet();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const { remaining: timeLockRemaining, isReady } = useCountdown({
    timestamp: pendingAction.approvedAt,
    duration: timeLockSeconds,
  });

  const confirmMutation = useMutation({
    mutationFn: async () => {
      const action = await prepareAction({
        type: "cardSLChangeExecute",
        activityId: pendingAction.activityId,
      });

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );

      const { activityId } = await executeAction({ signedTransactions });
      await confirmActivity({ activityId });

      await refetchCard({ queryClient });
    },
    onSuccess: () => {
      toast.info("Daily Limit updated");
    },
    onError: () => {
      toast.error("Daily Limit update failed. Please try again.");
    },
  });

  return (
    <View gap={16}>
      <AnimatedButtonGroup
        left={
          confirmMutation.isPending ? null : (
            <Button
              size={"medium-new"}
              variant="secondary"
              onPress={async () => {
                hapticSelect();
                useModalState.setState({
                  state: { type: "cancelling", timeLockSeconds, pendingAction },
                });
              }}
            >
              Cancel
            </Button>
          )
        }
        right={
          <Button
            size={"medium-new"}
            animation={isReady ? "left-right" : false}
            variant={isReady ? "success" : "primary"}
            loading={confirmMutation.isPending}
            loadingText={"Confirming"}
            style={{ text: { fontVariant: ["tabular-nums"] } }}
            onPress={async () => {
              if (isReady) {
                hapticSelect();
                await confirmMutation.mutateAsync();
                hide();
              } else {
                hapticError();
              }
            }}
          >
            {isReady ? "Complete" : timeLockRemaining.toFormat("mm:ss")}
          </Button>
        }
      />
    </View>
  );
}

function CancellingFooter({
  state,
}: {
  state: State & { type: "cancelling" };
}) {
  const { hide } = useModalState();
  const { timeLockSeconds, pendingAction } = state;
  const { wallet } = useActiveWallet();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const activityId = pendingAction.activityId;
  const cancelMutation = useMutation({
    mutationFn: async () => {
      invariant(activityId, "activityId is not set");

      const action = await prepareAction({
        type: "cardActivityCancel",
        activityId,
      });

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );

      await executeAction({ signedTransactions });
      await confirmActivity({ activityId });

      await refetchCard({ queryClient });
    },
    onSuccess: () => {
      toast.info("Daily Limit update cancelled");
    },
    onError: () => {
      toast.error("Daily Limit update failed. Please try again.");
    },
  });

  return (
    <View gap={16}>
      <AnimatedButtonGroup
        left={
          cancelMutation.isPending ? null : (
            <Button
              size={"medium-new"}
              variant="secondary"
              onPress={async () => {
                hapticSelect();
                useModalState.setState({
                  state: { type: "pending", timeLockSeconds, pendingAction },
                });
              }}
            >
              Back
            </Button>
          )
        }
        right={
          <Button
            size={"medium-new"}
            variant={"danger"}
            loading={cancelMutation.isPending}
            loadingText={"Cancelling"}
            onPress={async () => {
              hapticSelect();
              await cancelMutation.mutateAsync();
              hide();
            }}
          >
            Cancel
          </Button>
        }
      />
    </View>
  );
}
