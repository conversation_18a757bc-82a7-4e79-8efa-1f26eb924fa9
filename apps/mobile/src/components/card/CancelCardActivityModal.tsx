import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import { useActiveWallet } from "~/state/wallet";
import { useEffect, useRef } from "react";
import { create } from "zustand";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { executeAction, prepareAction } from "~/services/wallets";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import invariant from "invariant";
import { hapticDismissBottomTray, hapticSelect } from "~/utils/haptics";
import { confirmActivity } from "~/services/activities";
import { refetchCard } from "~/state/bridge";
import { AnimatedButtonGroup } from "~/components/AnimatedButtonGroup";
import { ActivitiesStackGraphic } from "./ActivitiesStackGraphic";

const useModalState = create<{
  activityId: string | null;
  hide: () => void;
}>((set) => ({ activityId: null, hide: () => set({ activityId: null }) }));

export function showCancelCardActivityModal(activityId: string) {
  useModalState.setState({ activityId });
}

export function CancelCardActivityModal() {
  const { wallet } = useActiveWallet();
  const queryClient = useQueryClient();

  const modalRef = useRef<BottomModalImperativeMethods>(null);
  const { activityId, hide } = useModalState();

  useEffect(() => {
    if (activityId) {
      modalRef.current?.present();
    } else {
      modalRef.current?.close();
    }
  }, [activityId]);

  const cancelMutation = useMutation({
    mutationFn: async () => {
      invariant(activityId, "activityId is not set");

      const action = await prepareAction({
        type: "cardActivityCancel",
        activityId,
      });

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );

      await executeAction({ signedTransactions });
      await confirmActivity({ activityId });

      await refetchCard({ queryClient });
    },
  });

  return (
    <BottomModal
      modalId="cancel-card-activity"
      modalRef={modalRef}
      title={" "}
      onClose={hide}
      onDismiss={hide}
      body={
        <View gap={16}>
          <ActivitiesStackGraphic redCross />
          <View gap={8} style={{ alignItems: "center" }}>
            <Text variant="semibold" size={20} align={"center"}>
              Are you sure?
            </Text>
            <Text
              variant="medium"
              colorToken="textSecondary"
              size={15}
              align={"center"}
            >
              Your transaction will be canceled.
            </Text>
          </View>
        </View>
      }
      footer={
        <AnimatedButtonGroup
          left={
            cancelMutation.isPending ? null : (
              <Button
                size={"medium-new"}
                variant="secondary"
                onPress={() => {
                  hapticDismissBottomTray();
                  hide();
                }}
              >
                Back
              </Button>
            )
          }
          right={
            <Button
              size={"medium-new"}
              variant="primary"
              loading={cancelMutation.isPending}
              loadingText={"Cancelling"}
              onPress={async () => {
                hapticSelect();
                await cancelMutation.mutateAsync();
                hide();
              }}
            >
              Cancel
            </Button>
          }
        />
      }
    />
  );
}
