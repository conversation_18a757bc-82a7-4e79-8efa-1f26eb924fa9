import { Suspense } from "react";
import { Text, useColor, View } from "~/components/Themed";
import Animated from "react-native-reanimated";
import { Icon } from "~/components/Icon";
import { Flex, Row } from "~/components/Grid";
import { ListSeparator } from "~/components/ListSeparator";
import { DURATION_FAST, ZoomAndFadeOut } from "~/constants/animations";
import { useSuspenseCardBalance } from "~/state/bridge";
import { Card } from "~/services/bridge";
import { mints } from "~/constants/tokens";
import { DateTime } from "luxon";
import { formatUsdValue } from "@squads/utils/numberFormats";
import { FuseErrorBoundary } from "~/components/errors/FuseErrorBoundary";
import { TouchableScale } from "~/components/TouchableScale";
import { useSettings } from "~/state/userPreferences";
import { z } from "zod";

const exitAnimation = ZoomAndFadeOut.duration(DURATION_FAST).withEndValues({
  opacity: 0,
  scale: 0.7,
});

const SPENDING_LIMIT_THRESHOLD = 10;

export function LowSpendingLimitWarning({
  onchainSpendingLimits,
}: {
  onchainSpendingLimits: Card["spendingLimits"];
}) {
  return (
    <FuseErrorBoundary FallbackComponent={() => null}>
      <Suspense fallback={null}>
        <LowSpendingLimitWarningInner
          onchainSpendingLimits={onchainSpendingLimits}
        />
      </Suspense>
    </FuseErrorBoundary>
  );
}

function LowSpendingLimitWarningInner({
  onchainSpendingLimits,
}: {
  onchainSpendingLimits: Card["spendingLimits"];
}) {
  const background = useColor("backgroundBanner");
  const { authLimits } = useSuspenseCardBalance();

  const [dismissedTimestamp, setDismissedTimestamp] = useSettings(
    "DISMISSED_LOW_SPENDING_LIMIT_WARNING",
    z.number(),
    undefined
  );

  const usdcSpendingLimit = onchainSpendingLimits[mints.usdc];
  const usdAuthLimit = authLimits.find((limit) => limit.currency === "usd");

  const lowerSpendingLimit = getLowSpendingLimit(
    usdcSpendingLimit && {
      left: usdcSpendingLimit.remainingAmount / 1e6,
      total: usdcSpendingLimit.amount / 1e6,
      nextResetAt: DateTime.fromSeconds(usdcSpendingLimit.lastReset).plus({
        hours: 24,
      }),
    },
    usdAuthLimit && {
      left: usdAuthLimit.remaining,
      total: usdAuthLimit.total,
      nextResetAt: DateTime.fromSeconds(usdAuthLimit.nextResetAt),
    }
  );

  if (!lowerSpendingLimit) return null;

  if (
    dismissedTimestamp &&
    lowerSpendingLimit.nextResetAt <= DateTime.fromSeconds(dismissedTimestamp)
  ) {
    return null;
  }

  return (
    <Animated.View
      exiting={exitAnimation}
      style={{
        padding: 16,
        gap: 8,
        flexDirection: "row",
        alignItems: "baseline",
        borderRadius: 16,
        borderCurve: "continuous",
        backgroundColor: background,
      }}
    >
      <Icon name="arrow.up.square.fill" size={12} colorToken="textSecondary" />
      <Flex gap={16}>
        <View gap={10}>
          <Text variant="medium" size={15} colorToken="textSecondary">
            Reaching daily limit
          </Text>
          <Text variant="medium">
            <Text variant="medium" colorToken="textSecondary">
              <Text variant="semibold">
                {formatUsdValue(lowerSpendingLimit.left)}
              </Text>
              {"  "}left until{"  "}
              <Text variant="semibold">
                {lowerSpendingLimit.nextResetAt.toFormat("hh:mm a")}
              </Text>{" "}
              <Text variant="semibold">
                {lowerSpendingLimit.nextResetAt.toRelativeCalendar({
                  unit: "days",
                })}
              </Text>
            </Text>
          </Text>
          <ListSeparator />
        </View>
        <Row>
          <Text
            style={{ maxWidth: "80%" }}
            variant="medium"
            size={13}
            colorToken="textSecondary"
          >
            You have less than {SPENDING_LIMIT_THRESHOLD}% of your daily
            spending limit left for purchases for today
          </Text>
        </Row>
      </Flex>
      <TouchableScale
        style={{
          position: "absolute",
          top: 16,
          right: 16,
        }}
        activeScale={0.9}
        onPress={() => {
          lowerSpendingLimit &&
            setDismissedTimestamp(lowerSpendingLimit.nextResetAt.toSeconds());
        }}
      >
        <Icon
          name="xmark"
          weight="bold"
          size={9}
          rectSize={24}
          colorToken="textTertiary"
        />
      </TouchableScale>
    </Animated.View>
  );
}

type SpendingLimit = {
  left: number;
  total: number;
  nextResetAt: DateTime;
};

function getLowSpendingLimit(
  first: SpendingLimit | undefined,
  second: SpendingLimit | undefined
) {
  if (first && second) {
    if (isLowerThanThreshold(first) && isLowerThanThreshold(second)) {
      return first.nextResetAt > second.nextResetAt ? first : second;
    } else if (isLowerThanThreshold(first)) {
      return first;
    } else if (isLowerThanThreshold(second)) {
      return second;
    }
  }
  if (first && isLowerThanThreshold(first)) {
    return first;
  }
  if (second && isLowerThanThreshold(second)) {
    return second;
  }

  return null;
}

function isLowerThanThreshold(spendingLimit: SpendingLimit) {
  return (
    (spendingLimit.left / spendingLimit.total) * 100 < SPENDING_LIMIT_THRESHOLD
  );
}
