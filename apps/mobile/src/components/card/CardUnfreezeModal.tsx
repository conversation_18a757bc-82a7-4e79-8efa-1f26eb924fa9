import { create } from "zustand";
import { useEffect, useId, useRef } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import { unfreezeCard } from "~/state/bridge";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "~/components/Toaster";
import { VisaCard } from "~/components/card/VisaCard";
import { BlurView } from "expo-blur";
import { IconWrapper } from "~/components/IconWrapper";
import { Icon } from "~/components/Icon";
import { router } from "expo-router";
import { hapticDismissBottomTray, hapticSelect } from "~/utils/haptics";
import { AnimatedButtonGroup } from "~/components/AnimatedButtonGroup";

const useModalState = create<{
  shown: boolean;
  hide: () => void;
}>((set) => ({ shown: false, hide: () => set({ shown: false }) }));

export function showCardUnfreezeModal() {
  useModalState.setState({ shown: true });
}

export function CardUnfreezeModal() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const id = useId();
  const modalRef = useRef<BottomModalImperativeMethods>(null);
  const { shown, hide } = useModalState();

  useEffect(() => {
    if (shown) {
      modalRef.current?.present();
    } else {
      modalRef.current?.close();
    }
  }, [shown]);

  const unfreezeCardMutation = useMutation({
    mutationFn: async () => {
      await unfreezeCard({ queryClient });
    },
    onSuccess: () => {
      toast.info("Card has been unfrozen");
    },
    onError: () => {
      toast.error("Failed to unfreeze card. Please try again");
    },
  });

  return (
    <BottomModal
      modalId={id}
      modalRef={modalRef}
      title={" "}
      onClose={hide}
      onDismiss={hide}
      body={
        <View gap={16}>
          <FreezeCardImage />
          <View gap={8} style={{ alignItems: "center" }}>
            <Text variant="semibold" size={20} align={"center"}>
              Unfreeze your card?
            </Text>
            <Text
              variant="medium"
              colorToken="textSecondary"
              size={15}
              align={"center"}
            >
              Your card will be immediately available for use.
            </Text>
          </View>
        </View>
      }
      footer={
        <AnimatedButtonGroup
          left={
            unfreezeCardMutation.isPending ? null : (
              <Button
                size={"medium-new"}
                variant="secondary"
                onPress={() => {
                  hapticDismissBottomTray();
                  hide();
                }}
              >
                Cancel
              </Button>
            )
          }
          right={
            <Button
              size={"medium-new"}
              loading={unfreezeCardMutation.isPending}
              loadingText={"Unfreezing"}
              variant="primary"
              onPress={async () => {
                hapticSelect();
                await unfreezeCardMutation.mutateAsync();
                hide();
                router.dismissTo("/unlocked/bridge/card/main");
              }}
            >
              Unfreeze
            </Button>
          }
        />
      }
    />
  );
}

export function FreezeCardImage() {
  const height = 164 + 80;

  return (
    <View
      style={{
        alignItems: "center",
        justifyContent: "center",
        marginHorizontal: -24,
        marginBottom: -24,
        height: height,
      }}
    >
      <View style={{ width: "80%" }}>
        <VisaCard />
      </View>
      <View style={[{ position: "absolute", left: 0, right: 0, bottom: 0 }]}>
        <View
          style={{
            alignItems: "center",
            position: "relative",
            top: 16,
            zIndex: 1,
          }}
        >
          <IconWrapper size={32} background={"#fff"}>
            <IconWrapper size={30} background={"#fff"}>
              <Icon name={"snowflake.slash"} size={14} />
            </IconWrapper>
          </IconWrapper>
        </View>
        <BlurView
          tint={"light"}
          intensity={50}
          style={{
            height: height / 2,
            borderTopColor: "#fff",
            borderTopWidth: 1,
          }}
        />
      </View>
    </View>
  );
}
