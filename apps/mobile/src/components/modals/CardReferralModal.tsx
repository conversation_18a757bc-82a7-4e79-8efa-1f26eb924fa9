import * as React from "react";
import { createRef, memo, RefObject, useId } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import {
  hapticDismissBottomTray,
  hapticOpenBottomTray,
  hapticSuccess,
} from "~/utils/haptics";
import { create } from "zustand";
import { Image } from "expo-image";
import { useLazyCard } from "~/state/bridge";
import { Card } from "~/services/bridge";
import { Row } from "~/components/Grid";
import { setStringAsync } from "expo-clipboard";
import { Share } from "react-native";
import { AnimatedButtonGroup } from "~/components/AnimatedButtonGroup";
import { ContentSkeleton } from "~/components/Skeleton";
import { Icon } from "~/components/Icon";

const useModalState = create<{
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}>(() => ({ modalRef: createRef<BottomModalImperativeMethods>() }));

export function showCardReferralModal() {
  hapticOpenBottomTray();
  useModalState.getState().modalRef?.current?.present();
}

export function CardReferralModal() {
  const id = useId();
  const { modalRef } = useModalState();

  const cardQuery = useLazyCard();

  return (
    <BottomModal
      modalId={id}
      modalRef={modalRef}
      title={" "}
      body={
        <View gap={24} style={{ alignItems: "center", marginTop: -10 }}>
          <Visual />

          <View gap={8} style={{ alignItems: "center" }}>
            <Text variant="semibold" size={20} align={"center"}>
              Invite friends, earn rewards
            </Text>
            <Text
              variant="medium"
              colorToken="textSecondary"
              size={15}
              align={"center"}
            >
              You’ll both{" "}
              <Text variant="medium" size={15}>
                earn $5 when
              </Text>{" "}
              a friend{"\n"}activates Fuse Card using your code.{"\n"}
              {cardQuery.data ? (
                <Text variant="medium" size={15} colorToken="textSecondary">
                  You have {cardQuery.data.referral.remaining}{" "}
                  {cardQuery.data.referral.remaining === 1
                    ? "invite"
                    : "invites"}{" "}
                  available.
                </Text>
              ) : null}
            </Text>
          </View>

          <View gap={8} style={{ alignItems: "center" }}>
            <InviteCodeView referral={cardQuery.data?.referral} />
            {cardQuery.data && (
              <Row gap={8}>
                <Icon name={"person.crop.circle.dashed"} size={10} />
                <Text size={13}>
                  {cardQuery.data.referral.used}/
                  {cardQuery.data.referral.used +
                    cardQuery.data.referral.remaining}{" "}
                  <Text size={13} colorToken={"textSecondary"}>
                    invites used
                  </Text>
                </Text>
              </Row>
            )}
          </View>
        </View>
      }
      footer={
        <AnimatedButtonGroup
          left={
            <Button
              size={"medium-new"}
              variant="secondary"
              onPress={() => {
                hapticDismissBottomTray();
                modalRef.current?.close();
              }}
            >
              Cancel
            </Button>
          }
          right={
            <Button
              size={"medium-new"}
              variant="primary"
              disabled={!cardQuery.data}
              onPress={async () => {
                hapticDismissBottomTray();
                modalRef.current?.close();

                await Share.share({
                  message: cardQuery.data
                    ? `Get $5 on your Fuse Card. Use referral code ${cardQuery.data.referral.inviteCode} when you create your card.`
                    : "",
                });

                hapticSuccess();
              }}
            >
              Share
            </Button>
          }
        />
      }
    />
  );
}

function InviteCodeView({
  referral,
}: {
  referral: Card["referral"] | undefined;
}) {
  return (
    <Row
      justify={"space-between"}
      background={"backgroundBanner"}
      style={{
        width: "100%",
        paddingVertical: 16,
        paddingLeft: 24,
        paddingRight: 16,
        borderRadius: 24,
        borderCurve: "continuous",
      }}
    >
      {referral ? (
        <Text variant={"semibold"} size={22} style={{ letterSpacing: 2 }}>
          {referral.inviteCode}
        </Text>
      ) : (
        <ContentSkeleton>
          <Text variant={"semibold"} size={22} style={{ letterSpacing: 2 }}>
            XXXXXX
          </Text>
        </ContentSkeleton>
      )}
      <Button
        onPress={() => {
          if (!referral) {
            return;
          }
          setStringAsync(referral.inviteCode);
          hapticSuccess();
        }}
        variant={"secondary"}
        size={"small"}
      >
        Copy
      </Button>
    </Row>
  );
}

const Visual = memo(function Visual() {
  return (
    <Image
      source={require("../../../assets/images/actions/card-referral.svg")}
      style={{ width: 90, aspectRatio: 1 }}
    />
  );
});
