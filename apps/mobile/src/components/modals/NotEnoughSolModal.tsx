import * as React from "react";
import { createRef, memo, RefObject, useId } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import { hapticDismissBottomTray, hapticOpenBottomTray } from "~/utils/haptics";
import Svg, { ClipPath, Defs, G, Path, Rect } from "react-native-svg";
import { create } from "zustand";

const useModalState = create<{
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}>(() => ({ modalRef: createRef<BottomModalImperativeMethods>() }));

export function showNotEnoughSolModal() {
  hapticOpenBottomTray();
  useModalState.getState().modalRef?.current?.present();
}

export function NotEnoughSolModal() {
  const id = useId();
  const { modalRef } = useModalState();

  return (
    <BottomModal
      modalId={id}
      modalRef={modalRef}
      title={" "}
      body={
        <View gap={24} style={{ alignItems: "center", marginTop: -10 }}>
          <Visual />

          <View gap={8} style={{ alignItems: "center" }}>
            <Text variant="semibold" size={20} align={"center"}>
              Insufficient SOL
            </Text>
            <Text
              variant="medium"
              colorToken="textSecondary"
              size={15}
              align={"center"}
            >
              Top up SOL to your wallet{"\n"}
              to cover network fee.
            </Text>
          </View>
        </View>
      }
      footer={
        <Button
          variant="secondary"
          onPress={() => {
            hapticDismissBottomTray();
            modalRef.current?.close();
          }}
        >
          Got it
        </Button>
      }
    />
  );
}

const Visual = memo(function Visual() {
  return (
    <Svg width={96} height={58} viewBox="0 0 96 58" fill="none">
      <Rect
        x={95.5}
        y={0.5}
        width={57}
        height={95}
        rx={19.5}
        transform="rotate(90 95.5 .5)"
        stroke="red"
        opacity={0.1}
      />
      <G clipPath="url(#clip0_6867_994)">
        <Rect
          x={88}
          y={8}
          width={42}
          height={80}
          rx={14}
          transform="rotate(90 88 8)"
          fill="#FF5C5C"
        />
        <Path
          d="M58.082 33.017c.297 0 .447.358.236.569L55.1 36.805a.668.668 0 01-.471.195H38.335a.333.333 0 01-.237-.57l3.22-3.218a.668.668 0 01.47-.195h16.294zm-3.454-6.047c.18 0 .348.072.471.195l3.22 3.219c.***********-.237.57H41.789a.668.668 0 01-.472-.196l-3.219-3.219a.334.334 0 01.237-.57h16.293zM58.083 21c.297 0 .446.359.236.57L55.1 24.787a.67.67 0 01-.472.195H38.335a.334.334 0 01-.237-.569l3.22-3.219A.687.687 0 0141.79 21h16.293z"
          fill="#fff"
        />
        <Path
          d="M10 22c0-6.627 5.373-12 12-12v38c-6.627 0-12-5.373-12-12V22z"
          fill="red"
        />
      </G>
      <Rect
        x={87.5}
        y={8.5}
        width={41}
        height={79}
        rx={13.5}
        transform="rotate(90 87.5 8.5)"
        stroke="red"
      />
      <Defs>
        <ClipPath id="clip0_6867_994">
          <Rect
            x={88}
            y={8}
            width={42}
            height={80}
            rx={14}
            transform="rotate(90 88 8)"
            fill="#fff"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
});
