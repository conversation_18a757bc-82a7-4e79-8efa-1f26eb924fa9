import blurryStarBackground from "~assets/images/blurry-star-background.png";
import { RefObject, useId } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import { hapticDismissBottomTray, hapticOpenModalSheet } from "~/utils/haptics";
import { pluralize } from "~/utils/pluralize";
import { Flex, Row } from "~/components/Grid";
import { DashedBorder } from "~/components/DashedBorder";
import { Icon } from "~/components/Icon";
import { Image } from "expo-image";
import { FusePlusText } from "~/components/FusePlusText";
import { router } from "expo-router";

export function SponsoredActionInfoModal({
  modalRef,
  actionsLeft,
  actionsUsed,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
  actionsLeft: number;
  actionsUsed: number;
}) {
  const id = useId();

  const title =
    actionsUsed === 0
      ? `You have ${actionsLeft} free\ntransactions remaining`
      : `${actionsLeft > 0 ? actionsLeft : "No"} free ${pluralize(actionsLeft, "transaction")}\nleft this month`;

  return (
    <BottomModal
      modalId={id}
      modalRef={modalRef}
      title={" "}
      body={
        <View gap={24} style={{ alignItems: "center", marginTop: -10 }}>
          <Visual left={actionsLeft} used={actionsUsed} />

          <View gap={8} style={{ alignItems: "center" }}>
            <Text variant="semibold" size={20} align={"center"}>
              {title}
            </Text>
            <Text
              variant="medium"
              colorToken="textSecondary"
              size={15}
              align={"center"}
            >
              Get Fuse Plus to unlock more.
            </Text>
          </View>
        </View>
      }
      footer={
        <Row gap={8}>
          <Flex>
            <Button
              size={"medium-new"}
              variant="secondary"
              onPress={() => {
                hapticDismissBottomTray();
                modalRef.current?.close();
              }}
            >
              Got it
            </Button>
          </Flex>
          <Flex>
            <Button
              size={"medium-new"}
              variant="primary"
              style={{ text: { top: 3 } }}
              onPress={() => {
                modalRef.current?.close();

                hapticOpenModalSheet();
                router.push("/unlocked/subscription/promo");
              }}
            >
              <FusePlusText
                colorToken={"textButtonPrimary"}
                prefix={"Get "}
                size={16}
              />
            </Button>
          </Flex>
        </Row>
      }
    />
  );
}

function Visual({ used, left }: { used: number; left: number }) {
  return (
    <View style={{ height: 100, justifyContent: "center", width: "100%" }}>
      {(() => {
        if (left > 0 && used > 0) {
          return <TransactionsUsageProgress used={used} left={left} />;
        }

        if (left === 0) {
          return <NoTransactionLeftVisual />;
        }

        if (used === 0) {
          return <AllTransactionsAvailableVisual />;
        }
      })()}
    </View>
  );
}

function TransactionsUsageProgress({
  used,
  left,
}: {
  used: number;
  left: number;
}) {
  return (
    <View
      style={{
        paddingHorizontal: 20,
        paddingTop: 28,
        paddingBottom: 16,
        width: "100%",
        gap: 4,
      }}
    >
      <Row style={{ height: 27 }} gap={4}>
        <View
          style={{
            height: "100%",
            flex: left,
            backgroundColor: "black",
            borderTopLeftRadius: 5,
            borderBottomLeftRadius: 5,
            borderTopRightRadius: 8,
            borderBottomRightRadius: 8,
            borderCurve: "continuous",
          }}
        />
        <Text size={12} colorToken="systemGreen">
          􀋦
        </Text>
        <View
          style={{
            height: "100%",
            flex: used,
            backgroundColor: "#E5E5E5",
            borderTopLeftRadius: 8,
            borderBottomLeftRadius: 8,
            borderTopRightRadius: 5,
            borderBottomRightRadius: 5,
            borderCurve: "continuous",
          }}
        />
      </Row>
      <Row justify={"space-between"} style={{ paddingHorizontal: 5 }}>
        <Text variant="medium" size={12} colorToken="text">
          {left} left
        </Text>
        <Text variant="medium" size={12} colorToken="textSecondary">
          {used} used
        </Text>
      </Row>
    </View>
  );
}

function NoTransactionLeftVisual() {
  const skeletonBackground = "#E5E5E5";

  return (
    <View
      style={{
        alignItems: "center",
      }}
    >
      <View
        background={"background"}
        style={{
          width: 148,
          height: 42,
          position: "absolute",
          top: -22,
          borderColor: "rgba(0,0,0,0.03)",
          borderWidth: 1,
          borderRadius: 17,
        }}
      />
      <View
        background={"background"}
        style={{
          width: 172,
          height: 48,
          position: "absolute",
          top: -12,
          borderColor: "rgba(0,0,0,0.04)",
          borderWidth: 1,
          borderRadius: 17,
        }}
      />
      <View
        background={"background"}
        style={{
          width: 200,
          height: 52,
          paddingHorizontal: 12,
          justifyContent: "center",
        }}
      >
        <DashedBorder
          borderWidth={1.5}
          borderColor={skeletonBackground}
          borderRadius={17}
        />
        <Row justify="space-between">
          <Row gap={9}>
            <Icon
              name={"arrow.up.square.fill"}
              color={skeletonBackground}
              size={12}
            />
            <View gap={4}>
              <View
                style={{
                  borderRadius: 42,
                  height: 9,
                  width: 32,
                  backgroundColor: skeletonBackground,
                }}
              />
              <View
                style={{
                  borderRadius: 42,
                  height: 9,
                  width: 62,
                  backgroundColor: skeletonBackground,
                }}
              />
            </View>
          </Row>
          <Row>
            <Text variant={"medium"} size={12} colorToken={"systemRed"}>
              0 left 􀋦
            </Text>
          </Row>
        </Row>
      </View>
    </View>
  );
}

function AllTransactionsAvailableVisual() {
  const fartherItemsBackground = "#EDEDED";
  const skeletonBackground = "rgba(255,255,255,0.24)";

  return (
    <View
      style={{
        alignItems: "center",
      }}
    >
      <View
        background={"background"}
        style={{
          width: 148,
          height: 42,
          position: "absolute",
          top: -22,
          borderColor: "rgba(0,0,0,0.03)",
          borderWidth: 1,
          borderRadius: 17,
          backgroundColor: fartherItemsBackground,
          opacity: 0.5,
        }}
      />
      <View
        style={{
          width: 172,
          height: 48,
          position: "absolute",
          top: -12,
          borderColor: "rgba(0,0,0,0.04)",
          borderWidth: 1,
          borderRadius: 17,
          backgroundColor: fartherItemsBackground,
        }}
      />
      <View
        background={"systemGreen"}
        style={{
          width: 200,
          height: 52,
          paddingHorizontal: 12,
          justifyContent: "center",
          borderRadius: 17,
          borderCurve: "continuous",
          overflow: "hidden",
        }}
      >
        <Image
          source={blurryStarBackground}
          style={{
            position: "absolute",
            width: 190,
            aspectRatio: 1,
            top: -95,
            right: -85,
            transform: [{ rotateZ: "15deg" }],
            opacity: 0.8,
            // backgroundColor: "pink",
          }}
        />
        <Row justify="space-between">
          <Row gap={9}>
            <Icon
              name={"arrow.up.square.fill"}
              color={skeletonBackground}
              size={12}
            />
            <View gap={4}>
              <View
                style={{
                  borderRadius: 42,
                  height: 9,
                  width: 32,
                  backgroundColor: skeletonBackground,
                }}
              />
              <View
                style={{
                  borderRadius: 42,
                  height: 9,
                  width: 62,
                  backgroundColor: skeletonBackground,
                }}
              />
            </View>
          </Row>
          <Row>
            <Text variant={"medium"} size={12} colorToken={"textButtonPrimary"}>
              Sponsored 􀋦
            </Text>
          </Row>
        </Row>
      </View>
    </View>
  );
}
