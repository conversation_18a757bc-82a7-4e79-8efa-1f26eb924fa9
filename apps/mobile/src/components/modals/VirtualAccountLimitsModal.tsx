import * as React from "react";
import { RefObject, useId } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Text, TextColor, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import { hapticDismissBottomTray } from "~/utils/haptics";
import { Row } from "~/components/Grid";
import { ListSeparator } from "~/components/ListSeparator";

export function VirtualAccountLimitsModal({
  modalRef,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  const id = useId();

  return (
    <BottomModal
      modalId={id}
      modalRef={modalRef}
      title={"Limits"}
      body={
        <View gap={24}>
          <View gap={12}>
            <LimitRow title={"From my own bank account"} value={"no limits"} />
            <ListSeparator />
            <LimitRow
              title={"From individuals"}
              value={"$4,000 / transaction"}
            />
            <ListSeparator />
            <LimitRow title={"From businesses"} value={"no limits"} />
            <ListSeparator />
            <LimitRow
              title={"From payroll platforms like\nDeel, Gusto, and Upwork"}
              value={"no limits"}
            />
          </View>
        </View>
      }
      footer={
        <Button
          size={"medium-new"}
          variant="secondary"
          onPress={() => {
            hapticDismissBottomTray();
            modalRef.current?.close();
          }}
        >
          Got it
        </Button>
      }
    />
  );
}

function LimitRow({
  title,
  value,
  valueColorToken,
}: {
  title: string;
  value: string;
  valueColorToken?: TextColor;
}) {
  return (
    <Row justify={"space-between"}>
      <Text variant={"medium"} size={14} colorToken={"textSecondary"}>
        {title}
      </Text>
      <Text variant={"medium"} size={14} colorToken={valueColorToken}>
        {value}
      </Text>
    </Row>
  );
}
