import * as React from "react";
import { RefObject, useId } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { PlusMembershipCardContent } from "~/app/unlocked/subscription/welcome";
import { useSharedValue } from "react-native-reanimated";
import { View } from "~/components/Themed";

export function FusePlusInfoModal({
  modalRef,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  const id = useId();
  const noAnimation = useSharedValue(1);

  return (
    <BottomModal
      modalId={id}
      modalRef={modalRef}
      title={" "}
      backgroundColor={"backgroundCard"}
      closeButtonColor={"textSecondaryOpposite"}
      body={
        // applying margin/padding hack for gradient
        <View
          style={{
            marginTop: -32,
            marginBottom: -24,
            paddingBottom: 24,
          }}
        >
          <PlusMembershipCardContent plusGlowAnimationProgress={noAnimation} />
        </View>
      }
      footer={null}
    />
  );
}
