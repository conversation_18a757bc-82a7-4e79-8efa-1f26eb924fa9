import { ReactNode, useRef, useState } from "react";
import { PagerImperativeMethods, PagerView } from "~/components/PagerView";
import Animated, {
  clamp,
  FadeIn,
  FadeOut,
  interpolate,
  LinearTransition,
  ReduceMotion,
  SharedValue,
  useAnimatedProps,
  useSharedValue,
} from "react-native-reanimated";
import { Icon } from "~/components/Icon";
import { Row } from "~/components/Grid";
import { Image } from "expo-image";
import { Text, View } from "~/components/Themed";
import { Settings } from "react-native";
import { DURATION_FAST, SPRING } from "~/constants/animations";
import { showDepositModal } from "~/components/DepositButton";
import { useSwapAction } from "~/components/SwapButton";
import * as Haptics from "expo-haptics";
import { useActiveWallet } from "~/state/wallet";
import { useAddSpendingLimitAction } from "~/components/security/AddSpendingLimitTile";
import { router } from "expo-router";
import {
  isWalletOffline,
  useIsWalletInactive,
} from "~/hooks/useAssertWalletState";
import { useStakeModal } from "~/components/StakeButton";
import { registerForPushNotifications } from "~/utils/pushNotifications";
import { useToast } from "~/components/Toaster";
import { ShieldIcon } from "~/components/icons/ShieldIcon";
import { showImproveSecurityModal } from "~/components/security/useImproveSecurityModalState";
import {
  isCardAvailable,
  useLazyBridgeAccount,
  useLazyCard,
  useVirtualAccount,
  useVirtualAccountsNeedMigration,
} from "~/state/bridge";
import { hapticOpenBottomTray, hapticOpenModalSheet } from "~/utils/haptics";
import { IconWrapper } from "~/components/IconWrapper";
import { BankPlusIcon } from "~/components/icons/home/<USER>";
import { useQueryClient } from "@tanstack/react-query";
import { FusePlusLogo } from "~/components/icons/FusePlusLogo";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import { useSubscription } from "~/state/subscription";
import { BankIcon } from "~/components/icons/home/<USER>";
import { showCardReferralModal } from "~/components/modals/CardReferralModal";

export const ACTION_CARD_TYPES = [
  "virtual_accounts_migration",
  "card_referral_reward",
  "card",
  "subscription",
  "virtual_bank_account",
  "improve_security",
  "push_notifications",
  "transfer",
  "swap",
  "stake",
  "add_ledger",
  "add_spending_limit",
] as const;
type ActionCardType = (typeof ACTION_CARD_TYPES)[number];

const ACTION_CARD_HEIGHT = 74;

export function ActionCards() {
  const { wallet } = useActiveWallet();
  const { data: usVirtualAccount } = useVirtualAccount({ currency: "usd" });
  const { data: euVirtualAccount } = useVirtualAccount({ currency: "eur" });
  const { data: bridgeAccount } = useLazyBridgeAccount();
  const { data: subscription } = useSubscription();
  const { data: card } = useLazyCard();
  const { data: virtualAccountsNeedMigration } =
    useVirtualAccountsNeedMigration();

  const hasICloudKey = wallet.keys.cloudKey.details.type === "iCloud";

  const pagerViewRef = useRef<PagerImperativeMethods>(null);
  const initialPageIndex = 0;
  const pagePosition = useSharedValue(initialPageIndex);

  const { dismissedCards, dismissCard } = useDismissedCards();

  const actionCards = ACTION_CARD_TYPES.filter((action) => {
    if (dismissedCards.includes(action)) {
      return false;
    }

    if (action === "improve_security" && wallet.keys.recoveryKeys !== null) {
      return false;
    }

    if (action === "push_notifications" && wallet.pushNotificationsSetUp) {
      return false;
    }

    if (action === "add_ledger") {
      if (!hasICloudKey) {
        return false;
      }
    }

    if (action === "add_spending_limit") {
      if (hasICloudKey) {
        return false;
      }
    }

    if (action === "virtual_bank_account") {
      if (usVirtualAccount !== null || euVirtualAccount !== null) {
        return false;
      }
    }

    if (action === "card") {
      if (
        //card already requested or issued
        wallet.card !== null ||
        //card not available in country
        (bridgeAccount?.country && !isCardAvailable(bridgeAccount.country))
      ) {
        return false;
      }
    }

    if (action === "subscription") {
      if (subscription !== undefined && subscription.status === "active") {
        return false;
      }
    }

    if (action === "virtual_accounts_migration") {
      return virtualAccountsNeedMigration?.needMigration;
    }

    if (action === "card_referral_reward") {
      return card && card.referral.remaining > 0;
    }

    return true;
  });

  function onDismissActionCard(action: ActionCardType) {
    const isLastCard = actionCards[actionCards.length - 1] === action;

    if (isLastCard) {
      pagerViewRef.current?.setPage(actionCards.length - 2);
    }

    dismissCard(action);
  }

  if (actionCards.length === 0) {
    return null;
  }

  return (
    <Animated.View
      layout={LinearTransition.springify()
        .stiffness(SPRING.stiffness)
        .damping(SPRING.damping)
        .mass(SPRING.mass)
        .reduceMotion(ReduceMotion.Never)}
      style={{ gap: 8 }}
    >
      <PagerView
        pagerRef={pagerViewRef}
        style={{
          marginHorizontal: 20,
          height: ACTION_CARD_HEIGHT,
        }}
        blurViewStyle={{
          borderRadius: 16,
          borderCurve: "continuous",
          overflow: "hidden",
        }}
        pagePosition={pagePosition}
        initialPageIndex={initialPageIndex}
      >
        {actionCards.map((action) => (
          <Animated.View
            key={action}
            entering={FadeIn.delay(DURATION_FAST).duration(DURATION_FAST)}
            exiting={FadeOut.duration(DURATION_FAST)}
          >
            <ActionCard action={action} onClose={onDismissActionCard} />
          </Animated.View>
        ))}
      </PagerView>
      {actionCards.length > 1 && (
        <Progress active={pagePosition} total={actionCards.length} />
      )}
    </Animated.View>
  );
}

function ActionCard({
  action,
  onClose,
}: {
  action: ActionCardType;
  onClose: (action: ActionCardType) => void;
}) {
  const queryClient = useQueryClient();
  const { wallet } = useActiveWallet();
  const swapAction = useSwapAction();
  const addSpendingLimitAction = useAddSpendingLimitAction();
  const { show: showStakeModal } = useStakeModal();
  const isWalletInactive = useIsWalletInactive();
  const { toast } = useToast();

  if (action === "push_notifications") {
    return (
      <ActionCardView
        icon={
          <Image
            source={require("../../../assets/images/actions/notifications.png")}
            style={{ width: 50, aspectRatio: 1, marginRight: -8 }}
          />
        }
        label={"Enable push notifications"}
        text={"Get balance and product updates"}
        onClose={() => onClose(action)}
        onPress={async () => {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          console.debug("Saving push notifications token for wallet");
          if (
            await registerForPushNotifications({ walletKey: wallet.walletKey })
          ) {
            onClose("push_notifications");
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            toast.success("Notifications enabled");
          }
        }}
      />
    );
  }

  if (action === "transfer") {
    return (
      <ActionCardView
        icon={
          <Image
            source={require("./../../../assets/images/actions/assets.png")}
            style={{ width: 50, aspectRatio: 1, marginRight: -8 }}
          />
        }
        label={"Protect your digital assets"}
        text={"Transfer them to Fuse"}
        onClose={() => onClose(action)}
        onPress={() => {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          showDepositModal();
        }}
      />
    );
  }

  if (action === "swap") {
    return (
      <ActionCardView
        icon={
          <Image
            source={require("../../../assets/images/actions/jupiter.png")}
            style={{ width: 50, aspectRatio: 1, marginRight: -8 }}
          />
        }
        label={"Swap tokens effortlessly"}
        text={"Use our Jupiter integration"}
        onClose={() => onClose(action)}
        onPress={() => swapAction()}
      />
    );
  }

  if (action === "stake") {
    return (
      <ActionCardView
        icon={
          <Image
            source={require("../../../assets/images/actions/stake.png")}
            style={{ width: 50, aspectRatio: 1, marginRight: -8 }}
          />
        }
        label={"Stake your SOL with Fuse"}
        text={"Earn passive income"}
        onClose={() => onClose(action)}
        onPress={async () => {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          await showStakeModal();
        }}
      />
    );
  }

  if (action === "add_ledger") {
    return (
      <ActionCardView
        icon={
          <Image
            source={require("../../../assets/images/actions/ledger.png")}
            style={{ width: 50, aspectRatio: 1, marginRight: -8 }}
          />
        }
        label={"Change your 2FA Key"}
        text={"Upgrade your security"}
        onClose={() => onClose(action)}
        onPress={async () => {
          if (await isWalletInactive()) {
            return;
          }

          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
          router.push("/unlocked/change-cloud-key/options");
        }}
      />
    );
  }

  if (action === "add_spending_limit") {
    return (
      <ActionCardView
        icon={
          <Image
            source={require("../../../assets/images/actions/spending_limits.png")}
            style={{ width: 50, aspectRatio: 1, marginRight: -8 }}
          />
        }
        label={"Create a Spending Limit"}
        text={"For seamless transactions"}
        onClose={() => onClose(action)}
        onPress={() => addSpendingLimitAction()}
      />
    );
  }

  if (action === "improve_security") {
    return (
      <ActionCardView
        icon={<ShieldIcon size={42} />}
        label={"Secure your wallet"}
        text={"Finish set up for advanced security"}
        onPress={() => {
          hapticOpenBottomTray();
          showImproveSecurityModal();
        }}
      />
    );
  }

  if (action === "virtual_bank_account") {
    return (
      <ActionCardView
        icon={
          <IconWrapper
            size={42}
            backgroundColorToken={"blue"}
            variant={"square"}
            style={{ borderRadius: 12 }}
          >
            <BankPlusIcon colorToken={"textButtonPrimary"} />
          </IconWrapper>
        }
        label={"Get your Virtual Bank Account"}
        text={"Receive USD and EUR for USDC"}
        onPress={async () => {
          if (await isWalletOffline(toast, wallet)) {
            return;
          }

          if (wallet.keys.recoveryKeys === null) {
            hapticOpenBottomTray();
            showImproveSecurityModal();
            return;
          }

          hapticOpenModalSheet();
          router.push(`/unlocked/bridge/virtual-account`);
        }}
        onClose={() => onClose(action)}
      />
    );
  }

  if (action === "card") {
    return (
      <ActionCardView
        icon={
          <Image
            source={require("../../../assets/images/actions/card.png")}
            style={{ width: 50, aspectRatio: 1.24 }}
          />
        }
        label={"Fuse Сard is here!"}
        text={"Get your debit card with Fuse Pay"}
        onPress={async () => {
          if (await isWalletOffline(toast, wallet)) {
            return;
          }

          if (wallet.keys.recoveryKeys === null) {
            hapticOpenBottomTray();
            showImproveSecurityModal();
            return;
          }

          hapticOpenModalSheet();
          router.push("/unlocked/bridge/card/onboarding/introduction");
        }}
      />
    );
  }

  if (action === "subscription") {
    return (
      <ActionCardView
        icon={
          <IconWrapper
            size={42}
            background={"black"}
            variant={"square"}
            style={{ borderRadius: 12 }}
          >
            <FusePlusLogo size={20} />
          </IconWrapper>
        }
        label={"Get Fuse Plus"}
        text={"Earn more, spend less with premium benefits"}
        onPress={async () => {
          hapticOpenModalSheet();
          router.push("/unlocked/subscription/promo");
        }}
        onClose={() => onClose(action)}
      />
    );
  }

  if (action === "virtual_accounts_migration") {
    return (
      <ActionCardView
        icon={
          <View>
            <IconWrapper
              size={42}
              backgroundColorToken={"blue"}
              variant={"square"}
              borderRadius={12}
            >
              <BankIcon colorToken={"textOpposite"} size={20} />
            </IconWrapper>
            <IconWrapper
              backgroundColorToken={"textOpposite"}
              size={20}
              style={{ position: "absolute", right: -5, top: -5 }}
            >
              <Icon
                name={"arrow.clockwise.circle.fill"}
                size={12}
                weight="bold"
                colorToken={"blue"}
              />
            </IconWrapper>
          </View>
        }
        label={"Receive payments from individuals"}
        text={"Tap to update your Virtual Account"}
        onPress={async () => {
          hapticOpenModalSheet();
          router.push("/unlocked/bridge/virtual-account");
        }}
        onClose={() => onClose(action)}
      />
    );
  }

  if (action === "card_referral_reward") {
    return (
      <ActionCardView
        icon={
          <Image
            source={require("../../../assets/images/actions/card-referral.svg")}
            style={{ width: 50, aspectRatio: 1 }}
          />
        }
        label={"Invite a friend to Fuse Card"}
        text={"$5 for you, $5 for them"}
        onPress={async () => {
          hapticOpenBottomTray();
          showCardReferralModal();
        }}
        onClose={() => onClose(action)}
      />
    );
  }

  action satisfies never;
}

function ActionCardView({
  icon,
  label,
  text,
  onPress,
  onClose,
}: {
  icon: ReactNode;
  label: string;
  text?: string;
  onPress: () => void;
  onClose?: () => void;
}) {
  return (
    <AnimatedTouchableScale pressedScale={0.99} onPress={onPress}>
      <Row
        background={"backgroundBanner"}
        style={{
          height: ACTION_CARD_HEIGHT,
          padding: 16,
          borderRadius: 20,
          borderWidth: 1,
          borderColor: "rgba(0,0,0,0.07)",
          borderCurve: "continuous",
          gap: 12,
        }}
      >
        {icon}
        <View gap={4}>
          <Text variant={"semibold"} size={15}>
            {label}
          </Text>
          {text && (
            <Text variant={"medium"} size={13} colorToken="textSecondary">
              {text}
            </Text>
          )}
        </View>

        {onClose && (
          <AnimatedTouchableScale
            style={{
              position: "absolute",
              top: 8,
              right: 8,
            }}
            hitSlop={10}
            pressedScale={0.9}
            onPress={onClose}
          >
            <Icon
              name={"xmark"}
              weight="bold"
              size={9}
              rectSize={24}
              colorToken="textTertiary"
            />
          </AnimatedTouchableScale>
        )}
      </Row>
    </AnimatedTouchableScale>
  );
}

export function Progress({
  active,
  total,
}: {
  active: SharedValue<number>;
  total: number;
}) {
  return (
    <Row gap={8} justify={"center"} style={{ height: 10 }}>
      {total !== 1 &&
        Array.from({ length: total }).map((_, index) => (
          <ProgressPoint key={index} index={index} active={active} />
        ))}
    </Row>
  );
}

function ProgressPoint({
  index,
  active,
}: {
  index: number;
  active: SharedValue<number>;
}) {
  const animatedBlurProps = useAnimatedProps(() => {
    const transparency = clamp(
      interpolate(active.value, [index - 1, index, index + 1], [0.3, 1, 0.3]),
      0.3,
      1
    );
    return {
      backgroundColor: `rgba(0,0,0,${transparency})`,
    };
  });

  return (
    <Animated.View
      style={[
        animatedBlurProps,
        {
          width: 5,
          height: 5,
          borderRadius: 4,
        },
      ]}
    />
  );
}

function settingsKey(action: string) {
  return `ACTION_CARDS.${action}`;
}

function shouldShowActionCard(action: ActionCardType): boolean {
  return Settings.get(settingsKey(action)) ?? true;
}

export function setActionCardVisibilityPreference(
  action: ActionCardType,
  visible: boolean
) {
  Settings.set({
    [settingsKey(action)]: visible,
  });
}

function useDismissedCards() {
  const [dismissedCards, setDismissedCards] = useState(
    ACTION_CARD_TYPES.filter((action) => {
      return !shouldShowActionCard(action);
    })
  );

  function hide(action: ActionCardType) {
    setActionCardVisibilityPreference(action, false);

    setDismissedCards((actions) => {
      return [...actions, action];
    });
  }

  return { dismissedCards: dismissedCards, dismissCard: hide };
}
