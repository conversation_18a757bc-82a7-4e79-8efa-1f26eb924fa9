import { Wallet } from "~/services/wallets";
import { Flex, Grid, Row } from "~/components/Grid";
import { BottomInset } from "~/components/BottomInset";
import { HOME_TAB_BAR_HEIGHT } from "~/constants/sizes";
import { RefreshControl } from "react-native";
import { Image } from "expo-image";
import { Suspense, useState } from "react";
import { Text, useColor, View } from "~/components/Themed";
import { TouchableScale } from "~/components/TouchableScale";
import * as Haptics from "expo-haptics";
import { router } from "expo-router";
import { refetchNfts, useHiddenNfts, useNftsByVault } from "~/state/nfts";
import { NftsByVault } from "~/services/nfts";
import { Address } from "@squads/models/solana";
import { ContentSkeleton } from "~/components/Skeleton";
import { useQueryClient } from "@tanstack/react-query";
import { IconWrapper } from "~/components/IconWrapper";
import { Icon } from "~/components/Icon";
import { H3 } from "~/components/typography/H3";
import { NftImage } from "~/components/NftImage";
import {
  FallbackProps,
  FuseErrorBoundary,
} from "~/components/errors/FuseErrorBoundary";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Button } from "~/components/Button";
import { S2 } from "~/components/typography/S2";
import Animated, { AnimatedRef } from "react-native-reanimated";

export function Nfts({
  wallet,
  scrollViewRef,
}: {
  wallet: Wallet;
  scrollViewRef: AnimatedRef<any>;
}) {
  return (
    <FuseErrorBoundary
      FallbackComponent={({ error, resetErrorBoundary, errorId }) => {
        return (
          <NftsErrorFallback
            error={error}
            errorId={errorId}
            resetErrorBoundary={resetErrorBoundary}
            wallet={wallet}
          />
        );
      }}
    >
      <NftsInner wallet={wallet} scrollViewRef={scrollViewRef} />
    </FuseErrorBoundary>
  );
}

function NftsInner({
  wallet,
  scrollViewRef,
}: {
  wallet: Wallet;
  scrollViewRef: AnimatedRef<any>;
}) {
  const queryClient = useQueryClient();

  const refreshControlColor = useColor("textTertiary");
  const [refreshing, setRefreshing] = useState(false);

  return (
    <Animated.ScrollView
      ref={scrollViewRef}
      style={{ overflow: "visible" }}
      contentContainerStyle={{
        paddingTop: 10,
        minHeight: "100%",
        overflow: "visible",
      }}
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          tintColor={refreshControlColor}
          refreshing={refreshing}
          onRefresh={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            setRefreshing(true);
            refetchNfts({
              queryClient,
              vaultKey: wallet.defaultVault,
            }).finally(() => setRefreshing(false));
          }}
        />
      }
    >
      <View style={{ flex: 1, minHeight: "100%", gap: 24 }}>
        <NftsWarning />
        <Suspense fallback={<NftsGridSkeleton />}>
          <NftsGrid vaultKey={wallet.defaultVault} />
        </Suspense>
        <BottomInset offset={HOME_TAB_BAR_HEIGHT} />
      </View>
    </Animated.ScrollView>
  );
}

export function NftsWarning() {
  return (
    <Row
      background={"backgroundSecondary"}
      gap={8}
      style={{ padding: 12, borderRadius: 12, borderCurve: "continuous" }}
    >
      <Icon
        name={"info.circle"}
        colorToken={"textSecondary"}
        size={10}
        rectSize={16}
        style={{ alignSelf: "flex-start" }}
      />
      <Flex>
        <S2 colorToken={"textSecondary"}>
          Fuse supports NFTs, pNFTs and some cNFT collections. Contact support
          for more information.
        </S2>
      </Flex>
    </Row>
  );
}

function NftsErrorFallback({
  resetErrorBoundary,
  wallet,
}: FallbackProps & { wallet: Wallet }) {
  const insets = useSafeAreaInsets();
  const queryClient = useQueryClient();

  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        gap: 32,
        marginBottom: insets.bottom + 46,
      }}
    >
      <View style={{ gap: 18, justifyContent: "center", alignItems: "center" }}>
        <IconWrapper backgroundColorToken="backgroundSecondary" size={48}>
          <Icon
            size={18}
            rectSize={20}
            name={"exclamationmark.triangle.fill"}
            weight={"medium"}
            colorToken={"textSecondary"}
            style={{ transform: [{ translateY: -2 }] }}
          />
        </IconWrapper>
        <H3
          colorToken="textSecondary"
          style={{ maxWidth: 210, textAlign: "center" }}
        >
          Failed to load NFTs
        </H3>
      </View>
      <Button
        variant="secondary"
        size="medium"
        onPress={async () => {
          await refetchNfts({
            queryClient,
            vaultKey: wallet.defaultVault,
          });
          resetErrorBoundary();
        }}
      >
        Retry
      </Button>
    </View>
  );
}

export function NftsGridSkeleton() {
  return (
    <Grid
      items={Array.from({ length: 3 })}
      renderItem={() => <NftsGridItemSkeleton />}
      columns={2}
      gap={16}
    />
  );
}

export function NftsGridItemSkeleton() {
  return (
    <ContentSkeleton borderRadius={16}>
      <View background={"text"} style={{ aspectRatio: 1, flex: 1 }} />
    </ContentSkeleton>
  );
}

export function NftsGrid({ vaultKey }: { vaultKey: Address }) {
  const assets = useNftsByVault({ vaultKey });

  const gridItems = NftGridItem.parseResponse(assets);

  if (assets.nfts.length === 0) {
    return <NoNftsView />;
  }

  const hasCollections = gridItems.some((item) => item.type === "collection");

  return (
    <Grid
      items={gridItems}
      renderItem={(item) => <NftsGridItem item={item} />}
      columns={2}
      gap={16}
      style={{ gap: hasCollections ? 24 : 16 }}
    />
  );
}

function NoNftsView() {
  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        gap: 18,
      }}
    >
      <IconWrapper backgroundColorToken="backgroundSecondary" size={48}>
        <Icon
          size={18}
          name={"photo.stack.fill"}
          weight={"medium"}
          colorToken={"textSecondary"}
        />
      </IconWrapper>
      <H3 colorToken="textSecondary" style={{ textAlign: "center" }}>
        No NFTs yet
      </H3>
    </View>
  );
}

export function NftsGridItem({ item }: { item: NftGridItem }) {
  if (item.type === "collection") {
    return (
      <TouchableScale
        onPress={() => {
          Haptics.selectionAsync();
          router.push(`/unlocked/nft/folder/${item.collection.id}`);
        }}
        activeScale={0.98}
      >
        {item.nfts.slice(0, 3).map((nft, i) => (
          <View
            key={nft.address}
            background={"backgroundSecondary"}
            style={[
              {
                aspectRatio: 1,
                borderRadius: 16,
                borderCurve: "continuous",
                overflow: "hidden",
              },
              i > 0 && {
                position: "absolute",
                top: -i * (8 - i),
                width: "100%",
                height: "100%",
                zIndex: -i,
                transformOrigin: "top",
                transform: [{ scale: 1 - i * 0.08 }],
              },
            ]}
          >
            <Image
              source={nft.imageUrl}
              style={{
                width: "100%",
                height: "100%",
              }}
            />
            {i > 0 && (
              <View
                style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: `rgba(255, 255, 255, ${0.3 + 0.1 * i})`,
                }}
              />
            )}
          </View>
        ))}
      </TouchableScale>
    );
  }

  if (item.type === "nft") {
    return (
      <TouchableScale
        onPress={() => {
          Haptics.selectionAsync();
          router.push(`/unlocked/nft/${item.nft.address}`);
        }}
        activeScale={0.98}
        hitSlop={6}
      >
        <NftImage nft={item.nft} borderRadius={16} />
      </TouchableScale>
    );
  }

  if (item.type === "hidden") {
    return (
      <TouchableScale
        onPress={() => {
          Haptics.selectionAsync();
          router.push(`/unlocked/nft/folder/hidden`);
        }}
        activeScale={0.98}
        hitSlop={6}
      >
        <View
          key={item.type}
          background={"backgroundSecondary"}
          style={[
            {
              aspectRatio: 1,
              borderRadius: 16,
              borderCurve: "continuous",
              justifyContent: "center",
              alignItems: "center",
            },
          ]}
        >
          <Icon name={"eye.slash"} size={20} colorToken={"textSecondary"} />
          <View
            background="background"
            gap={10}
            style={{
              flexDirection: "row",
              paddingHorizontal: 10,
              paddingVertical: 6,
              borderRadius: 8,
              borderCurve: "continuous",
              position: "absolute",
              bottom: 18,
            }}
          >
            <Text colorToken="text" variant="medium" style={{ fontSize: 15 }}>
              Hidden
            </Text>
            <Text
              colorToken="textSecondary"
              variant="medium"
              style={{ fontSize: 15 }}
            >
              {item.nfts.length}
            </Text>
          </View>
        </View>
      </TouchableScale>
    );
  }
}

type Nft = {
  address: string;
  name: string;
  imageUrl: string | null;
  videoUrl: string | null;
};

type Collection = {
  id: string;
  name: string;
  imageUrl: string;
};

export type NftGridItem =
  | { type: "collection"; collection: Collection; nfts: Nft[] }
  | { type: "nft"; nft: Nft }
  | { type: "hidden"; nfts: Nft[] };

const NftGridItem = {
  parseResponse: (response: NftsByVault): NftGridItem[] => {
    const NO_COLLECTION_ID = "none";
    const { hiddenNftAddresses } = useHiddenNfts.getState();
    const hiddenNftsList: Nft[] = [];

    const collectionsById = response.collections.reduce(
      (acc, collection) => {
        acc[collection.id] = collection;
        return acc;
      },
      {} as Record<string, Collection>
    );

    const nftsByCollectionId = response.nfts.reduce(
      (acc, nft) => {
        if (hiddenNftAddresses.includes(nft.address)) {
          hiddenNftsList.push(nft);
          return acc;
        }

        const collectionId = nft.collectionId ?? NO_COLLECTION_ID;
        if (!acc[collectionId]) {
          acc[collectionId] = [];
        }
        acc[collectionId].push(nft);
        return acc;
      },
      {} as Record<string, Nft[]>
    );

    const nftsAssets = Object.entries(nftsByCollectionId).flatMap(
      ([collectionId, nfts]) => {
        if (nfts.length === 1 || collectionId === NO_COLLECTION_ID) {
          return nfts.map((nft) => ({ type: "nft", nft }) as NftGridItem);
        }

        return [
          {
            type: "collection",
            collection: collectionsById[collectionId],
            nfts,
          } as NftGridItem,
        ];
      }
    );

    if (hiddenNftsList.length > 0) {
      nftsAssets.push({ type: "hidden", nfts: hiddenNftsList });
    }

    return nftsAssets;
  },
};
