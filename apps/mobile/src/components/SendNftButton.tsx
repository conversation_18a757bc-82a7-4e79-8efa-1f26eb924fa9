import { Button } from "~/components/Button";
import { Address } from "@squads/models/solana";
import * as Haptics from "expo-haptics";
import { router } from "expo-router";
import { useLock } from "~/hooks/useLock";

import { useIsWalletInactive } from "~/hooks/useAssertWalletState";
import { SendNftRouteParams } from "~/app/unlocked/nft/send/_layout";

export function SendNftButton({ id }: { id: Address }) {
  const withLock = useLock();

  const isWalletInactive = useIsWalletInactive();

  return (
    <Button
      onPress={withLock(async () => {
        if (await isWalletInactive()) {
          return;
        }

        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);

        router.push({
          pathname: `/unlocked/nft/send/recipient`,
          params: { id } satisfies SendNftRouteParams,
        });
      })}
    >
      Send
    </Button>
  );
}
