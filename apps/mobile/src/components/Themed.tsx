/**
 * Learn more about Light and Dark modes:
 * https://docs.expo.io/guides/color-schemes/
 */

import {
  SafeAreaView as DefaultSafeAreaView,
  Text as DefaultText,
  View as DefaultView,
} from "react-native";

import Colors from "~/constants/Colors";
import { createContext, ForwardedRef, forwardRef, useContext } from "react";
import { ThemeType, useTheme } from "~/components/ThemeContext";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { TextStyle } from "react-native/Libraries/StyleSheet/StyleSheetTypes";
import { debugColor, DebugProps } from "~/types/debug";

type ThemeProps = {
  lightColor?: string;
  darkColor?: string;
};

export type TextProps = ThemeProps & DefaultText["props"];
export type ViewProps = ThemeProps & DefaultView["props"];
export type SafeViewProps = ThemeProps & DefaultSafeAreaView["props"];

export function useThemedValue<
  const T extends { light: unknown; dark: unknown },
>(props: T): T["light"] | T["dark"] {
  const theme = useFuseTheme();
  return props[theme];
}

export const ThemeOverrideContext = createContext<"light" | "dark" | null>(
  null
);

export function useFuseTheme() {
  const { theme } = useTheme();
  const overrideTheme = useContext(ThemeOverrideContext);
  return overrideTheme ?? theme;
}

export function useColor(
  colorName: keyof typeof Colors.light & keyof typeof Colors.dark,
  theme?: ThemeType
) {
  const contextTheme = useFuseTheme();
  return Colors[theme ?? contextTheme][colorName];
}

/**
 * @deprecated use useColor instead
 */
export function useThemeColor(
  props: { light?: string; dark?: string },
  colorName: keyof typeof Colors.light & keyof typeof Colors.dark
) {
  const overrideTheme = useContext(ThemeOverrideContext);
  const { theme } = useTheme();
  const colorFromProps = props[theme];

  if (colorFromProps) {
    return colorFromProps;
  } else {
    return Colors[overrideTheme ?? theme][colorName];
  }
}

export const FONT_FAMILY_MEDIUM = "SFProDisplayMedium";

export const FontFamilyByVariant = {
  regular: "SFProTextRegular",
  medium: FONT_FAMILY_MEDIUM,
  semibold: "SFProDisplaySemibold",
  bold: "SFProDisplayBold",
  heavy: "SFProDisplayHeavy",
  "display-medium": FONT_FAMILY_MEDIUM,
  "display-bold": "SFProDisplayBold",
} as const;

export type TextColor =
  | "text"
  | "textOpposite"
  | "textButtonPrimary"
  | "textButtonSecondary"
  | "textButtonPrimaryOpposite"
  | "textButtonSecondaryOpposite"
  | "textButtonDanger"
  | "textButtonSuccess"
  | "textSecondary"
  | "textSecondaryOpposite"
  | "textSecondaryV2"
  | "textTertiary"
  | "textDisabled"
  | "textToastInfo"
  | "textToastSuccess"
  | "textToastDanger"
  | "textToastWarning"
  | "blue"
  | "red"
  | "systemRed"
  | "systemGreen"
  | "green"
  | "yellow"
  | "newYellow"
  | "orange"
  | "newRed"
  | "textError"
  | "textWarning";

export const Text = forwardRef(function Text(
  props: TextProps & {
    variant?: keyof typeof FontFamilyByVariant;
    colorToken?: TextColor;
    size?: number;
    align?: TextStyle["textAlign"];
  },
  ref: ForwardedRef<DefaultText>
) {
  const {
    style,
    lightColor,
    darkColor,
    variant = "regular",
    colorToken = "text",
    size = 14,
    ...otherProps
  } = props;
  const color = useThemeColor(
    { light: lightColor, dark: darkColor },
    colorToken
  );

  const fontFamily = FontFamilyByVariant[variant];

  return (
    <DefaultText
      allowFontScaling={false}
      ref={ref}
      style={[
        { color, fontFamily, fontSize: props.size, textAlign: props.align },
        style,
      ]}
      {...otherProps}
    />
  );
});

export const View = forwardRef(function View(
  props: ViewProps & {
    gap?: number;
    background?: keyof typeof Colors.light & keyof typeof Colors.dark;
  } & DebugProps,
  ref: ForwardedRef<DefaultView>
) {
  const { style, lightColor, darkColor, ...otherProps } = props;

  const backgroundColor = useThemeColor(
    { light: lightColor, dark: darkColor },
    props.background ?? "transparent"
  );

  return (
    <DefaultView
      ref={ref}
      style={[
        {
          backgroundColor,
          gap: props.gap,
        },
        style,
        props.debug && { backgroundColor: debugColor },
      ]}
      {...otherProps}
    />
  );
});

export function SafeAreaView(
  props: SafeViewProps & { ignoreBottom?: true; ignoreTop?: true }
) {
  const {
    ignoreBottom,
    ignoreTop,
    style,
    lightColor,
    darkColor,
    ...otherProps
  } = props;
  const backgroundColor = useThemeColor(
    { light: lightColor, dark: darkColor },
    "background"
  );

  const insets = useSafeAreaInsets();

  return (
    <View
      style={[
        { backgroundColor },
        style,
        [
          {
            paddingTop: ignoreTop ? undefined : insets.top,
            paddingBottom: ignoreBottom ? undefined : insets.bottom,
            paddingLeft: insets.left,
            paddingRight: insets.right,
          },
        ],
      ]}
      {...otherProps}
    />
  );
}
