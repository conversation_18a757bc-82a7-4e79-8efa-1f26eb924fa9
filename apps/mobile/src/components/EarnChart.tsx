import { FONT_FAMILY_MEDIUM, Text, useColor, View } from "~/components/Themed";
import { ComponentProps, memo, useEffect, useMemo, useState } from "react";
import {
  calculateEarnedAtPointInTime,
  EarnPeriod,
  earnPeriodToEarnedAtPointInTime,
  earnPeriodToPeriod,
  HistoricalBalance,
} from "~/services/balances";
import { DateTime } from "luxon";
import Animated, {
  DerivedValue,
  Easing,
  FadeIn,
  interpolate,
  LayoutAnimationConfig,
  runOnJS,
  SharedValue,
  useAnimatedReaction,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withSpring,
  withTiming,
} from "react-native-reanimated";
import {
  Bar,
  CartesianChart,
  useBarPath,
  useChartPressState,
} from "victory-native";
import {
  Group,
  LinearGradient,
  matchFont,
  Path,
  Skia,
  Text as SkiaText,
  vec,
  Vector,
} from "@shopify/react-native-skia";
import { useActiveWallet } from "~/state/wallet";
import { useLazyHistoricalBalances } from "~/state/balances";
import {
  abbreviateAmount,
  formatUsdValue,
  formatUsdValueWorklet,
} from "~/vendor/squads/utils/numberFormats";
import { PeriodButton } from "./BalanceChart";
import { assertNever } from "~/utils/assertNever";
import { ContentSkeleton, ViewSkeleton } from "./Skeleton";
import { BarChartIcon } from "~/components/icons/earn/BarChartIcon";
import { Row } from "./Grid";
import { hapticLightTouch, hapticSelect } from "~/utils/haptics";
import Colors, { hexToRgba } from "~/constants/Colors";
import { CrossfadeView } from "./animations/CrossfadeView";
import { usePrevious } from "~/hooks/usePrevious";
import { DURATION_FAST, DURATION_MEDIUM, SPRING } from "~/constants/animations";
import { Address } from "@squads/models/solana";
import invariant from "invariant";
import { ChartPressState } from "victory-native";
import { AnimatedTextInput } from "~/components/AnimatedTextInput";
import { EarnProvider } from "~/services/earn";
import { BalanceNumbers } from "./earn/BalanceNumbers";
import { EarnBalance } from "~/state/earn";

const AnimatedText = Animated.createAnimatedComponent(Text);

type EarnChartProps = {
  totalEarnBalance: number;
  weightedApy: number;
  provider: EarnProvider;
};

export function EarnChart({
  totalEarnBalance,
  weightedApy,
  provider,
}: EarnChartProps) {
  const [period, setPeriod] = useState<EarnPeriod>("7d");

  const { state } = useChartPressState({
    x: DateTime.now().toMillis(),
    y: { earned: 0, actualEarned: 0 },
  });

  return (
    <View>
      <BalanceNumbers
        totalEarnBalance={totalEarnBalance}
        weightedApy={weightedApy}
      />
      <Earnings
        provider={provider}
        period={period}
        chartPressState={state}
        totalEarnBalance={totalEarnBalance}
      />
      <View style={{ height: 258 }}>
        <Chart period={period} chartPressState={state} provider={provider} />
      </View>
      <View style={{ alignItems: "center", marginTop: 24 }}>
        <EarnChartPeriods selected={period} onSelect={setPeriod} />
      </View>
    </View>
  );
}

const Earnings = memo(function Earnings({
  provider,
  period,
  chartPressState,
  totalEarnBalance,
}: {
  provider: EarnProvider;
  period: EarnPeriod;
  chartPressState: ChartPressState<{
    x: number;
    y: { earned: number; actualEarned: number };
  }>;
  totalEarnBalance: number;
}) {
  const { wallet } = useActiveWallet();
  const historicalBalances = useLazyHistoricalBalances({
    vault: wallet.defaultVault,
    period: earnPeriodToPeriod(period),
  });
  const prevPeriod = usePrevious(period);

  const calculateEarnings = () => {
    const currentEarnBalance = totalEarnBalance;

    const lifetimeEarned = calculateEarnedAtPointInTime(
      provider,
      "now",
      currentEarnBalance,
      historicalBalances.data ?? []
    );
    if (lifetimeEarned === undefined) {
      return { lifetimeEarned: undefined, lastXEarned: undefined };
    }

    if (period === "all") {
      return { lifetimeEarned, lastXEarned: undefined };
    }

    const historicalXAgoEarned = calculateEarnedAtPointInTime(
      provider,
      earnPeriodToEarnedAtPointInTime(period),
      currentEarnBalance,
      historicalBalances.data ?? []
    );
    if (historicalXAgoEarned === undefined) {
      return { lifetimeEarned, lastXEarned: undefined };
    }

    const lastXEarned = lifetimeEarned - historicalXAgoEarned;
    return { lifetimeEarned, lastXEarned };
  };

  const { lifetimeEarned, lastXEarned } = calculateEarnings();

  const periodV = useSharedValue(period);
  useEffect(() => {
    periodV.value = period;
  }, [period]);

  const leftLabel = useDerivedValue(() => {
    return formatTimestampWorklet(chartPressState.x.value.value, periodV.value);
  });

  const formattedSelectedAmount = useDerivedValue(() => {
    return chartPressState.isActive.value
      ? formatUsdValueWorklet(chartPressState.y.actualEarned.value.value)
      : formatUsdValueWorklet(lifetimeEarned ?? 0);
  });

  const textColor = useColor("text");

  const leftLabelStaticTextStyle = useAnimatedStyle(() => {
    const isPressActive = chartPressState.isActive.value;

    return {
      opacity: withSpring(isPressActive ? 0 : 0.3, SPRING),
      transform: [
        {
          translateY: withSpring(isPressActive ? -12 : 0, SPRING),
        },
      ],
    };
  });

  const leftLabelTimestampTextStyle = useAnimatedStyle(() => {
    const isPressActive = chartPressState.isActive.value;

    return {
      opacity: withSpring(isPressActive ? 0.3 : 0, SPRING),
      transform: [
        {
          translateY: withSpring(isPressActive ? 0 : 12, SPRING),
        },
      ],
    };
  });

  return (
    <View
      style={{
        flexDirection: "row",
        justifyContent: "space-between",
        paddingVertical: 24,
        gap: 4,
      }}
    >
      <View style={{ minWidth: 110 }}>
        <View>
          <AnimatedText
            variant="medium"
            colorToken="text"
            style={[leftLabelStaticTextStyle, { position: "absolute" }]}
          >
            Lifetime Earnings
          </AnimatedText>
          <AnimatedTextInput
            style={[
              leftLabelTimestampTextStyle,
              {
                color: textColor,
                fontFamily: FONT_FAMILY_MEDIUM,
                fontSize: 14,
              },
            ]}
            text={leftLabel}
          />
        </View>
        <View style={{ position: "relative" }}>
          {lifetimeEarned === undefined ? (
            <CrossfadeView key={`lifetimeEarned-skeleton-${period}`}>
              <ContentSkeleton variant="small">
                <Text
                  variant="semibold"
                  size={16}
                  colorToken="text"
                  style={{ lineHeight: 19.2 }}
                >
                  $123.45
                </Text>
              </ContentSkeleton>
            </CrossfadeView>
          ) : (
            <CrossfadeView key="lifetimeEarned-value">
              <AnimatedTextInput
                style={{
                  color: Colors.light.text,
                  fontFamily: "SFProDisplaySemibold",
                  fontSize: 16,
                  lineHeight: 19.2,
                }}
                text={formattedSelectedAmount}
              />
            </CrossfadeView>
          )}
        </View>
      </View>
      {period !== "all" && (
        <View style={{ alignItems: "flex-end", minWidth: 80 }}>
          <Text
            colorToken="text"
            style={{ opacity: 0.3 }}
            size={14}
            variant="medium"
          >
            Last {period.toUpperCase()}
          </Text>
          {lastXEarned === undefined ? (
            <LayoutAnimationConfig
              skipEntering
              skipExiting={prevPeriod === "all"}
            >
              <CrossfadeView key={`lastEarned-skeleton-${period}`}>
                <ContentSkeleton variant="small">
                  <Text
                    variant="semibold"
                    size={16}
                    colorToken="text"
                    style={{ lineHeight: 19.2 }}
                  >
                    $12.45
                  </Text>
                </ContentSkeleton>
              </CrossfadeView>
            </LayoutAnimationConfig>
          ) : (
            <Text
              variant="semibold"
              size={16}
              colorToken="text"
              style={{ lineHeight: 19.2 }}
            >
              {formatUsdValue(lastXEarned < 0.01 ? 0 : lastXEarned)}
            </Text>
          )}
        </View>
      )}
    </View>
  );
});

type EarnChartPoint = {
  timestamp: number;
  earned: number;
  actualEarned: number;
  usdcBalance: number;
};

type SimplifiedQueryResult<DATA> =
  | { status: "pending"; data: undefined }
  | { status: "error"; data: undefined; error: Error }
  | { status: "success"; data: DATA };

function useEarnHistoryForChart({
  walletKey,
  vault,
  period,
  provider,
}: {
  walletKey: Address;
  vault: Address;
  period: EarnPeriod;
  provider: EarnProvider;
}): SimplifiedQueryResult<{
  points: EarnChartPoint[];
  actualMaxEarned: number;
  maxEarned: number;
}> {
  const result = useLazyHistoricalBalances({
    vault,
    period: earnPeriodToPeriod(period),
  });

  const massagedData = useMemo(() => {
    if (!result.data) return result.data;

    // Filter data based on period
    // We only take the latest array entry for each day/month/year.
    // In case of "today", we would always use the realtime entry since its the latest.
    const filteredDataObj = result.data.reduce(
      (acc, point) => {
        if (period === "7d" || period === "30d") {
          const dateKey = DateTime.fromSeconds(point.timestamp).toFormat(
            "yyyy-MM-dd"
          );
          if (!acc[dateKey] || point.timestamp > acc[dateKey].timestamp) {
            acc[dateKey] = point;
          }
        } else if (period === "1y") {
          const monthKey = DateTime.fromSeconds(point.timestamp).toFormat(
            "yyyy-MM"
          );
          if (!acc[monthKey] || point.timestamp > acc[monthKey].timestamp) {
            acc[monthKey] = point;
          }
        } else if (period === "all") {
          const yearKey = DateTime.fromSeconds(point.timestamp).toFormat(
            "yyyy"
          );
          if (!acc[yearKey] || point.timestamp > acc[yearKey].timestamp) {
            acc[yearKey] = point;
          }
        }
        return acc;
      },
      {} as Record<string, HistoricalBalance>
    );

    // Sort by timestamp
    const filteredData = Object.values(filteredDataObj).sort(
      (a, b) => a.timestamp - b.timestamp
    );

    // Calculate drift earn
    const reducedData = filteredData.map((point) => {
      const providerEarn = {
        drift: point.driftEarn,
        lulo: point.luloProtectedEarn,
        kamino: point.kaminoEarn,
      }[provider];

      return {
        ...point,
        earned: Object.values(providerEarn).reduce(
          (acc, curr) => acc + curr!.balance - curr!.netDeposited,
          0
        ),
      };
    });

    // Calculate differences
    const diffCalculatedData = reducedData.map((point, index) => {
      if (index === 0) return point;

      const prevPoint = reducedData[index - 1];

      // When the resolution is one day, make sure we adjust the last point for the partial day
      if (
        (period === "30d" || period === "7d") &&
        index === reducedData.length - 1
      ) {
        const minutesInADay = 24 * 60;
        const minutesSinceMidnight =
          DateTime.fromSeconds(point.timestamp).hour * 60 +
          DateTime.fromSeconds(point.timestamp).minute;

        const dayPercentage = minutesSinceMidnight / minutesInADay;

        return {
          ...point,
          earned: (point.earned - prevPoint.earned) * dayPercentage,
        };
      }

      return {
        ...point,
        earned: point.earned - prevPoint.earned,
      };
    });

    // Crop data based on period
    const croppedData =
      period === "7d"
        ? diffCalculatedData.slice(-7)
        : period === "30d"
          ? diffCalculatedData.slice(-30)
          : period === "1y"
            ? diffCalculatedData.slice(-12)
            : diffCalculatedData;

    // Remove leading zeroes
    const dataWithoutLeadingZeroes = croppedData.slice(
      croppedData.findIndex((point) => point.earned !== 0)
    );

    const actualMaxEarned = Math.max(
      ...dataWithoutLeadingZeroes.map((e) => e.earned)
    );
    const maxEarned = Math.max(actualMaxEarned, 1);

    // Add artificial bump and adjust timestamps
    const artificiallyBumpedValuesAndAdjustedTimestamps =
      dataWithoutLeadingZeroes.map((point) => {
        const datetime = DateTime.fromSeconds(point.timestamp);
        const newDatetime =
          period === "7d" || period === "30d"
            ? datetime.startOf("day")
            : period === "1y"
              ? datetime.startOf("month")
              : datetime.startOf("year");

        return {
          ...point,
          timestamp: newDatetime.toSeconds(),
          // Sometimes we receive tiny negative values, so we clamp them to 0.
          earned: Math.max(point.earned, 0) + maxEarned * 0.02,
          actualEarned: Math.max(point.earned, 0),
        };
      });

    // for 1y (when showing months), we need to change the timestamp behavior, otherwise we get unevenly spaced bars because of the number of days in each month
    const points = artificiallyBumpedValuesAndAdjustedTimestamps.map(
      (point) => {
        if (period !== "1y") return point;

        const diff = Math.round(
          DateTime.fromSeconds(point.timestamp)
            .startOf("month")
            .diff(DateTime.now().startOf("month"))
            .as("months")
        );

        return {
          ...point,
          timestamp: diff,
        };
      }
    );

    return { points, actualMaxEarned, maxEarned };
  }, [result.data, period]);

  if (result.status === "pending")
    return { status: "pending", data: undefined };

  if (result.status === "error") {
    return { status: "error", data: undefined, error: result.error };
  }

  invariant(massagedData, "massagedData is expected");

  return {
    ...result,
    data: massagedData,
  };
}

const Chart = memo(function Chart({
  period,
  chartPressState,
  provider,
}: {
  period: EarnPeriod;
  chartPressState: ChartPressState<{
    x: number;
    y: { earned: number; actualEarned: number };
  }>;
  provider: EarnProvider;
}) {
  const { wallet } = useActiveWallet();

  useAnimatedReaction(
    () => chartPressState.matchedIndex.value,
    () => runOnJS(hapticLightTouch)()
  );

  const earnHistoryQueryResult = useEarnHistoryForChart({
    walletKey: wallet.walletKey,
    vault: wallet.defaultVault,
    period,
    provider,
  });

  const font = matchFont({
    fontFamily: "SF Pro Display",
    fontSize: 12,
    fontWeight: "600",
  });

  const [outsideChartBounds, setChartBounds] = useState({
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  });

  if (earnHistoryQueryResult.status === "pending") {
    return <EmptyChartView period={period} loading />;
  }

  if (earnHistoryQueryResult.status === "error") {
    return (
      <EmptyChartView period={period} message="Couldn't load chart data" />
    );
  }

  const { points, maxEarned, actualMaxEarned } = earnHistoryQueryResult.data;

  if (
    points.length === 0 ||
    actualMaxEarned < EarnBalance.MIN_EARN_BALANCE_TO_SHOW
  ) {
    return (
      <EmptyChartView
        period={period}
        message={
          "Earnings will display once you’ve\naccrued a minimum balance."
        }
      />
    );
  }

  const roundedCornersPx =
    points.length <= 8
      ? 12
      : points.length <= 12
        ? 8
        : points.length <= 16
          ? 6
          : points.length <= 20
            ? 5
            : 3;
  const roundedCorners = {
    topLeft: roundedCornersPx,
    topRight: roundedCornersPx,
    bottomLeft: roundedCornersPx,
    bottomRight: roundedCornersPx,
  };

  const gapBetweenBars = 4;
  const totalSpacing = points.length * gapBetweenBars;
  const barWidth =
    (outsideChartBounds.right - outsideChartBounds.left - totalSpacing) /
    points.length;

  const timestamps = points.map((e) => e.timestamp);
  const minTimestamp = Math.min(...timestamps);
  const maxTimestamp = Math.max(...timestamps);
  const domainX = domainFor(period, minTimestamp!, maxTimestamp!);

  return (
    <CartesianChart
      data={points}
      xKey="timestamp"
      yKeys={["earned", "actualEarned"]}
      xAxis={
        points.length < 7
          ? {
              font,
              labelColor: "#1f1e1e40",
              tickValues: points.map((e) => e.timestamp),
              formatXLabel: (timestamp) => {
                if (period === "7d" || period === "30d") {
                  return DateTime.fromSeconds(timestamp).toFormat("MMM d");
                } else if (period === "1y") {
                  return DateTime.now()
                    .startOf("month")
                    .plus({ months: timestamp })
                    .toFormat("MMM");
                } else if (period === "all") {
                  return DateTime.fromSeconds(timestamp).toFormat("yyyy");
                } else {
                  assertNever(period);
                }
              },
              lineColor: "transparent",
              labelOffset: 6,
            }
          : undefined
      }
      yAxis={[
        {
          font,
          axisSide: "right",
          labelColor: hexToRgba(Colors.light.text, 0.25),
          formatYLabel: (label) =>
            "$" + abbreviateAmount(label, { type: "usd", decimals: 0 }),
          lineColor: "transparent",
        },
      ]}
      domain={{ x: domainX, y: [0, maxEarned * 1.2] }}
      // domainPadding={{ bottom: 1, top: 1 }} // for 0 and max tick to appear. there is an open issue here https://github.com/FormidableLabs/victory-native-xl/issues/492
      chartPressState={chartPressState}
      onChartBoundsChange={setChartBounds}
      renderOutside={({ chartBounds }) => {
        const shouldRenderEdgeLabels = points.length >= 7;

        const leftEdgeTimestamp = points[0].timestamp;
        const rightEdgeTimestamp = points.at(-1)!.timestamp;

        const [leftEdgeLabel, rightEdgeLabel] = (() => {
          if (period === "7d" || period === "30d") {
            return [
              DateTime.fromSeconds(leftEdgeTimestamp).toFormat("MMM d"),
              DateTime.fromSeconds(rightEdgeTimestamp).toFormat("MMM d"),
            ];
          } else if (period === "1y") {
            return [
              DateTime.now()
                .startOf("month")
                .plus({ months: leftEdgeTimestamp })
                .toFormat("MMM"),
              DateTime.now()
                .startOf("month")
                .plus({ months: rightEdgeTimestamp })
                .toFormat("MMM"),
            ];
          } else if (period === "all") {
            return [
              DateTime.fromSeconds(leftEdgeTimestamp).toFormat("yyyy"),
              DateTime.fromSeconds(rightEdgeTimestamp).toFormat("yyyy"),
            ];
          } else {
            assertNever(period);
          }
        })();
        const rightEdgeLabelSize = font.measureText(rightEdgeLabel);

        return (
          <Group>
            <SkiaText
              font={font}
              color={hexToRgba(Colors.light.text, 0.25)}
              text="$0"
              x={chartBounds.right + 4}
              y={chartBounds.bottom}
            />
            {shouldRenderEdgeLabels && (
              <SkiaText
                font={font}
                color={hexToRgba(Colors.light.text, 0.25)}
                text={leftEdgeLabel}
                x={chartBounds.left + 4}
                y={chartBounds.bottom + 18}
              />
            )}
            {shouldRenderEdgeLabels && (
              <SkiaText
                font={font}
                color={hexToRgba(Colors.light.text, 0.25)}
                text={rightEdgeLabel}
                x={chartBounds.right - rightEdgeLabelSize.width}
                y={chartBounds.bottom + 18}
              />
            )}
          </Group>
        );
      }}
    >
      {({ points, chartBounds }) => {
        return points.earned.map((point, i) => {
          const timestamp = point.xValue;

          return (
            <RegularBar
              key={String(timestamp) + period}
              points={[point]}
              barCount={points.earned.length}
              chartBounds={chartBounds}
              barWidth={barWidth}
              roundedCorners={roundedCorners}
              innerPadding={period === "all" ? 0.1 : undefined}
              index={i}
              isActive={chartPressState.isActive}
              activeIndex={chartPressState.matchedIndex}
            />
          );
        });
      }}
    </CartesianChart>
  );
});

function AnimatedLinearGradient({
  start,
  end,
  colors,
}: {
  start: Vector;
  end: Vector;
  colors: DerivedValue<string[]>;
}) {
  return (
    <LinearGradient
      start={start}
      end={end}
      positions={[0, 0.4, 1]}
      colors={colors}
    />
  );
}

const transformMatrix = Skia.Matrix().translate(0, -1);

function RegularBar({
  barWidth,
  barCount,
  chartBounds,
  roundedCorners,
  isActive,
  index,
  activeIndex,
  points,
  innerPadding,
  color,
}: ComponentProps<typeof Bar> & {
  isActive: SharedValue<boolean>;
  activeIndex: SharedValue<number>;
  index: number;
}) {
  // Can't use useColor because this component is rendered by Skia, which isn't a part of the regular react tree
  // const systemGreen = useColor("systemGreen");
  const systemGreen = Colors.light.systemGreen;
  const nonSelectedColor = hexToRgba(systemGreen, 0.25);
  const gradientTopColor = hexToRgba(systemGreen, 1);
  const gradientBottomColor = hexToRgba(systemGreen, 0.65);
  const currentBarColor = "rgba(52, 199, 89, 0.05)";
  const currentBarSmallColor = hexToRgba(systemGreen, 0.35);

  const isCurrentBar = barCount != null && index === barCount - 1;

  const isSelected = useDerivedValue(() => {
    return isActive.value && index === activeIndex.value;
  });

  const { path: originalBarPath } = useBarPath(
    points,
    chartBounds,
    innerPadding,
    roundedCorners,
    barWidth,
    points.length
  );
  const boundingBox = originalBarPath.getBounds();
  const isSmallBar = boundingBox.height < 8;

  const newPoints =
    // If bar is less than 8px tall, render as a "zero" bar
    isSmallBar ? [{ ...points[0], y: chartBounds.bottom - 4 }] : points;

  const { path: barPath } = useBarPath(
    newPoints,
    chartBounds,
    innerPadding,
    roundedCorners,
    barWidth,
    newPoints.length
  );
  const { path: barPath2 } = useBarPath(
    newPoints,
    chartBounds,
    innerPadding,
    roundedCorners,
    barWidth,
    barCount
  );
  barPath2.dash(5, 3, 0);

  const duration = 50;

  const topColor = useDerivedValue(() => {
    if (isActive.value) {
      if (isSelected.value) {
        return withTiming(gradientTopColor, { duration });
      } else {
        return withTiming(nonSelectedColor, { duration });
      }
    }

    if (isCurrentBar && !isSmallBar) {
      return withTiming(currentBarColor, { duration });
    } else {
      return withTiming(gradientTopColor, { duration });
    }
  });

  const bottomColor = useDerivedValue(() => {
    if (isActive.value) {
      if (isSelected.value) {
        return withTiming(gradientBottomColor, { duration });
      } else {
        return withTiming(nonSelectedColor, { duration });
      }
    }

    if (isCurrentBar) {
      return withTiming(isSmallBar ? currentBarSmallColor : currentBarColor, {
        duration,
      });
    } else {
      return withTiming(gradientBottomColor, { duration });
    }
  });

  const colors = useDerivedValue(() => {
    return [topColor.value, topColor.value, bottomColor.value];
  });

  const currentBarBorderColor = useDerivedValue(() => {
    const color =
      isCurrentBar && !isSmallBar && !isActive.value
        ? "#34C759"
        : "transparent";

    return withTiming(color, {
      duration: duration,
    });
  });

  return (
    <Group matrix={transformMatrix}>
      <Path path={barPath} style="fill" color={color}>
        {color === undefined && (
          <AnimatedLinearGradient
            start={vec(0, 0)}
            end={vec(0, chartBounds.bottom)}
            colors={colors}
          />
        )}
      </Path>
      <Path
        path={barPath2}
        style="stroke"
        strokeWidth={1.5}
        color={currentBarBorderColor}
      />
    </Group>
  );
}

function EarnChartPeriods({
  selected: period,
  onSelect: setPeriod,
}: {
  selected: EarnPeriod;
  onSelect: (period: EarnPeriod) => void;
}) {
  return (
    <Row gap={12} style={{ width: "100%", justifyContent: "center" }}>
      <PeriodButton
        selected={period === "7d"}
        onSelect={() => {
          hapticSelect();
          setPeriod("7d");
        }}
      >
        7D
      </PeriodButton>
      <PeriodButton
        selected={period === "30d"}
        onSelect={() => {
          hapticSelect();
          setPeriod("30d");
        }}
      >
        30D
      </PeriodButton>
      <PeriodButton
        selected={period === "1y"}
        onSelect={() => {
          hapticSelect();
          setPeriod("1y");
        }}
      >
        1Y
      </PeriodButton>
      <PeriodButton
        selected={period === "all"}
        onSelect={() => {
          hapticSelect();
          setPeriod("all");
        }}
      >
        ALL
      </PeriodButton>
    </Row>
  );
}

function EmptyChartView({
  period,
  loading = false,
  message,
}: {
  period: EarnPeriod;
  loading?: boolean;
  message?: string;
}) {
  const scale = useSharedValue(1);

  useEffect(() => {
    scale.value = withTiming(loading ? 1 : 0.75, {
      duration: DURATION_FAST,
      easing: Easing.inOut(Easing.quad),
    });
  }, [loading]);

  const shrinkAndMoveUpAnim = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { translateY: interpolate(scale.value, [0, 1], [0, -20]) },
    ],
  }));

  const roundedCorners = {
    topLeft: 12,
    topRight: 12,
    bottomLeft: 12,
    bottomRight: 12,
  };

  const maxEarned = 10;

  const bars = 7;

  const isActive = useSharedValue(false);
  const activeIndex = useSharedValue(0);

  return (
    <View style={{ flex: 1 }}>
      <CartesianChart
        data={Array.from({ length: bars }, (_, n) => ({
          timestamp: n,
          earn: maxEarned * 0.02,
        }))}
        xKey="timestamp"
        yKeys={["earn"]}
        yAxis={[{ lineColor: "transparent" }]}
        domain={{ x: [-0.5, bars - 0.5], y: [0, maxEarned * 1.2] }}
        // domainPadding={{ bottom: 1, top: 1 }} // for 0 and max tick to appear. there is an open issue here https://github.com/FormidableLabs/victory-native-xl/issues/492
      >
        {({ points, chartBounds }) => {
          const gapBetweenBars = 4;
          const totalSpacing = points.earn.length * gapBetweenBars;
          const barWidth =
            (chartBounds.right - chartBounds.left - totalSpacing) /
            points.earn.length;

          return points.earn.map((point, i) => {
            const timestamp = point.xValue;

            return (
              <RegularBar
                key={String(timestamp) + period}
                points={[point]}
                barCount={points.earn.length}
                chartBounds={chartBounds}
                barWidth={barWidth}
                roundedCorners={roundedCorners}
                innerPadding={period === "all" ? 0.1 : undefined}
                index={i}
                isActive={isActive}
                activeIndex={activeIndex}
                color="rgba(19, 19, 19, 0.05)"
              />
            );
          });
        }}
      </CartesianChart>

      <View
        style={{
          position: "absolute",
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
          alignItems: "center",
          justifyContent: "center",
          gap: 10,
        }}
      >
        <Animated.View style={shrinkAndMoveUpAnim}>
          <ViewSkeleton disabled={!loading}>
            <BarChartIcon
              size={34}
              color={loading ? "black" : Colors.light.textSecondary}
            />
          </ViewSkeleton>
        </Animated.View>
      </View>
      {message && (
        <View
          style={{
            position: "absolute",
            left: 0,
            right: 0,
            top: "55%",
            alignItems: "center",
          }}
        >
          <AnimatedText
            variant="medium"
            colorToken="textTertiary"
            style={{ textAlign: "center" }}
            entering={FadeIn.duration(DURATION_MEDIUM)}
          >
            {message}
          </AnimatedText>
        </View>
      )}
    </View>
  );
}

const formatTimestampWorklet = (seconds: number, period: EarnPeriod) => {
  "worklet";
  const date = new Date(seconds * 1000);
  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  if (period === "7d" || period === "30d") {
    const month = monthNames[date.getMonth()];
    const day = date.getDate();
    const year = date.getFullYear();
    return `${month} ${day}, ${year}`;
  } else if (period === "1y") {
    const diff = seconds;
    const now = new Date();
    const actualDate = new Date();
    actualDate.setDate(1);
    actualDate.setMonth(now.getMonth() + diff);
    const month = monthNames[actualDate.getMonth()];
    const year = actualDate.getFullYear();
    return `${month} ${year}`;
  } else if (period === "all") {
    return `${date.getFullYear()}`;
  } else {
    throw new Error("Invalid period value");
  }
};

function domainFor(
  period: EarnPeriod,
  minTimestamp: number,
  maxTimestamp: number
): [number, number] {
  return period === "7d"
    ? [
        DateTime.fromSeconds(minTimestamp)
          .startOf("day")
          .minus({ days: 0.5 })
          .toSeconds(),
        DateTime.fromSeconds(maxTimestamp)
          .startOf("day")
          .plus({ days: 0.5 })
          .toSeconds(),
      ]
    : period === "30d"
      ? [
          DateTime.fromSeconds(minTimestamp)
            .startOf("day")
            .minus({ days: 0.5 })
            .toSeconds(),
          DateTime.fromSeconds(maxTimestamp)
            .startOf("day")
            .plus({ days: 0.5 })
            .toSeconds(),
        ]
      : period === "1y"
        ? [minTimestamp - 0.5, maxTimestamp + 0.5]
        : [
            DateTime.fromSeconds(minTimestamp)
              .startOf("year")
              .minus({ years: 0.5 })
              .toSeconds(),
            DateTime.fromSeconds(maxTimestamp)
              .startOf("year")
              .plus({ years: 0.5 })
              .toSeconds(),
          ];
}

const mockData = [
  {
    timestamp: DateTime.now().minus({ day: 7 }).toSeconds(),
    earned: 1,
  },
  {
    timestamp: DateTime.now().minus({ day: 6 }).toSeconds(),
    earned: 1.2,
  },
  {
    timestamp: DateTime.now().minus({ day: 5 }).toSeconds(),
    earned: 1.5,
  },
  {
    timestamp: DateTime.now().minus({ day: 4 }).toSeconds(),
    earned: 2,
  },
  {
    timestamp: DateTime.now().minus({ day: 3 }).toSeconds(),
    earned: 2.5,
  },
  {
    timestamp: DateTime.now().minus({ day: 2 }).toSeconds(),
    earned: 3,
  },
  {
    timestamp: DateTime.now().minus({ day: 1 }).toSeconds(),
    earned: 3.1,
  },
  {
    timestamp: DateTime.now().minus({ day: 0 }).toSeconds(),
    earned: 0.2,
  },
];
