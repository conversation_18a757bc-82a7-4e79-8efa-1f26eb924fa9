import { PropsWithChildren } from "react";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { SafeAreaView } from "~/components/Themed";
import { Flex, Row } from "~/components/Grid";
import Animated, { FadeOut, ZoomIn } from "react-native-reanimated";
import { DURATION_FAST } from "~/constants/animations";
import { TouchableScale } from "~/components/TouchableScale";
import { Icon } from "~/components/Icon";
import * as Haptics from "expo-haptics";

export function OnboardingContainer({
  children,
  onBack,
}: PropsWithChildren<{
  onBack?: () => void;
}>) {
  const insets = useSafeAreaInsets();

  return (
    <>
      <Header onBack={onBack} />
      <SafeAreaView ignoreTop style={{ flex: 1 }}>
        <Flex
          background={"background"}
          style={{
            paddingTop: insets.top + 75,
            paddingHorizontal: 20,
            paddingBottom: 20,
          }}
        >
          {children}
        </Flex>
      </SafeAreaView>
    </>
  );
}

function Header({ onBack }: { onBack?: () => void }) {
  const insets = useSafeAreaInsets();

  return (
    <Row
      style={{
        paddingTop: insets.top + 20,
        position: "absolute",
        top: 0,
        left: 0,
        paddingHorizontal: 20,
        justifyContent: "flex-start",
        zIndex: 1,
      }}
    >
      {onBack && (
        <Animated.View
          entering={ZoomIn.duration(DURATION_FAST)}
          exiting={FadeOut.duration(DURATION_FAST)}
          style={{ alignSelf: "flex-start" }}
        >
          <TouchableScale
            hitSlop={10}
            onPress={() => {
              Haptics.selectionAsync();
              onBack();
            }}
            style={{
              marginLeft: -7,
              alignItems: "flex-start",
              justifyContent: "center",
            }}
          >
            <Icon
              name={"arrow.backward"}
              size={16}
              rectSize={32}
              weight="medium"
            />
          </TouchableScale>
        </Animated.View>
      )}
    </Row>
  );
}
