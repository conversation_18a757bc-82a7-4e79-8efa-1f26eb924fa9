import {
  ClearInputButton,
  <PERSON>e<PERSON>utton,
  Props as TextInputProps,
  TextInput,
} from "~/components/TextInput";
import { useState } from "react";
import { Icon } from "~/components/Icon";
import { TouchableScale } from "~/components/TouchableScale";
import { QrScannerModal } from "~/components/QrScannerModal";
import { Alert, Linking } from "react-native";
import { Row } from "~/components/Grid";
import { AddressZ } from "@squads/models/solana";
import { Spinner } from "~/components/Spinner";
import { useCameraPermission } from "react-native-vision-camera";

export function AddressInput(
  props: TextInputProps & { isLoading?: boolean; showScan?: boolean }
) {
  const [qrModalVisible, setQrModalVisible] = useState(false);
  const isEmpty = (props.value?.length ?? 0) === 0;

  return (
    <>
      <TextInput
        autoCorrect={false}
        spellCheck={false}
        autoCapitalize="none"
        right={
          isEmpty ? (
            <Row>
              <PasteButton
                schema={AddressZ}
                onChangeText={(val) => props.onChangeText?.(val)}
              />
              {props.showScan !== false && (
                <ScanAddressButton onPress={() => setQrModalVisible(true)} />
              )}
            </Row>
          ) : props.isLoading ? (
            <Row style={{ paddingHorizontal: 14 }}>
              <Spinner size={18} colorToken={"textSecondary"} />
            </Row>
          ) : (
            <ClearInputButton onPress={() => props.onChangeText?.("")} />
          )
        }
        {...props}
      />
      <QrScannerModal
        visible={qrModalVisible}
        onScanned={(address) => {
          if (!address) return;
          props.onChangeText?.(address);
          setQrModalVisible(false);
        }}
        onClose={() => setQrModalVisible(false)}
      />
    </>
  );
}

export function ScanAddressButton({ onPress }: { onPress: () => void }) {
  const { hasPermission, requestPermission } = useCameraPermission();

  return (
    <TouchableScale
      style={{
        justifyContent: "center",
        alignItems: "center",
        paddingHorizontal: 14,
      }}
      hitSlop={{ top: 16, bottom: 16, right: 10 }}
      onPress={async () => {
        const granted = hasPermission || (await requestPermission());

        if (!granted) {
          Alert.alert(
            "Camera is not available",
            "Please make sure camera access is enabled",
            [
              { text: "Dismiss", style: "cancel" },
              {
                text: "Open Settings",
                onPress: () => Linking.openURL("app-settings:"),
                isPreferred: true,
              },
            ]
          );
          return;
        }

        onPress();
      }}
    >
      <Icon name={"viewfinder"} size={14} weight={"semibold"} />
    </TouchableScale>
  );
}
