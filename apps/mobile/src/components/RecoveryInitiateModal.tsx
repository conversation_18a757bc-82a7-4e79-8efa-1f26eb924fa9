import { RefObject, useState } from "react";
import { Text } from "~/components/Themed";
import { Button } from "~/components/Button";
import {
  BottomModal,
  BottomModalImperativeMethods,
  BottomModalMenuOption,
} from "~/components/BottomModal";
import { RecoveryKey } from "~/services/wallets";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import { FlatList } from "react-native";
import { ListSeparator } from "~/components/ListSeparator";
import { assertNever } from "~/utils/assertNever";
import invariant from "invariant";
import * as Haptics from "expo-haptics";

type ModalState =
  | { type: "select-key"; selectedRecoveryKey: RecoveryKey | null }
  | { type: "recovery-warning"; selectedRecoveryKey: RecoveryKey | null };

export function RecoveryInitiateModal({
  recoveredKeyType,
  onConfirm,
  recoveryKeys,
  modalRef,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
  recoveredKeyType: "cloudKey" | "deviceKey";
  recoveryKeys: Array<RecoveryKey> | null;
  onConfirm: (recoveryKey: RecoveryKey | null) => void;
}) {
  const hasRecoveryKeys = recoveryKeys !== null;
  const hasSingleKey = recoveryKeys !== null && recoveryKeys.length === 1;

  const defaultState: ModalState = !hasRecoveryKeys
    ? { type: "recovery-warning", selectedRecoveryKey: null }
    : hasSingleKey
      ? { type: "recovery-warning", selectedRecoveryKey: recoveryKeys[0] }
      : { type: "select-key", selectedRecoveryKey: null };

  const [modalState, setModalState] = useState<ModalState>(defaultState);

  return (
    <BottomModal
      modalRef={modalRef}
      modalId={modalState.type}
      themeOverride="dark"
      title={
        modalState.type === "recovery-warning"
          ? "Recovery warning"
          : "Select Recovery Key"
      }
      iconName={
        modalState.type === "recovery-warning"
          ? "exclamationmark.triangle"
          : undefined
      }
      iconVariant="warning"
      body={
        modalState.type === "recovery-warning" ? (
          <Text
            variant="medium"
            colorToken="textSecondary"
            style={{ fontSize: 18 }}
          >
            If you begin and finish your recovery you acknowledge and agree that
            your Fuse wallet will no longer be available{" "}
            {recoveredKeyType === "deviceKey"
              ? "on the previous device"
              : "via the previous iCloud account"}
          </Text>
        ) : modalState.type === "select-key" ? (
          <FlatList
            data={recoveryKeys}
            ItemSeparatorComponent={ListSeparator}
            renderItem={({ item: key }) => {
              function setSelectedRecoveryKey(key: RecoveryKey) {
                setModalState({
                  ...modalState,
                  selectedRecoveryKey: key,
                });
              }

              switch (key.recoveryKeyType) {
                case "selfCustody": {
                  return (
                    <BottomModalMenuOption
                      key={key.address}
                      selected={key === modalState.selectedRecoveryKey}
                      label={
                        <>
                          Wallet{" "}
                          <Text
                            colorToken="textSecondary"
                            style={{ fontSize: 15 }}
                          >
                            {"  •  "}
                            {abbreviateAddress(key.address)}
                          </Text>
                        </>
                      }
                      onSelect={() => {
                        setSelectedRecoveryKey(key);
                      }}
                    />
                  );
                }

                case "turnkeyEmail":
                case "email": {
                  return (
                    <BottomModalMenuOption
                      key={key.email}
                      selected={key === modalState.selectedRecoveryKey}
                      label={
                        <>
                          Email{" "}
                          <Text
                            colorToken="textSecondary"
                            style={{ fontSize: 15 }}
                          >
                            {"  •  "}
                            {key.email}
                          </Text>
                        </>
                      }
                      onSelect={() => {
                        setSelectedRecoveryKey(key);
                      }}
                    />
                  );
                }

                case "phone": {
                  return null;
                }

                case "keystone":
                  return (
                    <BottomModalMenuOption
                      key={key.address}
                      selected={key === modalState.selectedRecoveryKey}
                      label={
                        <>
                          Keystone{" "}
                          <Text
                            colorToken="textSecondary"
                            style={{ fontSize: 15 }}
                          >
                            {"  •  "}
                            {abbreviateAddress(key.address)}
                          </Text>
                        </>
                      }
                      onSelect={() => {
                        setSelectedRecoveryKey(key);
                      }}
                    />
                  );

                default:
                  return key satisfies never;
              }
            }}
          />
        ) : null
      }
      footer={
        // modalState.type === "select-key" ? (
        <Button
          variant="primary"
          disabled={hasRecoveryKeys && !modalState.selectedRecoveryKey}
          onPress={() => {
            if (modalState.type === "select-key") {
              Haptics.selectionAsync();
              invariant(
                modalState.selectedRecoveryKey,
                "selectedRecoveryKey is expected to be set"
              );
              setModalState({
                type: "recovery-warning",
                selectedRecoveryKey: modalState.selectedRecoveryKey,
              });
            } else if (modalState.type === "recovery-warning") {
              onConfirm(modalState.selectedRecoveryKey);
            } else {
              assertNever(modalState);
            }
          }}
        >
          {modalState.type === "recovery-warning"
            ? "Agree and Continue"
            : "Continue"}
        </Button>
      }
      onClose={() => setModalState(defaultState)}
      onDismiss={() => setModalState(defaultState)}
    />
  );
}
