import { memo } from "react";
import Svg, { Path } from "react-native-svg";

export const PlusIcon = memo(function PlusIcon({
  size = 20,
}: {
  size?: number;
}) {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 20">
      <Path
        d="M2.14141 11.8917C1.09452 11.8917 0.24585 11.043 0.24585 9.99609C0.24585 8.9492 1.09452 8.10053 2.14141 8.10053H6.4521C7.39444 8.10053 8.15835 7.33662 8.15835 6.39428V2.07422C8.15835 1.06457 8.97683 0.246094 9.98647 0.246094C10.9961 0.246094 11.8146 1.06457 11.8146 2.07422V6.39428C11.8146 7.33661 12.5785 8.10053 13.5208 8.10053H17.8503C18.8972 8.10053 19.7458 8.9492 19.7458 9.99609C19.7458 11.043 18.8972 11.8917 17.8503 11.8917L13.5209 11.8917C12.5785 11.8917 11.8146 12.6556 11.8146 13.5979L11.8146 17.918C11.8146 18.9276 10.9961 19.7461 9.98647 19.7461C8.97683 19.7461 8.15835 18.9276 8.15835 17.918V13.5979C8.15835 12.6556 7.39443 11.8917 6.4521 11.8917H2.14141Z"
        fill="white"
      />
    </Svg>
  );
});
