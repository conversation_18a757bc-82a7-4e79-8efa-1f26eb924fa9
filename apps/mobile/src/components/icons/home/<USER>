import Svg, { Path, Rect } from "react-native-svg";

export function BankCircleIcon({ size = 24 }: { size?: number }) {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 20" fill="none">
      <Rect width="20" height="20" rx="10" fill={"black"} />
      <Path
        d="M9.48825 9.24054C9.49483 9.10584 9.60596 9 9.74083 9H10.2592C10.394 9 10.5052 9.10583 10.5118 9.24053L10.6793 12.6685C10.6881 12.8489 10.5442 13 10.3636 13H9.63659C9.45596 13 9.31207 12.8489 9.32087 12.6685L9.48825 9.24054Z"
        fill="white"
      />
      <Path
        d="M12.4883 9.24054C12.4948 9.10584 12.606 9 12.7408 9H13.2592C13.394 9 13.5052 9.10583 13.5118 9.24053L13.6793 12.6685C13.6881 12.8489 13.5442 13 13.3636 13H12.6366C12.456 13 12.3121 12.8489 12.3209 12.6685L12.4883 9.24054Z"
        fill="white"
      />
      <Path
        d="M6.48825 9.24054C6.49483 9.10584 6.60596 9 6.74083 9H7.25918C7.39404 9 7.50517 9.10583 7.51176 9.24053L7.67929 12.6685C7.68811 12.8489 7.54421 13 7.36357 13H6.63659C6.45596 13 6.31207 12.8489 6.32087 12.6685L6.48825 9.24054Z"
        fill="white"
      />
      <Path
        d="M9.15015 5.07444C9.31422 4.94781 9.50396 4.85853 9.70611 4.81282V4.81282C9.88906 4.77146 10.0805 4.77183 10.2635 4.81319V4.81319C10.4635 4.85841 10.6534 4.94621 10.8169 5.06996L14.6224 7.94909C14.8641 8.13197 14.7348 8.51726 14.4317 8.51726H5.61634C5.31478 8.51726 5.18448 8.13519 5.42321 7.95094L9.15015 5.07444Z"
        fill="white"
      />
      <Path
        d="M5.5 14.125C5.5 13.7798 5.77982 13.5 6.125 13.5H13.875C14.2202 13.5 14.5 13.7798 14.5 14.125V14.5C14.5 14.6381 14.3881 14.75 14.25 14.75H5.75C5.61193 14.75 5.5 14.6381 5.5 14.5V14.125Z"
        fill="white"
      />
    </Svg>
  );
}
