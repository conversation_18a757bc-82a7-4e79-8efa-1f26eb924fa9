import Svg, {
  Rect,
  <PERSON>,
  <PERSON>,
  Defs,
  LinearGradient,
  Stop,
} from "react-native-svg";

export function MarinadeIcon({ size = 42 }: { size?: number }) {
  return (
    <Svg width={size} height={size} fill="none" viewBox="0 0 42 42">
      <Rect
        x={42}
        width={42}
        height={42}
        rx={21}
        transform="rotate(90 42 0)"
        fill="url(#prefix__paint0_linear_2483_20874)"
      />
      <G filter="url(#prefix__filter0_d_2483_20874)">
        <Path
          d="M25.699 12.067c-3.433.607-5.867 2.918-8.304 5.232-.776.736-1.553 1.473-2.36 2.155-.33-.15-.784-.214-1.155-.265a5.769 5.769 0 01-.442-.072c-1.795-.148-3.838.312-5.1 1.67-2.006 2.254-1.964 5.736 1.555 6.23.825.148 1.67.12 2.483-.083.027.108.064.214.112.316.028.064.055.128.077.194.135.343.252.692.35 1.047.154.515.307 1.03.568 1.495a.43.43 0 00.32.175.427.427 0 00.306.142 35.234 35.234 0 018.321-2.466c1.606-.304 3.214-.454 4.832-.604.92-.085 1.844-.171 2.772-.286a.52.52 0 00.534-.708.475.475 0 00.045-.315c-.081-.34-.183-.675-.282-1.009l-.05-.169a60.757 60.757 0 00-.078-.265c-.112-.381-.224-.764-.312-1.152.41-.226.836-.744 1.126-1.096.08-.096.148-.18.204-.242a6.393 6.393 0 001.137-2.33c1.324-4.687-1.918-8.193-6.66-7.594zm4.636 9.457c-.27.351-.584.667-.934.939a2.208 2.208 0 00-.429-.066 4.768 4.768 0 01-.159-.015c.438-.735.665-1.893.64-2.386.051-.636-.902-.73-.97-.096-.016.387-.07.771-.163 1.147a2.93 2.93 0 01-.542 1.05.497.497 0 00-.258.225 24.556 24.556 0 00-1.123.006c.286-.648.444-1.413.373-1.735-.04-.4-.122-.794-.242-1.176-.201-.61-1.114-.317-.933.295.32.876.228 1.939-.381 2.676-.727.054-1.451.14-2.172.258-.893.185-1.773.43-2.633.736a.491.491 0 00.29.936c.19-.057.378-.117.566-.176.467-.146.932-.292 1.407-.408.896-.211 1.764-.303 2.672-.357.06.008.123.005.182-.01.218-.013.438-.023.662-.032.905-.075 1.81-.024 2.71.035.01.231.091.494.166.73.043.126.078.255.108.384.077.257.148.516.218.776.066.242.131.483.203.723-.809.094-1.62.174-2.43.253-2.061.203-4.124.405-6.149.85-1.487.3-2.954.695-4.392 1.18h-.004c-.82.261-1.647.526-2.399.945a21.299 21.299 0 01-.32-.954c-.177-.554-.354-1.11-.6-1.633.565-.195 1.223-.47 1.012-1.024a.502.502 0 00-.662-.24c-1.575.844-3.793 1.167-5.237-.084-.971-1.215-.116-3.053.856-4.013 1.306-1.114 3.136-1.345 4.795-1.033-.265.2-.536.39-.81.574-.545.365-.011 1.168.535.835 1.525-.96 2.82-2.207 4.117-3.455.816-.786 1.633-1.573 2.508-2.287 1.979-1.676 4.55-3.23 7.24-2.832 1.34.274 2.783.883 3.417 2.176 1.067 1.958.614 4.567-.735 6.283z"
          fill="#fff"
        />
        <Path
          d="M17.515 17.426h0l-.002.001c-.775.736-1.554 1.475-2.366 2.16l-.084.072-.101-.046c-.302-.138-.727-.198-1.105-.25a6.275 6.275 0 01-.445-.072c-1.76-.143-3.735.313-4.944 1.614-.976 1.097-1.44 2.479-1.251 3.629.094.572.35 1.088.785 1.49.437.403 1.064.7 1.916.82l.006.001h0c.8.143 1.621.115 2.41-.081l.17-.043.042.17c.024.098.058.193.1.285l.002.004h0a10.688 10.688 0 01.435 1.262m4.432-11.016l-4.43 11.018-.001-.002m4.431-11.016c2.444-2.32 4.84-4.588 8.21-5.186 2.321-.292 4.255.421 5.447 1.758 1.192 1.337 1.666 3.321 1.018 5.616h0l-.001.005a6.22 6.22 0 01-1.103 2.26 7.74 7.74 0 00-.188.223l-.016.02-.004.005a10.06 10.06 0 01-.505.58c-.19.198-.384.368-.567.468l-.115.064.03.128c.088.392.2.776.311 1.153l.003.01h0l.078.265v.002l.05.168-12.648-7.54zm-4.431 11.016c.153.514.3 1.006.546 1.448m-.546-1.448v-.001l.546 1.449m0 0a.256.256 0 00.184.096l.071.005.048.053a.254.254 0 00.147.08 35.41 35.41 0 018.32-2.46c1.613-.305 3.227-.455 4.842-.605h.004l.016.174-.016-.174a90.31 90.31 0 002.767-.286l.012-.001h.013c.178.004.28-.07.335-.155a.364.364 0 00.03-.316l-.027-.073.037-.07a.3.3 0 00.03-.197c-.073-.303-.162-.602-.253-.906l-.026-.09L13.63 29.89zm15.81-7.236l-.083-.021a2.03 2.03 0 00-.394-.061h-.005 0a5.321 5.321 0 01-.165-.017l-.269-.031.139-.232c.207-.347.368-.802.474-1.235.106-.436.152-.83.14-1.051v-.024a.296.296 0 00-.072-.238.334.334 0 00-.21-.098.329.329 0 00-.223.054.29.29 0 00-.114.213c-.017.397-.073.79-.168 1.176l-.001.006h0c-.114.406-.31.784-.575 1.113l-.03.036-.044.017a.323.323 0 00-.137.101h0a.277.277 0 00-.03.044l-.05.093-.106-.002a24.499 24.499 0 00-1.115.006l-.277.007.112-.253c.138-.312.246-.654.31-.955.065-.31.08-.55.052-.672l-.002-.01v-.01l2.843 2.044zm0 0l.068-.053c.362-.281.686-.607.966-.97 1.382-1.758 1.856-4.44.752-6.47-.673-1.368-2.185-1.99-3.539-2.267h0l-.01-.002c-2.769-.41-5.391 1.19-7.377 2.87l9.14 6.892zm.119 2.56c-.07-.257-.14-.517-.218-.775a4.055 4.055 0 00-.11-.393c-.077-.241-.15-.48-.158-.683l-.006-.157-.157-.01c-.899-.06-1.814-.111-2.733-.036l-.66.031-.018.001-.017.005a.288.288 0 01-.113.006l-.018-.002-.018.001c-.912.055-1.791.147-2.701.361h-.002c-.476.118-.942.264-1.402.408l-.015.005 5.966 2.434m2.38-1.195l-.17.045.17-.046s0 0 0 0zm0 0c.066.241.13.481.201.718l.06.2-.207.024c-.81.094-1.623.174-2.434.253m0 0h0zm-1.83-3.91c.654-.793.746-1.92.412-2.843l-4.548 4.319-.09.028c-.158.05-.316.099-.474.146h-.002a.315.315 0 11-.186-.601h0l.006-.002a19.757 19.757 0 012.607-.728 24.997 24.997 0 012.153-.257l.074-.006.048-.057zm-11.38 3.009a.327.327 0 01.18.16l3.635-7.611c-1.299 1.25-2.582 2.484-4.088 3.432a.305.305 0 01-.25.042.344.344 0 01-.188-.144.33.33 0 01-.055-.227c.01-.07.05-.146.147-.212.277-.184.55-.377.818-.579l.318-.238-.39-.074c-1.692-.318-3.582-.088-4.941 1.072h0l-.01.008c-.504.499-.979 1.223-1.202 1.987-.223.764-.201 1.593.332 2.26l.01.012.012.01c1.524 1.322 3.83.965 5.429.11a.328.328 0 01.243-.008z"
          stroke="url(#prefix__paint1_linear_2483_20874)"
          strokeOpacity={0.5}
          strokeWidth={0.35}
        />
        <Path
          d="M18.898 25.312c-1.014.22-2.008.527-2.969.918l-.123.049c-.44.17-1.084.42-.777.97.29.379.68.174 1.059-.026.178-.094.354-.187.516-.217.446-.176.9-.328 1.361-.456.294-.078.59-.15.885-.222l.339-.084c.608-.192.322-1.119-.291-.932z"
          fill="#fff"
        />
        <Path
          d="M18.935 25.483l.007-.002.006-.002a.29.29 0 01.248.022.334.334 0 01.138.187.335.335 0 01-.008.232.287.287 0 01-.185.156l-.333.082s0 0 0 0a54.49 54.49 0 00-.887.223h0-.002c-.463.128-.918.28-1.364.457-.18.037-.367.135-.528.219a4.36 4.36 0 01-.02.01v.001c-.198.104-.363.189-.51.212a.336.336 0 01-.173-.01.346.346 0 01-.148-.117c-.055-.102-.058-.173-.046-.223a.342.342 0 01.122-.175c.15-.13.39-.225.617-.313h0l.125-.05h.001c.952-.387 1.936-.691 2.94-.91z"
          stroke="url(#prefix__paint2_linear_2483_20874)"
          strokeOpacity={0.5}
          strokeWidth={0.35}
        />
      </G>
      <Defs>
        <LinearGradient
          id="prefix__paint0_linear_2483_20874"
          x1={89}
          y1={-3.5}
          x2={37.5}
          y2={42}
          gradientUnits="userSpaceOnUse"
        >
          <Stop />
          <Stop offset={1} stopColor="#8C8C8C" />
        </LinearGradient>
        <LinearGradient
          id="prefix__paint1_linear_2483_20874"
          x1={13.683}
          y1={10.823}
          x2={26.88}
          y2={35.469}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#fff" />
          <Stop offset={1} stopColor="#fff" stopOpacity={0} />
        </LinearGradient>
        <LinearGradient
          id="prefix__paint2_linear_2483_20874"
          x1={16.141}
          y1={25.147}
          x2={17.333}
          y2={28.489}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#fff" />
          <Stop offset={1} stopColor="#fff" stopOpacity={0} />
        </LinearGradient>
      </Defs>
    </Svg>
  );
}
