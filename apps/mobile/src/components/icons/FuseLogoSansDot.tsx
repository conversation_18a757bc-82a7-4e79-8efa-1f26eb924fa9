import Svg, { Path } from "react-native-svg";

export function FuseLogoSansDot({
  size = 15,
  color,
}: {
  size?: number;
  color?: string;
}) {
  return (
    <Svg width={size} height={size} viewBox="0 0 15 15">
      <Path
        d="M12.7228 0.397118C12.1651 0.396895 11.627 0.605337 11.2118 0.982436C7.91646 3.45059 5.56381 1.40539 5.04264 0.873283C5.01032 0.836445 4.93356 0.754583 4.93356 0.754583C4.56097 0.388611 4.09021 0.141967 3.58008 0.0454602C3.06995 -0.0510469 2.54306 0.00685964 2.06522 0.211947C1.58738 0.417035 1.17978 0.760213 0.893314 1.19862C0.60685 1.63702 0.454226 2.15122 0.454507 2.67699C0.458579 3.04048 0.532686 3.39967 0.672668 3.73438C0.84235 4.15734 1.24366 4.553 1.45913 4.88864C1.86313 5.5067 2.07995 6.05518 2.07995 6.8397C2.07301 7.83477 1.69433 8.79026 1.02011 9.51388C0.947391 9.6012 0.629576 9.96821 0.625536 9.97094C0.278394 10.4367 0.0663395 10.9913 0.0132049 11.5725C-0.0399297 12.1536 0.0679606 12.7383 0.32475 13.2608C0.581539 13.7832 0.977052 14.2227 1.46684 14.53C1.95663 14.8372 2.52129 15 3.09737 15C3.67344 15 4.2381 14.8372 4.72789 14.53C5.21768 14.2227 5.61319 13.7832 5.86998 13.2608C6.12677 12.7383 6.23466 12.1536 6.18153 11.5725C6.12839 10.9913 5.91634 10.4367 5.5692 9.97094L5.28909 9.64758C4.59808 8.96487 4.17913 8.04846 4.11208 7.07296C4.04502 6.09746 4.33455 5.13106 4.92548 4.3579C5.05887 4.18276 5.20712 4.01978 5.36854 3.87082C5.37416 3.86421 5.38048 3.85826 5.38739 3.85308C6.14207 3.15711 7.13466 2.78637 8.15463 2.81948C9.1746 2.8526 10.1417 3.28696 10.8509 4.03045C10.9236 4.10822 11.1445 4.34698 11.1795 4.377C11.6912 4.82588 12.1127 5.01689 12.7201 5.01689C13.3247 5.01689 13.9047 4.77353 14.3322 4.34034C14.7598 3.90715 15 3.31962 15 2.707C15 2.09438 14.7598 1.50686 14.3322 1.07367C13.9047 0.64048 13.3247 0.397118 12.7201 0.397118H12.7228Z"
        fill={color}
      />
    </Svg>
  );
}
