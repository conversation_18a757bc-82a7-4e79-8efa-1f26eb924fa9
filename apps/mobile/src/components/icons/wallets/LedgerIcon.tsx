import Svg, { <PERSON>, ClipPath, Defs, G, Path, Rect } from "react-native-svg";

export function LedgerIcon({ size = 53 }: { size?: number }) {
  return (
    <Svg width={size} height={size} viewBox="0 0 36 36" fill="none">
      <G clip-path="url(#clip0_5961_88117)">
        <Circle cx="18" cy="18" r="18" fill="#0A0A0A" />
        <Path
          d="M19.7938 24.9925V25.9998H26.7073V21.4569H25.7V24.9925H19.7938ZM19.7938 10V11.0073H25.7V14.5431H26.7073V10H19.7938ZM17.1308 14.5431H16.1234V21.4566H20.6666V20.548H17.1308V14.5431ZM9.19995 21.4569V26H16.1134V24.9925H10.2073V21.4569H9.19995ZM9.19995 10V14.5431H10.2073V11.0073H16.1134V10H9.19995Z"
          fill="white"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_5961_88117">
          <Rect width="36" height="36" fill="white" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}
