import Svg, {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  Defs,
  <PERSON>dial<PERSON><PERSON><PERSON>,
  Stop,
  LinearGradient,
  ClipPath,
} from "react-native-svg";

export function SolflareIcon({ size = 53 }: { size?: number }) {
  return (
    <Svg width={size} height={size} fill="none" viewBox="0 0 53 53">
      <G clipPath="url(#a)">
        <Mask
          id="b"
          width={53}
          height={53}
          x={0}
          y={0}
          maskUnits="userSpaceOnUse"
        >
          <Path fill="#fff" d="M53 0H0v53h53z" />
        </Mask>
        <G mask="url(#b)">
          <Path
            fill="url(#c)"
            d="M26.908 50.898c.556 0 1.007.446 1.007.997s-.45.997-1.007.997a1 1 0 0 1-1.008-.997c0-.55.451-.997 1.008-.997m-1.095-47.61c.492.04.89.414.957.897l1.198 8.709c.403 2.877 3.873 4.124 6.015 2.169l12.009-10.93a.72.72 0 0 1 **********.7 0 0 1 .017.936l-10.472 12.08c-1.928 2.22-.506 5.686 2.433 5.933l9.24.89a.83.83 0 0 1 .752.904.83.83 0 0 1-.703.737l-9.709 1.516c-2.818.384-4.095 3.722-2.262 5.891l3.414 4.024a.76.76 0 0 1-.097 *********** 0 0 1-.968.021l-4.208-3.177c-2.27-1.708-5.543-.257-5.783 2.57l-.928 11.02a.834.834 0 0 1-.903.757.83.83 0 0 1-.76-.713l-1.47-10.646c-.395-2.877-3.865-4.124-6.015-2.169L5.803 47.46a.657.657 0 0 1-.92-.037.64.64 0 0 1-.016-.855L16.016 33.7c1.928-2.22.514-5.686-2.425-5.933l-9.242-.891a.83.83 0 0 1-.752-.904.83.83 0 0 1 .703-.737l9.702-1.515c2.82-.384 4.105-3.722 2.27-5.891l-2.323-2.74a.94.94 0 0 1 .12-1.337.97.97 0 0 1 1.201-.027l2.854 2.157c2.27 1.708 5.543.257 5.783-2.57l.768-9.073c.048-.573.558-1 1.138-.951M1.234 24.954c.557 0 1.008.446 1.008.997s-.451.996-1.008.996a1 1 0 0 1-1.007-.996c0-.551.45-.997 1.007-.997m50.54-1.012c.557 0 1.008.446 1.008.996 0 .551-.45.997-1.007.997a1 1 0 0 1-1.008-.997c0-.55.451-.996 1.008-.996M25.668.113c.557 0 1.008.446 1.008.997s-.451.997-1.008.997a1 1 0 0 1-1.007-.997c0-.55.45-.997 1.007-.997"
          />
          <Path
            fill="url(#d)"
            d="M26.269 34.861c5.257 0 9.519-4.215 9.519-9.415s-4.262-9.415-9.52-9.415c-5.256 0-9.518 4.216-9.518 9.415 0 5.2 4.262 9.415 9.519 9.415"
          />
        </G>
      </G>
      <Defs>
        <RadialGradient
          id="d"
          cx={0}
          cy={0}
          r={1}
          gradientTransform="rotate(67.52 -4.387 29.009)scale(13.8394 13.9738)"
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#FFC10B" />
          <Stop offset={1} stopColor="#FB3F2E" />
        </RadialGradient>
        <LinearGradient
          id="c"
          x1={7.094}
          x2={37.232}
          y1={8.508}
          y2={35.792}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#FFC10B" />
          <Stop offset={1} stopColor="#FB3F2E" />
        </LinearGradient>
        <ClipPath id="a">
          <Path fill="#fff" d="M0 0h53v53H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}
