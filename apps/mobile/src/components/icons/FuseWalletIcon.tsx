import { FuseLogo } from "~/components/icons/FuseLogo";
import { IconWrapper } from "~/components/IconWrapper";
import { useColor } from "~/components/Themed";
import { FusePlusLogo } from "~/components/icons/FusePlusLogo";
import Animated, {
  FadeIn,
  FadeOut,
  LayoutAnimationConfig,
  ReduceMotion,
} from "react-native-reanimated";
import { DURATION_FAST } from "~/constants/animations";
import { useSubscription } from "~/state/subscription";

const AnimatedIconWrapper = Animated.createAnimatedComponent(IconWrapper);

export function FuseWalletIcon({
  size,
}: {
  size: "medium" | "large" | "xlarge";
}) {
  const subscriptionQuery = useSubscription();

  const color = useColor("text");

  const wrapperSize = {
    medium: 42,
    large: 52,
    xlarge: 62,
  };

  const borderRadius = {
    medium: 12,
    large: 15,
    xlarge: 18,
  };

  const iconSize = {
    medium: 16,
    large: 20,
    xlarge: 24,
  };

  const entering = FadeIn.duration(DURATION_FAST).reduceMotion(
    ReduceMotion.Never
  );
  const exiting = FadeOut.duration(DURATION_FAST).reduceMotion(
    ReduceMotion.Never
  );

  const variant =
    subscriptionQuery.data?.status === "active" ? "plus" : "default";

  return (
    <LayoutAnimationConfig skipEntering skipExiting>
      {variant === "plus" ? (
        <AnimatedIconWrapper
          key={variant}
          entering={entering}
          exiting={exiting}
          style={{ borderRadius: borderRadius[size] }}
          size={wrapperSize[size]}
          background={"black"}
          variant={"square"}
        >
          <FusePlusLogo size={iconSize[size]} />
        </AnimatedIconWrapper>
      ) : (
        <AnimatedIconWrapper
          key={variant}
          entering={entering}
          exiting={exiting}
          backgroundColorToken="backgroundSecondary"
          style={{ borderRadius: borderRadius[size] }}
          size={wrapperSize[size]}
          variant="square"
        >
          <FuseLogo color={color} size={iconSize[size]} />
        </AnimatedIconWrapper>
      )}
    </LayoutAnimationConfig>
  );
}
