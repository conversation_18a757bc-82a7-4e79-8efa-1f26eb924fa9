import blurryPlusUri from "~assets/images/blurry-plus.png";
import { DimensionValue, View } from "react-native";
import { Image } from "expo-image";

export const PlusLogo = function FusePlusLogo({
  size = 40,
}: {
  size?: DimensionValue;
}) {
  return (
    <View
      style={{
        width: size,
        height: size,
        overflow: "visible",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <Image
        source={blurryPlusUri}
        contentFit="cover"
        style={{ width: "450%", aspectRatio: 1 }}
      />
    </View>
  );
};
