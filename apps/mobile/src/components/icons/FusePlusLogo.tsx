import { View } from "react-native";
import { FuseLogoSansDot } from "~/components/icons/FuseLogoSansDot";
import { PlusLogo } from "~/components/icons/PlusLogo";

export function FusePlusLogo({ size = 15 }: { size?: number }) {
  return (
    <View style={{ width: size, aspectRatio: 1 }}>
      <FuseLogoSansDot size={size} color={"white"} />
      <View
        style={{
          width: "50%",
          aspectRatio: 1,
          position: "absolute",
          right: "-10%",
          bottom: "-5%",
        }}
      >
        <PlusLogo size={"100%"} />
      </View>
    </View>
  );
}
