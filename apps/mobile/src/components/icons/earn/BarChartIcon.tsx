import Svg, { Rect } from "react-native-svg";
import { TextColor, useColor } from "~/components/Themed";

export function BarChartIcon({
  size = 22,
  color,
}: {
  size?: number;
  color?: string;
}) {
  return (
    <Svg width={size} height={size} viewBox="0 0 22 22" fill="none">
      <Rect
        x={2.29169}
        y={12.8335}
        width={3.66667}
        height={6.41667}
        rx={1.83333}
        fill={color}
      />
      <Rect
        x={9.16669}
        y={3.66675}
        width={3.66667}
        height={15.5833}
        rx={1.83333}
        fill={color}
      />
      <Rect
        x={16.0417}
        y={8.25}
        width={3.66667}
        height={11}
        rx={1.83333}
        fill={color}
      />
    </Svg>
  );
}
