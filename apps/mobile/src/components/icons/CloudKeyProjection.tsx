import Svg, { Path } from "react-native-svg";

export function CloudKeyProjection({ color }: { color: string }) {
  return (
    <Svg width={80} height={49} fill={"none"}>
      <Path
        d="M15 16.744l-11.536 6.66c-1.913 1.105-1.913 2.896 0 4L36.278 46.35c1.914 1.105 5.016 1.105 6.929 0L76.02 27.405c1.913-1.105 1.913-2.896 0-4L47.875 7.155"
        stroke={color}
        strokeDasharray="2 2"
      />
      <Path
        d="M52.524 20.143C52.278 13.706 47.78 6.092 42.267 2.91L36.78 8.397c5.513 3.183 10.011 10.797 10.257 17.234l5.488-5.488zM33.978 2.984c1.867-1.869 4.885-2.04 8.29-.075L36.78 8.397c-3.405-1.965-6.423-1.794-8.29.075l5.488-5.488z"
        stroke={color}
        strokeWidth={0.75}
        strokeLinejoin="round"
      />
      <Path
        d="M58.349 31.761c0-4.07-2.5-8.911-5.825-11.617l-5.488 5.488c3.324 2.706 5.825 7.546 5.825 11.617M30.95 1.802c.849.49 1.564 1.12 2.293 1.914L27.81 9.15a9.59 9.59 0 00-2.348-1.859M30.95 1.802c-1.948-1.125-3.675-1.026-4.744.043l-5.488 5.488c1.069-1.07 2.796-1.168 4.744-.043M22.184 27.965l23.379 13.498M15.133 14.896c0 4.68 3.095 10.313 7.051 13.028"
        stroke={color}
        strokeWidth={0.75}
        strokeLinejoin="round"
      />
      <Path
        d="M45.562 41.463s0 0 0 0zm0 0c2.42 1.397 4.566 1.275 5.893-.053.884-.884 1.405-2.301 1.405-4.16 0-4.072-2.5-8.912-5.824-11.619-.245-6.437-4.743-14.05-10.256-17.234-3.405-1.965-6.422-1.794-8.29.075a5.5 5.5 0 00-.644.777c-.729-.795-1.535-1.468-2.384-1.958-1.949-1.125-3.675-1.027-4.744.042-.48.48-.828 1.157-1.003 2.007-1.231-.067-2.292.324-3.081 1.113-.944.944-1.5 2.458-1.5 4.443 0 4.68 3.094 10.314 7.05 13.029v.04l23.378 13.498z"
        stroke={color}
        strokeWidth={0.75}
      />
      <Path
        d="M58.349 31.76c0 1.86-.521 3.278-1.405 4.162l-5.488 5.488c.883-.884 1.405-2.302 1.405-4.161"
        stroke={color}
        strokeWidth={0.75}
        strokeLinejoin="round"
      />
    </Svg>
  );
}
