import blurryPlusBackgroundUri from "~assets/images/blurry-plus-background.png";
import { Image } from "expo-image";
import { View } from "react-native";

export function PlusBlur({ size = 40 }: { size?: number }) {
  return (
    <View
      style={{
        width: size,
        height: size,
        overflow: "visible",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <Image
        source={blurryPlusBackgroundUri}
        style={{ aspectRatio: 1, width: "450%" }}
      />
    </View>
  );
}
