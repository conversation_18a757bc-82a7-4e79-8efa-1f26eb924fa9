import Svg, { Path } from "react-native-svg";
import Colors from "~/constants/Colors";
import { useThemeColor } from "~/components/Themed";

export function MagicLinkLogo({
  size = 59,
  colorToken = "text",
}: {
  size?: number;
  colorToken?: keyof typeof Colors.light & keyof typeof Colors.dark;
}) {
  const color = useThemeColor({}, colorToken);

  return (
    <Svg width={size} height={(size * 22) / 59} viewBox="0 0 59 22" fill="none">
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M50.5097 7.51611C51.1349 7.51611 51.6418 7.00926 51.6418 6.38403C51.6418 5.7588 51.1349 5.25195 50.5097 5.25195C49.8845 5.25195 49.3776 5.7588 49.3776 6.38403C49.3776 7.00926 49.8845 7.51611 50.5097 7.51611ZM23.3398 6.24246H25.6746L28.1405 12.1296H28.2454L30.7113 6.24246H33.0461V15.0047H31.2097V9.30159H31.1354L28.8181 14.9619H27.5677L25.2505 9.28015H25.1761V15.0047H23.3398V6.24246ZM36.594 15.1288C35.9528 15.1288 35.4208 14.9648 34.9982 14.6368C34.5756 14.3087 34.3642 13.8196 34.3642 13.1692C34.3642 12.6787 34.4823 12.2936 34.7184 12.0141C34.9545 11.7345 35.2635 11.5306 35.6453 11.4023C36.03 11.271 36.4439 11.1855 36.887 11.1456C37.4699 11.0885 37.8868 11.0286 38.1374 10.9659C38.3881 10.9031 38.5134 10.7662 38.5134 10.5551V10.5295C38.5134 10.2585 38.426 10.0489 38.2511 9.90051C38.0791 9.75223 37.8343 9.67806 37.5165 9.67806C37.1814 9.67806 36.9146 9.74939 36.7165 9.89199C36.5183 10.0346 36.3871 10.2157 36.323 10.4353L34.6004 10.2984C34.7286 9.69942 35.0463 9.22458 35.5535 8.87373C36.0635 8.52288 36.7209 8.34749 37.5253 8.34749C38.0238 8.34749 38.4901 8.4245 38.9244 8.57852C39.3587 8.73255 39.7084 8.97352 39.9737 9.30159C40.2419 9.62674 40.3759 10.0503 40.3759 10.5723V15.0047H38.6096V14.0934H38.5572C38.3939 14.3986 38.1491 14.6481 37.8226 14.8421C37.4991 15.0332 37.0895 15.1288 36.594 15.1288ZM37.1274 13.8709C37.5355 13.8709 37.8707 13.7554 38.1331 13.5243C38.3954 13.2905 38.5266 13.001 38.5266 12.6558V11.9585C38.4537 12.007 38.34 12.0498 38.1855 12.0868C38.0339 12.1239 37.8707 12.1567 37.6958 12.1853C37.5239 12.2109 37.3694 12.2337 37.2324 12.2537C36.9059 12.2993 36.6422 12.3892 36.441 12.5233C36.2428 12.6573 36.1437 12.8527 36.1437 13.1094C36.1437 13.3604 36.2355 13.55 36.4191 13.6784C36.6057 13.8068 36.8418 13.8709 37.1274 13.8709ZM42.7172 17.0413C43.2332 17.3892 43.9429 17.5633 44.8465 17.5633C45.4878 17.5633 46.0547 17.4691 46.5473 17.2808C47.0399 17.0926 47.4261 16.8131 47.7059 16.4423C47.9886 16.0715 48.13 15.6137 48.13 15.0689V8.43302H46.2805V9.53688H46.2106C46.129 9.35716 46.0095 9.17603 45.8521 8.99354C45.6947 8.81098 45.4892 8.65838 45.2356 8.53572C44.982 8.41023 44.6672 8.34749 44.2912 8.34749C43.8015 8.34749 43.3497 8.47298 42.9359 8.72396C42.5249 8.9721 42.1941 9.34432 41.9434 9.8406C41.6956 10.337 41.5718 10.9587 41.5718 11.7061C41.5718 12.4334 41.6927 13.0338 41.9346 13.5072C42.1795 13.9807 42.5074 14.333 42.9184 14.564C43.3323 14.7951 43.7914 14.9106 44.2956 14.9106C44.8319 14.9106 45.2545 14.8008 45.5635 14.5811C45.8725 14.3587 46.0882 14.112 46.2106 13.841H46.2893V15.0432C46.2893 15.4739 46.1567 15.7834 45.8914 15.9716C45.6291 16.1599 45.2953 16.254 44.8902 16.254C44.4792 16.254 44.1629 16.1799 43.9415 16.0315C43.7229 15.8861 43.5757 15.7249 43.4999 15.5481L41.7773 15.7749C41.888 16.2712 42.2014 16.6933 42.7172 17.0413ZM45.9264 13.0537C45.6816 13.3789 45.3347 13.5415 44.8859 13.5415C44.4282 13.5415 44.0785 13.3761 43.8365 13.0452C43.5946 12.7143 43.4737 12.2651 43.4737 11.6975C43.4737 11.1385 43.5931 10.6821 43.8322 10.3284C44.0741 9.97468 44.4253 9.79787 44.8859 9.79787C45.3377 9.79787 45.6859 9.97042 45.9308 10.3155C46.1756 10.6607 46.2981 11.1213 46.2981 11.6975C46.2981 12.2765 46.1742 12.7286 45.9264 13.0537ZM49.6121 15.0047V8.43302H51.4746V15.0047H49.6121ZM54.2258 14.7052C54.7214 14.9905 55.313 15.1331 56.001 15.1331C56.5985 15.1331 57.1158 15.0275 57.5531 14.8164C57.9903 14.6025 58.3328 14.3045 58.5805 13.9222C58.8312 13.5401 58.9711 13.098 59.0002 12.5959H57.2427C57.1872 12.9468 57.0502 13.2178 56.8316 13.4089C56.613 13.5971 56.3434 13.6912 56.0228 13.6912C55.5885 13.6912 55.2402 13.5201 54.9779 13.1778C54.7184 12.8327 54.5888 12.3464 54.5888 11.7188C54.5888 11.0971 54.7199 10.6165 54.9822 10.2771C55.2445 9.93763 55.5914 9.76792 56.0228 9.76792C56.3638 9.76792 56.6393 9.86914 56.8492 10.0716C57.0619 10.2713 57.1931 10.5352 57.2427 10.8632H59.0002C58.974 10.3555 58.8327 9.91336 58.5762 9.53688C58.3225 9.16034 57.9742 8.86798 57.5312 8.6598C57.0911 8.45154 56.5781 8.34749 55.9922 8.34749C55.313 8.34749 54.7272 8.4915 54.2346 8.77961C53.742 9.0648 53.3616 9.46271 53.0935 9.97326C52.8282 10.481 52.6956 11.0714 52.6956 11.7445C52.6956 12.4091 52.8268 12.9967 53.0891 13.5072C53.3543 14.0178 53.7333 14.4171 54.2258 14.7052Z"
        fill={color}
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.83078 0C9.75223 1.12859 10.7686 2.17668 11.8676 3.13212C11.1353 5.50746 10.741 8.03104 10.741 10.6468C10.741 13.2625 11.1353 15.7861 11.8676 18.1614C10.7686 19.1169 9.75223 20.165 8.83071 21.2937C7.90919 20.165 6.89277 19.1168 5.79366 18.1614C6.526 15.7861 6.92026 13.2625 6.92026 10.6468C6.92026 8.03111 6.52601 5.50759 5.79369 3.1323C6.89281 2.17682 7.90919 1.12866 8.83078 0ZM3.69305 16.5193C2.52961 15.7038 1.29485 14.9831 0 14.3685C0.359161 13.1912 0.55237 11.9416 0.552371 10.6468C0.552371 9.35203 0.359164 8.10245 1.00731e-05 6.92518C1.29486 6.31053 2.52962 5.58986 3.69308 4.77439C4.13794 6.6596 4.37332 8.6257 4.37332 10.6468C4.37332 12.6679 4.13793 14.634 3.69305 16.5193ZM13.2883 10.6468C13.2883 12.6679 13.5236 14.6339 13.9685 16.5191C15.1319 15.7037 16.3666 14.9831 17.6613 14.3685C17.3022 13.1912 17.109 11.9416 17.109 10.6468C17.109 9.35203 17.3022 8.10238 17.6613 6.92512C16.3666 6.31049 15.1319 5.58986 13.9685 4.77443C13.5236 6.65962 13.2883 8.6257 13.2883 10.6468Z"
        fill={color}
      />
    </Svg>
  );
}
