import Svg, { Circle, Path } from "react-native-svg";
import { memo } from "react";

export const FuseLogoDuoColor = memo(function FuseLogoDuoColor({
  size = 30,
  primaryColor = "#F5F5F5",
  secondaryColor = "#FFFFFF",
}: {
  size?: number;
  primaryColor?: string;
  secondaryColor?: string;
}) {
  return (
    <Svg width={size} height={size} viewBox="0 0 30 30">
      <Circle cx="26" cy="25" r="4" fill={primaryColor} />
      <Path
        opacity="0.1"
        d="M25.4455 0.794235C24.3302 0.79379 23.2541 1.21067 22.4236 1.96487C15.8329 6.90118 11.1276 2.81078 10.0853 1.74657C10.0207 1.67289 9.86713 1.50917 9.86713 1.50917C9.12195 0.777221 8.18043 0.283934 7.16017 0.0909203C6.13991 -0.102094 5.08613 0.0137193 4.13045 0.423895C3.17477 0.834071 2.35956 1.52043 1.78663 2.39723C1.2137 3.27404 0.908453 4.30244 0.909014 5.35398C0.917158 6.08096 1.06537 6.79934 1.34534 7.46876C1.6847 8.31467 2.48732 9.10601 2.91826 9.77728C3.72627 11.0134 4.1599 12.1104 4.1599 13.6794C4.14602 15.6695 3.38866 17.5805 2.04022 19.0278C1.89478 19.2024 1.25915 19.9364 1.25107 19.9419C0.556788 20.8734 0.132679 21.9827 0.0264098 23.145C-0.0798595 24.3073 0.135921 25.4766 0.6495 26.5215C1.16308 27.5664 1.9541 28.4455 2.93369 29.06C3.91327 29.6745 5.04259 30 6.19473 30C7.34688 30 8.4762 29.6745 9.45578 29.06C10.4354 28.4455 11.2264 27.5664 11.74 26.5215C12.2535 25.4766 12.4693 24.3073 12.3631 23.145C12.2568 21.9827 11.8327 20.8734 11.1384 19.9419L10.5782 19.2952C9.19616 17.9297 8.35826 16.0969 8.22415 14.1459C8.09004 12.1949 8.66909 10.2621 9.85097 8.7158C10.1177 8.36552 10.4142 8.03955 10.7371 7.74163C10.7483 7.72843 10.761 7.71653 10.7748 7.70616C12.2841 6.31423 14.2693 5.57274 16.3093 5.63897C18.3492 5.70519 20.2835 6.57393 21.7018 8.0609C21.8472 8.21644 22.2889 8.69396 22.3589 8.754C23.3824 9.65176 24.2254 10.0338 25.4401 10.0338C26.6495 10.0338 27.8093 9.54706 28.6644 8.68068C29.5196 7.81431 30 6.63925 30 5.41401C30 4.18877 29.5196 3.01371 28.6644 2.14734C27.8093 1.28096 26.6495 0.794235 25.4401 0.794235H25.4455Z"
        fill={secondaryColor}
      />
    </Svg>
  );
});
