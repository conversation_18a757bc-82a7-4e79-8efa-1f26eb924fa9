import { Text, View } from "~/components/Themed";
import { CodeInput } from "~/components/CodeInput";
import { useEffect, useState } from "react";
import { Button } from "~/components/Button";
import { MagicLinkLogo } from "~/components/icons/MagicLinkLogo";
import { Row } from "~/components/Grid";
import { Keyboard, ScrollView, StyleSheet } from "react-native";
import { MagicLinkEmailControl } from "~/services/magicLink";
import { useIsKeyboardShown } from "~/hooks/useIsKeyboardShown";
import { TouchableScale } from "~/components/TouchableScale";
import Animated, {
  Easing,
  FadeInDown,
  FadeInUp,
  FadeOutUp,
  LayoutAnimationConfig,
  LinearTransition,
  ReduceMotion,
} from "react-native-reanimated";
import {
  DURATION_FAST,
  DURATION_FASTER,
  DURATION_SLOW,
  IOS_KEYBOARD_ANIMATION_DURATION,
  ZoomAndFadeIn,
  ZoomAndFadeOut,
} from "~/constants/animations";
import { H1 } from "~/components/typography/H1";
import { Spinner } from "~/components/Spinner";
import { Icon } from "~/components/Icon";
import {
  useAccessibleEntryAnimation,
  useAccessibleExitAnimation,
} from "~/utils/animations";
import { Address } from "@squads/models/solana";
import { useMagicLinkCodeInput } from "~/hooks/useMagicLinkCodeInput";

export default function MagicLinkCodeConfirmation({
  control,
  onSuccess,
}: {
  control: MagicLinkEmailControl;
  onSuccess(newKey: { email: string; address: Address }): void;
}) {
  const {
    value,
    onValueChange,
    resendMutation,
    verifyCodeMutation,
    handlePaste,
  } = useMagicLinkCodeInput({
    control,
    onSuccess,
  });

  //replace with reanimated
  const isShownKeyboard = useIsKeyboardShown();
  const eventualKeyboardHeight = useEventualKeyboardHeight();

  const hideInstructions =
    isShownKeyboard ||
    verifyCodeMutation.isPending ||
    verifyCodeMutation.data?.type === "success";

  const isPendingOrCompleted =
    verifyCodeMutation.isPending || verifyCodeMutation.data?.type === "success";

  return (
    <LayoutAnimationConfig skipEntering skipExiting>
      <View style={styles.container}>
        <ScrollView
          contentContainerStyle={{
            flex: 1,
            justifyContent: "space-between",
            paddingBottom: 18,
          }}
        >
          <View
            style={[
              styles.headingContainer,
              {
                justifyContent: hideInstructions ? "center" : "flex-start",
              },
            ]}
          >
            {!hideInstructions && (
              <Animated.View
                exiting={FadeOutUp.duration(DURATION_FAST).reduceMotion(
                  ReduceMotion.Never
                )}
                entering={FadeInUp.duration(IOS_KEYBOARD_ANIMATION_DURATION)
                  .delay(50)
                  .reduceMotion(ReduceMotion.Never)}
                style={{ marginBottom: 8 }}
              >
                <H1 style={{ marginBottom: 32 }}>Confirm email</H1>
                <Text
                  variant="medium"
                  colorToken="textSecondary"
                  style={[styles.text, { marginBottom: 16 }]}
                >
                  The code has been sent to
                </Text>
                <Text
                  variant="medium"
                  style={{ fontSize: 26, marginBottom: 32 }}
                >
                  {control.email}
                </Text>
                <Text
                  variant="medium"
                  colorToken="textSecondary"
                  style={styles.text}
                >
                  Please check your inbox{"\n"}and paste the code below
                </Text>
              </Animated.View>
            )}
            <Animated.View
              layout={LinearTransition.duration(
                IOS_KEYBOARD_ANIMATION_DURATION
              ).reduceMotion(ReduceMotion.Never)}
              style={{ gap: 24 }}
            >
              <CodeInput
                disabled={isPendingOrCompleted}
                error={
                  value.length == 6 &&
                  verifyCodeMutation.data?.type === "wrong-code"
                    ? "Wrong verification code"
                    : undefined
                }
                value={value}
                setValue={onValueChange}
              />
              <Row justify={"center"} style={{ height: 42 }}>
                <Animated.View
                  key={verifyCodeMutation.isPending ? "spinner" : "button"}
                  exiting={ZoomAndFadeOut.duration(
                    DURATION_FASTER
                  ).reduceMotion(ReduceMotion.Never)}
                  entering={ZoomAndFadeIn.duration(DURATION_FASTER)
                    .delay(DURATION_FAST)
                    .reduceMotion(ReduceMotion.Never)}
                >
                  {verifyCodeMutation.isPending ? (
                    <Spinner size={24} />
                  ) : verifyCodeMutation.data?.type === "success" ? (
                    <Icon name="checkmark.circle.fill" size={20} />
                  ) : (
                    <Button
                      size="medium"
                      variant={"secondary"}
                      onPress={handlePaste}
                      disabled={verifyCodeMutation.isPending}
                    >
                      Paste
                    </Button>
                  )}
                </Animated.View>
              </Row>
            </Animated.View>
          </View>
          <View
            style={{
              gap: 16,
              justifyContent: "flex-end",
              height:
                eventualKeyboardHeight > 0 ? eventualKeyboardHeight : "auto",
            }}
          >
            <Row justify="center">
              {!isPendingOrCompleted && (
                <ResendButton
                  onResend={() => resendMutation.mutateAsync()}
                  loading={resendMutation.isPending}
                />
              )}
            </Row>
            <Row justify={"center"}>
              <Text colorToken={"textSecondary"}>powered by </Text>
              <MagicLinkLogo colorToken={"textSecondary"} />
            </Row>
          </View>
        </ScrollView>
      </View>
    </LayoutAnimationConfig>
  );
}

function useEventualKeyboardHeight() {
  const [height, setHeight] = useState(0);

  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      "keyboardWillShow",
      (ev) => {
        setHeight(ev.endCoordinates.height);
      }
    );

    const keyboardWillHideListener = Keyboard.addListener(
      "keyboardWillHide",
      () => {
        setHeight(0);
      }
    );

    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, []);

  return height;
}

export function ResendButton({
  onResend,
  loading,
}: {
  onResend: () => Promise<void>;
  loading: boolean;
}) {
  const showTimeout = 10;
  const duration = 59;
  const { seconds, reset } = useTimer(duration);

  const textEntryAnimation = useAccessibleEntryAnimation(
    FadeInDown.duration(DURATION_SLOW)
      .delay(100)
      .easing(Easing.inOut(Easing.ease))
  );
  const textExitAnimation = useAccessibleExitAnimation(
    FadeOutUp.duration(DURATION_SLOW)
      .delay(100)
      .easing(Easing.inOut(Easing.ease))
  );

  if (seconds > duration - showTimeout) {
    return null;
  }

  return (
    <Animated.View
      key={seconds > 0 ? "countdown" : "button"}
      entering={textEntryAnimation}
      exiting={textExitAnimation}
    >
      {seconds > 0 ? (
        <Text
          variant={"medium"}
          colorToken={"textSecondary"}
          style={{ fontSize: 15 }}
        >
          Resend available in:{" "}
          <Text
            variant={"medium"}
            colorToken={"textSecondary"}
            style={{ fontVariant: ["tabular-nums"] }}
          >
            0:{seconds < 10 ? `0${seconds}` : seconds}
          </Text>
        </Text>
      ) : (
        <TouchableScale
          onPress={async () => {
            await onResend();
            reset();
          }}
          disabled={loading}
          style={{ borderBottomWidth: 1, alignSelf: "center" }}
        >
          <Text
            variant={"medium"}
            colorToken={"text"}
            style={{
              fontSize: 15,
            }}
          >
            Resend code
          </Text>
        </TouchableScale>
      )}
    </Animated.View>
  );
}

function useTimer(duration: number) {
  const [seconds, setSeconds] = useState(duration);

  useEffect(() => {
    const interval = setInterval(() => {
      setSeconds((seconds) => Math.max(seconds - 1, 0));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return {
    seconds,
    reset: () => setSeconds(duration),
  };
}

export const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headingContainer: {
    flex: 1,
    paddingTop: 20,
    gap: 20,
  },
  heading: {
    fontSize: 45,
    letterSpacing: -1.5,
    lineHeight: 45,
  },
  subheading: {
    fontSize: 15,
    lineHeight: 20,
  },
  buttons: {
    gap: 8,
  },
  text: {
    fontSize: 18,
    flexShrink: 1,
    lineHeight: 24,
  },
});
