import Animated from "react-native-reanimated";
import {
  DURATION_FAST,
  ZoomAndFadeIn,
  ZoomAndFadeOut,
} from "~/constants/animations";
import { hapticGoBack } from "~/utils/haptics";
import { Icon } from "~/components/Icon";
import { useRouter } from "expo-router";
import { ColorName } from "~/constants/Colors";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";

export function BackButton(props: {
  onPress: () => void;
  colorToken?: ColorName;
}) {
  return (
    <Animated.View
      entering={ZoomAndFadeIn.duration(DURATION_FAST)}
      exiting={ZoomAndFadeOut.duration(DURATION_FAST)}
      style={{ alignSelf: "flex-start" }}
    >
      <AnimatedTouchableScale
        hitSlop={10}
        onPress={() => {
          hapticGoBack();
          props.onPress();
        }}
        style={{
          marginLeft: -8,
          alignItems: "flex-start",
          justifyContent: "center",
        }}
      >
        <Icon
          colorToken={props.colorToken}
          name="chevron.left"
          size={10}
          rectSize={32}
          weight="semibold"
        />
      </AnimatedTouchableScale>
    </Animated.View>
  );
}

export function OptionalBackButton({ colorToken }: { colorToken?: ColorName }) {
  const router = useRouter();

  return router.canGoBack() ? (
    <BackButton colorToken={colorToken} onPress={() => router.back()} />
  ) : null;
}
