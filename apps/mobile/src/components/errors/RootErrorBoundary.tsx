import bgImage from "~assets/images/crash-screen-bg.png";
import { ErrorBoundaryProps } from "expo-router";
import { useEffect, useState } from "react";
import { SafeAreaView, Text, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import { ImageBackground } from "expo-image";
import { H1 } from "~/components/typography/H1";
import { FeatureList, FeatureListItem } from "~/components/FeatureList";
import { queryClient } from "~/state/queryClient";
import { reportError } from "~/utils/errors";
import * as Clipboard from "expo-clipboard";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { ThemeProvider } from "~/components/ThemeContext";
import { toast, ToastProvider } from "~/components/Toaster";
import { Wallet } from "~/services/wallets";
import * as Application from "expo-application";

export function ErrorBoundary({ error, retry }: ErrorBoundaryProps) {
  const [errorId, setErrorId] = useState<string | null>(null);

  useEffect(() => {
    try {
      if (error) {
        if (!(error instanceof Error)) {
          error = new Error(
            "RootErrorBoundary caught a non-Error: " + JSON.stringify(error)
          );
        }

        const id = reportError(queryClient, error, { errorBoundary: true });
        setErrorId(id);
      }
    } catch (e) {
      setErrorId(`Failed to report sentry error: ${e}`);
    }
  }, [error]);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ThemeProvider>
        <ToastProvider>
          <SafeAreaView style={{ flex: 1 }}>
            <ImageBackground
              source={bgImage}
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                flex: 1,
                justifyContent: "center",
              }}
            />
            <View style={{ flex: 1, padding: 20 }}>
              <View
                style={{
                  paddingTop: 50,
                  flex: 1,
                  gap: 32,
                }}
              >
                <View style={{ gap: 20 }}>
                  <View>
                    <H1>There was</H1>
                    <H1>an error</H1>
                  </View>
                </View>

                <View style={{ gap: 24 }}>
                  <Text variant="medium" style={{ fontSize: 18 }}>
                    Please help us fix this issue quickly
                  </Text>
                  <FeatureList>
                    <FeatureListItem
                      icon="doc.on.doc"
                      label="Copy the Error Details"
                      text="Tap the button below to copy it to your clipboard"
                    />
                    <FeatureListItem
                      icon="square.and.arrow.up"
                      label="Share it with us"
                      text="Send the error <NAME_EMAIL> or share it in our Telegram support channel."
                    />
                    <FeatureListItem
                      icon="square.and.pencil"
                      label="Add details"
                      text="Describe what you were doing when the error occurred and send it to us"
                    />
                  </FeatureList>
                </View>
              </View>
              <View style={{ gap: 12, paddingTop: 20 }}>
                <Button
                  variant="secondary"
                  onPress={async () => {
                    const dataByKey = queryClient.getQueriesData({
                      predicate: (query) =>
                        query.queryKey[0] === "wallet" &&
                        query.queryKey[1] !== null,
                    });

                    const wallet = (
                      dataByKey.at(0)?.[1] as { wallet: Wallet | null } | null
                    )?.wallet;

                    const appVersion = Application.nativeApplicationVersion;
                    const appUpdateId = Application.nativeBuildVersion;

                    await Clipboard.setStringAsync(
                      JSON.stringify({
                        errorId,
                        error: String(error),
                        wallet: wallet?.walletKey,
                        vault: wallet?.defaultVault,
                        appVersion,
                        appUpdateId,
                        stack: String(error?.stack).slice(0, 100),
                      })
                    );
                    toast.info("Copied to clipboard", {
                      id: "copy-error-id",
                    });
                  }}
                >
                  Copy Error Details
                </Button>
                <Button
                  onPress={() => {
                    // Invalidate all queries
                    queryClient.clear();
                    retry?.();
                  }}
                >
                  Reload
                </Button>
              </View>
            </View>
          </SafeAreaView>
        </ToastProvider>
      </ThemeProvider>
    </GestureHandlerRootView>
  );
}
