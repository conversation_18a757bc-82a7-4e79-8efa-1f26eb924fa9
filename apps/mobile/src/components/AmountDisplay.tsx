import { TokenBalance } from "~/services/balances";
import { Text, View } from "~/components/Themed";
import {
  formatAmountInputWithSeparators,
  formatUsdValue,
} from "@squads/utils/numberFormats";
import { z } from "zod";
import { Address } from "@squads/models/solana";

export function AmountDisplay({
  amount,
  balance,
}: {
  balance: TokenBalance;
  amount: string | null;
}) {
  const amountNumber = Number(amount || 0);
  const usdAmount = (balance.usdcPrice ?? 0) * amountNumber;

  return (
    <View
      style={{
        flex: 1,
        flexDirection: "row",
        alignItems: "baseline",
        gap: 4,
      }}
    >
      <View style={{ gap: 4, flexShrink: 1 }}>
        <Text
          adjustsFontSizeToFit={true}
          numberOfLines={1}
          colorToken={amount ? "text" : "textSecondary"}
          variant={"display-medium"}
          style={{
            fontSize: 45,
            paddingBottom: 0,
          }}
        >
          {amount ? formatAmountInputWithSeparators(amount) : "0"}
        </Text>
        <Text variant="medium" colorToken={"textSecondary"}>
          {formatUsdValue(usdAmount)}
        </Text>
      </View>
      <Text
        variant="medium"
        colorToken={"textSecondary"}
        style={{ fontSize: 20 }}
      >
        {balance.metadata?.symbol ?? ""}
      </Text>
    </View>
  );
}

export type AmountError = "insufficientBalance" | `minAmountExceeded:${number}`;

export const AmountZ = (maxAmount: number, minAmount?: number) =>
  z
    .string()
    .nonempty()
    .superRefine((v, ctx) => {
      const result = z.coerce
        .number()
        .gt(0, "zeroAmount")
        .max(maxAmount, "insufficientBalance")
        .min(minAmount ?? -1, `minAmountExceeded:${minAmount}`)
        .safeParse(v);
      if (!result.success) {
        result.error.issues.forEach((issue) => ctx.addIssue(issue));
      }
      return result.success;
    });

export function getMaxAvailableAmount(balance: {
  mint: Address | null;
  amount: number;
  decimals: number;
}) {
  return balance.amount / 10 ** balance.decimals;
}
