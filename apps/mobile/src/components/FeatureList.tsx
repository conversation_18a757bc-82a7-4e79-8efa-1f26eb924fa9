import { Text, View } from "~/components/Themed";
import { Icon } from "~/components/Icon";
import { ComponentProps, ReactNode } from "react";
import { SFSymbol } from "expo-symbols";
import { Row } from "~/components/Grid";

export function FeatureList({ children }: { children: ReactNode }) {
  return <View style={{ gap: 24 }}>{children}</View>;
}

export function FeatureListItem({
  icon = "checkmark.circle",
  label,
  text,
  color,
  colorToken,
}: {
  icon?: SFSymbol;
  label?: string;
  text: string;
  color?: string;
  colorToken?: ComponentProps<typeof Text>["colorToken"];
}) {
  return (
    <View gap={6}>
      <Row gap={8}>
        <Icon name={icon} size={12} color={color} style={{ marginLeft: 2 }} />
        <Text variant="semibold" size={16}>
          {label}
        </Text>
      </Row>
      <Text
        variant="medium"
        size={16}
        colorToken={colorToken ?? "textSecondary"}
      >
        {text}
      </Text>
    </View>
  );
}
