import { RefObject, useEffect, useState } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
  BottomModalMenuOption,
} from "~/components/BottomModal";
import { CloudKey } from "~/services/wallets";
import { LedgerAccountError, useLedgerDevice } from "~/hooks/useLedgerDevice";
import { Text, View } from "~/components/Themed";
import { SelectOption } from "~/components/SelectOption";
import { LedgerIcon } from "~/components/icons/LedgerIcon";
import * as Haptics from "expo-haptics";
import { Address } from "@squads/models/solana";
import { loadWalletByMember } from "~/state/wallet";
import { useQueryClient } from "@tanstack/react-query";
import { ListSeparator } from "~/components/ListSeparator";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import { FlatList } from "react-native";
import Animated, {
  FadeInDown,
  FadeOutUp,
  ReduceMotion,
} from "react-native-reanimated";
import { DURATION_FAST } from "~/constants/animations";
import { Button } from "~/components/Button";
import { useBleStatusCheckMutation } from "~/hooks/useBleStatusCheckMutation";
import { useDiscoverableLedgerDevices } from "~/hooks/useDiscoverableLedgerDevices";

type ModalState =
  | { type: "options" }
  | { type: "ledger" }
  | { type: "ledger/address"; deviceId: string };

const ModalState = {
  getTitle: (state: ModalState) => {
    switch (state.type) {
      case "options":
        return "Choose 2FA Key";
      case "ledger":
        return "Choose Ledger device";
      case "ledger/address":
        return "Choose Ledger account";
    }
  },
};

export function Recovery2FAKeyModal({
  onConfirm,
  modalRef,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
  onConfirm: (cloudKey: CloudKey) => void;
}) {
  const [modalState, setModalState] = useState<ModalState>({ type: "options" });

  return (
    <BottomModal
      modalRef={modalRef}
      modalId={modalState.type}
      themeOverride="dark"
      title={ModalState.getTitle(modalState)}
      body={() => {
        switch (modalState.type) {
          case "options":
            return (
              <OptionsView
                onSelect={(type: "ledger") => setModalState({ type })}
              />
            );
          case "ledger":
            return (
              <ChooseLedgerDeviceView
                onSelect={(deviceId: string) =>
                  setModalState({ type: "ledger/address", deviceId })
                }
              />
            );
          case "ledger/address":
            return (
              <ChooseLedgerAddressView
                deviceId={modalState.deviceId}
                onBack={() => setModalState({ type: "ledger" })}
                onSelect={(ledgerAccount: LedgerAccount) => {
                  onConfirm({
                    address: ledgerAccount.address,
                    details: {
                      type: "ledger",
                      derivationPath: ledgerAccount.derivationPath,
                      deviceId: modalState.deviceId,
                    },
                  });

                  modalRef.current?.dismiss();
                }}
              />
            );

          default:
            return modalState satisfies never;
        }
      }}
      // footer={
      // modalState.type === "select-key" ? (
      //}
      // onDismissButtonPress={() => {
      //   Haptics.selectionAsync();
      //   if (modalState.type === "recovery-warning") {
      //     if (hasSingleKey) {
      //       modalRef.current?.close();
      //     } else {
      //       setModalState({
      //         type: "select-key",
      //         selectedRecoveryKey: modalState.selectedRecoveryKey,
      //       });
      //     }
      //   } else if (modalState.type === "select-key") {
      //     modalRef.current?.close();
      //   } else {
      //     assertNever(modalState);
      //   }
      // }}
      onDismiss={() => {
        setModalState({ type: "options" });
      }}
    />
  );
}

function OptionsView({ onSelect }: { onSelect: (type: "ledger") => void }) {
  const bleStatusMutation = useBleStatusCheckMutation({
    onAvailable: () => onSelect("ledger"),
  });

  return (
    <SelectOption
      text={"Ledger"}
      icon={<LedgerIcon colorToken="textSecondary" size={23} rectSize={28} />}
      loading={bleStatusMutation.isPending}
      onPress={() => {
        Haptics.selectionAsync();
        bleStatusMutation.mutate();
      }}
    />
  );
}

export function ChooseLedgerDeviceView({
  onSelect,
}: {
  onSelect: (deviceId: string) => void;
}) {
  const devices = useDiscoverableLedgerDevices();

  return (
    <View style={{ flex: 1, gap: 12, minHeight: 120, maxHeight: 200 }}>
      <View style={{ flex: 1, justifyContent: "center" }}>
        {devices.length === 0 ? (
          <Animated.View
            key="loader"
            exiting={FadeOutUp.duration(DURATION_FAST).reduceMotion(
              ReduceMotion.Never
            )}
            style={{ gap: 4 }}
          >
            <Text
              variant="medium"
              style={{ fontSize: 18, textAlign: "center" }}
            >
              Looking for Ledger devices...
            </Text>
            <Text
              variant="medium"
              colorToken="textSecondary"
              style={{ fontSize: 14, textAlign: "center" }}
            >
              Make sure the device is powered{"\n"}on and unlocked
            </Text>
          </Animated.View>
        ) : (
          <Animated.View
            entering={FadeInDown.duration(DURATION_FAST).reduceMotion(
              ReduceMotion.Never
            )}
            style={{
              gap: 8,
            }}
          >
            {devices.map((device) => (
              <SelectOption
                key={device.id}
                text={device.localName || device.name}
                icon={
                  <LedgerIcon
                    colorToken={"textSecondary"}
                    size={23}
                    rectSize={28}
                  />
                }
                onPress={() => {
                  Haptics.selectionAsync();
                  onSelect(device.id);
                }}
              />
            ))}
          </Animated.View>
        )}
      </View>
    </View>
  );
}

export type LedgerAccount = {
  address: Address;
  derivationPath: string;
  walletKey: Address;
};

function ChooseLedgerAddressView({
  deviceId,
  onSelect,
  onBack,
}: {
  deviceId: string;
  onSelect: (ledgerAccount: LedgerAccount) => void;
  onBack: () => void;
}) {
  const queryClient = useQueryClient();
  const { device, loading: deviceLoading, error } = useLedgerDevice(deviceId);
  const [accounts, setAccounts] = useState<LedgerAccount[] | null>(null);
  const [selectedAccount, setSelectedAccount] = useState<LedgerAccount | null>(
    null
  );

  useEffect(() => {
    //todo sync amount of accounts we check with adding Ledger flow
    device?.fetchAccounts(0, 10).then(async (accounts) => {
      const foundAccounts = await Promise.all(
        accounts.map(async (account) => {
          const { walletKey } = await loadWalletByMember(queryClient, {
            memberKey: account.address,
          });

          if (walletKey === null) {
            return null;
          }

          return { ...account, walletKey };
        })
      );

      const validAccounts = foundAccounts.filter(Boolean) as LedgerAccount[];
      setAccounts(validAccounts);
      validAccounts && setSelectedAccount(validAccounts[0]);
    });
  }, [device]);

  const loading = deviceLoading || accounts === null;

  const placeholderMessage =
    typeof error === "string"
      ? LedgerAccountError.toString(error)
      : "Loading...";

  const accountsNotFound = accounts?.length === 0;

  if (accountsNotFound) {
    return (
      <View style={{ minHeight: 140, maxHeight: 260, gap: 20 }}>
        <Animated.View
          key="accounts-not-found"
          entering={FadeInDown.duration(DURATION_FAST).reduceMotion(
            ReduceMotion.Never
          )}
          exiting={FadeOutUp.duration(DURATION_FAST).reduceMotion(
            ReduceMotion.Never
          )}
          style={{ flex: 1, justifyContent: "center" }}
        >
          <Text
            variant="medium"
            colorToken="textSecondary"
            style={{ fontSize: 18 }}
          >
            There are no accounts{"\n"}associated with Fuse wallets
          </Text>
        </Animated.View>
        <Button
          variant="primary"
          onPress={() => {
            Haptics.selectionAsync();
            onBack();
          }}
        >
          Try another device
        </Button>
      </View>
    );
  }

  return (
    <View style={{ minHeight: 220, maxHeight: 260, gap: 20 }}>
      {error || loading ? (
        <Animated.View
          key={placeholderMessage}
          entering={FadeInDown.duration(DURATION_FAST).reduceMotion(
            ReduceMotion.Never
          )}
          exiting={FadeOutUp.duration(DURATION_FAST).reduceMotion(
            ReduceMotion.Never
          )}
          style={{ flex: 1, justifyContent: "center", gap: 4 }}
        >
          <Text variant="medium" style={{ fontSize: 18, textAlign: "center" }}>
            {placeholderMessage}
          </Text>
          <Text
            variant="medium"
            colorToken="textSecondary"
            style={{ fontSize: 14, textAlign: "center" }}
          >
            {error === "locked" || loading
              ? "Make sure the device is powered\non and unlocked"
              : error === "unknown_app"
                ? "Open the Solana App on the Ledger"
                : ""}
          </Text>
        </Animated.View>
      ) : (
        <Animated.View
          key="accounts"
          entering={FadeInDown.duration(DURATION_FAST).reduceMotion(
            ReduceMotion.Never
          )}
          exiting={FadeOutUp.duration(DURATION_FAST).reduceMotion(
            ReduceMotion.Never
          )}
          style={{ flex: 1, gap: 12 }}
        >
          <Text
            variant="medium"
            colorToken="textSecondary"
            style={{ fontSize: 18 }}
          >
            We found these accounts{"\n"}associated with Fuse wallets
          </Text>
          <FlatList
            showsVerticalScrollIndicator={false}
            data={accounts}
            ItemSeparatorComponent={ListSeparator}
            renderItem={({ item: account }) => {
              return (
                <BottomModalMenuOption
                  key={account.address}
                  selected={account.address === selectedAccount?.address}
                  label={
                    <>
                      {abbreviateAddress(account.address)}
                      <Text colorToken="textSecondary" style={{ fontSize: 15 }}>
                        {"  •  "}1 Fuse wallet
                      </Text>
                    </>
                  }
                  onSelect={() => setSelectedAccount(account)}
                />
              );
            }}
          />
        </Animated.View>
      )}
      <Button
        variant="primary"
        disabled={selectedAccount === null}
        onPress={() => {
          Haptics.selectionAsync();
          onSelect(selectedAccount!);
        }}
      >
        Continue
      </Button>
    </View>
  );
}
