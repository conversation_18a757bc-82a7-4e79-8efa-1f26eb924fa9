import {
  Activity,
  RecipientBankAccount,
  TransferFees,
} from "~/services/activities";
import { DefaultActivityHeader } from "~/components/activities/details/DefaultActivityHeader";
import { TransferStatus } from "~/components/activities/TransferStatus";
import {
  ActivityDetailsTextRow,
  ActivityRowsContainer,
} from "~/components/activities/ActivityDetailsRow";
import { DashedListSeparator } from "~/components/ListSeparator";
import { formatFiatValue, formatUsdValue } from "@squads/utils/numberFormats";
import { OnchainFees } from "~/components/activities/OnchainFees";
import { View } from "~/components/Themed";
import { ExecuteOnchainTransaction } from "~/components/activities/OnchainTransactions";

export function SendFiat({
  activity,
  showDetails,
}: {
  activity: Activity & { details: { type: "sendFiat" } };
  showDetails: boolean;
}) {
  return (
    <View gap={24}>
      <DefaultActivityHeader activity={activity} />

      <TransferStatus activity={activity} />

      <ActivityRowsContainer>
        <ActivityRows activity={activity} />

        {showDetails && (
          <>
            <DashedListSeparator />
            <ActivityDetailedRows activity={activity} />
          </>
        )}
      </ActivityRowsContainer>
    </View>
  );
}

function ActivityRows({
  activity,
}: {
  activity: Activity & { details: { type: "sendFiat" } };
}) {
  const destination = activity.details.destination;

  const source = activity.details.source;
  const amountFloat = source.amount / 10 ** source.decimals;

  const totalFees = TransferFees.totalFees(activity.details.fees);
  const bankReceivesUsd = amountFloat - totalFees;

  const isEur = destination.currency === "eur";
  const bankReceives =
    isEur && destination.exchangeRate
      ? formatFiatValue(bankReceivesUsd * destination.exchangeRate, {
          currency: "EUR",
        })
      : formatUsdValue(bankReceivesUsd);

  return (
    <>
      <ActivityDetailsTextRow
        label="To"
        value={RecipientBankAccount.getLabel(destination.bankAccount)}
      />

      <ActivityDetailsTextRow
        label="Account name"
        value={destination.accountOwnerName}
      />

      <ActivityDetailsTextRow
        label="Transfer fees"
        value={formatUsdValue(totalFees)}
      />

      <ActivityDetailsTextRow
        label={isEur ? "Bank receives (est.)" : "Bank receives"}
        value={bankReceives}
      />
    </>
  );
}

function ActivityDetailedRows({
  activity,
}: {
  activity: Activity & { details: { type: "sendFiat" } };
}) {
  const destination = activity.details.destination;
  const paymentRail = destination.paymentRail;

  return (
    <>
      <ActivityDetailsTextRow
        label="Payment rail"
        value={paymentRail === "sepa" ? "SEPA" : paymentRail}
      />
      <ExecuteOnchainTransaction transactions={activity.transactions} />
      <OnchainFees activity={activity} />
    </>
  );
}
