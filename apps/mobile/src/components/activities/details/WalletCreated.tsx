import { Text, View } from "~/components/Themed";
import { ActivityRowsContainer } from "~/components/activities/ActivityDetailsRow";
import { OnchainTransactions } from "~/components/activities/OnchainTransactions";
import { AlwaysSponsoredOnchainFeesRow } from "~/components/activities/OnchainFees";
import { Activity } from "~/services/activities";
import { WalletCreatedImage } from "~/components/icons/home/<USER>";
import { ActivityTimestamp } from "~/components/activities/ActivityTimestamp";

export function WalletCreated({
  activity,
}: {
  activity: Activity & { details: { type: "walletCreated" } };
}) {
  return (
    <View gap={24}>
      <View style={{ alignItems: "center" }}>
        <WalletCreatedImage />
        <View gap={4} style={{ alignItems: "center" }}>
          <Text variant="semibold" size={18}>
            Wallet created
          </Text>
          <ActivityTimestamp activity={activity} />
        </View>
      </View>

      <ActivityRowsContainer>
        <OnchainTransactions transactions={activity.transactions} />
        <AlwaysSponsoredOnchainFeesRow />
      </ActivityRowsContainer>
    </View>
  );
}
