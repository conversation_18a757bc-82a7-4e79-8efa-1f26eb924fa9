import { Text, View } from "~/components/Themed";
import { ActivityRowsContainer } from "~/components/activities/ActivityDetailsRow";
import { OnchainTransactions } from "~/components/activities/OnchainTransactions";
import { OnchainFees } from "~/components/activities/OnchainFees";
import { Activity } from "~/services/activities";
import { CardCreatedIcon } from "~/components/icons/home/<USER>";
import { ActivityTimestamp } from "~/components/activities/ActivityTimestamp";

export function CardCreate({
  activity,
}: {
  activity: Activity & { details: { type: "cardCreate" } };
}) {
  return (
    <View gap={24}>
      <View style={{ alignItems: "center" }}>
        <CardCreatedIcon />
        <View gap={4} style={{ alignItems: "center" }}>
          <Text variant="semibold" size={18}>
            Card activated
          </Text>
          <ActivityTimestamp activity={activity} />
        </View>
      </View>

      <ActivityRowsContainer>
        <OnchainTransactions transactions={activity.transactions} />
        <OnchainFees activity={activity} />
      </ActivityRowsContainer>
    </View>
  );
}
