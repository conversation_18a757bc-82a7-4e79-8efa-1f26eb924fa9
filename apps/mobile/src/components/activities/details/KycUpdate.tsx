import { Text, View } from "~/components/Themed";
import { Activity } from "~/services/activities";
import { KycUpdateImage } from "~/components/icons/home/<USER>";
import { ActivityTimestamp } from "~/components/activities/ActivityTimestamp";
import {
  ActivityDetailsTextRow,
  ActivityRowsContainer,
} from "~/components/activities/ActivityDetailsRow";
import { StampClockIcon } from "~/components/icons/StampClockIcon";
import { StampCheckmarkIcon } from "~/components/icons/StampCheckmarkIcon";
import { StampCrossIcon } from "~/components/icons/StampCrossIcon";
import { IconWrapper } from "~/components/IconWrapper";

export function KycUpdate({
  activity,
}: {
  activity: Activity & {
    details: { type: "kycStarted" | "kycRejected" | "kycApproved" };
  };
}) {
  const label = {
    kycStarted: "Verification Pending",
    kycRejected: "Verification Failed",
    kycApproved: "Verification Successful",
  } as const;

  const image = {
    kycStarted: <StampClockIcon />,
    kycRejected: <StampCrossIcon />,
    kycApproved: <StampCheckmarkIcon />,
  } as const;

  return (
    <View gap={24}>
      <View style={{ alignItems: "center" }}>
        <View>
          <KycUpdateImage />
          <View style={{ position: "absolute", top: 20, right: 20 }}>
            <IconWrapper size={20}>{image[activity.details.type]}</IconWrapper>
          </View>
        </View>
        <View gap={4} style={{ alignItems: "center" }}>
          <Text variant="semibold" size={18}>
            {label[activity.details.type]}
          </Text>
          <ActivityTimestamp activity={activity} />
        </View>
      </View>

      <ActivityRowsContainer>
        <ActivityDetailsTextRow label="Provider" value="Bridge" />
      </ActivityRowsContainer>
    </View>
  );
}
