import { Activity, TransferFees } from "~/services/activities";
import { DefaultActivityHeader } from "~/components/activities/details/DefaultActivityHeader";
import { TransferStatus } from "~/components/activities/TransferStatus";
import {
  ActivityDetailsTextRow,
  ActivityRowsContainer,
} from "~/components/activities/ActivityDetailsRow";
import { formatFiatValue, formatUsdValue } from "@squads/utils/numberFormats";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import { AlwaysSponsoredOnchainFeesRow } from "~/components/activities/OnchainFees";
import { DashedListSeparator } from "~/components/ListSeparator";
import { View } from "~/components/Themed";
import { ExecuteOnchainTransaction } from "~/components/activities/OnchainTransactions";

export function VirtualAccountReceive({
  activity,
  showDetails,
}: {
  activity: Activity & { details: { type: "virtualAccountReceive" } };
  showDetails: boolean;
}) {
  return (
    <View gap={24}>
      <DefaultActivityHeader activity={activity} />

      <TransferStatus activity={activity} />

      <ActivityRowsContainer>
        <ActivityRows activity={activity} />

        {showDetails && (
          <>
            <DashedListSeparator />
            <ActivityDetailedRows activity={activity} />
          </>
        )}
      </ActivityRowsContainer>
    </View>
  );
}

function ActivityRows({
  activity,
}: {
  activity: Activity & { details: { type: "virtualAccountReceive" } };
}) {
  const source = activity.details.source;

  if (source.payment_rail === "wire") {
    return (
      <>
        <ActivityDetailsTextRow
          label={"From"}
          value={source.bankBeneficiaryName}
        />
        <ActivityDetailsTextRow label={"Bank name"} value={source.bankName} />
        <ActivityDetailsTextRow
          label={"IMAD"}
          value={source.imad}
          copyValue={source.imad}
        />
        <ActivityDetailsTextRow
          label={"Transfer fees"}
          value={formatUsdValue(TransferFees.totalFees(activity.details.fees))}
        />
      </>
    );
  }
  if (source.payment_rail === "achPush") {
    return (
      <>
        <ActivityDetailsTextRow label={"From"} value={source.senderName} />
        <ActivityDetailsTextRow
          label={"Trace number"}
          value={source.traceNumber}
          copyValue={source.traceNumber}
        />
        <ActivityDetailsTextRow
          label={"Transfer fees"}
          value={formatUsdValue(TransferFees.totalFees(activity.details.fees))}
        />
      </>
    );
  } else if (source.payment_rail === "sepa") {
    return (
      <>
        <ActivityDetailsTextRow label={"From"} value={source.senderName} />
        <ActivityDetailsTextRow
          label={"IBAN"}
          value={`· · · ${source.ibanLast4}`}
        />
        <ActivityDetailsTextRow
          label={"Trace number"}
          value={source.uetr}
          copyValue={source.uetr}
        />
        {source.exchangeRate && (
          <ActivityDetailsTextRow
            label={"Exchange rate"}
            value={formatFiatValue(source.exchangeRate) + " USD / EUR"}
          />
        )}
        <ActivityDetailsTextRow
          label={"Transfer fees"}
          value={formatUsdValue(TransferFees.totalFees(activity.details.fees))}
        />
      </>
    );
  } else {
    source satisfies never;
    return null;
  }
}

function ActivityDetailedRows({
  activity,
}: {
  activity: Activity & { details: { type: "virtualAccountReceive" } };
}) {
  const paymentRail = activity.details.source.payment_rail;

  const paymentRailLabel =
    paymentRail === "sepa"
      ? "SEPA"
      : paymentRail === "wire"
        ? "Wire"
        : paymentRail === "achPush"
          ? "ACH Push"
          : paymentRail;

  return (
    <>
      <ActivityDetailsTextRow label={"Payment rail"} value={paymentRailLabel} />
      <ExecuteOnchainTransaction transactions={activity.transactions} />
      <AlwaysSponsoredOnchainFeesRow />
    </>
  );
}
