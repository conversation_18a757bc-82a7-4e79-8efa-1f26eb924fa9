import { Activity } from "~/services/activities";
import {
  ActivityDetailsTextRow,
  ActivityRowsContainer,
} from "~/components/activities/ActivityDetailsRow";
import { Text, useColor, View } from "~/components/Themed";
import { ActivityTimestamp } from "~/components/activities/ActivityTimestamp";
import { SwapIcon } from "~/components/icons/SwapIcon";
import { formatTokenAmount, formatUsdValue } from "@squads/utils/numberFormats";
import { Address } from "@squads/models/solana";
import { useSuspenseToken } from "~/state/tokens";
import { Flex, Row } from "~/components/Grid";
import { OnchainTransactions } from "~/components/activities/OnchainTransactions";
import { OnchainFees } from "~/components/activities/OnchainFees";
import { StyleSheet } from "react-native";
import { DashedListSeparator } from "~/components/ListSeparator";
import { Icon } from "~/components/Icon";
import { CoinLogo } from "~/components/CoinLogo";
import invariant from "invariant";
import { TokenFeeRow } from "~/components/activities/TokenFeeRow";

export function Swap({
  activity,
}: {
  activity: Activity & { details: { type: "swap" } };
}) {
  return (
    <View gap={24}>
      <Header activity={activity} />

      <SwapOverview activity={activity} />

      <ActivityRowsContainer>
        <ActivityRows activity={activity} />
      </ActivityRowsContainer>
    </View>
  );
}

function Header({
  activity,
}: {
  activity: Activity & { details: { type: "swap" } };
}) {
  return (
    <View style={{ alignItems: "center", gap: 12 }}>
      <SwapIcon size={54} />
      <View style={{ alignItems: "center", gap: 4 }}>
        <Text variant="semibold" size={18} colorToken="text">
          Swap
        </Text>
        <ActivityTimestamp activity={activity} />
      </View>
    </View>
  );
}

function SwapOverview({
  activity,
}: {
  activity: Activity & { details: { type: "swap" } };
}) {
  const border = useColor("border");
  return (
    <View
      style={{
        paddingVertical: 16,
        paddingHorizontal: 16,
        marginHorizontal: -16,
        borderColor: border,
        borderWidth: StyleSheet.hairlineWidth,
        borderRadius: 16,
        borderCurve: "continuous",
        gap: 10,
      }}
    >
      <SwapOverviewRow
        mint={activity.details.inputMint}
        amount={activity.details.inAmount}
        price={activity.details.inUsdPrice}
      />

      <Row flex gap={10}>
        <Flex>
          <DashedListSeparator />
        </Flex>
        <Icon
          name="arrow.down.circle.fill"
          weight="bold"
          size={12}
          color="#aaa"
        />
        <Flex>
          <DashedListSeparator />
        </Flex>
      </Row>

      <SwapOverviewRow
        mint={activity.details.outputMint}
        amount={activity.details.outAmount}
        price={activity.details.outUsdPrice}
      />
    </View>
  );
}

function SwapOverviewRow({
  mint,
  amount,
  price,
}: {
  mint: Address;
  amount: number;
  price: number | null;
}) {
  const token = useSuspenseToken({ mint });
  invariant(token, "token not found");

  return (
    <View gap={2}>
      <Row justify="space-between">
        <Row gap={4}>
          <CoinLogo mint={mint} size={14} />
          <Text variant="medium" size={12}>
            {token.symbol}
          </Text>
        </Row>
        <Text variant="medium" size={12} colorToken="textSecondary">
          Price
        </Text>
      </Row>
      <Row justify="space-between">
        <Text variant="semibold" size={18}>
          {formatTokenAmount(amount / 10 ** token.decimals, undefined, {
            filterSmallAmounts: true,
          })}
        </Text>
        <Text variant="semibold" size={12} colorToken="textSecondary">
          {price
            ? formatUsdValue(price, { maximumFractionDigits: "auto" })
            : "--"}
        </Text>
      </Row>
    </View>
  );
}

function ActivityRows({
  activity,
}: {
  activity: Activity & { details: { type: "swap" } };
}) {
  const slippage =
    activity.details.type === "swap" && activity.details.slippageBps
      ? activity.details.slippageBps / 100
      : null;

  return (
    <>
      {slippage && (
        <ActivityDetailsTextRow
          label="Slippage"
          value={`${slippage.toFixed(2)}%`}
        />
      )}

      <TokenFeeRow
        mint={activity.details.inputMint}
        feeAmount={activity.details.inputTokenFee?.amount ?? 0}
      />
      <TokenFeeRow
        mint={activity.details.outputMint}
        feeAmount={activity.details.outputTokenFee?.amount ?? 0}
      />

      <OnchainTransactions transactions={activity.transactions} />
      <OnchainFees activity={activity} />
    </>
  );
}
