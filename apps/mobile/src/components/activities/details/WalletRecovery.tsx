import { Text, View } from "~/components/Themed";
import { ActivityRowsContainer } from "~/components/activities/ActivityDetailsRow";
import { OnchainTransactions } from "~/components/activities/OnchainTransactions";
import { OnchainFees } from "~/components/activities/OnchainFees";
import { Activity } from "~/services/activities";
import { WalletRecoveryImage } from "~/components/icons/home/<USER>";
import { ActivityTimestamp } from "~/components/activities/ActivityTimestamp";
import { SingleStatusView } from "~/components/activities/TransferStatus";

export function WalletRecovery({
  activity,
}: {
  activity: Activity & { details: { type: "walletRecovery" } };
}) {
  return (
    <View gap={24}>
      <View style={{ alignItems: "center" }}>
        <WalletRecoveryImage />
        <View gap={4} style={{ alignItems: "center" }}>
          <Text variant="semibold" size={18}>
            Wallet recovery
          </Text>
          <ActivityTimestamp activity={activity} />
        </View>
      </View>

      <Status activity={activity} />

      <ActivityRowsContainer>
        <OnchainTransactions transactions={activity.transactions} />
        <OnchainFees activity={activity} />
      </ActivityRowsContainer>
    </View>
  );
}

function Status({ activity }: { activity: Activity }) {
  const status = activity.status.type;

  if (status === "unknown") {
    return null;
  }

  const statusView = {
    confirmed: {
      label: "Completed",
      icon: "checkmark.circle.fill",
      color: "green",
    } as const,
    pending: {
      label: "In progress",
      icon: "clock.fill",
      color: "textSecondary",
    } as const,
    cancelled: {
      label: "Cancelled",
      icon: "xmark.circle.fill",
      color: "red",
    } as const,
    failed: {
      label: "Cancelled",
      icon: "xmark.circle.fill",
      color: "red",
    } as const,
  } as const;

  return (
    <SingleStatusView
      label={statusView[status].label}
      icon={statusView[status].icon}
      color={statusView[status].color}
    />
  );
}
