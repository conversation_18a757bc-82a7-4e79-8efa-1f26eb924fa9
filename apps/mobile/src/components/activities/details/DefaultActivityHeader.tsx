import { Text, TextColor, View } from "~/components/Themed";
import {
  Activity,
  DriftEarnDepositDetails,
  KaminoEarnDepositDetails,
  LuloProtectedEarnDepositDetails,
} from "~/services/activities";
import { Row } from "~/components/Grid";
import { formatSolAmount } from "~/utils/tokens";
import { useTokenAmountString } from "~/state/tokens";
import { RecoveryKey } from "~/services/wallets";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import { formatTokenAmount, formatUsdValue } from "@squads/utils/numberFormats";
import { LAMPORTS_PER_SOL } from "@solana/web3.js";
import { mints } from "~/constants/tokens";
import { useBalanceSettings } from "~/state/balanceSettings";
import { ActivityIcon } from "~/components/activities/ActivityIcon";
import { ActivityTimestamp } from "~/components/activities/ActivityTimestamp";

export function DefaultActivityHeader({ activity }: { activity: Activity }) {
  return (
    <View style={{ alignItems: "center", gap: 12 }}>
      <ActivityIcon activity={activity} size={54} />
      <View style={{ alignItems: "center", gap: 4 }}>
        <ActivityLabel activity={activity} />
        <ActivityAmount activity={activity} />
        <ActivityTimestamp activity={activity} />
      </View>
    </View>
  );
}

function ActivityLabel({ activity }: { activity: Activity }) {
  let name;
  let color: TextColor = "textSecondary";

  switch (activity.details.type) {
    case "walletCreated":
      name = "Wallet Created";
      color = "text";
      break;
    case "sendSol":
      name = "Sent";
      break;
    case "sendToken":
      name = "Sent";
      break;
    case "updateRecoveryKeys":
      const label =
        activity.details.details.type === "addKey"
          ? "Added Recovery Key"
          : activity.details.details.type === "changeKey"
            ? "Updated Recovery Key"
            : activity.details.details.type === "removeKey"
              ? "Removed Recovery Key"
              : (activity.details.details satisfies never);

      name = label;
      break;
    case "updateCloudKey":
      name =
        activity.details.newKey.details.type === "iCloud"
          ? "Changed to Cloud Key"
          : "Changed 2FA Key";
      break;
    case "receiveToken":
      name = "Received";
      break;
    case "receiveSol":
      name = "Received";
      break;
    case "walletRecovery":
      name = "Wallet Recovery";
      break;
    case "swap":
      name = "Swap";
      break;
    case "marinadeNativeStake":
      name = "Staked";
      break;
    case "marinadeNativeDeactivate":
      name = "Deactivated";
      break;
    case "marinadeNativeWithdraw":
      name = "Withdrawn";
      break;
    case "unknown":
      name = "Unknown activity";
      break;
    case "stake":
      name =
        activity.details.details.type === "deposit"
          ? "Staked"
          : activity.details.details.type === "deactivate"
            ? "Unstaked"
            : activity.details.details.type === "withdraw"
              ? "Withdrawn"
              : (activity.details.details satisfies never);
      break;

    case "depositLiquidStake":
      name = "Staked";
      break;

    case "liquidStakeWithdraw":
      name = "Unstaked";
      break;

    case "receiveNft":
      name = "Received";
      break;

    case "sendNft":
      name = "Sent";
      break;

    case "spendingLimit":
      name =
        activity.details.details.type === "add"
          ? "Created Spending Limit"
          : "Removed Spending Limit";
      break;

    case "spendingLimitTransfer":
      name = "Spending Limit Transfer";
      break;

    case "spendingLimitUse":
      name = "Sent with Spending Limit";
      break;

    case "payFeesWithFuseSolEnable":
      name = "fuseSOL top-up enabled";
      color = "text";
      break;

    case "payFeesWithFuseSolDisable":
      name = "fuseSOL top-up disabled";
      color = "text";
      break;

    case "relayerTopUp":
      name = "Paymaster top-up";
      break;

    case "relayerWithdraw":
      name = "Paymaster Withdraw";
      break;

    case "driftEarnDeposit":
      name = "Drift Earn Deposit";
      break;

    case "driftEarnWithdraw":
      name = "Drift Earn Withdraw";
      break;

    case "kycStarted":
      name = "Verification Pending";
      color = "text";
      break;

    case "kycApproved":
      name = "Verification Successful";
      color = "text";
      break;

    case "kycRejected":
      name = "Verification Failed";
      color = "text";
      break;

    case "virtualAccountReceive":
      if (activity.details.status === "microdeposit") {
        name = "Microdeposit";
      } else {
        name = "USDC Deposit";
      }
      break;

    case "sendFiat":
      name = "Bank transfer";
      break;

    case "cardCreate":
      name = "Card Issued";
      color = "text";
      break;

    case "cardTopUp":
      name = "Card Deposit";
      break;

    case "cardWithdraw":
      name = "Card Withdrawal";
      break;

    case "cardSLChange":
      name = "Updated Card Daily limit";
      color = "text";
      break;

    case "luloProtectedEarnDeposit":
      name = "Lulo Earn Deposit";
      break;

    case "luloProtectedEarnWithdraw":
      name = "Lulo Earn Withdraw";
      break;

    case "kaminoEarnDeposit":
      name = "Kamino Earn Deposit";
      break;

    case "kaminoEarnWithdraw":
      name = "Kamino Earn Withdraw";
      break;

    case "cardReferralReward":
      name = "Referral Bonus";
      break;

    default:
      activity.details satisfies never;
  }

  return (
    <Row justify="center">
      <Text
        variant="semibold"
        size={14}
        colorToken={color}
        style={{ textAlign: "center" }}
      >
        {name}
      </Text>
    </Row>
  );
}

function ActivityAmount({ activity }: { activity: Activity }) {
  let amountText;
  switch (activity.details.type) {
    case "walletCreated":
      amountText = null;
      break;
    case "sendSol":
      amountText = formatSolAmount(activity.details.lamports);
      break;
    case "sendToken":
      amountText = useTokenAmountString({
        mint: activity.details.tokenMint,
        amount: activity.details.amount,
        filterSmallAmounts: true,
      });
      break;
    case "updateRecoveryKeys":
      const keyValue =
        activity.details.details.type === "addKey"
          ? RecoveryKey.getStringValue(activity.details.details.newKey)
          : activity.details.details.type === "changeKey"
            ? RecoveryKey.getStringValue(activity.details.details.newKey)
            : activity.details.details.type === "removeKey"
              ? RecoveryKey.getStringValue(activity.details.details.oldKey)
              : (activity.details.details satisfies never);

      amountText = keyValue;
      break;
    case "updateCloudKey":
      amountText = abbreviateAddress(activity.details.newKey.address);
      break;
    case "receiveToken":
      amountText = useTokenAmountString({
        mint: activity.details.tokenMint,
        amount: activity.details.amount,
        filterSmallAmounts: true,
      });
      break;
    case "receiveSol":
      amountText = formatSolAmount(activity.details.lamports);
      break;
    case "walletRecovery":
      amountText = null;
      break;
    case "swap":
      const input = useTokenAmountString({
        mint: activity.details.inputMint,
        amount: activity.details.inAmount,
        filterSmallAmounts: true,
      });
      const output = useTokenAmountString({
        mint: activity.details.outputMint,
        amount: activity.details.outAmount,
        filterSmallAmounts: true,
      });
      amountText = `${input} → ${output}`;
      break;
    case "marinadeNativeStake":
    case "marinadeNativeDeactivate":
    case "marinadeNativeWithdraw":
      amountText = formatSolAmount(activity.details.lamports);
      amountText = formatSolAmount(activity.details.lamports);
      break;
    case "stake":
      amountText = formatSolAmount(activity.details.details.lamports);
      break;

    case "depositLiquidStake":
      amountText = formatSolAmount(activity.details.lamports);
      break;

    case "liquidStakeWithdraw":
      amountText = useTokenAmountString({
        mint: activity.details.lstMint,
        amount: activity.details.amount,
        filterSmallAmounts: true,
      });
      break;

    case "sendNft":
    case "receiveNft":
      amountText = activity.details.metadata?.name ?? "Unknown NFT";
      break;

    case "spendingLimit":
      amountText =
        activity.details.details.mint === null
          ? formatSolAmount(activity.details.details.amount)
          : useTokenAmountString({
              mint: activity.details.details.mint,
              amount: activity.details.details.amount,
              filterSmallAmounts: true,
            });
      break;

    case "spendingLimitTransfer":
      amountText = `${abbreviateAddress(activity.details.newMember)}`;
      break;

    case "spendingLimitUse":
      amountText =
        activity.details.mint === null
          ? formatSolAmount(activity.details.amount)
          : useTokenAmountString({
              mint: activity.details.mint,
              amount: activity.details.amount,
              filterSmallAmounts: true,
            });
      break;

    case "payFeesWithFuseSolEnable": {
      amountText = null;
      break;
    }

    case "payFeesWithFuseSolDisable": {
      amountText = null;
      break;
    }

    case "unknown":
      amountText = null;
      break;

    case "relayerTopUp":
      amountText = null;
      break;

    case "relayerWithdraw":
      amountText = formatTokenAmount(
        activity.details.amount / LAMPORTS_PER_SOL,
        "SOL",
        { filterSmallAmounts: true }
      );
      break;

    case "driftEarnDeposit":
    case "driftEarnWithdraw":
      amountText = useTokenAmountString({
        mint: DriftEarnDepositDetails.mint(activity.details.deposit),
        amount: activity.details.deposit.amount,
        filterSmallAmounts: true,
      });
      break;

    case "kycStarted":
    case "kycApproved":
    case "kycRejected":
      amountText = null;
      break;

    case "virtualAccountReceive":
      amountText =
        formatUsdValue(activity.details.amount, {
          renderCurrency: false,
        }) + " USDC";
      break;

    case "sendFiat":
      amountText = useTokenAmountString({
        mint: activity.details.source.mint,
        amount: activity.details.source.amount,
        filterSmallAmounts: true,
      });
      break;

    case "cardCreate":
    case "cardSLChange":
      amountText = null;
      break;

    case "cardWithdraw":
    case "cardTopUp":
      amountText = useTokenAmountString({
        mint: mints.usdc,
        amount: activity.details.amount,
        filterSmallAmounts: true,
      });
      break;

    case "luloProtectedEarnDeposit":
      amountText = useTokenAmountString({
        mint: LuloProtectedEarnDepositDetails.mint(activity.details.deposit),
        amount: activity.details.deposit.amount,
        filterSmallAmounts: true,
      });
      break;

    case "luloProtectedEarnWithdraw":
      amountText = useTokenAmountString({
        mint: LuloProtectedEarnDepositDetails.mint(activity.details.withdrawal),
        amount: activity.details.withdrawal.amount,
        filterSmallAmounts: true,
      });
      break;

    case "kaminoEarnDeposit":
      amountText = useTokenAmountString({
        mint: KaminoEarnDepositDetails.mint(activity.details.deposit),
        amount: activity.details.deposit.amount,
        filterSmallAmounts: true,
      });
      break;

    case "kaminoEarnWithdraw":
      amountText = useTokenAmountString({
        mint: KaminoEarnDepositDetails.mint(activity.details.withdrawal),
        amount: activity.details.withdrawal.amount,
        filterSmallAmounts: true,
      });
      break;

    case "cardReferralReward":
      amountText = useTokenAmountString({
        mint: activity.details.mint,
        amount: activity.details.amount,
        filterSmallAmounts: true,
      });
      break;

    default:
      activity.details satisfies never;
  }

  const { displayBalance } = useBalanceSettings();

  return (
    <Text
      size={28}
      variant="bold"
      numberOfLines={1}
      adjustsFontSizeToFit={true}
    >
      {amountText ? displayBalance(amountText) : ""}
    </Text>
  );
}
