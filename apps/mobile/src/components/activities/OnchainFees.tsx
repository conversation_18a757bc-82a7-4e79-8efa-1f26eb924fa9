import { Activity } from "~/services/activities";
import { Flex, Row } from "~/components/Grid";
import { Text, View } from "~/components/Themed";
import { useRef, useState } from "react";
import {
  ActivityDetailsRow,
  ActivityDetailsTextRow,
} from "~/components/activities/ActivityDetailsRow";
import { formatSolAmount } from "~/utils/tokens";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import { AlwaysSponsoredFeesModal } from "~/components/send/AlwaysSponsoredFeesModal";
import { DropdownChevron } from "~/components/DropdownChevron";
import Animated, { FadeInDown, ReduceMotion } from "react-native-reanimated";
import { DURATION_FAST } from "~/constants/animations";
import {
  SolanaFees,
  SubscriptionPlan,
  TransactionFees,
} from "~/services/wallets";
import { BottomModalImperativeMethods } from "~/components/BottomModal";
import { FusePlusText } from "~/components/FusePlusText";
import { hapticOpenBottomTray } from "~/utils/haptics";
import { FusePlusInfoModal } from "~/components/modals/FusePlusInfoModal";

export function OnchainFees({ activity }: { activity: Activity }) {
  if (
    activity.status.type === "failed" ||
    activity.status.type === "cancelled"
  ) {
    return null;
  }

  const selfFundedFees = activity.solanaFees
    .flatMap((solanaFees) =>
      solanaFees.type === "selfFunded" ? solanaFees.fees : []
    )
    .reduce(TransactionFees.add, TransactionFees.zero());

  if (TransactionFees.total(selfFundedFees) > 0) {
    return <SelfFundedOnchainFees transactionFees={selfFundedFees} />;
  }

  const alwaysSponsoredSolanaFees = activity.solanaFees.filter(
    (solanaFees) => solanaFees.type === "alwaysSponsored"
  );

  if (alwaysSponsoredSolanaFees.length > 0) {
    return <AlwaysSponsoredOnchainFeesRow />;
  }

  const subscriptionPlanId = activity.solanaFees.find(
    (
      solanaFees
    ): solanaFees is SolanaFees & {
      type: "sponsoredBySubscription";
    } => solanaFees.type === "sponsoredBySubscription"
  )?.subscriptionUsage?.planId;

  if (subscriptionPlanId) {
    return (
      <SponsoredBySubscriptionOnchainFeesRow
        isFreePlan={SubscriptionPlan.isFree(subscriptionPlanId)}
      />
    );
  }

  const legacyTransactionFees = activity.transactions
    .flatMap((tx) => tx.fees ?? [])
    .reduce(TransactionFees.add, TransactionFees.zero());

  if (TransactionFees.total(legacyTransactionFees) > 0) {
    return <SelfFundedOnchainFees transactionFees={legacyTransactionFees} />;
  }

  return null;
}

export function SelfFundedOnchainFees({
  transactionFees,
}: {
  transactionFees: TransactionFees;
}) {
  const [open, setOpen] = useState(false);

  const totalFees = TransactionFees.total(transactionFees);

  if (totalFees === 0) {
    return null;
  }

  return (
    <View
      gap={12}
      style={{ paddingTop: open ? 4 : 0, paddingBottom: open ? 4 : 0 }}
    >
      <View>
        <ActivityDetailsRow
          label="Onchain fees"
          labelColor={open ? "text" : "textSecondary"}
        >
          <AnimatedTouchableScale
            pressedScale={0.98}
            style={{ transformOrigin: "right" }}
            onPress={() => {
              setOpen((show) => !show);
            }}
          >
            <Row gap={6}>
              <Text variant="medium">{formatSolAmount(totalFees)}</Text>
              <DropdownChevron open={open} />
            </Row>
          </AnimatedTouchableScale>
        </ActivityDetailsRow>
      </View>
      {open && <FeeRows transactionFees={transactionFees} />}
    </View>
  );
}

function FeeRows({ transactionFees }: { transactionFees: TransactionFees }) {
  const { networkFee, priorityFee, rentFee } = transactionFees;

  const fees = [
    networkFee > 0 && {
      label: "Network",
      value: formatSolAmount(networkFee, { maximumFractionDigits: 6 }),
    },
    priorityFee > 0 && {
      label: "Priority",
      value: formatSolAmount(priorityFee),
    },
    rentFee > 0 && { label: "Rent", value: formatSolAmount(rentFee) },
  ].filter(Boolean) as { label: string; value: string }[];

  return fees.map((fee, index) => (
    <Animated.View
      key={fee.label}
      entering={FadeInDown.duration(DURATION_FAST)
        .delay(index * 15)
        .reduceMotion(ReduceMotion.Never)}
    >
      <ActivityDetailsTextRow
        label={fee.label}
        value={fee.value}
        valueColor="textSecondary"
      />
    </Animated.View>
  ));
}

export function AlwaysSponsoredOnchainFeesRow() {
  const modalRef = useRef<BottomModalImperativeMethods>(null);
  return (
    <>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          gap: 12,
        }}
      >
        <Text variant="medium" colorToken="textSecondary">
          Onchain fees
        </Text>
        <Flex style={{ alignItems: "flex-end" }}>
          <AnimatedTouchableScale
            hitSlop={8}
            onPress={() => modalRef.current?.present()}
          >
            <Text variant="medium" colorToken="green">
              Sponsored
            </Text>
          </AnimatedTouchableScale>
        </Flex>
      </View>
      <AlwaysSponsoredFeesModal modalRef={modalRef} />
    </>
  );
}

export function SponsoredBySubscriptionOnchainFeesRow({
  isFreePlan,
}: {
  isFreePlan: boolean;
}) {
  const modalRef = useRef<BottomModalImperativeMethods>(null);

  return (
    <>
      <FusePlusInfoModal modalRef={modalRef} />
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          gap: 12,
        }}
      >
        <Text variant="medium" colorToken="textSecondary">
          Onchain fees
        </Text>
        <Flex style={{ alignItems: "flex-end" }}>
          {isFreePlan ? (
            <Text variant="medium" colorToken="green">
              Sponsored
            </Text>
          ) : (
            <AnimatedTouchableScale
              onPress={() => {
                hapticOpenBottomTray();
                modalRef.current?.present();
              }}
            >
              <Row gap={1}>
                <FusePlusText colorToken={"textSecondary"} />
                <Text variant="medium" colorToken="green">
                  Covered
                </Text>
              </Row>
            </AnimatedTouchableScale>
          )}
        </Flex>
      </View>
    </>
  );
}
