import { DateTime } from "luxon";
import { Text } from "~/components/Themed";
import { Activity } from "~/services/activities";

export function ActivityTimestamp({ activity }: { activity: Activity }) {
  const executeTx = activity.transactions.find(
    (tx) => tx.transactionType === "execute"
  );

  return (
    <Text variant="semibold" size={14} colorToken="textSecondary">
      {DateTime.fromSeconds(
        executeTx?.timestamp ?? activity.timestamp
      ).toLocaleString(DateTime.DATETIME_MED)}
    </Text>
  );
}
