import { Activity, ActivityTransactionType } from "~/services/activities";
import { Row } from "~/components/Grid";
import { Text, View } from "~/components/Themed";
import { useState } from "react";
import { ActivityDetailsTextRow } from "~/components/activities/ActivityDetailsRow";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import Animated, {
  FadeInDown,
  ReduceMotion,
  useAnimatedStyle,
  withTiming,
} from "react-native-reanimated";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import { DropdownChevron } from "~/components/DropdownChevron";
import { DURATION_FAST } from "~/constants/animations";

export function OnchainTransactions({
  transactions,
}: {
  transactions: Activity["transactions"];
}) {
  const [open, setOpen] = useState(false);

  const headerAnimatedStyles = useAnimatedStyle(() => {
    return {
      transform: [
        { translateY: withTiming(open ? 4 : 0, { duration: DURATION_FAST }) },
      ],
    };
  });

  const filteredTransactions = transactions.filter((tx) => {
    return tx.transactionType !== "relayerSponsorship";
  });

  if (filteredTransactions.length === 0) {
    return null;
  }

  if (filteredTransactions.length === 1) {
    const [tx] = filteredTransactions;

    return (
      <ActivityDetailsTextRow
        label="Onchain transaction"
        value={abbreviateAddress(tx.signature)}
        copyValue={tx.signature}
      />
    );
  }

  return (
    <View gap={12}>
      <Animated.View style={headerAnimatedStyles}>
        <Row flex justify="space-between" gap={8}>
          <Text colorToken={open ? "text" : "textSecondary"} variant="medium">
            Onchain transactions
          </Text>
          <AnimatedTouchableScale
            pressedScale={0.98}
            style={{
              transformOrigin: "right",
              minWidth: 80,
            }}
            onPress={() => {
              setOpen((show) => !show);
            }}
          >
            <Row
              gap={6}
              style={{ justifyContent: "flex-end", flex: 1, height: 14 }}
            >
              {!open && (
                <Text colorToken="textSecondary" variant="medium">
                  Show more
                </Text>
              )}
              <DropdownChevron open={open} />
            </Row>
          </AnimatedTouchableScale>
        </Row>
      </Animated.View>
      {open && (
        <View gap={12} style={{ paddingBottom: 4, paddingTop: 4 }}>
          {filteredTransactions.map((tx, index) => (
            <Animated.View
              key={tx.signature}
              entering={FadeInDown.duration(DURATION_FAST)
                .delay(index * 15)
                .reduceMotion(ReduceMotion.Never)}
            >
              <ActivityDetailsTextRow
                label={ActivityTransactionType.label(tx.transactionType)}
                value={abbreviateAddress(tx.signature)}
                valueColor="textSecondary"
                copyValue={tx.signature}
              />
            </Animated.View>
          ))}
        </View>
      )}
    </View>
  );
}

export function ExecuteOnchainTransaction({
  transactions,
}: {
  transactions: Activity["transactions"];
}) {
  const executeTx = transactions.find((tx) => tx.transactionType === "execute");

  if (!executeTx) {
    return null;
  }

  return (
    <ActivityDetailsTextRow
      label="Onchain transaction"
      value={abbreviateAddress(executeTx.signature)}
      copyValue={executeTx.signature}
    />
  );
}
