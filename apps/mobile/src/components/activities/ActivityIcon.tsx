import {
  Activity,
  ActivityStatus,
  DriftEarnDepositDetails,
  KaminoEarnDepositDetails,
  LuloProtectedEarnDepositDetails,
} from "~/services/activities";
import { IconWrapper } from "~/components/IconWrapper";
import { Icon } from "~/components/Icon";
import { CoinLogo, CoinLogoVariant, CoinPairLogo } from "~/components/CoinLogo";
import { KeyChangeIcon } from "~/components/icons/KeyChange";
import { MarinadeIcon } from "~/components/icons/MarinadeIcon";
import { ValidatorIcon } from "~/components/icons/ValidatorIcon";
import { mints } from "~/constants/tokens";
import { NftImage } from "~/components/NftImage";
import { BoltIcon } from "~/components/icons/BoltIcon";
import { StampClockIcon } from "~/components/icons/StampClockIcon";
import { StampCheckmarkIcon } from "~/components/icons/StampCheckmarkIcon";
import { StampCrossIcon } from "~/components/icons/StampCrossIcon";
import { View } from "~/components/Themed";
import { FlagEU } from "~/components/icons/FlagEU";
import { FlagUS } from "~/components/icons/FlagUS";
import { BankCircleIcon } from "~/components/icons/home/<USER>";

export function ActivityIcon({
  activity,
  size = 42,
}: {
  activity: Activity;
  size?: number;
}) {
  const innerIconSize = size * 0.38;
  const offset = innerIconSize / 4;

  switch (activity.details.type) {
    case "walletCreated":
      return (
        <IconWrapper backgroundColorToken="backgroundSecondary" size={size}>
          <Icon
            name="sparkles"
            size={innerIconSize}
            colorToken="textSecondary"
          />
        </IconWrapper>
      );
    case "sendSol":
      return (
        <CoinLogo size={size} mint="SOL" variant={coinVariant(activity)} />
      );
    case "sendToken":
      return (
        <CoinLogo
          size={size}
          mint={activity.details.tokenMint}
          variant={coinVariant(activity)}
        />
      );
    case "updateRecoveryKeys":
      return (
        <IconWrapper backgroundColorToken="backgroundSecondary" size={size}>
          <KeyChangeIcon
            type={
              activity.details.details.type === "removeKey" ? "remove" : "add"
            }
          />
        </IconWrapper>
      );
    case "updateCloudKey":
      return (
        <IconWrapper backgroundColorToken="backgroundSecondary" size={size}>
          <KeyChangeIcon type="add" />
        </IconWrapper>
      );
    case "receiveToken":
      return (
        <CoinLogo
          size={size}
          mint={activity.details.tokenMint}
          variant={coinVariant(activity)}
        />
      );
    case "receiveSol":
      return (
        <CoinLogo size={size} mint="SOL" variant={coinVariant(activity)} />
      );
    case "walletRecovery":
      return (
        <IconWrapper backgroundColorToken="backgroundSecondary" size={size}>
          {activity.status.type === "confirmed" ? (
            <Icon
              name="checkmark.gobackward"
              weight="medium"
              size={innerIconSize}
              colorToken="textSecondary"
              style={{ top: -1 }}
            />
          ) : activity.status.type === "cancelled" ? (
            <Icon
              name="xmark.circle"
              weight="medium"
              size={innerIconSize}
              colorToken="textSecondary"
            />
          ) : (
            <Icon
              name="gobackward"
              weight="medium"
              size={innerIconSize}
              colorToken="textSecondary"
              style={{ top: -1 }}
            />
          )}
        </IconWrapper>
      );
    case "swap":
      return (
        <CoinPairLogo
          size={size}
          variant={coinVariant(activity)}
          mint1={activity.details.inputMint}
          mint2={activity.details.outputMint}
        />
      );
    case "marinadeNativeStake":
    case "marinadeNativeDeactivate":
    case "marinadeNativeWithdraw":
      return <MarinadeIcon size={size} />;

    case "stake":
      return (
        <ValidatorIcon
          validator={activity.details.details.voteKey}
          size={size}
        />
      );

    case "depositLiquidStake":
    case "liquidStakeWithdraw":
      return (
        <CoinLogo
          size={size}
          mint={mints.fuseSol}
          variant={coinVariant(activity)}
        />
      );

    case "receiveNft":
    case "sendNft":
      return (
        <NftImage
          nft={{
            imageUrl: activity.details.metadata?.imageUrl ?? null,
            videoUrl: activity.details.metadata?.videoUrl ?? null,
          }}
          width={size}
          borderRadius={8}
        />
      );

    case "unknown":
      return (
        <IconWrapper backgroundColorToken="backgroundSecondary" size={size}>
          <Icon
            name="questionmark.circle"
            size={innerIconSize}
            colorToken="textSecondary"
          />
        </IconWrapper>
      );

    case "spendingLimit":
      return (
        <IconWrapper backgroundColorToken="backgroundSecondary" size={size}>
          <Icon
            name="clock.arrow.2.circlepath"
            size={innerIconSize}
            colorToken="textSecondary"
          />
        </IconWrapper>
      );

    case "spendingLimitTransfer":
      return (
        <IconWrapper backgroundColorToken="backgroundSecondary" size={size}>
          <Icon
            name="clock.arrow.2.circlepath"
            size={innerIconSize}
            colorToken="textSecondary"
          />
        </IconWrapper>
      );

    case "spendingLimitUse":
      return (
        <CoinLogo
          size={size}
          mint={activity.details.mint ?? "SOL"}
          variant={coinVariant(activity)}
        />
      );

    case "payFeesWithFuseSolEnable":
    case "payFeesWithFuseSolDisable":
    case "relayerTopUp":
    case "relayerWithdraw":
      return (
        <IconWrapper size={size} backgroundColorToken="backgroundSecondary">
          <BoltIcon colorToken="textSecondary" size={innerIconSize * 1.3} />
        </IconWrapper>
      );

    case "driftEarnDeposit":
    case "driftEarnWithdraw":
      return (
        <CoinLogo
          size={size}
          mint={DriftEarnDepositDetails.mint(activity.details.deposit)}
        />
      );

    case "kycStarted":
    case "kycApproved":
    case "kycRejected":
      const icon =
        activity.details.type === "kycStarted" ? (
          <StampClockIcon size={innerIconSize} />
        ) : activity.details.type === "kycApproved" ? (
          <StampCheckmarkIcon size={innerIconSize} />
        ) : activity.details.type === "kycRejected" ? (
          <StampCrossIcon size={innerIconSize} />
        ) : null;

      return (
        <IconWrapper backgroundColorToken="backgroundSecondary" size={size}>
          <Icon
            name="person.fill"
            weight="medium"
            size={innerIconSize}
            colorToken="textSecondary"
          />
          <View style={{ position: "absolute", right: -3, top: -3 }}>
            {icon}
          </View>
        </IconWrapper>
      );

    case "virtualAccountReceive":
      return (
        <View>
          <CoinLogo mint={mints.usdc} size={size} />
          <IconWrapper
            backgroundColorToken="background"
            size={innerIconSize + offset}
            style={{ position: "absolute", right: -offset, top: -offset }}
          >
            {activity.details.source.payment_rail === "sepa" ? (
              <FlagEU size={innerIconSize} />
            ) : (
              <FlagUS size={innerIconSize} />
            )}
          </IconWrapper>
        </View>
      );

    case "sendFiat":
      return (
        <View>
          <CoinLogo mint={mints.usdc} size={size} />
          <IconWrapper
            backgroundColorToken="background"
            size={innerIconSize + offset}
            style={{ position: "absolute", right: -offset, top: -offset }}
          >
            <BankCircleIcon size={innerIconSize} />
          </IconWrapper>
        </View>
      );

    case "cardCreate":
    case "cardSLChange":
      return (
        <IconWrapper backgroundColorToken="backgroundSecondary" size={size}>
          <Icon
            name="creditcard.fill"
            weight="medium"
            size={innerIconSize}
            colorToken="textSecondary"
          />
        </IconWrapper>
      );

    case "cardWithdraw":
    case "cardReferralReward":
    case "cardTopUp":
      return (
        <View>
          <CoinLogo mint={mints.usdc} size={size} />
          <IconWrapper
            backgroundColorToken="background"
            size={innerIconSize + offset}
            style={{ position: "absolute", right: -offset, top: -offset }}
          >
            <IconWrapper background="#000" size={innerIconSize}>
              <Icon
                name="creditcard.fill"
                weight="medium"
                size={innerIconSize - size * 0.24}
                colorToken="backgroundBanner"
              />
            </IconWrapper>
          </IconWrapper>
        </View>
      );

    case "luloProtectedEarnDeposit":
      return (
        <CoinLogo
          size={size}
          mint={LuloProtectedEarnDepositDetails.mint(activity.details.deposit)}
        />
      );
    case "luloProtectedEarnWithdraw":
      return (
        <CoinLogo
          size={size}
          mint={LuloProtectedEarnDepositDetails.mint(
            activity.details.withdrawal
          )}
        />
      );

    case "kaminoEarnDeposit":
      return (
        <CoinLogo
          size={size}
          mint={KaminoEarnDepositDetails.mint(activity.details.deposit)}
        />
      );
    case "kaminoEarnWithdraw":
      return (
        <CoinLogo
          size={size}
          mint={KaminoEarnDepositDetails.mint(activity.details.withdrawal)}
        />
      );

    default:
      activity.details satisfies never;
  }
}

function coinVariant(activity: {
  timestamp: number;
  status: ActivityStatus;
}): CoinLogoVariant {
  const is2MinsOld = activity.timestamp + 120 < Date.now() / 1000;

  const status =
    activity.status.type === "pending" && is2MinsOld
      ? "failed"
      : activity.status.type;

  switch (status) {
    case "confirmed":
      return "default";
    case "pending":
      return "loading";
    case "failed":
      return "error";
    case "cancelled":
      return "error";
    case "unknown":
      return "error";
    default:
      return status satisfies never;
  }
}
