import { Address } from "~/vendor/squads/models/solana";
import { useSuspenseToken } from "~/state/tokens";
import { formatTokenAmount } from "@squads/utils/numberFormats";
import { ActivityDetailsTextRow } from "~/components/activities/ActivityDetailsRow";

export function TokenFeeRow({
  feeAmount,
  mint,
}: {
  feeAmount: number;
  mint: Address;
}) {
  const token = useSuspenseToken({ mint });

  if (!token || feeAmount === 0) return null;
  return (
    <ActivityDetailsTextRow
      label={`${token.symbol} Token Fee`}
      value={`${formatTokenAmount(feeAmount / Math.pow(10, token.decimals), token.symbol)}`}
    />
  );
}
