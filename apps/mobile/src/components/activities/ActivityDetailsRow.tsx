import { Flex, Row } from "~/components/Grid";
import { Text, TextColor, View } from "~/components/Themed";
import { ReactNode } from "react";
import { CopyText } from "~/components/CopyText";
import * as Clipboard from "expo-clipboard";
import { hapticSuccess } from "~/utils/haptics";

export function ActivityRowsContainer({ children }: { children: ReactNode }) {
  return <View style={{ gap: 16 }}>{children}</View>;
}

export function ActivityDetailsRow({
  label,
  labelColor,
  children,
}: {
  label: string;
  labelColor?: TextColor;
  children?: ReactNode;
}) {
  return (
    <Row flex justify="space-between" gap={8}>
      <Flex>
        <Text colorToken={labelColor ?? "textSecondary"} variant="medium">
          {label}
        </Text>
      </Flex>
      <Flex style={{ alignItems: "flex-end" }}>{children}</Flex>
    </Row>
  );
}

export function ActivityDetailsTextRow({
  label,
  value,
  valueColor,
  copyValue,
}: {
  label: string;
  value: string;
  valueColor?: TextColor;
  copyValue?: string;
}) {
  return (
    <ActivityDetailsRow label={label}>
      {copyValue ? (
        <CopyText
          colorToken={valueColor}
          variant="medium"
          numberOfLines={1}
          style={{ flexShrink: 1 }}
          onPress={() => {
            Clipboard.setStringAsync(copyValue);
            hapticSuccess();
          }}
        >
          {value}
        </CopyText>
      ) : (
        <Text numberOfLines={1} variant="medium" colorToken={valueColor}>
          {value}
        </Text>
      )}
    </ActivityDetailsRow>
  );
}
