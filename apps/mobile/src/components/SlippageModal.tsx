import { ComponentP<PERSON>, RefObject, useState } from "react";
import { Text, useColor, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import * as Haptics from "expo-haptics";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import Animated, {
  FadeIn,
  FadeInLeft,
  FadeInRight,
  FadeInUp,
  FadeOut,
  FadeOutLeft,
  FadeOutRight,
  ReduceMotion,
} from "react-native-reanimated";
import { DURATION_FAST } from "~/constants/animations";
import { H5 } from "~/components/typography/H5";
import { useSlippageSetting } from "~/state/slippageSetting";
import { P3 } from "~/components/typography/P3";
import { ColorName } from "~/constants/Colors";
import { SegmentedControl } from "~/components/SegmentedControl";
import { TickerWithUpdatingColor } from "~/components/TickerWithUpdatingColor";
import { TransparentGradient } from "~/components/TransparentGradient";

export function SlippageModal({
  suggestedSlippageBps,
  isSlippageExceeded,
  modalRef,
}: {
  suggestedSlippageBps: number;
  isSlippageExceeded: boolean;
  modalRef: RefObject<BottomModalImperativeMethods | null>;
}) {
  const {
    slippageSetting: persistedSlippageSetting,
    setSlippageSetting: setPersistedSlippageSetting,
  } = useSlippageSetting();

  let [slippageSetting, setSlippageSetting] = useState(
    persistedSlippageSetting
  );

  const slippagePercent =
    slippageSetting.type === "manual"
      ? slippageSetting.bps / 100
      : slippageSetting.maxBps / 100;

  const suggestedSlippagePercent = suggestedSlippageBps / 100;
  const hasLowSlippage = slippagePercent < suggestedSlippagePercent;

  const borderColorError = useColor("newRed");
  const borderColorWarning = useColor("newYellow");
  const borderColorDynamic = useColor("blue");
  const borderColorManual = useColor("border");
  const borderColor = isSlippageExceeded
    ? borderColorError
    : hasLowSlippage
      ? borderColorWarning
      : slippageSetting.type === "manual"
        ? borderColorManual
        : borderColorDynamic;

  const controlLabelTextColorManual = useColor("textSecondary");
  const controlLabelTextColorDynamic = useColor("blue");
  const controlLabelTextColorWarning = useColor("newYellow");
  const controlLabelTextColorError = useColor("red");
  const controlLabelTextColor = isSlippageExceeded
    ? controlLabelTextColorError
    : hasLowSlippage
      ? controlLabelTextColorWarning
      : slippageSetting.type === "manual"
        ? controlLabelTextColorManual
        : controlLabelTextColorDynamic;

  const tickerTextColorToken: ColorName = isSlippageExceeded
    ? "newRed"
    : hasLowSlippage
      ? "newYellow"
      : slippageSetting.type === "manual"
        ? "text"
        : "blue";

  const controlSectionBackground: ColorName = isSlippageExceeded
    ? "backgroundRed"
    : hasLowSlippage
      ? "backgroundNewYellow"
      : slippageSetting.type === "manual"
        ? "backgroundSecondary"
        : "backgroundBlue";

  const buttonActiveScale = 0.9;

  return (
    <BottomModal
      modalRef={modalRef}
      modalId={"slippage-modal"}
      title={"Slippage"}
      onDismiss={() => setSlippageSetting(persistedSlippageSetting)}
      body={
        <View style={{ gap: 24, paddingBottom: 14 }}>
          <H5 colorToken={"textSecondary"}>
            This setting controls your tolerance for price slippage.{"\n\n"}If
            the estimated token price changes by more than the slippage
            tolerance, your transaction will fail.
          </H5>
          <View style={{ gap: 16 }}>
            <SegmentedControl
              options={["manual", "dynamic"] as const}
              value={slippageSetting.type}
              setValue={(value) => {
                Haptics.selectionAsync();

                if (value === "manual") {
                  setSlippageSetting({ type: "manual", bps: 50 });
                } else {
                  setSlippageSetting({ type: "dynamic", maxBps: 300 });
                }
              }}
              render={(value) => {
                return (
                  <Text variant={"semibold"} size={14}>
                    {value === "manual" ? "Manual" : "Dynamic"}
                  </Text>
                );
              }}
            />

            {/* Slippage Mode Explainer */}
            {slippageSetting.type === "manual" ? (
              <Animated.View
                key={"manual"}
                entering={FadeInRight.duration(100)}
                exiting={FadeOutRight.duration(100)}
              >
                <P3
                  colorToken="textSecondary"
                  style={{ textAlign: "center", fontSize: 14 }}
                >
                  Uses a fixed slippage{"\n"}percentage setting for all swaps
                </P3>
              </Animated.View>
            ) : (
              <Animated.View
                key={"dynamic"}
                entering={FadeInLeft.duration(100)}
                exiting={FadeOutLeft.duration(100)}
              >
                <P3
                  colorToken="textSecondary"
                  style={{ textAlign: "center", fontSize: 14 }}
                >
                  Estimates the optimal slippage{"\n"}but doesn't exceed the
                  maximum
                </P3>
              </Animated.View>
            )}

            {/* Slippage Percentage Control */}
            <View style={{ gap: 14 }}>
              <View
                background={controlSectionBackground}
                style={{
                  paddingHorizontal: 16,
                  paddingTop: 20,
                  paddingBottom: 16,
                  borderColor,
                  borderWidth: 1,
                  borderCurve: "continuous",
                  borderRadius: 24,
                  alignItems: "center",
                  gap: 10,
                }}
              >
                <Animated.View
                  key={slippageSetting.type}
                  entering={FadeIn.duration(100)}
                  exiting={FadeOut.duration(100)}
                >
                  <P3
                    colorToken="textSecondary"
                    style={{ fontSize: 14, color: controlLabelTextColor }}
                  >
                    {slippageSetting.type === "manual"
                      ? "Fixed Slippage"
                      : "Max Slippage"}
                  </P3>
                </Animated.View>

                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    gap: 20,
                    // Add a bit of padding to give more space to the ticker digits to fade out into.
                    paddingVertical: 4,
                  }}
                >
                  <Button
                    circle
                    variant={"secondary"}
                    style={{
                      view: { backgroundColor: "white", width: 48, height: 48 },
                    }}
                    size={"large"}
                    activeScale={buttonActiveScale}
                    disabled={slippagePercent === 0.1}
                    onPress={() => {
                      Haptics.selectionAsync();
                      setSlippageSetting((slippage) => {
                        const percentage =
                          slippage.type === "manual"
                            ? slippage.bps / 100
                            : slippage.maxBps / 100;

                        const step = percentage <= 0.5 ? 0.1 : 0.5;

                        const newPercentage = Math.max(0.1, percentage - step);

                        return slippage.type === "manual"
                          ? {
                              ...slippage,
                              bps: Math.round(newPercentage * 100),
                            }
                          : {
                              ...slippage,
                              maxBps: Math.round(newPercentage * 100),
                            };
                      });
                    }}
                    iconName={"minus"}
                    iconWeight={"bold"}
                  />
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "center",
                      width: 114,
                      position: "relative",
                      right: -2,
                      overflow: "hidden",
                      paddingVertical: 4,
                    }}
                  >
                    <TickerWithUpdatingColor
                      fontSize={46}
                      wholeNumbersColorToken={tickerTextColorToken}
                      decimalsColorToken={tickerTextColorToken}
                      value={new Intl.NumberFormat("en", {
                        minimumFractionDigits: 1,
                        maximumFractionDigits: 1,
                        notation: "standard",
                      }).format(slippagePercent)}
                    />
                    <Text
                      variant={"display-bold"}
                      style={{ fontSize: 46, opacity: 0.3 }}
                      colorToken={tickerTextColorToken}
                    >
                      %
                    </Text>
                    <TransparentGradient
                      size={20}
                      position={"top"}
                      colorToken={controlSectionBackground}
                    />
                  </View>
                  <Button
                    circle
                    variant={"secondary"}
                    style={{
                      view: { backgroundColor: "white", width: 48, height: 48 },
                    }}
                    size={"medium"}
                    activeScale={buttonActiveScale}
                    disabled={slippagePercent === 5}
                    onPress={() => {
                      Haptics.selectionAsync();
                      setSlippageSetting((slippage) => {
                        const percentage =
                          slippage.type === "manual"
                            ? slippage.bps / 100
                            : slippage.maxBps / 100;

                        const step = percentage < 0.5 ? 0.1 : 0.5;

                        const newPercentage = Math.min(5, percentage + step);

                        return slippage.type === "manual"
                          ? {
                              ...slippage,
                              bps: Math.round(newPercentage * 100),
                            }
                          : {
                              ...slippage,
                              maxBps: Math.round(newPercentage * 100),
                            };
                      });
                    }}
                    iconName={"plus"}
                    iconWeight={"bold"}
                  />
                </View>
              </View>
            </View>
          </View>

          {isSlippageExceeded || hasLowSlippage ? (
            <View
              style={{
                position: "absolute",
                bottom: -10,
                width: "100%",
              }}
            >
              {isSlippageExceeded ? (
                <Message
                  colorToken={"red"}
                  style={{ textAlign: "center", fontSize: 13 }}
                  variant={"medium"}
                >
                  Slippage exceeded during simulation
                </Message>
              ) : hasLowSlippage ? (
                <Message
                  colorToken={"textWarning"}
                  style={{ textAlign: "center", fontSize: 13 }}
                  variant={"medium"}
                >
                  Slippage is too low. Recommended ≥
                  {suggestedSlippagePercent.toFixed(2)}%
                </Message>
              ) : null}
            </View>
          ) : null}
        </View>
      }
      footer={
        <Button
          variant="primary"
          onPress={() => {
            Haptics.selectionAsync();
            setPersistedSlippageSetting(slippageSetting);
            modalRef.current?.close();
          }}
        >
          Save
        </Button>
      }
    />
  );
}

const AnimatedText = Animated.createAnimatedComponent(Text);

const MESSAGE_ENTERING = FadeInUp.duration(DURATION_FAST)
  .withInitialValues({ transform: [{ translateY: -10 }] })
  .reduceMotion(ReduceMotion.Never);
const MESSAGE_EXITING = FadeOut.duration(DURATION_FAST).reduceMotion(
  ReduceMotion.Never
);

function Message(props: ComponentProps<typeof Text>) {
  return (
    <AnimatedText
      key={String(props.children)}
      entering={MESSAGE_ENTERING}
      exiting={MESSAGE_EXITING}
      {...props}
    >
      {props.children}
    </AnimatedText>
  );
}
