import { PropsWithChildren } from "react";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { asyncStoragePersister, queryClient } from "~/state/queryClient";
import { ThemeProvider } from "~/components/ThemeContext";
import { ToastProvider } from "~/components/Toaster";
import { PersistQueryClientProvider } from "@tanstack/react-query-persist-client";
import { deviceQueryKey } from "~/state/deviceKey";
import * as Application from "expo-application";
import { RecoveryApiRelayer } from "~/services/magicLink";
import { PortalProvider } from "@gorhom/portal";
import { KeyboardProvider } from "react-native-keyboard-controller";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { updateId } from "expo-updates";

export function Providers({ children }: PropsWithChildren) {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      {/*<QueryClientProvider client={queryClient}>*/}
      <PersistQueryClientProvider
        client={queryClient}
        persistOptions={{
          buster: __DEV__
            ? `${Math.random()}`
            : Application.nativeApplicationVersion
              ? Application.nativeApplicationVersion +
                Application.nativeBuildVersion +
                updateId
              : undefined,
          persister: asyncStoragePersister,
          dehydrateOptions: {
            shouldDehydrateQuery: (query) => {
              // Don't persist failed queries, since js Error can't be properly serialized,
              // and it leads to empty object being thrown
              if (query.state.error) {
                return false;
              }

              // Don't persist the device key.
              return query.queryKey.at(0) !== deviceQueryKey.at(0);
            },
          },
        }}
      >
        <SafeAreaProvider>
          <ThemeProvider>
            <PortalProvider>
              <KeyboardProvider>
                <ToastProvider>
                  <RecoveryApiRelayer />
                  {children}
                </ToastProvider>
              </KeyboardProvider>
            </PortalProvider>
          </ThemeProvider>
        </SafeAreaProvider>
      </PersistQueryClientProvider>
      {/*</QueryClientProvider>*/}
    </GestureHandlerRootView>
  );
}
