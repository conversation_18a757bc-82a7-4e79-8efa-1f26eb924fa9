import { ContentSkeleton } from "~/components/Skeleton";
import { Text } from "~/components/Themed";
import { formatTokenAmount } from "@squads/utils/numberFormats";
import { TokenBalance } from "~/services/balances";
import { getMaxAvailableAmount } from "~/components/AmountDisplay";
import { TouchableScale } from "~/components/TouchableScale";
import * as Haptics from "expo-haptics";
import { Suspense } from "react";
import { FuseErrorBoundary } from "~/components/errors/FuseErrorBoundary";

//todo remove suspence as it's not needed now
export function MaxButton({
  balance,
  onMaxPressed,
}: {
  balance: TokenBalance | null;
  onMaxPressed: (maxAmount: number) => void;
}) {
  return (
    <FuseErrorBoundary FallbackComponent={MaxButtonErrorFallback}>
      <Suspense fallback={<MaxButtonSkeleton />}>
        <MaxButtonInner balance={balance} onMaxPressed={onMaxPressed} />
      </Suspense>
    </FuseErrorBoundary>
  );
}

function MaxButtonInner({
  balance,
  onMaxPressed,
}: {
  balance: TokenBalance | null;
  onMaxPressed: (maxAmount: number) => void;
}) {
  const maxAmount = balance ? getMaxAvailableAmount(balance) : 0;

  return (
    <TouchableScale
      hitSlop={6}
      onPress={() => {
        Haptics.selectionAsync();
        onMaxPressed(maxAmount);
      }}
    >
      <Text variant="medium" style={{ fontSize: 15 }}>
        MAX{" "}
        <Text variant="medium" colorToken="textSecondary">
          {formatTokenAmount(maxAmount, undefined)}
        </Text>
      </Text>
    </TouchableScale>
  );
}

function MaxButtonSkeleton() {
  return (
    <ContentSkeleton borderRadius={999}>
      <Text variant="medium" style={{ fontSize: 15 }}>
        MAX {formatTokenAmount(1_000_000, undefined)}
      </Text>
    </ContentSkeleton>
  );
}

function MaxButtonErrorFallback() {
  return (
    <Text variant="medium" style={{ fontSize: 15, opacity: 0.5 }}>
      MAX {formatTokenAmount(0, undefined)}
    </Text>
  );
}

export function RawMaxButton({
  maxAmount,
  onMaxPressed,
}: {
  maxAmount: number;
  onMaxPressed: () => void;
}) {
  return (
    <TouchableScale
      hitSlop={6}
      onPress={() => {
        Haptics.selectionAsync();
        onMaxPressed();
      }}
    >
      <Text variant="medium" style={{ fontSize: 15 }}>
        MAX{" "}
        <Text colorToken="textSecondary">
          {formatTokenAmount(maxAmount, undefined)}
        </Text>
      </Text>
    </TouchableScale>
  );
}
