import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { Button } from "~/components/Button";
import * as Haptics from "expo-haptics";
import { H5 } from "~/components/typography/H5";
import { RefObject } from "react";

export function PriceImpactWarningModal(props: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
  onConfirm: () => void;
}) {
  return (
    <BottomModal
      modalRef={props.modalRef}
      modalId={"price-impact-warning"}
      title={"High price impact"}
      iconName={"exclamationmark.triangle"}
      iconVariant="warning"
      body={
        <H5 colorToken="textSecondary">
          The price impact is over 10%, doing this swap is not recommended and
          will result in partial loss of funds. By continuing, you agree to the
          following:
          {"\n\n"}
          <H5>1. I understand that the price impact is greater than 10%</H5>
          {"\n\n"}
          <H5>
            2. I understand that this swap will result in partial loss of funds
          </H5>
        </H5>
      }
      footer={
        <Button
          variant="primary"
          onPress={() => {
            Haptics.selectionAsync();
            props.modalRef.current?.close();
            props.onConfirm();
          }}
        >
          Agree and continue
        </Button>
      }
    />
  );
}
