import { Text, View } from "~/components/Themed";
import { TouchableScale } from "~/components/TouchableScale";
import { Icon } from "~/components/Icon";
import { PropsWithChildren, ReactNode, useRef } from "react";
import { hapticLightTouch } from "~/utils/haptics";

export function NumPad({
  value,
  onChange,
  decimals,
}: {
  value: string;
  onChange: (value: string) => void;
  decimals: number;
}) {
  function updateWithHaptics(newValue: string) {
    if (value === newValue) {
      return;
    }

    hapticLightTouch();
    onChange(newValue);
  }

  function onKeyPress(key: string) {
    if (key === ".") {
      if (value.includes(".")) {
        return;
      }
      if (value === "" || value === "0") {
        updateWithHaptics("0.");
        return;
      }
    }

    if (value === "" && key === "0") {
      updateWithHaptics("0.");
      return;
    }

    if (value === "0") {
      return;
    }

    const [, decimal] = value.split(".");
    if (decimal && decimal.length >= decimals) {
      return;
    }

    if (value.length >= 20) {
      return;
    }

    const newValue = value + key;
    updateWithHaptics(newValue);
  }

  function onDeletePress() {
    if (value === "0.") {
      updateWithHaptics("");
      return;
    }
    const newValue = value.slice(0, -1);
    updateWithHaptics(newValue);
    return;
  }

  return (
    <View>
      <Row>
        {["1", "2", "3"].map((key) => (
          <Button key={key} onPress={() => onKeyPress(key)}>
            {key}
          </Button>
        ))}
      </Row>
      <Row>
        {["4", "5", "6"].map((key) => (
          <Button key={key} onPress={() => onKeyPress(key)}>
            {key}
          </Button>
        ))}
      </Row>
      <Row>
        {["7", "8", "9"].map((key) => (
          <Button key={key} onPress={() => onKeyPress(key)}>
            {key}
          </Button>
        ))}
      </Row>
      <Row>
        <Button onPress={() => onKeyPress(".")}>.</Button>
        <Button onPress={() => onKeyPress("0")}>0</Button>
        <ProgressiveButton onPress={() => onDeletePress()} />
      </Row>
    </View>
  );
}

function Row({ children }: PropsWithChildren) {
  return (
    <View
      style={{
        flexDirection: "row",
        gap: 10,
        justifyContent: "space-between",
      }}
    >
      {children}
    </View>
  );
}

function Button({
  children,
  onPress,
}: {
  children: ReactNode;
  onPress: () => void;
}) {
  return (
    <TouchableScale
      activeScale={0.9}
      onPress={onPress}
      style={{
        justifyContent: "center",
        alignItems: "center",
        padding: 16,
        flex: 1,
      }}
    >
      {typeof children === "string" ? (
        <Text variant={"semibold"} style={{ fontSize: 26 }}>
          {children}
        </Text>
      ) : (
        children
      )}
    </TouchableScale>
  );
}

const ProgressiveButton = ({ onPress }: { onPress: () => void }) => {
  const onPressRef = useRef(onPress);
  onPressRef.current = onPress;

  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const speedRef = useRef(300);
  const speedFactor = 0.5;

  const startPressHandler = () => {
    if (timerRef.current) return;
    onPressRef.current();

    const handler = () => {
      onPressRef.current();
      speedRef.current = speedRef.current * speedFactor;
      timerRef.current && clearInterval(timerRef.current);
      timerRef.current = setInterval(handler, speedRef.current);
    };

    timerRef.current = setInterval(handler, speedRef.current);
  };

  const stopPressHandler = () => {
    timerRef.current && clearInterval(timerRef.current);
    timerRef.current = null;
    speedRef.current = 300;
  };

  return (
    <TouchableScale
      activeScale={0.9}
      style={{
        justifyContent: "center",
        alignItems: "center",
        padding: 16,
        flex: 1,
      }}
      onPressIn={startPressHandler}
      onPressOut={stopPressHandler}
    >
      <Icon name="delete.left" colorToken="text" weight="medium" />
    </TouchableScale>
  );
};
