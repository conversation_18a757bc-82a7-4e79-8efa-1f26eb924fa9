import { useFonts } from "expo-font";
import { router, Stack, useNavigationContainerRef } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { useEffect, useRef, useState } from "react";
import * as Sentry from "@sentry/react-native";
import { AppState, AppStateStatus, Platform } from "react-native";
import { focusManager } from "@tanstack/react-query";
import { Providers } from "~/components/Providers";
import { DURATION_FAST } from "~/constants/animations";
import { queryClient } from "~/state/queryClient";
import {
  loadWalletByMember,
  walletByKeyQuery,
  walletByMemberQuery,
} from "~/state/wallet";
import { appConfig } from "~/state/config";
import { useReactQueryDevTools } from "@dev-plugins/react-query";
import { loadCloudKey } from "~/state/cloudKey";
import { SignWithLedgerModal } from "~/components/modals/SignWithLedgerModal";
import { SignWithEmailModal } from "~/components/modals/SignWithEmailModal";
import { authenticateDeviceKey } from "~/state/auth";
import { preloadWalletData } from "~/utils/preload";
import { AppUpdatesIndicator } from "~/components/AppUpdatesIndicator";
import { OnlineIndicator } from "~/components/OnlineIndicator";
import { registerDevMenuItems } from "expo-dev-menu";
import { loadDeviceKey } from "~/state/deviceKey";
import * as Clipboard from "expo-clipboard";
import { CloudKeyUnavailableModal } from "~/components/CloudKeyUnavailableModal";
import { FullScreenViewsHost } from "~/components/stacking/FullScreenView";
import { InactiveOverlay } from "~/components/InactiveOverlay";
import * as Notifications from "expo-notifications";
import { FONT_FAMILY_MEDIUM } from "~/components/Themed";
import { useMigrations } from "~/hooks/useMigrations";
import { useReactNavigationDevTools } from "@dev-plugins/react-navigation";
import { ReducedMotionConfig, ReduceMotion } from "react-native-reanimated";

// Catch any errors thrown by the Layout component.
export { ErrorBoundary } from "~/components/errors/RootErrorBoundary";

export const unstable_settings = {
  // Ensure that reloading on `/modal` keeps a back button present.
  initialRouteName: "index",
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();
SplashScreen.setOptions({
  duration: 400,
  fade: true,
});

function onAppStateChange(status: AppStateStatus) {
  if (Platform.OS !== "web") {
    focusManager.setFocused(status === "active");
  }
}

registerDevMenuItems([
  { name: "Playground", callback: () => router.replace("/playground") },
  { name: "Reset query cache", callback: () => queryClient.clear() },
  {
    name: "Copy wallet key",
    callback: async () => {
      const deviceKey = await loadDeviceKey(queryClient);
      const { walletKey } = await loadWalletByMember(queryClient, {
        memberKey: deviceKey.type === "success" ? deviceKey.key : null,
      });
      walletKey && (await Clipboard.setStringAsync(walletKey));
    },
  },
]);

setupPushNotifications();

// Construct a new instrumentation instance. This is needed to communicate between the integration and React
const navigationIntegration = Sentry.reactNavigationIntegration({
  enableTimeToInitialDisplay: true,
});

Sentry.init({
  dsn: process.env.EXPO_PUBLIC_SENTRY_DSN,
  environment: appConfig.environment,
  // If `true`, Sentry will try to print out useful debugging information
  // if something goes wrong with sending the event. Set it to `false` in production
  // debug: __DEV__,
  integrations: [navigationIntegration],
  sampleRate: 1,
});

export default Sentry.wrap(RootWrapper);

function RootWrapper() {
  return (
    <Providers>
      <RootLayout />
    </Providers>
  );
}

function RootLayout() {
  const navigationContainerRef = useNavigationContainerRef();
  useReactNavigationDevTools(navigationContainerRef as any);
  useReactQueryDevTools(queryClient);

  useEffect(() => {
    if (navigationContainerRef) {
      navigationIntegration.registerNavigationContainer(navigationContainerRef);
    }
  }, [navigationContainerRef]);

  // Make react-query refetch data when the App comes to focus.
  useEffect(() => {
    const subscription = AppState.addEventListener("change", onAppStateChange);
    return () => subscription.remove();
  }, []);

  const migrated = useMigrations();
  const fontsLoaded = useFontsPreload();
  const dataLoaded = useDataPreload();
  const loaded = fontsLoaded && migrated && dataLoaded;

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) return null;

  return <RootLayoutNav />;
}

function RootLayoutNav() {
  return (
    <>
      <ReducedMotionConfig mode={ReduceMotion.Never} />
      <Stack
        screenOptions={{
          headerShown: false,
          animation: "fade",
          animationDuration: DURATION_FAST,
        }}
      >
        <Stack.Screen name="index" />
      </Stack>
      <SignWithLedgerModal />
      <SignWithEmailModal />
      <AppUpdatesIndicator />
      <OnlineIndicator />
      <CloudKeyUnavailableModal />
      <InactiveOverlay />
      <FullScreenViewsHost />
    </>
  );
}

function useFontsPreload() {
  const startRef = useRef(Date.now());

  const [loaded, error] = useFonts({
    SFProDisplayRegular: require("../../assets/fonts/SFProDisplay-Regular.otf"),
    [FONT_FAMILY_MEDIUM]: require("../../assets/fonts/SFProDisplay-Medium.otf"),
    SFProDisplaySemibold: require("../../assets/fonts/SFProDisplay-Semibold.otf"),
    SFProDisplayBold: require("../../assets/fonts/SFProDisplay-Bold.otf"),
    SFProDisplayHeavy: require("../../assets/fonts/SFProDisplay-Heavy.otf"),
    SFProTextRegular: require("../../assets/fonts/SFProText-Regular.otf"),
    SFProTextMedium: require("../../assets/fonts/SFProText-Medium.otf"),
    SFProTextBold: require("../../assets/fonts/SFProText-Bold.otf"),
  });

  // Expo Router uses Error Boundaries to catch errors in the navigation tree.
  useEffect(() => {
    if (error) throw error;

    if (loaded) {
      console.debug("Loaded fonts:", Date.now() - startRef.current, "ms");
    }
  }, [error, loaded]);

  return loaded;
}

function useDataPreload() {
  const [loaded, setLoaded] = useState(false);

  useEffect(() => {
    //There's no way to prevent rq to call the suspense query function conditionally,
    //and that might trigger a suspense.
    const presetEmptyQueries = async () => {
      await queryClient.fetchQuery(walletByMemberQuery(null));
      await queryClient.fetchQuery(walletByKeyQuery(null));
    };

    const preloadCloudKey = async () => {
      const maybeCloudKey = await loadCloudKey({ queryClient });
      return maybeCloudKey.type === "success" && maybeCloudKey.key
        ? loadWalletByMember(queryClient, { memberKey: maybeCloudKey.key })
        : { walletKey: null };
    };

    Promise.race([
      Promise.allSettled([
        presetEmptyQueries(),
        preloadCloudKey(),
        authenticateDeviceKey().then((wallet) =>
          preloadWalletData({ queryClient, wallet })
        ),
      ]),
      new Promise((res) =>
        setTimeout(() => res({ type: "timeout preloading queries" }), 5_000)
      ),
    ]).finally(() => {
      console.debug("Data preloading complete.");
      setLoaded(true);
    });
  }, []);

  return loaded;
}

function setupPushNotifications() {
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldPlaySound: false,
      shouldSetBadge: false,
      shouldShowBanner: true,
      shouldShowList: true,
    }),
  });
}
