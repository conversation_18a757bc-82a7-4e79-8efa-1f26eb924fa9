import { Text, View } from "~/components/Themed";
import { H1 } from "~/components/typography/H1";
import { Button } from "~/components/Button";
import { router } from "expo-router";
import * as Haptics from "expo-haptics";
import { useRef } from "react";
import { ModalContainer } from "~/components/ModalContainer";
import { Flex } from "~/components/Grid";
import { useAddSpendingLimitState } from "~/app/unlocked/add-spending-limit/_layout";
import { prepareAction } from "~/services/wallets";
import { useActiveWallet } from "~/state/wallet";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { TimeframeModal } from "~/components/security/TimeframeModal";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { refetchSpendingLimits } from "~/state/spendingLimits";
import { Address } from "@squads/models/solana";
import { SpendingLimitPeriod } from "~/services/spendingLimits";
import invariant from "invariant";
import { Icon } from "~/components/Icon";
import { SelectOption } from "~/components/SelectOption";
import { P2 } from "~/components/typography/P2";
import { CoinLogo } from "~/components/CoinLogo";
import { P3 } from "~/components/typography/P3";
import { useTokenAmountString } from "~/state/tokens";
import { sendTransactionWithTrackingStatus } from "~/utils/sendTransactionWithTracking";
import { BottomModalImperativeMethods } from "~/components/BottomModal";
import { isClientErrorReasonType } from "~/services/utils";
import { showNotEnoughSolModal } from "~/components/modals/NotEnoughSolModal";

export default function AddSpendingLimitOverviewScreen() {
  const queryClient = useQueryClient();
  const { wallet } = useActiveWallet();

  const [state, setState] = useAddSpendingLimitState();

  const timeframeModalRef = useRef<BottomModalImperativeMethods>(null);

  const createSpendingLimitMutation = useMutation({
    mutationFn: async ({
      amount,
      mint,
      period,
    }: {
      mint: Address | null;
      amount: number;
      period: SpendingLimitPeriod;
    }) => {
      const action = await prepareAction({
        type: "spendingLimitAdd",
        vaultIndex: 0,
        member: wallet.keys.deviceKey.address,
        amount,
        mint,
        period,
      });

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );

      router.dismissTo("/unlocked/root/security");

      await sendTransactionWithTrackingStatus({
        walletKey: wallet.walletKey,
        transaction: { type: "action", signedTransactions },
        confirmationFailureMessage: `Failed to add Spending Limit`,
      }).finally(() => {
        refetchSpendingLimits(queryClient, {
          walletKey: wallet.walletKey,
          vaultKey: wallet.defaultVault,
        });
      });
    },
    onError: (error) => {
      if (isClientErrorReasonType(error, "notEnoughSol")) {
        showNotEnoughSolModal();
        return;
      }
    },
  });

  return (
    <>
      <ModalContainer>
        <Flex gap={32}>
          <H1>Spending Limit</H1>
          <Text variant={"medium"} style={{ fontSize: 18 }}>
            The limit determines the amount that can be sent without using 2FA
            key
          </Text>

          <View gap={24}>
            <View gap={12}>
              <P2>Timeframe</P2>
              <SelectOption
                text={
                  state.period ? (
                    <P3 style={{ textTransform: "capitalize" }}>
                      {state.period}
                    </P3>
                  ) : (
                    <P2 colorToken={"textSecondary"}>Select timeframe</P2>
                  )
                }
                icon={
                  <Icon
                    colorToken={"textSecondary"}
                    name={"clock"}
                    size={14}
                    weight="medium"
                    rectSize={28}
                  />
                }
                onPress={() => {
                  Haptics.selectionAsync();
                  timeframeModalRef.current?.present();
                }}
              />
            </View>
            <View gap={12}>
              <P2>Coin</P2>
              <SelectOption
                text={
                  state.token ? (
                    <P3>
                      <SpendingLimitTokenAmount token={state.token} />
                    </P3>
                  ) : (
                    <P2 colorToken={"textSecondary"}>Select coin</P2>
                  )
                }
                icon={
                  state.token ? (
                    <CoinLogo mint={state.token.mintOrSol} size={24} />
                  ) : (
                    <Icon
                      colorToken={"textSecondary"}
                      name={"plus.circle"}
                      size={14}
                      weight="medium"
                      rectSize={28}
                    />
                  )
                }
                onPress={() => {
                  Haptics.selectionAsync();
                  router.push("/unlocked/add-spending-limit/sl-select-token");
                }}
              />
            </View>
          </View>
        </Flex>

        <Button
          loadingText={"Creating"}
          onPress={async () => {
            invariant(state.token, "token is required");
            invariant(state.period, "period is required");

            Haptics.selectionAsync();
            createSpendingLimitMutation.mutate({
              amount: state.token.amount,
              mint:
                state.token.mintOrSol === "SOL" ? null : state.token.mintOrSol,
              period: state.period,
            });
          }}
          disabled={!state.token || !state.period}
          loading={createSpendingLimitMutation.isPending}
        >
          Create Spending Limit
        </Button>
      </ModalContainer>

      <TimeframeModal
        modalRef={timeframeModalRef}
        selected={state.period}
        onSelect={(period) => setState({ period })}
      />
    </>
  );
}

export function SpendingLimitTokenAmount({
  token,
}: {
  token: { mintOrSol: Address | "SOL"; amount: number };
}) {
  const tokenAmount = useTokenAmountString({
    mint: token.mintOrSol,
    amount: token.amount,
  });

  return <>{tokenAmount}</>;
}
