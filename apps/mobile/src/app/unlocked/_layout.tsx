import { Stack } from "expo-router";
import { StakeModal } from "~/components/StakeButton";
import { EditContactModal } from "~/components/EditContactModal";
import { AddContactModal } from "~/components/AddContactModal";
import { ImproveSecurityModal } from "~/components/security/ImproveSecurityModal";
import { TwoFAKeyWarningModal } from "~/components/security/2FAKeyWarningModal";
import { DepositModal } from "~/components/DepositButton";
import { CardIntroductionModal } from "~/components/card/CardIntroductionModal";
import { NotEnoughSolModal } from "~/components/modals/NotEnoughSolModal";
import { CardReferralModal } from "~/components/modals/CardReferralModal";

export default function UnlockedLayout() {
  return (
    <>
      <Stack
        screenOptions={{
          statusBarStyle: "dark",
        }}
      >
        <Stack.Screen
          name="root"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="activity"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="token/[mint]"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="nft/[id]"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="nft/folder/[collection_id]"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="nft/send"
          options={{
            headerShown: false,
            presentation: "modal",
          }}
        />
        <Stack.Screen
          name="stake"
          options={{
            headerShown: false,
            presentation: "modal",
          }}
        />
        <Stack.Screen
          name="send"
          options={{
            title: "Send assets",
            headerShown: false,
            presentation: "modal",
          }}
        />
        <Stack.Screen
          name="swap"
          options={{
            title: "Swap coins",
            headerShown: false,
            presentation: "modal",
          }}
        />
        <Stack.Screen
          name="change-recovery"
          options={{
            title: "Change Recovery Key",
            headerShown: false,
            presentation: "modal",
          }}
        />
        <Stack.Screen
          name="migrate-email-recovery"
          options={{
            title: "Migrate Recovery Key",
            headerShown: false,
            presentation: "modal",
          }}
        />
        <Stack.Screen
          name="change-cloud-key"
          options={{
            title: "Change Cloud Key",
            headerShown: false,
            presentation: "modal",
          }}
        />
        <Stack.Screen
          name="add-spending-limit"
          options={{
            title: "Add Spending Limit",
            headerShown: false,
            presentation: "modal",
          }}
        />
        <Stack.Screen
          name="settings/nfts"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="settings/address-book"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="settings/fuse-plus"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="security/export-cloud-key"
          options={{
            presentation: "modal",
            headerShown: false,
          }}
        />
        <Stack.Screen name="earn/index" options={{ headerShown: false }} />
        <Stack.Screen
          name="earn/deposit"
          options={{
            headerShown: false,
            presentation: "modal",
          }}
        />
        <Stack.Screen
          name="bridge/onboarding"
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="bridge/virtual-account"
          options={{ headerShown: false, presentation: "modal" }}
        />
        <Stack.Screen
          name="bridge/add-external-account"
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="bridge/send-fiat"
          options={{ headerShown: false, presentation: "modal" }}
        />
        <Stack.Screen name="bridge/card" options={{ headerShown: false }} />
        <Stack.Screen
          name="subscription/promo"
          options={{
            headerShown: false,
            presentation: "fullScreenModal",
            statusBarStyle: "light",
            animation: "fade",
          }}
        />
        <Stack.Screen
          name="subscription/welcome"
          options={{
            headerShown: false,
            presentation: "fullScreenModal",
            statusBarStyle: "light",
            animation: "fade",
          }}
        />
      </Stack>
      <StakeModal />
      <EditContactModal />
      <AddContactModal />
      <ImproveSecurityModal />
      <TwoFAKeyWarningModal />
      <DepositModal />
      <CardIntroductionModal />
      <NotEnoughSolModal />
      <CardReferralModal />
    </>
  );
}
