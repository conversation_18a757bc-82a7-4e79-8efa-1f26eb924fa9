import { SafeAreaView, View } from "~/components/Themed";
import {
  HEADER_HEIGHT,
  StackHeader,
} from "~/components/navigation/StackHeader";
import Animated, { useAnimatedRef } from "react-native-reanimated";
import { Nfts } from "~/components/home/<USER>";
import { useActiveWallet } from "~/state/wallet";

export default function NFTsScreen() {
  const { wallet } = useActiveWallet();
  const scrollViewRef = useAnimatedRef<Animated.ScrollView>();

  return (
    <SafeAreaView style={{ flex: 1, gap: 42 }}>
      <StackHeader title="NFTs" scrollViewRef={scrollViewRef} />

      <View
        style={{
          flex: 1,
          paddingHorizontal: 20,
          paddingTop: HEADER_HEIGHT + 4,
        }}
      >
        <Nfts wallet={wallet} scrollViewRef={scrollViewRef} />
      </View>
    </SafeAreaView>
  );
}
