import { Text, View } from "~/components/Themed";
import { StackScreenHeader } from "~/components/headers/StackScreenHeader";
import { Flex, Row } from "~/components/Grid";
import { PlusMembershipCardContent } from "~/app/unlocked/subscription/welcome";
import * as React from "react";
import { useSharedValue } from "react-native-reanimated";
import { TopInset } from "~/components/BottomInset";
import { OptionalBackButton } from "~/components/headers/BackButton";
import { useHeaderHeight } from "@react-navigation/elements";
import { IconWrapper } from "~/components/IconWrapper";
import { FusePlusLogo } from "~/components/icons/FusePlusLogo";
import { Switch } from "~/components/Switch";
import { getAppIcon, setAppIcon } from "~/utils/appIcon";
import { useState } from "react";

export default function FusePlusSettingsScreen() {
  const noAnimation = useSharedValue(1);
  const headerHeight = useHeaderHeight();

  const [icon, setIcon] = useState(getAppIcon());

  return (
    <>
      <StackScreenHeader
        headerTitle={() => (
          <Text
            variant={"medium"}
            size={18}
            colorToken={"textSecondaryOpposite"}
          >
            Membership
          </Text>
        )}
        headerLeft={() => (
          <OptionalBackButton colorToken={"textButtonPrimary"} />
        )}
      />

      {/*todo fix color*/}
      <Flex
        background={"textButtonSecondary"}
        style={{ paddingHorizontal: 24 }}
      >
        <TopInset offset={headerHeight} />
        <View
          background={"backgroundCard"}
          style={{
            padding: 24,
            //todo fix color
            borderRadius: 32,
            borderCurve: "continuous",
          }}
        >
          <PlusMembershipCardContent plusGlowAnimationProgress={noAnimation} />
        </View>

        <View style={{ height: 16 }} />

        <Text
          align={"center"}
          variant={"medium"}
          size={14}
          colorToken={"textSecondaryOpposite"}
        >
          You need to maintain a $100 balance across{"\n"}
          Earn products to keep your membership.
        </Text>

        <View style={{ height: 42 }} />

        <Row
          background={"backgroundCard"}
          style={{
            paddingHorizontal: 20,
            paddingVertical: 17,
            borderRadius: 20,
            borderCurve: "continuous",
            justifyContent: "space-between",
          }}
        >
          <Row gap={12}>
            <IconWrapper
              size={24}
              background={"black"}
              variant={"square"}
              style={{ borderRadius: 8 }}
            >
              <FusePlusLogo size={11} />
            </IconWrapper>

            <Text colorToken={"textButtonPrimary"} variant={"medium"} size={14}>
              Fuse Plus Wallet Icon
            </Text>
          </Row>
          <Switch
            value={icon === "app-icon-plus"}
            onValueChange={async (enabled) => {
              if (enabled) {
                setIcon("app-icon-plus");
                await setAppIcon("app-icon-plus");
              } else {
                setIcon(null);
                await setAppIcon(null);
              }
            }}
          />
        </Row>
      </Flex>
    </>
  );
}
