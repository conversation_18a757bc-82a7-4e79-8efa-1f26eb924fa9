import { Text, View } from "~/components/Themed";
import invariant from "invariant";
import { useEffect } from "react";
import {
  useBridgeFees,
  useExchangeRateLazy,
  useExternalAccounts,
} from "~/state/bridge";
import { useSendFiatState } from "~/app/unlocked/bridge/send-fiat/_layout";
import { Flex } from "~/components/Grid";
import { formatFiatValue, formatUsdValue } from "@squads/utils/numberFormats";
import { useSuspenseToken } from "~/state/tokens";
import {
  useMutation,
  UseMutationResult,
  useQueryClient,
} from "@tanstack/react-query";
import {
  OffRampFee,
  prepareAction,
  PrepareActionResponse,
} from "~/services/wallets";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { useActiveWallet } from "~/state/wallet";
import { sendTransactionWithTrackingStatus } from "~/utils/sendTransactionWithTracking";
import { clientErrorMessage } from "~/services/utils";
import { refetchBalances } from "~/state/balances";
import { refetchSpendingLimits } from "~/state/spendingLimits";
import * as Haptics from "expo-haptics";
import { Button } from "~/components/Button";
import { ListSeparator } from "~/components/ListSeparator";
import { toast } from "~/components/Toaster";
import {
  AddressRow,
  PropertyRow,
  SponsoredFeesRow,
} from "~/components/PropertyRow";
import { BridgeFees, Currency } from "~/services/bridge";
import { P3 } from "~/components/typography/P3";
import { ContentSkeleton } from "~/components/Skeleton";
import { router } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { TransferPartyDetails } from "~/components/send/TransferPartyDetails";

export default function ReviewDetailsScreen() {
  const { wallet } = useActiveWallet();
  const queryClient = useQueryClient();
  const insets = useSafeAreaInsets();

  const [sendFiatState] = useSendFiatState();

  const source = sendFiatState.source;
  invariant(source, "Invalid source");

  const externalAccountId = sendFiatState.externalAccountId;
  const externalAccounts = useExternalAccounts();
  const externalAccount = externalAccounts.find(
    (account) => account.id === externalAccountId
  );
  invariant(externalAccount, "Invalid external account");

  const mint = source.mint;
  const token = useSuspenseToken({ mint });
  invariant(token, `Unknown token ${mint}`);

  const bridgeFeesQuery = useBridgeFees();
  const offRampFees = BridgeFees.forCurrency(
    bridgeFeesQuery.offRamp,
    externalAccount.currency
  );

  const amountFloat = source.addFeeOnTop
    ? BridgeFees.addFees({ fees: offRampFees, amountFloat: source.amountFloat })
    : source.amountFloat;

  const prepareSendFiatActionMutation = useMutation({
    mutationFn: () => {
      let amount = Math.round(amountFloat * 10 ** token.decimals);
      console.debug(`Preparing transaction to send ${amount}`);
      return prepareAction({
        type: "sendFiat",
        vaultIndex: 0,
        source: { amount, mint: source.mint },
        destination: { externalAccountId },
      });
    },
    onError: () => {
      toast.error("Transaction simulation failed");
    },
    retry: 1,
  });

  //create a tx every time the reservedForTopUp changes
  useEffect(() => {
    prepareSendFiatActionMutation.mutate();
  }, []);

  const sendFiatMutation = useMutation({
    mutationFn: async () => {
      invariant(prepareSendFiatActionMutation.data, "Invalid transaction");
      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        prepareSendFiatActionMutation.data.transactions
      );

      router.dismissTo("/unlocked/root/home");
      await sendTransactionWithTrackingStatus({
        walletKey: wallet.walletKey,
        transaction: { type: "action", signedTransactions },
        confirmationFailureMessage: (e) =>
          clientErrorMessage("Failed to confirm", e),
      }).finally(() => {
        Promise.all([
          refetchBalances({
            queryClient,
            address: wallet.defaultVault,
          }),
          refetchSpendingLimits(queryClient, {
            walletKey: wallet.walletKey,
            vaultKey: wallet.defaultVault,
          }),
        ]);
      });
    },
  });

  return (
    <Flex
      justify={"space-between"}
      style={{ padding: 20, paddingBottom: insets.bottom }}
    >
      <Flex gap={24}>
        <TransferPartyDetails
          token={{ mint: token.mint, amountFloat: amountFloat }}
          destination={{ type: "off-ramp", externalAccount }}
        />
        <ListSeparator />
        <View gap={16}>
          <View gap={12}>
            <View style={{ gap: 12 }}>
              <AddressRow label={"From"} address={wallet.defaultVault} />
              <PropertyRow
                label={"Bank account owner"}
                value={
                  <P3 numberOfLines={1}>{externalAccount.accountOwnerName}</P3>
                }
              />
              <PropertyRow
                label={
                  externalAccount.currency === "usd"
                    ? "Bank account number"
                    : "IBAN"
                }
                value={
                  <P3 numberOfLines={1}>...{externalAccount.accountNumber}</P3>
                }
              />
              <ListSeparator />
              <SponsoredFeesRow label="Onchain fees" />
              <TransferDetailsRows
                currency={externalAccount.currency}
                sendAmountFloat={amountFloat}
                prepareActionMutation={prepareSendFiatActionMutation}
              />
            </View>
          </View>
        </View>
      </Flex>
      <Button
        iconName={"faceid"}
        iconWeight="medium"
        iconSize={14}
        disabled={
          prepareSendFiatActionMutation.isError ||
          prepareSendFiatActionMutation.isPending ||
          sendFiatMutation.isPending
        }
        onPress={() => {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          sendFiatMutation.mutate();
        }}
      >
        Send
      </Button>
    </Flex>
  );
}

function TransferDetailsRows({
  currency,
  sendAmountFloat,
  prepareActionMutation,
}: {
  currency: Currency;
  sendAmountFloat: number;
  prepareActionMutation: UseMutationResult<PrepareActionResponse, Error, void>;
}) {
  const bankReceivesLabel =
    currency === "eur" ? "Bank receives (est.)" : "Bank receives";

  if (
    prepareActionMutation.status === "idle" ||
    prepareActionMutation.status === "pending"
  ) {
    return (
      <>
        <PropertyRow
          label={"Transfer Fee"}
          value={
            <ContentSkeleton>
              <Text>0.00649 SOL</Text>
            </ContentSkeleton>
          }
        />
        <PropertyRow
          label={bankReceivesLabel}
          value={
            <ContentSkeleton>
              <Text>0.00649 SOL</Text>
            </ContentSkeleton>
          }
        />
      </>
    );
  } else if (prepareActionMutation.status === "error") {
    return (
      <>
        <PropertyRow
          label={"Transfer Fee"}
          value={<Text variant="medium">-</Text>}
        />
        <PropertyRow
          label={bankReceivesLabel}
          value={<Text variant="medium">-</Text>}
        />
      </>
    );
  } else if (prepareActionMutation.status === "success") {
    const platformFee = prepareActionMutation.data.platformFees.find(
      (f): f is OffRampFee => f.type === "offRamp"
    );
    const railsFee = (platformFee?.railsFee ?? 0) + (platformFee?.fuseFee ?? 0);
    const bankReceives = sendAmountFloat - railsFee;

    return (
      <>
        <PropertyRow
          label={"Transfer Fee"}
          value={<Text variant="medium">{formatUsdValue(railsFee)}</Text>}
        />
        <PropertyRow
          label={bankReceivesLabel}
          value={
            currency === "eur" ? (
              <EurBankReceivesText bankReceives={bankReceives} />
            ) : (
              <Text variant="medium">{formatUsdValue(bankReceives)}</Text>
            )
          }
        />
      </>
    );
  } else {
    prepareActionMutation satisfies never;
    return null;
  }
}

function EurBankReceivesText({ bankReceives }: { bankReceives: number }) {
  const exchangeRateQuery = useExchangeRateLazy({
    from: "usd",
    to: "eur",
  });

  if (exchangeRateQuery.error) {
    return (
      <Text variant="medium">
        {formatFiatValue(bankReceives, { currency: "USD" })}
      </Text>
    );
  }

  const exchangeRate = exchangeRateQuery.data;
  if (exchangeRate === undefined) {
    return (
      <ContentSkeleton>
        <Text variant="medium">0.00649 SOL</Text>
      </ContentSkeleton>
    );
  }

  const bankReceivesEur = bankReceives * exchangeRate.sellRate;

  return (
    <Text variant="medium">
      {formatFiatValue(bankReceivesEur, { currency: "EUR" })}
    </Text>
  );
}
