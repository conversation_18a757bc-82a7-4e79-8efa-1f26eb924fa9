import { Text, View } from "~/components/Themed";
import invariant from "invariant";
import { ReactNode, useEffect, useMemo, useRef, useState } from "react";
import { useBridgeFeesLazy, useExternalAccounts } from "~/state/bridge";
import { useSendFiatState } from "~/app/unlocked/bridge/send-fiat/_layout";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Flex, Row } from "~/components/Grid";
import { NumPad } from "~/components/NumPad";
import { Button } from "~/components/Button";
import { useFormik } from "formik";
import { toFormikValidationSchema } from "zod-formik-adapter";
import * as Haptics from "expo-haptics";
import { router } from "expo-router";
import { getMaxAmount, useBalances } from "~/state/balances";
import { z } from "zod";
import { AmountZ } from "~/components/AmountDisplay";
import { useActiveWallet } from "~/state/wallet";
import { mints } from "~/constants/tokens";
import {
  AssetAmountInputBlock,
  SwitchRow,
  TokenLabel,
} from "~/components/AssetAmountInputBlock";
import { DashedListSeparator } from "~/components/ListSeparator";
import { IconWrapper } from "~/components/IconWrapper";
import { BridgeFees, ExternalAccount } from "~/services/bridge";
import { BankIcon } from "~/components/icons/home/<USER>";
import { formatUsdValue } from "@squads/utils/numberFormats";
import { ContentSkeleton } from "~/components/Skeleton";
import { useSuspenseToken } from "~/state/tokens";
import { Address } from "@squads/models/solana";
import { Icon } from "~/components/Icon";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import { FreeOfframpFeeLimitModal } from "~/components/send/FreeOfframpFeeLimitModal";
import { BottomModalImperativeMethods } from "~/components/BottomModal";
import { numberToString } from "~/utils/conversions";

const MIN_SEND_FIAT_AMOUNT = 5.0;

export default function EnterAmountScreen() {
  const insets = useSafeAreaInsets();

  const [sendFiatFlowState, setSendFiatFlowState] = useSendFiatState();

  const { wallet } = useActiveWallet();

  const externalAccounts = useExternalAccounts();
  const externalAccount = externalAccounts.find(
    (account) => account.id === sendFiatFlowState.externalAccountId
  );
  invariant(externalAccount, "Invalid external account");

  // const { spendingLimits } = useSpendingLimits({
  //   walletKey: wallet.walletKey,
  //   vaultKey: wallet.defaultVault,
  // });
  //
  // const availableSpendingLimit =
  //   spendingLimits.find((s) => s.mint === mints.usdc) ?? null;
  // const selectedSpendingLimit = sendFiatFlowState.useSpendingLimit
  //   ? availableSpendingLimit
  //   : null;

  const mint = mints.usdc;
  const token = useSuspenseToken({ mint });
  invariant(token, "token is not defined");

  const balances = useBalances({
    address: wallet.defaultVault,
  });
  const balance = balances.find((b) => b.mint === mint) ?? {
    mint: mint,
    amount: 0,
    decimals: token.decimals,
    usdcPrice: null,
  };

  const [addFeeOnTop, setAddFeeOnTop] = useState(false);

  const maxAmountQuery = useMinMaxSendFiatAmount({
    balance,
    addFeeOnTop,
    currency: externalAccount.currency,
  });

  const FormValuesZ = z.object({
    amount: AmountZ(
      maxAmountQuery.status === "success" ? maxAmountQuery.maxAmount : Infinity,
      maxAmountQuery.status === "success"
        ? maxAmountQuery.minAmount
        : MIN_SEND_FIAT_AMOUNT
    ),
  });
  type FormValues = z.infer<typeof FormValuesZ>;

  const formik = useFormik<FormValues>({
    initialValues: {
      amount: "",
    },

    validationSchema: toFormikValidationSchema(FormValuesZ),

    onSubmit(values) {
      const amountFloat = Number(values.amount);

      setSendFiatFlowState({
        source: { amountFloat, mint, addFeeOnTop },
      });

      Haptics.selectionAsync();
      router.push(`/unlocked/bridge/send-fiat/review`);
    },
  });

  //this needs to go after formik is initialized
  useEffect(() => {
    formik.validateForm();
  }, [addFeeOnTop]);

  return (
    <View
      style={{ flex: 1, paddingHorizontal: 20, paddingBottom: insets.bottom }}
    >
      <Flex justify={"flex-end"} gap={28}>
        <View>
          <View gap={8} style={{ marginBottom: 20 }}>
            <View gap={12} style={{ marginBottom: 12 }}>
              <TokenLabel token={token} />
              <AssetAmountInputBlock
                amount={formik.values.amount}
                usdPrice={balance.usdcPrice}
                maxAmountQuery={maxAmountQuery}
                onInstantChange={(amount) =>
                  formik.handleChange("amount")(numberToString(amount))
                }
              />
            </View>

            <View style={{ gap: 20 }}>
              <DashedListSeparator />
              <FeeSwitchRow
                currency={externalAccount.currency}
                amountFloat={
                  formik.values.amount ? Number(formik.values.amount) : 0
                }
                enabled={addFeeOnTop}
                onSwitchChange={(enabled) => {
                  setAddFeeOnTop(enabled);
                }}
              />
              {/*{availableSpendingLimit && (*/}
              {/*  <UseSpendingLimitRow*/}
              {/*    enabled={!!selectedSpendingLimit}*/}
              {/*    onSwitchChange={(enabled) => {*/}
              {/*      setSendFiatFlowState({ useSpendingLimit: enabled });*/}
              {/*    }}*/}
              {/*  />*/}
              {/*)}*/}
            </View>
          </View>

          <ExternalAccountDestinationRow externalAccount={externalAccount} />
        </View>

        <View style={{ gap: 20 }}>
          <NumPad
            value={formik.values.amount}
            onChange={formik.handleChange("amount")}
            decimals={2}
          />
          <Button
            error={
              formik.errors.amount === "insufficientBalance"
                ? "Insufficient balance"
                : formik.errors.amount?.startsWith("minAmountExceeded")
                  ? `Minimum amount is ${MIN_SEND_FIAT_AMOUNT} USDC`
                  : undefined
            }
            disabled={!formik.dirty || !formik.isValid}
            onPress={() => formik.handleSubmit()}
          >
            Review
          </Button>
        </View>
      </Flex>
    </View>
  );
}

function ExternalAccountDestinationRow({
  externalAccount,
}: {
  externalAccount: ExternalAccount;
}) {
  return (
    <DestinationRow
      icon={
        <IconWrapper
          backgroundColorToken="backgroundTertiary"
          size={36}
          variant={"square"}
        >
          <BankIcon colorToken={"text"} size={16} />
        </IconWrapper>
      }
      title={
        <Text size={14} variant={"medium"} colorToken="textSecondary">
          To:{" "}
          <Text size={14} variant={"medium"} colorToken="text">
            {externalAccount.accountOwnerName}
          </Text>
        </Text>
      }
      description={`${externalAccount.bankName} ${externalAccount.accountNumber}`}
    />
  );
}

export function DestinationRow({
  icon,
  title,
  description,
}: {
  icon: ReactNode;
  title: ReactNode;
  description: ReactNode;
}) {
  return (
    <Row
      gap={12}
      style={{
        paddingVertical: 12,
        paddingHorizontal: 14,
        borderRadius: 16,
        borderCurve: "continuous",
        backgroundColor: "white",
      }}
    >
      {icon}
      <View gap={2}>
        <Text size={14} variant={"medium"} numberOfLines={1}>
          {title}
        </Text>
        <Text
          size={13}
          variant="medium"
          colorToken="textSecondary"
          style={{ flexShrink: 1 }}
        >
          {description}
        </Text>
      </View>
    </Row>
  );
}

function useMinMaxSendFiatAmount({
  balance,
  addFeeOnTop,
  currency,
}: {
  balance: {
    mint: Address | null;
    amount: number;
    decimals: number;
  };
  addFeeOnTop: boolean;
  currency: "usd" | "eur";
}) {
  const bridgeFeesQuery = useBridgeFeesLazy();

  return useMemo(() => {
    if (
      bridgeFeesQuery.status === "error" ||
      bridgeFeesQuery.status === "pending"
    ) {
      return { status: bridgeFeesQuery.status };
    }

    const fees = BridgeFees.forCurrency(bridgeFeesQuery.data.offRamp, currency);

    const amountFloat = toFixed2(
      getMaxAmount({
        balance,
        spendingLimit: null,
      })
    );

    const amountFee = BridgeFees.getAmountFees({ fees, amountFloat }).value;
    return {
      status: "success" as const,
      maxAmount: Number(
        addFeeOnTop
          ? Math.max(toFixed2(amountFloat - amountFee), 0)
          : amountFloat
      ),
      minAmount: addFeeOnTop
        ? Math.max(toFixed2(MIN_SEND_FIAT_AMOUNT - amountFee), 0)
        : MIN_SEND_FIAT_AMOUNT,
    };
  }, [bridgeFeesQuery, addFeeOnTop]);
}

function FeeSwitchRow({
  amountFloat,
  currency,
  enabled,
  onSwitchChange,
}: {
  amountFloat: number;
  currency: "usd" | "eur";
  enabled: boolean;
  onSwitchChange: (enabled: boolean) => void;
}) {
  const bridgeFeesQuery = useBridgeFeesLazy();

  if (!bridgeFeesQuery.data) {
    return (
      <SwitchRow
        title={
          <>
            {"Fee "}
            <ContentSkeleton>
              <Text size={14} variant="medium">
                $1.00
              </Text>
            </ContentSkeleton>
          </>
        }
        subtitle={"Add on top"}
        enabled={enabled}
        onSwitchChange={onSwitchChange}
      />
    );
  }

  const bridgeFees = BridgeFees.forCurrency(
    bridgeFeesQuery.data.offRamp,
    currency
  );

  const amountFees = BridgeFees.getAmountFees({
    fees: bridgeFees,
    amountFloat,
  });

  return (
    <SwitchRow
      title={
        amountFees.type === "onlyRailsFee" ? (
          <FeeTitleText
            title={bridgeFees.rails === "sepa" ? "SEPA Fee" : "ACH Fee"}
            fees={amountFees.value}
          />
        ) : amountFees.remainingFreeAmountUsd === null ? (
          <FeeTitleText title={"Fee"} fees={amountFees.value} />
        ) : (
          <FeeTitleWithAllowanceDetails
            title={"Fee"}
            fees={amountFees.value}
            remainingFreeAmountUsd={amountFees.remainingFreeAmountUsd}
          />
        )
      }
      subtitle={"Add on top"}
      enabled={enabled}
      onSwitchChange={onSwitchChange}
    />
  );
}

function FeeTitleText({ title, fees }: { title: string; fees: number }) {
  return (
    <Text variant="medium" size={14} colorToken="textSecondary">
      {title}{" "}
      <Text size={14} variant={"medium"} style={{ alignSelf: "baseline" }}>
        {formatUsdValue(fees)}
      </Text>
    </Text>
  );
}

function FeeTitleWithAllowanceDetails({
  title,
  fees,
  remainingFreeAmountUsd,
}: {
  title: string;
  fees: number;
  remainingFreeAmountUsd: number;
}) {
  const modalRef = useRef<BottomModalImperativeMethods>(null);

  return (
    <>
      <FreeOfframpFeeLimitModal
        modalRef={modalRef}
        remainingFreeAmountUsd={remainingFreeAmountUsd}
      />
      <AnimatedTouchableScale
        onPress={() => {
          modalRef.current?.present();
        }}
      >
        <Row flex gap={6}>
          <FeeTitleText title={title} fees={fees} />

          <Icon
            name={"info.circle"}
            colorToken={"textSecondary"}
            size={10}
            weight={"semibold"}
          />
        </Row>
      </AnimatedTouchableScale>
    </>
  );
}

function toFixed2(amount: number) {
  return Math.trunc(amount * 100) / 100;
}
