import { ModalContainer } from "~/components/ModalContainer";
import { Flex, Row } from "~/components/Grid";
import { DashedListSeparator } from "~/components/ListSeparator";
import { Text, useColor, View } from "~/components/Themed";
import * as Clipboard from "expo-clipboard";
import { Icon } from "~/components/Icon";
import { TouchableScale } from "~/components/TouchableScale";
import { PropsWithChildren, useRef } from "react";
import {
  createVirtualAccount,
  getKycStatus,
  loadBridgeAccount,
  markKycCompleted,
  refetchVirtualAccounts,
  refetchVirtualAccountsNeedMigration,
  useVirtualAccount,
  useVirtualAccountsNeedMigration,
} from "~/state/bridge";
import { Button } from "~/components/Button";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import * as Haptics from "expo-haptics";
import { useToast } from "~/components/Toaster";
import { Link, router } from "expo-router";
import { SegmentedControl } from "~/components/SegmentedControl";
import {
  BridgeAccount,
  DepositInstructions,
  migrateVirtualAccounts,
  openKycFlow,
  updateKycLink,
} from "~/services/bridge";
import { Spinner } from "~/components/Spinner";

import {
  FeatureListIcon,
  FeatureListItem,
  FeaturesList,
} from "~/components/bridge-onboarding/FeaturesList";
import { routeToKyc } from "~/components/DepositButton";
import Animated, {
  FadeIn,
  FadeOut,
  LayoutAnimationConfig,
  ReduceMotion,
  SlideInLeft,
  SlideInRight,
  SlideOutLeft,
  SlideOutRight,
} from "react-native-reanimated";
import { DURATION_FASTER, DURATION_MEDIUM } from "~/constants/animations";
import { FlagEU } from "~/components/icons/FlagEU";
import { FlagUS } from "~/components/icons/FlagUS";
import { VirtualAccountIntroUS } from "~/components/icons/VirtualAccountIntroUS";
import { VirtualAccountIntroEU } from "~/components/icons/VirtualAccountIntroEU";
import { VirtualAccountReloadIcon } from "~/components/icons/VirtualAccountReloadIcon";
import { UserPreferencesKey, useUserPreference } from "~/state/userPreferences";
import {
  hapticOpenBottomTray,
  hapticSelect,
  hapticSuccess,
} from "~/utils/haptics";
import { StartKycRouteParams } from "~/app/unlocked/bridge/onboarding/start-kyc";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import { VirtualAccountLimitsModal } from "~/components/modals/VirtualAccountLimitsModal";
import { BottomModalImperativeMethods } from "~/components/BottomModal";
import { IconWrapper } from "~/components/IconWrapper";
import { BankIcon } from "~/components/icons/home/<USER>";
import { DashedBorder } from "~/components/DashedBorder";
import { Share } from "react-native";
import { reportError } from "~/utils/errors";
import { queryClient } from "~/state/queryClient";

const SLIDE_DURATION = DURATION_MEDIUM;
const FADE_DURATION = DURATION_FASTER;

export default function VirtualAccountPage() {
  const virtualAccountsMigrationQuery = useVirtualAccountsNeedMigration();

  if (virtualAccountsMigrationQuery.isPending) {
    return (
      <ModalContainer>
        <Flex style={{ alignItems: "center", justifyContent: "center" }}>
          <Spinner />
        </Flex>
      </ModalContainer>
    );
  }

  if (virtualAccountsMigrationQuery?.data?.needMigration) {
    return <VirtualAccountsMigrationPage />;
  }

  return <MainVirtualAccountPage />;
}

function VirtualAccountsMigrationPage() {
  const queryClient = useQueryClient();

  const migrateVirtualAccountsMutation = useMutation({
    mutationFn: async () => {
      await migrateVirtualAccounts();

      await refetchVirtualAccounts(queryClient);
      await refetchVirtualAccountsNeedMigration(queryClient);

      hapticSuccess();
    },
  });

  return (
    <ModalContainer>
      <View style={{ flex: 1, justifyContent: "space-between" }}>
        <View />

        <View gap={32} style={{ alignItems: "center" }}>
          <View>
            <IconWrapper
              size={64}
              backgroundColorToken={"blue"}
              variant={"square"}
              borderRadius={18}
            >
              <BankIcon colorToken={"textOpposite"} size={30} />
            </IconWrapper>
            <IconWrapper
              backgroundColorToken={"background"}
              size={30}
              style={{ position: "absolute", right: -7.5, top: -7.5 }}
            >
              <Icon
                name={"arrow.clockwise.circle.fill"}
                size={18}
                weight="bold"
                colorToken={"blue"}
              />
            </IconWrapper>
          </View>

          <View gap={32}>
            <View gap={8} style={{ maxWidth: 320 }}>
              <Text variant={"semibold"} size={22} align={"center"}>
                Receive payments{"\n"}from individuals
              </Text>
              <Text
                variant={"medium"}
                size={14}
                colorToken={"textSecondary"}
                align={"center"}
              >
                US users can now receive payments from{"\n"}
                businesses and individuals. Update your{"\n"}
                Virtual Account to activate this feature.
              </Text>
            </View>
          </View>
          <View>
            <DashedBorder
              borderWidth={1.5}
              borderColorToken={"dashedListSeparator"}
            />
            <Row
              gap={16}
              style={{ gap: 18, paddingVertical: 20, paddingHorizontal: 24 }}
            >
              <Icon
                name={"arrow.up.right.diamond.fill"}
                size={14}
                colorToken={"text"}
              />
              <Text variant="medium" colorToken="textSecondary" size={14}>
                Payments to your existing Virtual{"\n"}
                Account will be returned to sender.
              </Text>
            </Row>
          </View>
        </View>

        <Button
          size={"medium-new"}
          variant={"primary"}
          loading={migrateVirtualAccountsMutation.isPending}
          loadingText={"Updating"}
          onPress={() => {
            hapticSelect();
            migrateVirtualAccountsMutation.mutate();
          }}
        >
          Update Virtual Account
        </Button>
      </View>
    </ModalContainer>
  );
}

function MainVirtualAccountPage() {
  const [currency, setCurrency] = useUserPreference(
    UserPreferencesKey.VirtualAccountView,
    "usd"
  );

  return (
    <ModalContainer>
      <Flex gap={40}>
        <View style={{ width: 200, alignSelf: "center", zIndex: 1 }}>
          <SegmentedControl
            options={["usd", "eur"] as const}
            value={currency}
            setValue={setCurrency}
            render={(value) => {
              return (
                <Row gap={4} justify={"center"}>
                  {value === "usd" ? <FlagUS /> : <FlagEU />}

                  <Text
                    variant={currency === value ? "semibold" : "medium"}
                    size={14}
                  >
                    {value === "usd" ? `USD` : `EUR`}
                  </Text>
                </Row>
              );
            }}
          />
        </View>
        <LayoutAnimationConfig skipEntering>
          <Animated.View
            key={`va-${currency}`}
            style={{ flex: 1 }}
            entering={
              currency === "usd"
                ? SlideInLeft.duration(SLIDE_DURATION).reduceMotion(
                    ReduceMotion.Never
                  )
                : SlideInRight.duration(SLIDE_DURATION).reduceMotion(
                    ReduceMotion.Never
                  )
            }
            exiting={
              currency === "usd"
                ? SlideOutLeft.duration(SLIDE_DURATION).reduceMotion(
                    ReduceMotion.Never
                  )
                : SlideOutRight.duration(SLIDE_DURATION).reduceMotion(
                    ReduceMotion.Never
                  )
            }
          >
            <Main currency={currency} />
          </Animated.View>
        </LayoutAnimationConfig>
      </Flex>
    </ModalContainer>
  );
}

function Main({ currency }: { currency: "usd" | "eur" }) {
  const {
    data: virtualAccount,
    isPending,
    isError,
    refetch,
  } = useVirtualAccount({ currency });

  const entering = FadeIn.delay(FADE_DURATION)
    .duration(FADE_DURATION)
    .reduceMotion(ReduceMotion.Never);
  const exiting = FadeOut.duration(FADE_DURATION).reduceMotion(
    ReduceMotion.Never
  );

  if (isPending) {
    return (
      <Animated.View
        key={"va-loading"}
        style={{ flex: 1 }}
        entering={entering}
        exiting={exiting}
      >
        <Flex style={{ alignItems: "center", justifyContent: "center" }}>
          <Spinner />
        </Flex>
      </Animated.View>
    );
  }

  if (isError) {
    return (
      <Animated.View
        key={"va-error"}
        style={{ flex: 1 }}
        entering={entering}
        exiting={exiting}
      >
        <Flex style={{ alignItems: "center" }}>
          <View style={{ marginTop: 32 }}>
            <VirtualAccountReloadIcon />
            <View gap={8} style={{ alignItems: "center", top: -90 }}>
              <Text variant={"semibold"} size={24}>
                Couldn't load data
              </Text>
              <Text
                variant={"medium"}
                size={14}
                colorToken={"textSecondary"}
                align={"center"}
              >
                For assistance regarding Virtual Bank Accounts{"\n"}
                and transfers, reach out to{" "}
                <Link href={"mailto:<EMAIL>"}>
                  <EMAIL>
                </Link>
              </Text>
            </View>
          </View>
        </Flex>
        <Button
          size={"medium-new"}
          variant={"secondary"}
          onPress={async () => {
            hapticSelect();
            await refetch();
          }}
        >
          Reload
        </Button>
      </Animated.View>
    );
  }

  if (!virtualAccount) {
    return (
      <Animated.View
        key={`not-found`}
        style={{ flex: 1 }}
        entering={entering}
        exiting={exiting}
      >
        <IntroView currency={currency} />
      </Animated.View>
    );
  }

  const depositInstructions = virtualAccount.deposit_instructions;

  return (
    <Animated.View
      key={`details`}
      style={{ flex: 1 }}
      entering={entering}
      exiting={exiting}
    >
      {depositInstructions.currency === "usd" ? (
        <UsdDetails depositInstructions={depositInstructions} />
      ) : (
        <EurDetails depositInstructions={depositInstructions} />
      )}
    </Animated.View>
  );
}

function HelpText() {
  return (
    <Text
      variant={"medium"}
      colorToken={"textSecondary"}
      align={"center"}
      size={13}
    >
      For assistance regarding issues with transfers and{"\n"}deposits, reach
      out to{" "}
      <Link href={"mailto:<EMAIL>"}><EMAIL></Link>
      .
    </Text>
  );
}

function Header({ currency }: { currency: "usd" | "eur" }) {
  const modalRef = useRef<BottomModalImperativeMethods>(null);

  return (
    <View gap={16} style={{ alignItems: "center" }}>
      <View gap={8} style={{ alignItems: "center" }}>
        <Text variant={"semibold"} size={24}>
          {currency === "usd"
            ? "Virtual US Bank Account"
            : "Virtual EU Bank Account"}
        </Text>
        <Text variant={"medium"} size={14} colorToken={"textSecondary"}>
          {currency === "usd"
            ? "Accept ACH Push & Wire Payments"
            : "Accept SEPA Payments"}
        </Text>
      </View>

      <Row gap={6}>
        <VirtualAccountLimitsModal modalRef={modalRef} />
        <AnimatedTouchableScale
          onPress={() => {
            hapticOpenBottomTray();
            modalRef.current?.present();
          }}
        >
          <Tag>
            <Text size={14} variant="medium" colorToken={"text"}>
              Limits
            </Text>
            <Icon weight={"heavy"} name={"arrow.up.right"} size={8} />
          </Tag>
        </AnimatedTouchableScale>

        <Tag>
          <Text size={14} variant="medium" colorToken={"textSecondary"}>
            {currency === "usd" ? "Min. transfer $2" : "Min. transfer €2"}
          </Text>
        </Tag>
      </Row>
    </View>
  );
}

function Tag({ children }: PropsWithChildren) {
  const borderColor = useColor("border");
  return (
    <View
      style={{
        paddingVertical: 8,
        paddingHorizontal: 14,
        gap: 6,
        flexDirection: "row",
        alignItems: "center",
        borderRadius: 999,
        borderCurve: "continuous",
        borderWidth: 1,
        borderColor,
      }}
    >
      {children}
    </View>
  );
}

function IntroView({ currency }: { currency: "usd" | "eur" }) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const createVirtualAccountMutation = useMutation({
    mutationFn: async () => {
      const bridgeAccount = await loadBridgeAccount({
        queryClient,
      });

      if (bridgeAccount === null) {
        router.dismissTo("/unlocked/root/home");
        router.push({
          pathname: "/unlocked/bridge/onboarding/start-kyc",
          params: {
            endorsements: currency === "eur" ? ["sepa"] : undefined,
          } satisfies StartKycRouteParams,
        });
        return;
      }

      const kycStatus = getKycStatus(bridgeAccount);
      if (kycStatus !== "approved") {
        router.dismissTo("/unlocked/root/home");
        routeToKyc(kycStatus);
        return;
      }

      const sepaEndorsement = BridgeAccount.endorsement(bridgeAccount, "sepa");

      if (currency === "eur" && sepaEndorsement?.status !== "approved") {
        if (
          sepaEndorsement &&
          sepaEndorsement.requirements.pending.length > 0
        ) {
          toast.info("Verification for EU features is still pending", {
            id: "kyc_pending",
          });
          return;
        }

        const { kycLink } = await updateKycLink({ endorsement: "sepa" });
        const result = await openKycFlow({ kycLink });
        if (result.status !== "completed") {
          toast.info("Verification has been cancelled");
        } else {
          markKycCompleted(bridgeAccount.customer_id);
          router.dismissTo("/unlocked/root/home");
          router.push("/unlocked/bridge/onboarding/kyc-status");
        }

        return;
      }

      await createVirtualAccount(queryClient, { currency });
    },
    onError: () => {
      toast.error("Failed to create Virtual Account. Please try again.");
    },
  });

  return (
    <Flex justify={"space-between"}>
      <View style={{ justifyContent: "center" }}>
        <IntroGraphic currency={currency} />

        <FeaturesList>
          <FeatureListItem
            icon={
              <FeatureListIcon
                iconName={"rectangle.2.swap"}
                iconSize={10}
                iconColorToken={"green"}
              />
            }
            text={
              currency === "usd"
                ? "Get paid in USD and automatically\nreceive USDC in your Fuse wallet"
                : "Get paid in EUR and automatically\nreceive USDC in your Fuse wallet"
            }
          />
          <FeatureListItem
            icon={
              <FeatureListIcon
                iconName={"arrow.down.circle.dotted"}
                iconColorToken={"purple"}
              />
            }
            text={
              "Receive payments from anyone\nwith a bank account for a 0% conversion fee"
            }
          />
          <FeatureListItem
            icon={
              <FeatureListIcon iconName={"timelapse"} iconColorToken={"blue"} />
            }
            text={"Quick setup through Bridge with\nstandard KYC verification"}
          />
        </FeaturesList>
      </View>

      <View style={{ gap: 16 }}>
        <Text
          size={14}
          variant={"medium"}
          colorToken={"textSecondary"}
          style={{ textAlign: "center" }}
        >
          Excluded states in the US: NY and AK.
        </Text>
        <Button
          size={"medium-new"}
          variant={"primary"}
          loading={createVirtualAccountMutation.isPending}
          loadingText={"Creating"}
          onPress={async () => {
            Haptics.selectionAsync();
            createVirtualAccountMutation.mutate();
          }}
        >
          Create account
        </Button>
      </View>
    </Flex>
  );
}

function IntroGraphic({ currency }: { currency: "usd" | "eur" }) {
  return (
    <View
      style={{
        marginTop: -83,
        alignItems: "center",
      }}
    >
      {currency === "usd" ? (
        <VirtualAccountIntroUS />
      ) : (
        <VirtualAccountIntroEU />
      )}
      <View style={{ position: "absolute", width: "100%", bottom: 32 }}>
        <Text size={20} variant={"semibold"} align={"center"}>
          Create your{"\n"}Virtual {currency === "usd" ? "US" : "EU"} Bank
          Account
        </Text>
      </View>
    </View>
  );
}

function UsdDetails({
  depositInstructions,
}: {
  depositInstructions: DepositInstructions & { currency: "usd" };
}) {
  const { toast } = useToast();

  return (
    <Flex>
      <Flex gap={24}>
        <Header currency="usd" />
        <DashedListSeparator />
        <View gap={24}>
          <DetailsRow
            title={"Bank routing number"}
            value={depositInstructions.bank_routing_number}
          />
          <DetailsRow
            title={"Bank account number"}
            value={depositInstructions.bank_account_number}
          />
          <DetailsRow
            title={"Bank name"}
            value={depositInstructions.bank_name}
          />
          <DetailsRow
            title={"Bank beneficiary name"}
            value={depositInstructions.bank_beneficiary_name}
          />
          <DetailsRow
            title={"Bank address"}
            value={depositInstructions.bank_address}
          />
        </View>
      </Flex>

      <View gap={16}>
        <HelpText />
        <Button
          size={"medium-new"}
          onPress={async () => {
            try {
              const accountDetails = `Bank routing number: ${depositInstructions.bank_routing_number}
Bank account number: ${depositInstructions.bank_account_number}
Bank name: ${depositInstructions.bank_name}
Bank address: ${depositInstructions.bank_address}
Bank beneficiary name: ${depositInstructions.bank_beneficiary_name}`;

              await Share.share({
                message: accountDetails,
              });

              hapticSuccess();
            } catch (error) {
              toast.error("Failed to share account details", {
                id: "share-error",
              });
              reportError(
                queryClient,
                error instanceof Error
                  ? error
                  : new Error(`Failed to share account details: ${error}`)
              );
            }
          }}
          variant="secondary"
        >
          Share account details
        </Button>
      </View>
    </Flex>
  );
}

function EurDetails({
  depositInstructions,
}: {
  depositInstructions: DepositInstructions & { currency: "eur" };
}) {
  const { toast } = useToast();

  return (
    <Flex>
      <Flex gap={24}>
        <Header currency="eur" />
        <DashedListSeparator />
        <View gap={24}>
          <DetailsRow title={"IBAN"} value={depositInstructions.iban} />
          <DetailsRow title={"BIC"} value={depositInstructions.bic} />
          <DetailsRow
            title={"Account holder name"}
            value={depositInstructions.account_holder_name}
          />
          <DetailsRow
            title={"Bank name"}
            value={depositInstructions.bank_name}
          />
          <DetailsRow
            title={"Bank address"}
            value={depositInstructions.bank_address}
          />
        </View>
      </Flex>

      <View gap={16}>
        <HelpText />
        <Button
          size={"medium-new"}
          onPress={async () => {
            try {
              const accountDetails = `IBAN: ${depositInstructions.iban}
BIC: ${depositInstructions.bic}
Account holder name: ${depositInstructions.account_holder_name}
Bank name: ${depositInstructions.bank_name}
Bank address: ${depositInstructions.bank_address}`;

              await Share.share({
                message: accountDetails,
              });

              hapticSuccess();
            } catch (error) {
              toast.error("Failed to share account details", {
                id: "share-error",
              });
              reportError(
                queryClient,
                error instanceof Error
                  ? error
                  : new Error(`Failed to share account details: ${error}`)
              );
            }
          }}
          variant="secondary"
        >
          Share account details
        </Button>
      </View>
    </Flex>
  );
}

function DetailsRow({ title, value }: { title: string; value: string }) {
  const { toast } = useToast();
  return (
    <TouchableScale
      activeScale={0.98}
      hitSlop={8}
      onPress={() => {
        Clipboard.setStringAsync(value);
        toast.info("Copied", { id: "copy" });
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }}
    >
      <View gap={6}>
        <Text variant={"medium"} size={14} colorToken={"textSecondary"}>
          {title}
        </Text>

        <Row gap={8} justify={"space-between"}>
          <Text size={14} variant={"medium"}>
            {value}
          </Text>
          <Icon
            colorToken={"textSecondary"}
            name="doc.on.doc"
            size={8}
            weight="semibold"
          />
        </Row>
      </View>
    </TouchableScale>
  );
}
