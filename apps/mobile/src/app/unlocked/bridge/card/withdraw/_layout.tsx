import { Stack } from "expo-router";
import { DURATION_FAST } from "~/constants/animations";
import { createContext, ReactNode, useContext, useState } from "react";
import { createStore, StoreApi, useStore } from "zustand";
import invariant from "invariant";
import { ModalHeader } from "~/components/ModalHeader";
import { Address } from "@squads/models/solana";

export default function CardWithdrawLayout() {
  return (
    <CardWithdrawStateProvider
      value={{
        details: null,
      }}
    >
      <Stack
        screenOptions={{
          animationDuration: DURATION_FAST,
          statusBarStyle: "dark",
          header: Mo<PERSON>Header,
          headerBackButtonMenuEnabled: false,
          headerShadowVisible: false,
        }}
      >
        <Stack.Screen name="enter-amount" options={{ title: "Enter amount" }} />
      </Stack>
    </CardWithdrawStateProvider>
  );
}

export type CardWithdrawState = {
  details: { amountFloat: number; mint: Address } | null;
};

const CardWithdrawStateContext =
  createContext<StoreApi<CardWithdrawState> | null>(null);

const CardWithdrawStateProvider = ({
  children,
  value,
}: {
  children: ReactNode;
  value: CardWithdrawState;
}) => {
  const [store] = useState(() => createStore<CardWithdrawState>(() => value));

  return (
    <CardWithdrawStateContext.Provider value={store}>
      {children}
    </CardWithdrawStateContext.Provider>
  );
};

export const useCardWithdrawState = () => {
  const store = useContext(CardWithdrawStateContext);
  invariant(
    store,
    "useCardWithdrawState must be used within a CardWithdrawStateProvider"
  );
  return [useStore(store), store.setState] as const;
};
