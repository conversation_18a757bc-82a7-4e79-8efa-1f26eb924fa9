import { View } from "~/components/Themed";

import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Flex } from "~/components/Grid";
import { NumPad } from "~/components/NumPad";
import { Button } from "~/components/Button";
import { useFormik } from "formik";
import { toFormikValidationSchema } from "zod-formik-adapter";
import { router } from "expo-router";
import { useBalances, getMaxAmount } from "~/state/balances";
import { z } from "zod";
import { AmountZ } from "~/components/AmountDisplay";
import { useActiveWallet } from "~/state/wallet";
import { mints } from "~/constants/tokens";
import {
  AssetAmountInputBlock,
  TokenLabel,
} from "~/components/AssetAmountInputBlock";
import { useCardWithdrawState } from "~/app/unlocked/bridge/card/withdraw/_layout";
import invariant from "invariant";
import { AddressTag } from "~/components/send/AddressDetails";
import { useSuspenseToken } from "~/state/tokens";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { executeAction, prepareAction } from "~/services/wallets";
import { awaitActivityStatus } from "~/services/activities";
import { refetchCard } from "~/state/bridge";
import { useToast } from "~/components/Toaster";
import { numberToString } from "~/utils/conversions";

export default function EnterAmountScreen() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const insets = useSafeAreaInsets();

  const { wallet } = useActiveWallet();
  const card = wallet.card;
  invariant(card?.status === "issued", "card is not issued");

  const mint = mints.usdc;
  const token = useSuspenseToken({ mint });
  invariant(token, "token is not defined");

  const balances = useBalances({
    address: card?.cardVaultAddress,
  });
  const balance = balances.find((b) => b.mint === mint) ?? {
    mint: mint,
    amount: 0,
    decimals: token.decimals,
    usdcPrice: null,
  };

  const maxAmount = getMaxAmount({
    balance,
    spendingLimit: null,
  });

  const FormValuesZ = z.object({
    amount: AmountZ(maxAmount),
  });
  type FormValues = z.infer<typeof FormValuesZ>;

  const cardWithdrawMutation = useMutation({
    mutationFn: async (amount: number) => {
      const action = await prepareAction({
        type: "cardWithdrawInit",
        vaultIndex: 0,
        amount,
        mint,
      });

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );

      const { activityId } = await executeAction({ signedTransactions });
      await awaitActivityStatus({
        activityId,
        status: { type: "pending", state: "awaitsExecution" },
      });

      await refetchCard({ queryClient });

      router.dismissTo("/unlocked/bridge/card/main");
    },
    onSuccess: () => {
      toast.info("Withdrawal initiated");
    },
    onError: () => {
      toast.error("Withdrawal failed. Please try again.");
    },
  });

  const formik = useFormik<FormValues>({
    initialValues: {
      amount: "",
    },

    validationSchema: toFormikValidationSchema(FormValuesZ),

    async onSubmit(values) {
      const amountFloat = Number(values.amount);
      const amount = Math.round(amountFloat * 10 ** token.decimals);
      await cardWithdrawMutation.mutateAsync(amount);
    },
  });

  return (
    <View
      style={{ flex: 1, paddingHorizontal: 20, paddingBottom: insets.bottom }}
    >
      <Flex justify={"flex-end"} gap={28}>
        <View>
          <View gap={8} style={{ marginBottom: 20 }}>
            <View gap={12} style={{ marginBottom: 12 }}>
              <TokenLabel token={token} />
              <AssetAmountInputBlock
                amount={formik.values.amount}
                usdPrice={balance.usdcPrice}
                maxAmountQuery={{ status: "success", maxAmount }}
                onInstantChange={(amount) =>
                  formik.handleChange("amount")(numberToString(amount))
                }
              />
            </View>
          </View>

          <AddressTag address={wallet.defaultVault} />
        </View>

        <View style={{ gap: 20 }}>
          <NumPad
            value={formik.values.amount}
            onChange={formik.handleChange("amount")}
            decimals={2}
          />
          <Button
            iconName={"faceid"}
            iconWeight="medium"
            iconSize={14}
            error={
              formik.errors.amount === "insufficientBalance"
                ? "Insufficient balance"
                : undefined
            }
            loading={formik.isSubmitting}
            loadingText={"Initiating withdrawal"}
            disabled={!formik.dirty || !formik.isValid}
            onPress={() => formik.handleSubmit()}
          >
            Withdraw
          </Button>
        </View>
      </Flex>
    </View>
  );
}
