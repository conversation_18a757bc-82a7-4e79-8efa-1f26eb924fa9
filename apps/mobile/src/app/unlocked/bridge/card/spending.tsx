import { StackScreenHeader } from "~/components/headers/StackScreenHeader";
import { useHeaderHeight } from "@react-navigation/elements";
import { useHeaderAnimatedValues } from "~/hooks/useHeaderAnimatedValues";
import { useCardSpending } from "~/state/bridge";
import {
  CardSpending,
  CardTransactionCategory,
  MonthlyCardSpending,
} from "~/services/bridge";
import { BalanceSkeleton, BalanceView } from "~/components/Balance";
import { Text, View } from "~/components/Themed";
import { useState } from "react";
import { Row } from "~/components/Grid";
import { formatUsdValue } from "@squads/utils/numberFormats";
import Animated, {
  FadeIn,
  FadeOut,
  interpolate,
  ReduceMotion,
} from "react-native-reanimated";
import { DateTime } from "luxon";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import { hapticOpenModalSheet, hapticSelect } from "~/utils/haptics";
import { DashedListSeparator } from "~/components/ListSeparator";
import { ActivitiesStackGraphic } from "~/components/card/ActivitiesStackGraphic";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import { router } from "expo-router";
import { CardTransactionsRouteParams } from "~/app/unlocked/bridge/card/transactions-by-month";
import { Icon } from "~/components/Icon";
import { IconWrapper } from "~/components/IconWrapper";
import { getCategoryIconDetails } from "~/components/card/CardIcons";
import { DURATION_FAST, SPRING, ZoomAndFadeIn } from "~/constants/animations";
import { ContentSkeleton, TextSkeleton } from "~/components/Skeleton";
import { CardEventsSkeleton } from "~/components/card/CardHomeList";
import { LinearGradient, LinearGradientProps } from "expo-linear-gradient";
import { easeGradient } from "~/utils/easeGradient";

export default function SpendingScreen() {
  const headerHeight = useHeaderHeight();
  const { animatedScrollRef, headerBackgroundOpacity } =
    useHeaderAnimatedValues({ headerHeight });

  const cardSpendingQuery = useCardSpending();

  return (
    <>
      <StackScreenHeader
        blurIntensity={80}
        headerTitle={"Spending"}
        backgroundOpacity={headerBackgroundOpacity}
      />
      <KeyboardAwareScrollView
        ref={animatedScrollRef}
        contentInsetAdjustmentBehavior={"automatic"}
        style={{
          flex: 1,
          overflow: "visible",
          gap: 40,
          paddingHorizontal: 20,
          paddingTop: 20,
        }}
      >
        {cardSpendingQuery.data ? (
          <CardSpendingView spending={cardSpendingQuery.data} />
        ) : (
          <CardSpendingSkeleton />
        )}
      </KeyboardAwareScrollView>
    </>
  );
}

function CardSpendingSkeleton() {
  return (
    <View gap={24}>
      <View gap={4}>
        <BalanceSkeleton size={"large"} />
        <Row>
          <TextSkeleton size={8} />
        </Row>
      </View>

      <AverageSpendingView diffPercentage={0} />

      <SpendingChartSkeleton />

      <DashedListSeparator colorToken={"textTertiary"} />

      <View gap={16} style={{ marginTop: 16 }}>
        <CardEventsSkeleton numberOfRows={5} />
      </View>
    </View>
  );
}

const AnimatedText = Animated.createAnimatedComponent(Text);

function CardSpendingView({ spending }: { spending: CardSpending }) {
  const lastMonthSpending = spending.byMonth
    .sort((a, b) => a.month - b.month)
    .at(-1)?.month;

  const [selectedMonth, setSelectedMonth] = useState<string>(
    lastMonthSpending
      ? DateTime.fromSeconds(lastMonthSpending, { zone: "utc" }).toFormat("MMM")
      : DateTime.now().toFormat("MMM")
  );

  const selectedSpending = spending.byMonth.find(
    (month) =>
      DateTime.fromSeconds(month.month, { zone: "utc" }).toFormat("MMM") ===
      selectedMonth
  );

  const totalAmount = selectedSpending
    ? selectedSpending.byCategory.reduce((acc, cur) => acc + cur.amount, 0)
    : 0;

  const averageAmount =
    spending.byMonth.reduce(
      (acc, cur) =>
        acc + cur.byCategory.reduce((acc, cur) => acc + cur.amount, 0),
      0
    ) / spending.byMonth.length;

  const diffPercentage = selectedSpending
    ? Math.round(((totalAmount - averageAmount) / averageAmount) * 100)
    : 0;

  return (
    <View gap={24}>
      <View gap={4}>
        <BalanceView size={"large"} amount={totalAmount} />
        <Row>
          <Text variant={"medium"} size={14}>
            Spent in{" "}
          </Text>
          <AnimatedText
            key={DateTime.fromFormat(selectedMonth, "MMM").toFormat("MMMM")}
            variant={"medium"}
            size={14}
            entering={FadeIn.duration(DURATION_FAST).reduceMotion(
              ReduceMotion.Never
            )}
            exiting={FadeOut.duration(DURATION_FAST).reduceMotion(
              ReduceMotion.Never
            )}
          >
            {DateTime.fromFormat(selectedMonth, "MMM").toFormat("MMMM")}
          </AnimatedText>
        </Row>
      </View>

      <AverageSpendingView diffPercentage={diffPercentage} />

      <SpendingChart
        selected={selectedMonth}
        onSelect={setSelectedMonth}
        spending={spending}
      />

      <DashedListSeparator colorToken={"textTertiary"} />

      <View gap={16} style={{ marginTop: 12 }}>
        {selectedSpending ? (
          <>
            <Text variant={"medium"} size={14} colorToken={"textSecondary"}>
              Categories
            </Text>
            <SpendingCategoryList monthlyCardSpending={selectedSpending} />
          </>
        ) : (
          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
              paddingVertical: 20,
              marginTop: 60,
            }}
          >
            <ActivitiesStackGraphic />
            <Text variant="semibold" size={14} colorToken={"textTertiary"}>
              No transactions
            </Text>
          </View>
        )}
      </View>
    </View>
  );
}

function AverageSpendingView({ diffPercentage }: { diffPercentage: number }) {
  if (diffPercentage === 0) {
    return (
      <View gap={6}>
        <Text variant={"semibold"} size={17} colorToken={"text"}>
          {" "}
        </Text>
        <Text variant={"medium"} size={14} colorToken={"textSecondary"}>
          {" \n "}
        </Text>
      </View>
    );
  }

  return (
    <Animated.View
      key={diffPercentage}
      style={{ gap: 6 }}
      entering={FadeIn.duration(DURATION_FAST).reduceMotion(ReduceMotion.Never)}
      exiting={FadeOut.duration(DURATION_FAST).reduceMotion(ReduceMotion.Never)}
    >
      <Row gap={4}>
        <Icon
          weight={"heavy"}
          name={diffPercentage > 0 ? "arrow.up.right" : "arrow.down.right"}
          size={10}
          colorToken={"textSecondary"}
        />
        <Text variant={"semibold"} size={17} colorToken={"text"}>
          {Math.abs(diffPercentage)}%
        </Text>
      </Row>
      <Text variant={"medium"} size={14} colorToken={"textSecondary"}>
        {diffPercentage > 0 ? "More" : "Less"} than your average{"\n"}
        monthly spending
      </Text>
    </Animated.View>
  );
}

function SpendingChart({
  spending,
  selected,
  onSelect,
}: {
  spending: CardSpending;
  selected: string;
  onSelect: (month: string) => void;
}) {
  const last6MonthsSpending = spending.byMonth
    .sort((a, b) => a.month - b.month)
    .slice(-6);

  const dataByMonth: Record<string, number> = last6MonthsSpending.reduce(
    (acc, month) => {
      acc[DateTime.fromSeconds(month.month, { zone: "utc" }).toFormat("MMM")] =
        month.byCategory.reduce((acc, cur) => acc + cur.amount, 0);
      return acc;
    },
    {} as Record<string, number>
  );

  const now = DateTime.now();
  const last6Month: string[] = Array.from({ length: 6 }).map((_, i) => {
    return now.minus({ months: 5 - i }).toFormat("MMM");
  });

  const maxSpending = Math.max(...Object.values(dataByMonth));

  const GRADIENT_STOPS = easeGradient({
    colorStops: {
      0: "#000000",
      1: "#373737",
    },
  });

  return (
    <Row gap={12} style={{ alignItems: "flex-end" }}>
      {last6Month.map((month) => {
        const heightPercentage = interpolate(
          dataByMonth[month] ?? 0,
          [0, maxSpending],
          [8, 100]
        );
        return (
          <AnimatedTouchableScale
            pressedScale={0.99}
            onPress={() => {
              hapticSelect();
              onSelect(month);
            }}
            key={month}
            style={{ flex: 1, transformOrigin: "bottom" }}
          >
            <View style={{ flex: 1, gap: 12, justifyContent: "flex-end" }}>
              {month === selected ? (
                <LinearGradient
                  start={[0, 0]}
                  end={[0, 1]}
                  locations={
                    GRADIENT_STOPS.locations as LinearGradientProps["locations"]
                  }
                  colors={
                    GRADIENT_STOPS.colors as LinearGradientProps["colors"]
                  }
                  style={{
                    borderRadius: 10,
                    borderCurve: "continuous",
                    height: 140 * (heightPercentage / 100),
                  }}
                />
              ) : (
                <View
                  background={"backgroundSecondary"}
                  style={{
                    borderRadius: 10,
                    borderCurve: "continuous",
                    height: 140 * (heightPercentage / 100),
                  }}
                />
              )}
              <Text
                align={"center"}
                variant={"medium"}
                size={14}
                colorToken={month === selected ? "text" : "textSecondary"}
              >
                {month}
              </Text>
            </View>
          </AnimatedTouchableScale>
        );
      })}
    </Row>
  );
}

function SpendingChartSkeleton() {
  const now = DateTime.now();
  const last6Month: string[] = Array.from({ length: 6 }).map((_, i) => {
    return now.minus({ months: 5 - i }).toFormat("MMM");
  });

  const heights = [25, 40, 75, 100, 85, 30];

  return (
    <Row gap={12} style={{ alignItems: "flex-end" }}>
      {last6Month.map((month, index) => {
        const heightPercentage = interpolate(
          heights[index],
          [0, 100],
          [8, 100]
        );
        return (
          <View key={month} style={{ flex: 1, transformOrigin: "bottom" }}>
            <View style={{ flex: 1, gap: 12, justifyContent: "flex-end" }}>
              <ContentSkeleton borderRadius={10}>
                <View
                  background={"backgroundSecondary"}
                  style={{
                    borderCurve: "continuous",
                    height: 140 * (heightPercentage / 100),
                  }}
                />
              </ContentSkeleton>
              <Text
                align={"center"}
                variant={"medium"}
                size={14}
                colorToken={"textSecondary"}
              >
                {month}
              </Text>
            </View>
          </View>
        );
      })}
    </Row>
  );
}

export const LIST_ITEM_ENTERING = ZoomAndFadeIn.withInitialValues({
  opacity: 0,
  transform: [{ scale: 0.97 }],
})
  .stiffness(SPRING.stiffness)
  .damping(SPRING.damping)
  .mass(SPRING.mass)
  .reduceMotion(ReduceMotion.Never);

function SpendingCategoryList({
  monthlyCardSpending,
}: {
  monthlyCardSpending: MonthlyCardSpending;
}) {
  const totalAmount = monthlyCardSpending.byCategory.reduce(
    (acc, cur) => acc + cur.amount,
    0
  );

  const sortedCategories = monthlyCardSpending.byCategory
    .sort((a, b) => b.amount - a.amount)
    .map(({ amount, category }) => {
      return {
        amount,
        category,
        percent: Math.floor((amount / totalAmount) * 100),
      };
    });

  // This is a hack to make sure the percents add up to 100
  const remainder =
    100 - sortedCategories.reduce((sum, { percent }) => sum + percent, 0);
  for (let i = 0; i < remainder; i++) {
    sortedCategories[i % sortedCategories.length].percent++;
  }

  return (
    <View gap={18}>
      {sortedCategories.map(({ category, amount, percent }) => (
        <Animated.View key={category} entering={LIST_ITEM_ENTERING}>
          <AnimatedTouchableScale
            onPress={() => {
              hapticOpenModalSheet();
              router.push({
                pathname: "/unlocked/bridge/card/transactions-by-month",
                params: {
                  category,
                  month: monthlyCardSpending.month,
                } satisfies CardTransactionsRouteParams,
              });
            }}
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
            }}
          >
            <Row gap={12}>
              <CategoryIcon category={category} />
              <Text variant={"medium"} size={14}>
                {CardTransactionCategory.toString(category)}
              </Text>
            </Row>

            <Row gap={12}>
              <Text variant={"semibold"} size={14}>
                {formatUsdValue(amount)}
              </Text>
              <Text
                align={"right"}
                style={{ minWidth: 30 }}
                variant={"medium"}
                size={14}
                colorToken={"textSecondary"}
              >
                {percent === 0 ? "< 1" : percent}%
              </Text>
            </Row>
          </AnimatedTouchableScale>
        </Animated.View>
      ))}
    </View>
  );
}

function CategoryIcon({ category }: { category: CardTransactionCategory }) {
  const { name, color } = getCategoryIconDetails({ category });

  return (
    <IconWrapper size={32} background={"transparent"}>
      <Icon name={name} color={color} size={13} />
    </IconWrapper>
  );
}
