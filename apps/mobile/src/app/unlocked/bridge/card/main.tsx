import { useSuspenseCard, useSuspenseCardBalance } from "~/state/bridge";
import { Address } from "@squads/models/solana";
import { Text, View } from "~/components/Themed";
import {
  BalanceFallback,
  BalanceSkeleton,
  BalanceView,
} from "~/components/Balance";
import { Suspense } from "react";
import { TouchableScale } from "~/components/TouchableScale";
import { Icon } from "~/components/Icon";
import { useRouter } from "expo-router";
import Animated, {
  AnimatedRef,
  useAnimatedStyle,
  useDerivedValue,
  useScrollViewOffset,
  withDelay,
  withTiming,
} from "react-native-reanimated";
import { useHeaderHeight } from "@react-navigation/elements";
import { DURATION_FAST } from "~/constants/animations";
import { useHeaderAnimatedValues } from "~/hooks/useHeaderAnimatedValues";
import { StackScreenHeader } from "~/components/headers/StackScreenHeader";
import { hapticGoForward } from "~/utils/haptics";
import { CardHomeList } from "~/components/card/CardHomeList";
import invariant from "invariant";
import { mints } from "~/constants/tokens";
import { FuseErrorBoundary } from "~/components/errors/FuseErrorBoundary";
import { OptionalBackButton } from "~/components/headers/BackButton";
import {
  useCardAnimationProgress,
  useCardOpacityAnimationStyle,
} from "~/hooks/useCardAnimation";
import { FuseSuspense } from "~/components/FuseSuspense";
import { Spinner } from "~/components/Spinner";

function HeaderTitle({
  animatedRef,
  headerHeight,
}: {
  animatedRef: AnimatedRef<Animated.ScrollView>;
  headerHeight: number;
  cardVaultAddress: Address;
}) {
  const scrollOffset = useScrollViewOffset(animatedRef);

  // BalanceView height + ScrollView paddingTop - initial negative scroll offset due to `contentInsetAdjustmentBehavior`
  const switchHeaderScrollThreshold = 72 + 12 - headerHeight;

  const activeTitle = useDerivedValue(() => {
    return scrollOffset.value <= switchHeaderScrollThreshold
      ? "screen-name"
      : "balance";
  });

  // // Haptic feedback when switching between screen and balance titles
  // useAnimatedReaction(
  //   () => activeTitle.value,
  //   (activeTitle, prevActiveTitle) => {
  //     runOnJS(log)(activeTitle, prevActiveTitle);
  //     if (activeTitle === "screen-name" && prevActiveTitle === "balance") {
  //       runOnJS(hapticGoBack)();
  //     } else if (
  //       activeTitle === "balance" &&
  //       prevActiveTitle === "screen-name"
  //     ) {
  //       runOnJS(hapticGoForward)();
  //     }
  //   },
  //   [activeTitle]
  // );

  const screenTitleStyle = useAnimatedStyle(() => {
    const shouldShow = activeTitle.value === "screen-name";

    return {
      transform: [
        {
          translateY: shouldShow
            ? withDelay(30, withTiming(0, { duration: DURATION_FAST }))
            : withTiming(-10, { duration: DURATION_FAST }),
        },
      ],
      opacity: shouldShow
        ? withDelay(30, withTiming(1, { duration: DURATION_FAST }))
        : withTiming(0, { duration: DURATION_FAST }),
    };
  });
  const balanceTitleStyle = useAnimatedStyle(() => {
    const shouldShow = activeTitle.value === "balance";

    return {
      transform: [
        {
          translateY: shouldShow
            ? withDelay(30, withTiming(0, { duration: DURATION_FAST }))
            : withTiming(16, { duration: DURATION_FAST }),
        },
      ],
      opacity: shouldShow
        ? withDelay(30, withTiming(1, { duration: DURATION_FAST }))
        : withTiming(0, { duration: DURATION_FAST }),
    };
  });

  return (
    <View style={{ overflow: "visible", justifyContent: "center" }}>
      <Animated.View style={screenTitleStyle}>
        <Text size={18} variant={"semibold"}>
          Fuse Card
        </Text>
      </Animated.View>
      <Animated.View
        style={[
          {
            position: "absolute",
            alignSelf: "center",
            alignItems: "center",
            width: "250%",
            overflow: "visible",
          },
          balanceTitleStyle,
        ]}
      >
        <FuseErrorBoundary
          FallbackComponent={() => <BalanceFallback size={"small"} />}
        >
          <Suspense fallback={<BalanceSkeleton size={"small"} />}>
            <BalanceViewInner />
          </Suspense>
        </FuseErrorBoundary>
      </Animated.View>
    </View>
  );
}

function BalanceViewInner() {
  const { balances } = useSuspenseCardBalance();

  const fiatAmount = balances[mints.usdc]?.total ?? 0;

  return <BalanceView size={"small"} amount={fiatAmount} />;
}

export default function CardMainScreen() {
  return (
    <FuseSuspense fallback={<CardMainScreenSkeleton />}>
      <CardMainScreenInner />
    </FuseSuspense>
  );
}

function CardMainScreenSkeleton() {
  return (
    <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
      <Spinner size={24} colorToken={"text"} />
    </View>
  );
}

function CardMainScreenInner() {
  const card = useSuspenseCard();
  invariant(card, "expected card to exist");

  const headerHeight = useHeaderHeight();
  const { animatedScrollRef, headerBackgroundOpacity } =
    useHeaderAnimatedValues({ headerHeight });

  const animationProgress = useCardAnimationProgress();
  const animatedStyle = useCardOpacityAnimationStyle(animationProgress);

  return (
    <>
      <StackScreenHeader
        blurIntensity={80}
        backgroundOpacity={headerBackgroundOpacity}
        headerTitle={() => (
          <Animated.View style={animatedStyle}>
            <HeaderTitle
              animatedRef={animatedScrollRef}
              headerHeight={headerHeight}
              cardVaultAddress={card.cardVaultAddress}
            />
          </Animated.View>
        )}
        headerRight={() => (
          <Animated.View style={animatedStyle}>
            <HeaderRight />
          </Animated.View>
        )}
        headerLeft={() => (
          <Animated.View style={animatedStyle}>
            <HeaderLeft />
          </Animated.View>
        )}
      />
      <CardHomeList
        card={card}
        animationProgress={animationProgress}
        animatedScrollRef={animatedScrollRef}
      />
    </>
  );
}

function HeaderRight() {
  const router = useRouter();

  return (
    <TouchableScale
      style={{ marginRight: -2 }}
      activeScale={0.95}
      onPress={() => {
        hapticGoForward();
        router.push("/unlocked/bridge/card/settings");
      }}
    >
      <Icon
        name={"gearshape"}
        size={13}
        rectSize={32}
        colorToken={"text"}
        weight="semibold"
      />
    </TouchableScale>
  );
}

function HeaderLeft() {
  return <OptionalBackButton />;
}
