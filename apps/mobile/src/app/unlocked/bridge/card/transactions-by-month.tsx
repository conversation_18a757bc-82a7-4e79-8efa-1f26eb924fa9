import { StackScreenHeader } from "~/components/headers/StackScreenHeader";
import {
  CardEvent,
  CardTransaction,
  CardTransactionCategory,
  CardTransactionCategoryZ,
} from "~/services/bridge";
import { z } from "zod";
import { useLocalSearchParams } from "expo-router";
import { useCardTransactionsByMonth } from "~/state/bridge";
import {
  CardEventsSkeleton,
  ListItem,
  ListItemSpacer,
  ListItemView,
  TRANSACTION_ITEM_ENTERING,
} from "~/components/card/CardHomeList";
import { Text, View } from "~/components/Themed";
import { DateTime } from "luxon";
import Animated from "react-native-reanimated";
import { useHeaderAnimatedValues } from "~/hooks/useHeaderAnimatedValues";
import { useHeaderHeight } from "@react-navigation/elements";

const RouteParamsZ = z.object({
  category: CardTransactionCategoryZ,
  month: z.coerce.number(),
});

export type CardTransactionsRouteParams = z.infer<typeof RouteParamsZ>;

export default function SpendingByCategoryScreen() {
  const { category, month } = RouteParamsZ.parse(useLocalSearchParams());

  const transactionsByMonthQuery = useCardTransactionsByMonth({ month });

  const headerHeight = useHeaderHeight();
  const { animatedScrollRef, headerBackgroundOpacity } =
    useHeaderAnimatedValues({ headerHeight });

  return (
    <>
      <StackScreenHeader
        headerTitle={() => (
          <View>
            {/*  `${CardTransactionCategory.toString(category)} in ${DateTime.fromSeconds(month).toFormat("MMMM")}`*/}
            <Text variant="medium" style={{ fontSize: 16 }} align={"center"}>
              {CardTransactionCategory.toString(category)}
            </Text>
            <Text
              variant="medium"
              style={{ fontSize: 14 }}
              colorToken="textSecondary"
              align={"center"}
            >
              {DateTime.fromSeconds(month, { zone: "utc" }).toFormat("MMMM")}
            </Text>
          </View>
        )}
        backgroundOpacity={headerBackgroundOpacity}
      />
      <Animated.ScrollView
        ref={animatedScrollRef}
        contentInsetAdjustmentBehavior="automatic"
        contentContainerStyle={{
          paddingHorizontal: 20,
        }}
      >
        <View style={{ width: "100%" }}>
          {transactionsByMonthQuery.data ? (
            <TransactionList
              transactions={transactionsByMonthQuery.data.transactions.filter(
                (transaction) => transaction.category === category
              )}
            />
          ) : (
            <CardEventsSkeleton numberOfRows={16} />
          )}
        </View>
      </Animated.ScrollView>
    </>
  );
}

function TransactionList({
  transactions,
}: {
  transactions: CardTransaction[];
}) {
  const cardEvents = transactions.map((transaction) => ({
    type: "transaction" as const,
    transaction,
  }));

  const eventsByDate = new Map<string, ListItem[]>();
  for (const event of cardEvents) {
    const dateTime = DateTime.fromSeconds(event.transaction.postedAt);
    const date = dateTime.equals(DateTime.now())
      ? "Today"
      : dateTime.toLocaleString(DateTime.DATE_MED);

    eventsByDate.set(date, [
      ...(eventsByDate.get(date) ?? []),
      { rowType: "event", event, pageIndex: 0 } as const,
    ]);
  }

  const sectionAndEventRows = Array.from(eventsByDate.entries()).flatMap(
    ([date, events]) => {
      return [
        { rowType: "section-header", date, pageIndex: 0 } as const,
        ...events,
      ] satisfies ListItem[];
    }
  );

  return (
    <>
      {sectionAndEventRows.map((item) => (
        <Animated.View
          key={
            item.rowType === "list-title"
              ? "title"
              : item.rowType === "section-header"
                ? item.date
                : CardEvent.id(item.event)
          }
          entering={TRANSACTION_ITEM_ENTERING}
        >
          <ListItemView item={item} />
          <ListItemSpacer leadingItem={item} />
        </Animated.View>
      ))}
    </>
  );
}
