import { Stack } from "expo-router";
import { DURATION_FAST } from "~/constants/animations";
import { ModalHeader } from "~/components/ModalHeader";

export default function CardDepositLayout() {
  return (
    <Stack
      screenOptions={{
        animationDuration: DURATION_FAST,
        statusBarStyle: "dark",
        header: <PERSON><PERSON><PERSON>eader,
        headerBackButtonMenuEnabled: false,
        headerShadowVisible: false,
      }}
    >
      <Stack.Screen name="enter-amount" options={{ title: "Enter amount" }} />
    </Stack>
  );
}
