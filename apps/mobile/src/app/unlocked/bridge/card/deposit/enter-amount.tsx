import { View } from "~/components/Themed";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Flex } from "~/components/Grid";
import { NumPad } from "~/components/NumPad";
import { Button } from "~/components/Button";
import { useFormik } from "formik";
import { toFormikValidationSchema } from "zod-formik-adapter";
import { router } from "expo-router";
import { refetchBalances, useBalances, getMaxAmount } from "~/state/balances";
import { z } from "zod";
import { AmountZ } from "~/components/AmountDisplay";
import { useActiveWallet } from "~/state/wallet";
import { mints } from "~/constants/tokens";
import {
  AssetAmountInputBlock,
  TokenLabel,
  UseSpendingLimitRow,
} from "~/components/AssetAmountInputBlock";
import invariant from "invariant";
import { AddressTag } from "~/components/send/AddressDetails";
import { useSuspenseToken } from "~/state/tokens";
import { executeAction, prepareAction } from "~/services/wallets";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { confirmActivity } from "~/services/activities";
import { refetchCardEvents } from "~/state/bridge";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "~/components/Toaster";
import { useSpendingLimits } from "~/state/spendingLimits";
import { useState } from "react";
import { DashedListSeparator } from "~/components/ListSeparator";
import { numberToString } from "~/utils/conversions";

const MIN_CARD_TOP_UP_AMOUNT = 5.0;

export default function EnterAmountScreen() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const insets = useSafeAreaInsets();

  const { wallet } = useActiveWallet();
  const card = wallet.card;
  invariant(card?.status === "issued", "card is not issued");

  const mint = mints.usdc;
  const token = useSuspenseToken({ mint });
  invariant(token, "token is not defined");

  const [shouldUseSpendingLimit, setShouldUseSpendingLimit] = useState(false);
  const { spendingLimits } = useSpendingLimits({
    walletKey: wallet.walletKey,
    vaultKey: wallet.defaultVault,
  });
  const availableSpendingLimit =
    spendingLimits.find((s) => (s.mint ?? "SOL") === mint) ?? null;
  const selectedSpendingLimit = shouldUseSpendingLimit
    ? availableSpendingLimit
    : null;

  const balances = useBalances({
    address: wallet.defaultVault,
  });
  const balance = balances.find((b) => b.mint === mint) ?? {
    mint: mint,
    amount: 0,
    decimals: token.decimals,
    usdcPrice: null,
  };

  const maxAmount = getMaxAmount({
    balance,
    spendingLimit: selectedSpendingLimit,
  });

  const FormValuesZ = z.object({
    amount: AmountZ(maxAmount, MIN_CARD_TOP_UP_AMOUNT),
  });
  type FormValues = z.infer<typeof FormValuesZ>;

  const cardDepositMutation = useMutation({
    mutationFn: async (amount: number) => {
      const action = await prepareAction({
        type: "cardTopUp",
        vaultIndex: 0,
        amount,
        mint,
        spendingLimitAddress: selectedSpendingLimit?.address ?? null,
      });

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );

      const { activityId } = await executeAction({ signedTransactions });
      await confirmActivity({ activityId });

      await Promise.all([
        refetchBalances({ queryClient, address: card.cardVaultAddress }),
        refetchCardEvents(queryClient),
      ]);

      router.dismissTo("/unlocked/bridge/card/main");
    },
    onSuccess: () => {
      toast.info("Deposit successful");
    },
    onError: () => {
      toast.error("Deposit failed. Please try again.");
    },
  });

  const formik = useFormik<FormValues>({
    initialValues: {
      amount: "",
    },

    validationSchema: toFormikValidationSchema(FormValuesZ),

    async onSubmit(values) {
      const amountFloat = Number(values.amount);
      const amount = Math.round(amountFloat * 10 ** token.decimals);
      await cardDepositMutation.mutateAsync(amount);
    },
  });

  return (
    <View
      style={{ flex: 1, paddingHorizontal: 20, paddingBottom: insets.bottom }}
    >
      <Flex justify="flex-end" gap={28}>
        <View>
          <View gap={8} style={{ marginBottom: 20 }}>
            <View gap={12} style={{ marginBottom: 12 }}>
              <TokenLabel token={token} />
              <AssetAmountInputBlock
                amount={formik.values.amount}
                usdPrice={balance.usdcPrice}
                maxAmountQuery={{ status: "success", maxAmount }}
                onInstantChange={(amount) =>
                  formik.handleChange("amount")(numberToString(amount))
                }
              />
            </View>

            {availableSpendingLimit && (
              <View style={{ gap: 20 }}>
                <DashedListSeparator />
                <UseSpendingLimitRow
                  enabled={!!selectedSpendingLimit}
                  onSwitchChange={(enabled) => {
                    setShouldUseSpendingLimit(enabled);
                  }}
                />
              </View>
            )}
          </View>

          <AddressTag address={card.cardVaultAddress} />
        </View>

        <View style={{ gap: 20 }}>
          <NumPad
            value={formik.values.amount}
            onChange={formik.handleChange("amount")}
            decimals={2}
          />
          <Button
            iconName="faceid"
            iconWeight="medium"
            iconSize={14}
            error={
              formik.errors.amount === "insufficientBalance"
                ? "Insufficient balance"
                : formik.errors.amount?.startsWith("minAmountExceeded")
                  ? `Minimum amount is ${MIN_CARD_TOP_UP_AMOUNT} USDC`
                  : undefined
            }
            loading={formik.isSubmitting}
            loadingText="Topping up"
            disabled={!formik.dirty || !formik.isValid}
            onPress={() => formik.handleSubmit()}
          >
            Top up
          </Button>
        </View>
      </Flex>
    </View>
  );
}
