import { Text, View } from "~/components/Themed";
import { Row } from "~/components/Grid";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import { AppleWalletIcon } from "~/components/icons/AppleWalletIcon";
import {
  Section,
  SectionHeading,
  SettingRow,
} from "~/components/settings/SettingRow";
import * as Haptics from "expo-haptics";
import { openURL } from "expo-linking";
import { showCardFreezeModal } from "~/components/card/CardFreezeModal";
import { VisaCard } from "~/components/card/VisaCard";
import { useState } from "react";
import {
  CardSLChangeModal,
  showCardSLChangeModal,
} from "~/components/card/CardSLChangeModal";
import { StackScreenHeader } from "~/components/headers/StackScreenHeader";
import { useHeaderHeight } from "@react-navigation/elements";
import { useHeaderAnimatedValues } from "~/hooks/useHeaderAnimatedValues";
import { P3 } from "~/components/typography/P3";
import { useSuspenseCard } from "~/state/bridge";
import invariant from "invariant";
import { mints } from "~/constants/tokens";
import { useSuspenseToken } from "~/state/tokens";
import { formatUsdValue } from "@squads/utils/numberFormats";
import {
  CardBankStatementModal,
  showCardBankStatementModal,
} from "~/components/card/CardBankStatementModal";
import { showCardUnfreezeModal } from "~/components/card/CardUnfreezeModal";
import {
  hapticDismissBottomTray,
  hapticOpenBottomTray,
  hapticOpenModalSheet,
  hapticSelect,
  hapticSuccess,
} from "~/utils/haptics";
import { Card, CardPendingAction } from "~/services/bridge";
import { Tag } from "~/components/Tag";
import { showCardPendingActivityModal } from "~/components/card/CardPendingActivityModal";
import { CardPinModal } from "~/components/card/CardPinModal";
import { router } from "expo-router";
import { addCardToAppleWallet } from "~/services/card";
import { useToast } from "~/components/Toaster";
import { useQueryClient } from "@tanstack/react-query";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import { Image } from "expo-image";
import { showCardReferralModal } from "~/components/modals/CardReferralModal";
import { Icon } from "~/components/Icon";

export default function CardSettingsScreen() {
  const card = useSuspenseCard();
  invariant(card, "card not set");

  const headerHeight = useHeaderHeight();
  const { animatedScrollRef, headerBackgroundOpacity } =
    useHeaderAnimatedValues({ headerHeight });

  return (
    <>
      <StackScreenHeader
        blurIntensity={80}
        headerTitle={"Card Settings"}
        backgroundOpacity={headerBackgroundOpacity}
      />
      <KeyboardAwareScrollView
        ref={animatedScrollRef}
        contentInsetAdjustmentBehavior={"automatic"}
        style={{ flex: 1, overflow: "visible", gap: 40, paddingHorizontal: 32 }}
      >
        <View
          gap={16}
          style={{ alignItems: "center", paddingTop: 20, marginBottom: 24 }}
        >
          <View gap={12} style={{ width: "80%", alignItems: "center" }}>
            <VisaCard showDigits={false} />
            <Text
              variant={"semibold"}
              style={{ fontSize: 14 }}
              colorToken="textSecondary"
            >
              ···· {card.details.last4}
            </Text>
          </View>
          <AddToAppleWalletButton card={card} />
        </View>

        {card.referral.remaining > 0 && (
          <View style={{ marginBottom: 24, marginHorizontal: -16 }}>
            <InviteFriendBanner />
          </View>
        )}

        <SectionHeading>General</SectionHeading>
        <Section>
          {card.status === "frozen" ? (
            <SettingRow
              iconName="snowflake"
              label="Unfreeze Card"
              onPress={() => {
                hapticSelect();
                showCardUnfreezeModal();
              }}
            />
          ) : (
            <SettingRow
              iconName="snowflake"
              label="Freeze Card"
              onPress={() => {
                hapticSelect();
                showCardFreezeModal();
              }}
            />
          )}
          {/*<SettingRow*/}
          {/*  iconName="arrow.counterclockwise"*/}
          {/*  label="Reissue Card"*/}
          {/*  onPress={() => {*/}
          {/*    Haptics.selectionAsync();*/}
          {/*  }}*/}
          {/*/>*/}
          <SpendingLimitSettingRow card={card} />
          <CardSLChangeModal />
          <SettingRow
            // @ts-ignore
            iconName="text.rectangle.page"
            label="Bank statement"
            onPress={() => {
              Haptics.selectionAsync();
              showCardBankStatementModal();
            }}
          />
          <CardBankStatementModal />
          <PinSettingsRow />
        </Section>

        <SectionHeading>About</SectionHeading>
        <Section>
          <SettingRow
            iconName="ellipsis.message"
            label="Contact support"
            onPress={() => {
              Haptics.selectionAsync();
              openURL(`https://t.me/+X2BzTAWsMN01ZTNk`);
            }}
          />
          <SettingRow
            iconName="star.bubble"
            label="Share your feedback"
            onPress={() => {
              Haptics.selectionAsync();
              router.push("/unlocked/bridge/card/complaint");
            }}
          />
          <SettingRow
            iconName="checkmark.shield"
            label="Terms & Conditions"
            onPress={() => {
              Haptics.selectionAsync();
              openURL(`https://fusewallet.com/legal/terms-of-service`);
            }}
          />
        </Section>
      </KeyboardAwareScrollView>
    </>
  );
}

function SpendingLimitSettingRow({ card }: { card: Card }) {
  return (
    <SettingRow
      iconName="dial.high"
      label="Daily Limit"
      value={<CardSpendingLimitValue card={card} />}
      onPress={() => {
        if (
          card.pendingAction &&
          !CardPendingAction.isSLChange(card.pendingAction)
        ) {
          const timeLock = Card.pendingActivityTimeLock(card);
          if (timeLock) {
            showCardPendingActivityModal({ isTimeLockReady: timeLock.isReady });
            return;
          }
        }

        showCardSLChangeModal({ card });
      }}
    />
  );
}

function CardSpendingLimitValue({ card }: { card: Card }) {
  const usdcSpendingLimit = card.spendingLimits[mints.usdc];
  invariant(usdcSpendingLimit, "usdc spending limit not set");

  const token = useSuspenseToken({ mint: mints.usdc });
  invariant(token, "usdc token not found");

  const usdcSLAmountFloat = usdcSpendingLimit.amount / 10 ** token.decimals;

  const pendingSLChange =
    card.pendingAction && CardPendingAction.isSLChange(card.pendingAction)
      ? card.pendingAction
      : null;

  if (pendingSLChange) {
    return (
      <Tag style={{ minWidth: 64, alignItems: "center" }}>
        <Text size={14} variant={"semibold"}>
          Pending
        </Text>
      </Tag>
    );
  }

  return (
    <P3 colorToken="textSecondary">
      {formatUsdValue(usdcSLAmountFloat, { maximumFractionDigits: 0 })}
    </P3>
  );
}

function PinSettingsRow() {
  const [pinModalVisible, setPinModalVisible] = useState(false);
  const { toast } = useToast();

  return (
    <>
      <CardPinModal
        visible={pinModalVisible}
        onClose={() => {
          hapticDismissBottomTray();
          setPinModalVisible(false);
        }}
        onComplete={() => {
          hapticSuccess();
          setPinModalVisible(false);
          toast.success("PIN changed");
        }}
      />
      <SettingRow
        iconName="staroflife"
        label="Change PIN"
        onPress={() => {
          hapticOpenModalSheet();
          setPinModalVisible(true);
        }}
      />
    </>
  );
}

function AddToAppleWalletButton({ card }: { card: Card }) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return (
    <AnimatedTouchableScale
      onPress={async () => {
        hapticOpenModalSheet();
        const response = await addCardToAppleWallet(queryClient, {
          cardholderName: "",
          primaryAccountSuffix: card.details.last4,
        });

        if (response.status === "success") {
          hapticSuccess();
          toast.success("Card added to Apple Wallet.");
        }
      }}
    >
      <Row
        background={"backgroundBanner"}
        style={{
          borderRadius: 20,
          borderCurve: "continuous",
          paddingVertical: 10,
          paddingHorizontal: 18,
          gap: 10,
        }}
      >
        <AppleWalletIcon />
        <Text size={15} variant={"semibold"}>
          Add to Apple Wallet
        </Text>
      </Row>
    </AnimatedTouchableScale>
  );
}

function InviteFriendBanner() {
  return (
    <AnimatedTouchableScale
      pressedScale={0.99}
      onPress={async () => {
        hapticOpenBottomTray();
        showCardReferralModal();
      }}
    >
      <Row
        background={"backgroundBanner"}
        justify={"space-between"}
        style={{
          paddingVertical: 13,
          paddingLeft: 10,
          paddingRight: 16,
          borderRadius: 20,
          borderWidth: 1,
          borderColor: "rgba(0,0,0,0.07)",
          borderCurve: "continuous",
        }}
      >
        <Row gap={12}>
          <Image
            source={require("../../../../../assets/images/actions/card-referral.svg")}
            style={{ width: 42, aspectRatio: 1 }}
          />
          <View gap={3}>
            <Text variant={"semibold"} size={15}>
              Invite friends
            </Text>
            <Text variant={"medium"} size={13} colorToken="textSecondary">
              $5 for you, $5 for them
            </Text>
          </View>
        </Row>
        <Icon
          name="chevron.right"
          size={8}
          colorToken="textSecondary"
          weight="heavy"
          rectSize={24}
        />
      </Row>
    </AnimatedTouchableScale>
  );
}
