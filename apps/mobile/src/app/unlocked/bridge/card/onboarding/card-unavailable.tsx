import earthImage from "~assets/images/card/earth.png";
import { Text, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import { Link, router } from "expo-router";
import { Image } from "expo-image";
import { FullscreenFlowContainer } from "~/components/FullscreenFlowContainer";
import { hapticSelect } from "~/utils/haptics";
import { StackScreenHeader } from "~/components/headers/StackScreenHeader";
import { Flex } from "~/components/Grid";

export default function CardUnavailableScreen() {
  return (
    <>
      <StackScreenHeader headerTitle={""} />
      <FullscreenFlowContainer
        body={
          <Flex
            style={{
              paddingHorizontal: 20,
              gap: 42,
              alignItems: "center",
              justifyContent: "center",
              paddingBottom: "25%",
            }}
          >
            <Image
              source={earthImage}
              style={{ width: "75%", aspectRatio: 1 }}
            />
            <View gap={8}>
              <Text size={24} variant={"semibold"} align={"center"}>
                Fuse Card is not yet{"\n"}available for your region
              </Text>
              <Text
                size={16}
                variant={"medium"}
                colorToken={"textSecondary"}
                align={"center"}
              >
                Fuse Card is only available to US users{"\n"}(excluded states:
                NY and AK). More regions{"\n"}will be announced later. If you
                need card support,{"\n"}please email{" "}
                <Link href={"mailto:<EMAIL>"}>
                  <EMAIL>
                </Link>
                .
              </Text>
            </View>
          </Flex>
        }
        footer={
          <Button
            variant={"primary"}
            onPress={async () => {
              hapticSelect();
              router.dismissTo("/unlocked/root/home");
            }}
          >
            Go Back
          </Button>
        }
      />
    </>
  );
}
