import { Stack } from "expo-router";
import { DURATION_FAST } from "~/constants/animations";

export default function CardLayout() {
  return (
    <Stack
      screenOptions={{
        animationDuration: DURATION_FAST,
        statusBarStyle: "dark",
      }}
    >
      <Stack.Screen name={"introduction"} />
      <Stack.Screen name={"card-unavailable"} />
      <Stack.Screen name={"card-ready"} />
      <Stack.Screen name={"pin"} />
    </Stack>
  );
}
