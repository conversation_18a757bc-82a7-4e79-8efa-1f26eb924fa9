import { FontFamilyByVariant, Text, useColor, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import { Button as RNButton, Keyboard, Modal, TextInput } from "react-native";
import { Link, router } from "expo-router";
import { FullscreenFlowContainer } from "~/components/FullscreenFlowContainer";
import { ReactNode, useState } from "react";
import { ScrollView } from "~/components/ScrollView";
import { BottomInset } from "~/components/BottomInset";
import {
  getKycStatus,
  isCardAvailable,
  loadBridgeAccount,
  markKycCompleted,
  requestCard,
} from "~/state/bridge";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  hapticDismissBottomTray,
  hapticError,
  hapticOpenModalSheet,
  hapticSelect,
  hapticSuccess,
} from "~/utils/haptics";
import { StackScreenHeader } from "~/components/headers/StackScreenHeader";
import { Href } from "expo-router/build/types";
import { DashedBorder } from "~/components/DashedBorder";
import { Flex, Row } from "~/components/Grid";
import { Icon } from "~/components/Icon";
import { Checkbox } from "~/components/Checkbox";
import { P3 } from "~/components/typography/P3";
import { VisaCard } from "~/components/card/VisaCard";
import { useActiveWallet } from "~/state/wallet";
import { routeToKyc } from "~/components/DepositButton";
import {
  BridgeAccount,
  openKycFlow,
  updateKycLink,
  verifyInviteCode,
} from "~/services/bridge";
import { sleep } from "~/utils/promise";
import * as Clipboard from "expo-clipboard";
import { z } from "zod";
import Animated, {
  FadeInDown,
  FadeInUp,
  FadeOut,
  FadeOutUp,
  interpolate,
  LayoutAnimationConfig,
  ReduceMotion,
  useAnimatedStyle,
} from "react-native-reanimated";
import { DURATION_FAST, DURATION_MEDIUM } from "~/constants/animations";
import { useAnimatedKeyboardProgress } from "~/hooks/useAnimatedKeyboardProgress";
import WebView from "react-native-webview";
import { toast } from "~/components/Toaster";
import { createCard } from "~/components/home/<USER>";

export default function CardIntroductionScreen() {
  return (
    <>
      <StackScreenHeader headerTitle={""} />
      <FullscreenFlowContainer
        body={
          <>
            <ScrollView
              contentInsetAdjustmentBehavior="automatic"
              contentContainerStyle={{
                paddingHorizontal: 20,
                gap: 42,
                alignItems: "center",
              }}
            >
              <View gap={16}>
                <View style={{ marginTop: 16 }}>
                  <View>
                    <VisaCard variant={"dark"} showDigits={true} />
                  </View>
                  <View
                    style={{
                      position: "absolute",
                      zIndex: -1,
                      transform: [{ translateY: -28 }, { scale: 0.9 }],
                      transformOrigin: "top",
                    }}
                  >
                    <VisaCard variant={"light"} showDigits={true} />
                  </View>
                </View>
                <View gap={8}>
                  <Text size={24} variant={"semibold"} align={"center"}>
                    Spend stablecoins{"\n"}anywhere.
                  </Text>
                  <Text
                    size={16}
                    variant={"medium"}
                    colorToken={"textSecondary"}
                    align={"center"}
                  >
                    A virtual Visa debit card to spend stablecoins{"\n"}
                    anywhere, with zero fees.
                  </Text>
                </View>
              </View>

              <View>
                <DashedBorder
                  borderWidth={1.5}
                  borderColorToken={"dashedListSeparator"}
                />
                <Row gap={16} style={{ gap: 18, padding: 20 }}>
                  <Icon name={"globe"} size={12} colorToken={"textSecondary"} />
                  <Text variant="medium" colorToken="textSecondary" size={14}>
                    Currently available for US users only.{"\n"}
                    Excluded states: NY and AK.
                  </Text>
                </Row>
              </View>
            </ScrollView>
            <BottomInset offset={165} />
          </>
        }
        footer={<Footer />}
      />
    </>
  );
}

function Footer() {
  const { wallet } = useActiveWallet();
  const queryClient = useQueryClient();

  const [eSignatureAccepted, setESignatureAccepted] = useState(false);
  const [inviteCodeVerified, setInviteCodeVerified] = useState(false);
  const [agreementModalVisible, setAgreementModalVisible] = useState(false);

  const issueCardMutation = useMutation({
    mutationKey: ["bridge/issue-card"],
    mutationFn: async () => {
      await requestCard(queryClient, { walletKey: wallet.walletKey });

      const bridgeAccount = await loadBridgeAccount({
        queryClient,
      });

      if (bridgeAccount === null) {
        router.replace(`/unlocked/bridge/onboarding/start-kyc`);
        return;
      }

      const kycStatus = getKycStatus(bridgeAccount);
      if (kycStatus !== "approved") {
        routeToKyc(kycStatus);
        return;
      }

      if (bridgeAccount.country && !isCardAvailable(bridgeAccount.country)) {
        router.replace(`/unlocked/bridge/card/onboarding/card-unavailable`);
        return;
      }

      if (
        BridgeAccount.endorsement(bridgeAccount, "cards")?.status !== "approved"
      ) {
        const { kycLink } = await updateKycLink({ endorsement: "cards" });
        const result = await openKycFlow({ kycLink });
        if (result.status !== "completed") {
          toast.info("Verification has been cancelled");
        } else {
          markKycCompleted(bridgeAccount.customer_id);
          router.dismissTo("/unlocked/root/home");
          router.push("/unlocked/bridge/onboarding/kyc-status");
        }
        return;
      }

      await createCard(wallet, queryClient);
      router.replace(`/unlocked/bridge/card/onboarding/pin`);
    },
    onError: () => {
      toast.error("Failed to activate card. Please try again later.");
    },
  });

  const keyboard = useAnimatedKeyboardProgress();
  const containerAnimatedStyle = useAnimatedStyle(() => {
    return {
      height: interpolate(keyboard.progress.value, [0, 0.5], [140, 54]),
    };
  });

  return (
    <>
      <AgreementModal
        visible={agreementModalVisible}
        onClose={() => setAgreementModalVisible(false)}
        onAgreed={() => {
          setAgreementModalVisible(false);
          issueCardMutation.mutate();
        }}
      />
      <Animated.View
        style={[
          containerAnimatedStyle,
          {
            justifyContent: "flex-end",
          },
        ]}
      >
        <LayoutAnimationConfig skipEntering skipExiting>
          {!inviteCodeVerified ? (
            <Animated.View
              key={1}
              exiting={FadeOutUp.duration(DURATION_MEDIUM).reduceMotion(
                ReduceMotion.Never
              )}
            >
              <InviteCodeForm
                onValid={() => {
                  setInviteCodeVerified(true);
                }}
              />
            </Animated.View>
          ) : (
            <Animated.View
              key={2}
              entering={FadeInDown.delay(DURATION_MEDIUM)
                .duration(DURATION_MEDIUM)
                .reduceMotion(ReduceMotion.Never)}
              style={{ gap: 16 }}
            >
              <Row justify={"center"}>
                <Checkbox
                  value={eSignatureAccepted}
                  onChange={setESignatureAccepted}
                >
                  <P3 colorToken="textSecondary">
                    I agree to{" "}
                    <TextLink
                      size={15}
                      href={"https://www.bridge.xyz/legal#e-sign-agreement"}
                    >
                      e-Signature
                    </TextLink>
                  </P3>
                </Checkbox>
              </Row>
              <TermsView />
              <Button
                disabled={!eSignatureAccepted}
                loading={issueCardMutation.isPending}
                loadingText={"Activating Card"}
                variant={"primary"}
                onPress={async () => {
                  setAgreementModalVisible(true);
                  hapticOpenModalSheet();
                }}
              >
                Continue
              </Button>
            </Animated.View>
          )}
        </LayoutAnimationConfig>
      </Animated.View>
    </>
  );
}

const AnimatedText = Animated.createAnimatedComponent(Text);

function InviteCodeForm({ onValid }: { onValid: () => void }) {
  const [value, setValue] = useState("");
  const [error, setError] = useState<string | null>(null);

  const verifyInviteCodeMutation = useMutation({
    mutationFn: async (code: string) => {
      const [res, _] = await Promise.all([verifyInviteCode(code), sleep(1000)]);
      return res;
    },
  });

  const textSecondary = useColor("textSecondary");
  const border = useColor("border");
  const errorBorder = useColor("red");

  return (
    <View>
      <Row gap={16} style={{ alignItems: "flex-end" }}>
        <Flex gap={8}>
          {!!error && (
            <AnimatedText
              align={"center"}
              variant="medium"
              colorToken="textError"
              style={{ fontSize: 12, paddingLeft: 6 }}
              entering={FadeInUp.duration(DURATION_FAST)
                .withInitialValues({
                  transform: [{ translateY: -10 }],
                })
                .reduceMotion(ReduceMotion.Never)}
              exiting={FadeOut.duration(DURATION_FAST)}
            >
              {error}
            </AnimatedText>
          )}
          <TextInput
            textAlign={"center"}
            placeholderTextColor={textSecondary}
            maxLength={6}
            placeholder="Enter invite code"
            value={value}
            autoCapitalize={"characters"}
            onChangeText={(text) => {
              setValue(text);
              setError(null);
            }}
            style={{
              height: 54,
              fontSize: 16,
              fontFamily: FontFamilyByVariant["medium"],
              borderRadius: 999,
              borderColor: !!error ? errorBorder : border,
              borderWidth: 1,
            }}
          />
        </Flex>
        <Button
          animation={"left-right"}
          disabled={value.length !== 0 && value.length !== 6}
          style={{ view: { minWidth: 100 } }}
          loading={verifyInviteCodeMutation.isPending}
          onPress={async () => {
            hapticSelect();

            let code: string;
            if (value) {
              code = value;
            } else {
              const clipboardValue = await Clipboard.getStringAsync();
              const isValid = z
                .string()
                .length(6)
                .safeParse(clipboardValue).success;

              if (!isValid) {
                hapticError();
                return;
              }

              setValue(clipboardValue);
              code = clipboardValue;
            }

            Keyboard.dismiss();
            let result = await verifyInviteCodeMutation.mutateAsync(code);

            if (result.type === "notFound") {
              setError("Invalid invite code");
              hapticError();
            } else if (result.type === "quotaExceeded") {
              setError("No more invites left");
              hapticError();
            } else {
              onValid();
              hapticSuccess();
            }
          }}
        >
          {value ? "Enter" : "Paste"}
        </Button>
      </Row>
    </View>
  );
}

//Bridge Privacy Policy: https://www.bridge.xyz/legal#row-privacy-policy
// Bridge Terms of Service: https://www.bridge.xyz/legal
// Lead Privacy Policy Lead - disclosure/static  https://www.lead.bank/privacy-and-terms
// E-Sign Consent - consent/static - https://www.bridge.xyz/legal#e-sign-agreement
// Cardholder agreement - https://www.bridge.xyz/card-agreements/fuse-card (not live yet)
function TermsView() {
  const size = 12;
  return (
    <Text
      variant={"medium"}
      colorToken={"textTertiary"}
      align={"center"}
      size={size}
    >
      By pressing Continue I accept Bridge{" "}
      <TextLink href={"https://www.bridge.xyz/legal"}>
        Terms of Service
      </TextLink>
      ,{"\n"}Bridge{" "}
      <TextLink href={"https://www.bridge.xyz/legal#row-privacy-policy"}>
        Privaсy Policy
      </TextLink>{" "}
      & Lead{" "}
      <TextLink href={"https://www.lead.bank/privacy-and-terms"}>
        Privaсy Policy
      </TextLink>
      .
    </Text>
  );
}

function TextLink({
  children,
  href,
  size,
}: {
  children: ReactNode;
  href: Href;
  size?: number;
}) {
  return (
    <Link href={href} target="_blank">
      <Text
        variant={"medium"}
        colorToken={"textSecondary"}
        size={size}
        style={{ textDecorationLine: "underline" }}
      >
        {children}
      </Text>
    </Link>
  );
}

function AgreementModal({
  visible,
  onClose,
  onAgreed,
}: {
  visible: boolean;
  onClose: () => void;
  onAgreed: () => void;
}) {
  const [ready, setReady] = useState(false);
  const [isAtBottom, setIsAtBottom] = useState(false);

  const BOTTOM_OFFSET = 900;

  return (
    <Modal
      animationType={"slide"}
      presentationStyle={"pageSheet"}
      visible={visible}
    >
      <Flex>
        <Row
          justify={"space-between"}
          style={{ paddingHorizontal: 20, paddingVertical: 8 }}
        >
          <RNButton
            title={"Cancel"}
            onPress={() => {
              hapticDismissBottomTray();
              onClose();
            }}
          />
          <RNButton
            disabled={ready ? !isAtBottom : true}
            title={"Accept and Apply"}
            onPress={() => {
              hapticSuccess();
              onAgreed();
            }}
          />
        </Row>
        <WebView
          mediaPlaybackRequiresUserAction={true}
          onLoadEnd={() => setReady(true)}
          onScroll={(event) => {
            const { layoutMeasurement, contentOffset, contentSize } =
              event.nativeEvent;

            const isBottom =
              layoutMeasurement.height + contentOffset.y >=
              contentSize.height - BOTTOM_OFFSET;

            if (isBottom) {
              setIsAtBottom(isBottom);
            }
          }}
          originWhitelist={["*"]}
          style={{ flex: 1 }}
          source={{
            uri: "https://www.bridge.xyz/card-agreements/fuse-card",
          }}
        />
      </Flex>
    </Modal>
  );
}
