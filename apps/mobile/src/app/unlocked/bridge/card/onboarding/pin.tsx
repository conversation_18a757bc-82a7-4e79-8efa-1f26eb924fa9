import { Text, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import { router } from "expo-router";
import { FullscreenFlowContainer } from "~/components/FullscreenFlowContainer";
import {
  hapticDismissBottomTray,
  hapticSelect,
  hapticSuccess,
} from "~/utils/haptics";
import { StackScreenHeader } from "~/components/headers/StackScreenHeader";
import { CardPinImage } from "~/components/icons/card/CardPinImage";
import { TouchableScale } from "~/components/TouchableScale";
import Animated from "react-native-reanimated";
import { CardPinModal } from "~/components/card/CardPinModal";
import { OnboardingText } from "~/components/card/OnboardingText";
import { useState } from "react";

export default function PinScreen() {
  const [pinModalVisible, setPinModalVisible] = useState(false);

  function routeToCard() {
    router.replace("/unlocked/bridge/card/main");
  }

  return (
    <>
      <CardPinModal
        visible={pinModalVisible}
        onClose={() => {
          hapticDismissBottomTray();
          setPinModalVisible(false);
        }}
        onComplete={() => {
          hapticSuccess();
          setPinModalVisible(false);
          routeToCard();
        }}
      />
      <StackScreenHeader headerTitle={""} />
      <FullscreenFlowContainer
        body={
          <View
            style={{
              //manually aligned with next card screen
              paddingTop: 210,
              paddingHorizontal: 20,
              gap: 42,
              paddingBottom: "25%",
              alignItems: "center",
            }}
          >
            <CardPinImage />
            <OnboardingText
              title={"Create PIN"}
              subtitle={"Set PIN code for your Fuse Card"}
            />
          </View>
        }
        footer={
          <Animated.View>
            <TouchableScale
              onPress={() => {
                routeToCard();
              }}
              style={{
                alignSelf: "center",
                justifyContent: "center",
                paddingHorizontal: 18,
                height: 54,
              }}
            >
              <Text variant="semibold" size={16}>
                Skip
              </Text>
            </TouchableScale>
            <Button
              variant={"primary"}
              onPress={async () => {
                setPinModalVisible(true);
                hapticSelect();
              }}
            >
              Set up PIN
            </Button>
          </Animated.View>
        }
      />
    </>
  );
}
