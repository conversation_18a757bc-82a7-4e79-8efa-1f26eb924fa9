import { Stack } from "expo-router";
import { DURATION_FAST } from "~/constants/animations";
import { CardFreezeModal } from "~/components/card/CardFreezeModal";

import { CardUnfreezeModal } from "~/components/card/CardUnfreezeModal";
import { CardPendingActivityModal } from "~/components/card/CardPendingActivityModal";
import { CardFrozenModal } from "~/components/card/CardFrozenModal";
import { CardEventModal } from "~/components/card/CardEventModal";

export default function CardLayout() {
  return (
    <>
      <Stack
        screenOptions={{
          animationDuration: DURATION_FAST,
          statusBarStyle: "dark",
        }}
      >
        <Stack.Screen name="onboarding" options={{ headerShown: false }} />
        <Stack.Screen name="main" />
        <Stack.Screen name="settings" />
        <Stack.Screen name="spending" />
        <Stack.Screen name="transactions-by-month" />
        <Stack.Screen
          name="withdraw"
          options={{ headerShown: false, presentation: "modal" }}
        />
        <Stack.Screen
          name="deposit"
          options={{ headerShown: false, presentation: "modal" }}
        />
        <Stack.Screen
          name={"complaint"}
          options={{ headerShown: false, presentation: "modal" }}
        />
      </Stack>

      <CardFreezeModal />
      <CardUnfreezeModal />
      <CardPendingActivityModal />
      <CardFrozenModal />
      <CardEventModal />
    </>
  );
}
