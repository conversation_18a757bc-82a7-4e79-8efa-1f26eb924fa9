import { Stack } from "expo-router";
import { DURATION_FAST } from "~/constants/animations";
import { createContext, ReactNode, useContext, useState } from "react";
import { createStore, StoreApi, useStore } from "zustand";
import invariant from "invariant";
import { CreateExternalAccountRequest } from "~/services/bridge";

export default function AddExternalAccountLayout() {
  return (
    <AddExternalAccountStateProvider value={{ accountDetails: null }}>
      <Stack
        screenOptions={{
          animationDuration: DURATION_FAST,
        }}
      >
        <Stack.Screen name={"account-details"} />
        <Stack.Screen name={"beneficiary-address"} />
      </Stack>
    </AddExternalAccountStateProvider>
  );
}

export type AddExternalAccountState = {
  accountDetails: {
    firstName: string;
    lastName: string;
    bankAccount: CreateExternalAccountRequest["bankAccount"];
  } | null;
};

const AddExternalAccountStateContext =
  createContext<StoreApi<AddExternalAccountState> | null>(null);

const AddExternalAccountStateProvider = ({
  children,
  value,
}: {
  children: ReactNode;
  value: AddExternalAccountState;
}) => {
  const [store] = useState(() =>
    createStore<AddExternalAccountState>(() => value)
  );

  return (
    <AddExternalAccountStateContext.Provider value={store}>
      {children}
    </AddExternalAccountStateContext.Provider>
  );
};

export const useAddExternalAccountState = () => {
  const store = useContext(AddExternalAccountStateContext);
  invariant(
    store,
    "useAddExternalAccountState must be used within a AddExternalAccountStateProvider"
  );
  return [useStore(store), store.setState] as const;
};
