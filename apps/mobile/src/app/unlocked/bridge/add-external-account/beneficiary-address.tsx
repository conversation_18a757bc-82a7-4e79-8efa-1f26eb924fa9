import { useAddExternalAccountState } from "~/app/unlocked/bridge/add-external-account/_layout";
import { useFormik } from "formik";
import { toFormikValidationSchema } from "zod-formik-adapter";
import { z } from "zod";
import * as Haptics from "expo-haptics";
import { FormField } from "~/components/FormField";
import { TextInput } from "~/components/TextInput";
import { Button } from "~/components/Button";
import { Picker } from "~/components/Picker";
import { createExternalAccount, useCountriesList } from "~/state/bridge";
import { Redirect } from "~/components/Redirect";
import { CreateExternalAccountRequest } from "~/services/bridge";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { router } from "expo-router";
import { FullscreenFlowContainer } from "~/components/FullscreenFlowContainer";
import { Text } from "~/components/Themed";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useHeaderHeight } from "@react-navigation/elements";
import { useHeaderAnimatedValues } from "~/hooks/useHeaderAnimatedValues";
import { StackScreenHeader } from "~/components/headers/StackScreenHeader";
import { FlatList, Keyboard, View, ScrollView } from "react-native";
import { useState, useCallback, useEffect, useMemo, useRef } from "react";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import { TextSkeleton } from "~/components/Skeleton";
import { useAddressAutocomplete } from "~/state/addressAutocomplete";
import type { PlaceSuggestion } from "~/services/addressAutocomplete";
import { getPlaceDetails } from "~/services/addressAutocomplete";
import { HEADER_HEIGHT } from "~/components/navigation/StackHeader";

export default function BeneficiaryAddressScreen() {
  const [state] = useAddExternalAccountState();

  if (state.accountDetails === null) {
    return (
      <Redirect
        href={"/unlocked/bridge/add-external-account/account-details"}
      />
    );
  }

  return <BeneficiaryAddressForm accountDetails={state.accountDetails} />;
}

function BeneficiaryAddressForm({
  accountDetails,
}: {
  accountDetails: {
    firstName: string;
    lastName: string;
    bankAccount: CreateExternalAccountRequest["bankAccount"];
  };
}) {
  const insets = useSafeAreaInsets();

  const isUsBankAccount = accountDetails.bankAccount.type === "us";
  const supportedCountries = useCountriesList();

  const queryClient = useQueryClient();

  const headerHeight = useHeaderHeight();

  const { animatedScrollRef, headerBackgroundOpacity } =
    useHeaderAnimatedValues({ headerHeight });

  const addExternalAccountMutation = useMutation({
    mutationFn: async (
      address: CreateExternalAccountRequest["owner"]["address"] | null
    ) => {
      const externalAccount = await createExternalAccount(queryClient, {
        owner: {
          firstName: accountDetails.firstName,
          lastName: accountDetails.lastName,
          address: address,
        },
        bankAccount: accountDetails.bankAccount,
      });

      router.replace(
        `/unlocked/bridge/send-fiat/enter-amount?externalAccountId=${externalAccount.id}`
      );
    },
  });

  const [autocompleteActive, setAutocompleteActive] = useState(true);
  const [shouldScrollToAddress, setShouldScrollToAddress] = useState(false);

  // Direct ref to the underlying ScrollView to bypass KeyboardAwareScrollView
  const directScrollRef = useRef<ScrollView>(null);

  const formik = useFormik({
    initialValues: {
      streetLine1: "",
      country: accountDetails.bankAccount.type === "us" ? "USA" : "",
      city: "",
      state: "",
      postalCode: "",
    },
    validationSchema: toFormikValidationSchema(
      z.object({
        streetLine1: z.string().min(1).max(35),
        country: z.string().min(1),
        city: z.string().min(1),
        state: z.string().min(1),
        postalCode: z.string().min(1),
      })
    ),

    validateOnChange: true,
    validateOnBlur: true,

    async onSubmit(address) {
      Haptics.selectionAsync();

      addExternalAccountMutation.mutate({
        streetLine1: address.streetLine1,
        country: address.country,
        city: address.city,
        state: address.state,
        postalCode: address.postalCode,
      });
    },
  });

  const selectedCountry = isUsBankAccount
    ? supportedCountries.us
    : supportedCountries.eu.find(
        (country) => country.alpha3 === formik.values.country
      );

  const regions = selectedCountry?.subdivisions ?? [];
  const firstRegion = regions[0]?.name;

  const { query, setQuery, places, loading } = useAddressAutocomplete();

  const handleAddressFocus = useCallback(() => {
    setAutocompleteActive(true);
    setShouldScrollToAddress(true);
  }, []);

  useEffect(() => {
    if (shouldScrollToAddress && autocompleteActive && places.length > 0) {
      const timeoutId = setTimeout(() => {
        console.log("🔍 Scrolling address field below navbar");
        if (animatedScrollRef.current?.scrollTo) {
          // Use a fixed position that positions the field appropriately
          animatedScrollRef.current.scrollTo({
            x: 0,
            y: 100,
            animated: true,
          });
        }
        setShouldScrollToAddress(false);
      }, 300);

      return () => clearTimeout(timeoutId);
    }
  }, [shouldScrollToAddress, autocompleteActive, places.length, headerHeight]);

  const handlePlaceSelect = async (item: PlaceSuggestion) => {
    try {
      const placeDetails = await getPlaceDetails(item.id);
      const addressComponents = placeDetails.addressComponents || [];

      const streetNumberComponent = addressComponents.find((component) =>
        component.types.includes("street_number")
      );
      const routeComponent = addressComponents.find((component) =>
        component.types.includes("route")
      );
      const stateComponent = addressComponents.find((component) =>
        component.types.includes("administrative_area_level_1")
      );
      const cityComponent = addressComponents.find((component) =>
        component.types.includes("locality")
      );
      const postalCodeComponent = addressComponents.find((component) =>
        component.types.includes("postal_code")
      );

      const streetAddress = [
        streetNumberComponent?.longText,
        routeComponent?.longText,
      ]
        .filter(Boolean)
        .join(" ");

      formik.setFieldValue("streetLine1", streetAddress || "");

      const stateName = stateComponent?.longText || "";
      const stateCode =
        regions.find((region) => region.name === stateName)?.code || stateName;
      formik.setFieldValue("state", stateCode);

      formik.setFieldValue("city", cityComponent?.longText || "");
      formik.setFieldValue("postalCode", postalCodeComponent?.longText || "");
    } catch (error) {
      console.error("Error fetching place details:", error);

      const fullAddress = item.fullAddress || "";
      const addressParts = fullAddress
        .split(",")
        .map((part: string) => part.trim());

      const streetAddress = addressParts[0] || "";
      formik.setFieldValue("streetLine1", streetAddress);
    } finally {
      setAutocompleteActive(false);
      Keyboard.dismiss(); // blur input
    }
  };

  return (
    <>
      <StackScreenHeader
        headerTitle={"Beneficiary Address"}
        backgroundOpacity={headerBackgroundOpacity}
      />
      <FullscreenFlowContainer
        body={
          <KeyboardAwareScrollView
            ref={animatedScrollRef}
            contentInsetAdjustmentBehavior="automatic"
            bottomOffset={insets.bottom + 72}
            style={{
              overflow: "visible",
              paddingHorizontal: 20,
            }}
            contentContainerStyle={{
              gap: 24,
              overflow: "visible",
              paddingBottom: insets.bottom + 48,
            }}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
          >
            <Text
              size={16}
              variant={"medium"}
              colorToken={"textSecondary"}
              style={{ textAlign: "center", marginBottom: 10 }}
            >
              Enter bank account beneficiary address
            </Text>
            <FormField label={"Address line 1"}>
              <TextInput
                autoComplete={"address-line1"}
                textContentType={"streetAddressLine1"}
                placeholder={"Street, building name etc."}
                value={autocompleteActive ? query : formik.values.streetLine1}
                error={
                  formik.touched.streetLine1
                    ? formik.errors.streetLine1
                    : undefined
                }
                onChangeText={setQuery}
                onFocus={handleAddressFocus}
                onBlur={() => formik.setFieldTouched("streetLine1")}
              />
            </FormField>
            {autocompleteActive && places.length > 0 && (
              <View
                style={{
                  overflow: "hidden",
                  position: "absolute",
                  left: 0,
                  right: 0,
                  top:
                    148 +
                    (formik.touched.streetLine1 && formik.errors.streetLine1
                      ? 8
                      : 0),
                  zIndex: 1000,
                  maxHeight: 200,
                  backgroundColor: "white",
                  borderRadius: 16,
                }}
              >
                <FlatList<PlaceSuggestion | string>
                  scrollEnabled={false}
                  style={{ flex: 1 }}
                  contentContainerStyle={{ padding: 24 }}
                  data={
                    loading
                      ? Array.from({ length: 5 }, (_, i) => `skeleton-${i}`)
                      : places
                  }
                  keyExtractor={(item) =>
                    typeof item === "string" ? item : item.id
                  }
                  renderItem={({ item }) => {
                    if (typeof item === "string") {
                      return <TextSkeleton />;
                    }

                    return (
                      <AnimatedTouchableScale
                        onPress={() => handlePlaceSelect(item)}
                        style={{ borderRadius: 10 }}
                      >
                        <Text colorToken="text">{item.fullAddress}</Text>
                      </AnimatedTouchableScale>
                    );
                  }}
                  ItemSeparatorComponent={() => <View style={{ height: 18 }} />}
                />
              </View>
            )}

            {!isUsBankAccount && (
              <FormField label={"Country"}>
                <Picker
                  title={"Country"}
                  placeholder={"Country name"}
                  options={supportedCountries.eu.map((country) => ({
                    label: country.name,
                    value: country.alpha3,
                  }))}
                  value={formik.values.country}
                  onSelect={formik.handleChange("country")}
                />
              </FormField>
            )}
            <FormField label={isUsBankAccount ? "State" : "Region"}>
              <Picker
                title={isUsBankAccount ? "State" : "Region"}
                placeholder={`e.g. ${firstRegion}`}
                disabled={selectedCountry === undefined}
                options={regions.map((region) => ({
                  label: region.name,
                  value: region.code,
                }))}
                value={formik.values.state}
                onSelect={formik.handleChange("state")}
              />
            </FormField>
            <FormField label={"City"}>
              <TextInput
                textContentType={"addressCity"}
                placeholder={"City name"}
                value={formik.values.city}
                error={formik.touched.city ? formik.errors.city : undefined}
                onChangeText={formik.handleChange("city")}
                onBlur={() => formik.setFieldTouched("city")}
              />
            </FormField>
            <FormField label={"Postal code"}>
              <TextInput
                textContentType={"postalCode"}
                placeholder={"Postal code"}
                value={formik.values.postalCode}
                error={
                  formik.touched.postalCode
                    ? formik.errors.postalCode
                    : undefined
                }
                onChangeText={formik.handleChange("postalCode")}
                onBlur={formik.handleBlur("postalCode")}
              />
            </FormField>
          </KeyboardAwareScrollView>
        }
        footer={
          <Button
            disabled={!formik.dirty || !formik.isValid}
            variant={"primary"}
            onPress={() => formik.handleSubmit()}
            loading={addExternalAccountMutation.isPending}
            loadingText={"Adding"}
          >
            Add account
          </Button>
        }
      />
    </>
  );
}
