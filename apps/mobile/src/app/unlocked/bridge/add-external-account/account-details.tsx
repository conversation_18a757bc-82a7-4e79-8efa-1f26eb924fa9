import { Text } from "~/components/Themed";
import * as Haptics from "expo-haptics";
import { ReactNode } from "react";
import { useFormik } from "formik";
import { toFormikValidationSchema } from "zod-formik-adapter";
import { z } from "zod";
import { router, useGlobalSearchParams } from "expo-router";
import { TextInput } from "~/components/TextInput";
import { Button } from "~/components/Button";
import { FormField } from "~/components/FormField";
import {
  AddExternalAccountState,
  useAddExternalAccountState,
} from "~/app/unlocked/bridge/add-external-account/_layout";
import { CreateExternalAccountRequest } from "~/services/bridge";
import { createExternalAccount } from "~/state/bridge";
import { Picker } from "~/components/Picker";
import { FullscreenFlowContainer } from "~/components/FullscreenFlowContainer";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { IbanZ } from "~/utils/validation";
import { AnimatedRef } from "react-native-reanimated";
import { useHeaderHeight } from "@react-navigation/elements";
import { useHeaderAnimatedValues } from "~/hooks/useHeaderAnimatedValues";
import { AnimatedScrollView } from "react-native-reanimated/lib/typescript/component/ScrollView";
import { StackScreenHeader } from "~/components/headers/StackScreenHeader";
import { useQueryClient } from "@tanstack/react-query";

const RouteParamsZ = z.object({
  accountType: z.union([z.literal("iban"), z.literal("us")]),
});

export default function AccountDetails() {
  const { accountType } = RouteParamsZ.parse(useGlobalSearchParams());
  const [, setState] = useAddExternalAccountState();

  function proceedToAddressView(
    accountDetails: AddExternalAccountState["accountDetails"]
  ) {
    setState({ accountDetails });
    router.push("/unlocked/bridge/add-external-account/beneficiary-address");
  }

  const headerHeight = useHeaderHeight();

  const { animatedScrollRef, headerBackgroundOpacity } =
    useHeaderAnimatedValues({ headerHeight });

  const queryClient = useQueryClient();

  return (
    <>
      <StackScreenHeader
        headerTitle={"Account Details"}
        backgroundOpacity={headerBackgroundOpacity}
      />
      {(() => {
        if (accountType === "iban") {
          return (
            <IbanFormView
              animatedScrollRef={animatedScrollRef}
              onSubmit={async (accountDetails) => {
                const externalAccount = await createExternalAccount(
                  queryClient,
                  {
                    owner: {
                      firstName: accountDetails.firstName,
                      lastName: accountDetails.lastName,
                      address: null,
                    },
                    bankAccount: accountDetails.bankAccount,
                  }
                );

                router.replace(
                  `/unlocked/bridge/send-fiat/enter-amount?externalAccountId=${externalAccount.id}`
                );
              }}
            />
          );
        }

        if (accountType === "us") {
          return (
            <UsFormView
              animatedScrollRef={animatedScrollRef}
              onSubmit={proceedToAddressView}
            />
          );
        }

        accountType satisfies never;
        return null;
      })()}
    </>
  );
}

function UsFormView({
  onSubmit,
  animatedScrollRef,
}: {
  animatedScrollRef?: AnimatedRef<AnimatedScrollView>;
  onSubmit: (account: {
    firstName: string;
    lastName: string;
    bankAccount: CreateExternalAccountRequest["bankAccount"] & { type: "us" };
  }) => void;
}) {
  const formik = useFormik({
    initialValues: {
      firstName: "",
      lastName: "",
      accountType: "checking" as const,
      bankName: "",
      routingNumber: "",
      accountNumber: "",
    },
    validationSchema: toFormikValidationSchema(
      z.object({
        firstName: z.string().min(1),
        lastName: z.string().min(1),
        accountType: z.union([z.literal("checking"), z.literal("savings")]),
        bankName: z.string().min(1),
        accountNumber: z.string().min(1),
        routingNumber: z.string().min(9).max(9),
      })
    ),

    validateOnChange: true,
    validateOnBlur: true,

    async onSubmit(account) {
      Haptics.selectionAsync();

      onSubmit({
        firstName: account.firstName,
        lastName: account.lastName,
        bankAccount: {
          type: "us",
          bankName: account.bankName,
          accountNumber: account.accountNumber,
          routingNumber: account.routingNumber,
          checkingOrSavings: account.accountType,
        },
      });
    },
  });

  return (
    <FullscreenFlowContainer
      body={
        <FormContainer animatedScrollRef={animatedScrollRef}>
          <FormField label={"Bank Name"}>
            <TextInput
              autoCorrect={false}
              spellCheck={false}
              placeholder={"e.g. Chase"}
              value={formik.values.bankName}
              error={
                formik.touched.bankName ? formik.errors.bankName : undefined
              }
              onChangeText={formik.handleChange("bankName")}
              onBlur={() => formik.setFieldTouched("bankName")}
            />
          </FormField>
          <FormField label={"Routing number"}>
            <TextInput
              autoCorrect={false}
              spellCheck={false}
              placeholder={"Routing number"}
              value={formik.values.routingNumber}
              error={
                formik.touched.routingNumber
                  ? formik.errors.routingNumber
                  : undefined
              }
              onChangeText={formik.handleChange("routingNumber")}
              onBlur={() => formik.setFieldTouched("routingNumber")}
            />
          </FormField>
          <FormField label={"Account number"}>
            <TextInput
              autoCorrect={false}
              spellCheck={false}
              placeholder={"Account number"}
              value={formik.values.accountNumber}
              error={
                formik.touched.accountNumber
                  ? formik.errors.accountNumber
                  : undefined
              }
              onChangeText={formik.handleChange("accountNumber")}
              onBlur={() => formik.setFieldTouched("accountNumber")}
            />
          </FormField>

          <FormField label={"First name"}>
            <TextInput
              autoComplete={"given-name"}
              placeholder={"John"}
              value={formik.values.firstName}
              error={
                formik.touched.firstName ? formik.errors.firstName : undefined
              }
              onChangeText={formik.handleChange("firstName")}
              onBlur={() => formik.setFieldTouched("firstName")}
            />
          </FormField>

          <FormField label={"Last name"}>
            <TextInput
              autoComplete={"family-name"}
              placeholder={"Doe"}
              value={formik.values.lastName}
              error={
                formik.touched.lastName ? formik.errors.lastName : undefined
              }
              onChangeText={formik.handleChange("lastName")}
              onBlur={() => formik.setFieldTouched("lastName")}
            />
          </FormField>
          <FormField label={"Account type"}>
            <Picker
              title={"Account type"}
              placeholder={"Not Selected"}
              options={[
                {
                  label: "Checking",
                  value: "checking",
                },
                {
                  label: "Savings",
                  value: "savings",
                },
              ]}
              value={formik.values.accountType}
              onSelect={formik.handleChange("accountType")}
            />
          </FormField>
        </FormContainer>
      }
      footer={
        <Button
          disabled={!formik.dirty || !formik.isValid}
          variant={"primary"}
          onPress={() => formik.handleSubmit()}
        >
          Next step
        </Button>
      }
    />
  );
}

function IbanFormView({
  onSubmit,
  animatedScrollRef,
}: {
  animatedScrollRef?: AnimatedRef<AnimatedScrollView>;
  onSubmit: (account: {
    firstName: string;
    lastName: string;
    bankAccount: CreateExternalAccountRequest["bankAccount"] & { type: "iban" };
  }) => Promise<void>;
}) {
  const formik = useFormik({
    initialValues: {
      firstName: "",
      lastName: "",
      bankName: "",
      accountNumber: "",
    },
    validationSchema: toFormikValidationSchema(
      z.object({
        firstName: z.string().min(1),
        lastName: z.string().min(1),
        bankName: z.string().min(1),
        accountNumber: IbanZ,
      })
    ),

    validateOnChange: true,
    validateOnBlur: true,

    async onSubmit(account) {
      Haptics.selectionAsync();

      await onSubmit({
        firstName: account.firstName,
        lastName: account.lastName,
        bankAccount: {
          type: "iban",
          bankName: account.bankName,
          accountNumber: account.accountNumber,
        },
      });
    },
  });

  return (
    <>
      <FullscreenFlowContainer
        body={
          <FormContainer animatedScrollRef={animatedScrollRef}>
            <FormField label={"Bank Name"}>
              <TextInput
                autoCorrect={false}
                spellCheck={false}
                placeholder={"e.g. ING"}
                value={formik.values.bankName}
                error={
                  formik.touched.bankName ? formik.errors.bankName : undefined
                }
                onChangeText={formik.handleChange("bankName")}
                onBlur={() => formik.setFieldTouched("bankName")}
              />
            </FormField>

            <FormField label={"IBAN"}>
              <TextInput
                autoCorrect={false}
                spellCheck={false}
                placeholder={"IBAN"}
                value={formik.values.accountNumber}
                error={
                  formik.touched.accountNumber
                    ? formik.errors.accountNumber
                    : undefined
                }
                onChangeText={(text) => {
                  formik.handleChange("accountNumber")(
                    text.replaceAll(" ", "")
                  );
                }}
                onBlur={() => formik.setFieldTouched("accountNumber")}
              />
            </FormField>

            <FormField label={"First name"}>
              <TextInput
                autoComplete={"name"}
                textContentType={"name"}
                placeholder={"John"}
                value={formik.values.firstName}
                error={
                  formik.touched.firstName ? formik.errors.firstName : undefined
                }
                onChangeText={formik.handleChange("firstName")}
                onBlur={() => formik.setFieldTouched("firstName")}
              />
            </FormField>

            <FormField label={"Last name"}>
              <TextInput
                autoComplete={"name"}
                textContentType={"name"}
                placeholder={"Doe"}
                value={formik.values.lastName}
                error={
                  formik.touched.lastName ? formik.errors.lastName : undefined
                }
                onChangeText={formik.handleChange("lastName")}
                onBlur={() => formik.setFieldTouched("lastName")}
              />
            </FormField>
          </FormContainer>
        }
        footer={
          <Button
            disabled={!formik.dirty || !formik.isValid}
            variant={"primary"}
            loading={formik.isSubmitting}
            loadingText={"Adding"}
            onPress={() => formik.handleSubmit()}
          >
            Add account
          </Button>
        }
      />
    </>
  );
}

function FormContainer({
  children,
  animatedScrollRef,
}: {
  children: ReactNode;
  animatedScrollRef?: AnimatedRef<AnimatedScrollView>;
}) {
  const insets = useSafeAreaInsets();

  return (
    <KeyboardAwareScrollView
      ref={animatedScrollRef}
      contentInsetAdjustmentBehavior="automatic"
      style={{
        overflow: "visible",
        paddingHorizontal: 20,
      }}
      contentContainerStyle={{
        gap: 24,
        overflow: "visible",
        paddingBottom: insets.bottom + 48,
      }}
      bottomOffset={insets.bottom + 72}
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}
    >
      <Text
        size={16}
        variant={"medium"}
        colorToken={"textSecondary"}
        style={{ textAlign: "center", marginBottom: 10 }}
      >
        Enter your bank account details
      </Text>
      {children}
    </KeyboardAwareScrollView>
  );
}
