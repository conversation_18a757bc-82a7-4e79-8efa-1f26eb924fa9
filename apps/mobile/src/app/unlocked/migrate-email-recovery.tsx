import { styles } from "~/app/unlocked/change-recovery/_layout";
import { View } from "~/components/Themed";
import TurnkeyOTPConfirmation from "~/components/TurnkeyOTPConfirmation";
import { router, useGlobalSearchParams } from "expo-router";
import { useEffect, useState } from "react";
import {
  TurnkeyEmailAuthControl,
  walletAddEmailRecovery,
} from "~/state/turnkey";
import { z } from "zod";
import { refetchWallet, useActiveWallet } from "~/state/wallet";
import { Flex } from "~/components/Grid";
import { Spinner } from "~/components/Spinner";
import { prepareAction, RecoveryKey } from "~/services/wallets";
import { toast } from "~/components/Toaster";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { sendTransactionWithTrackingStatus } from "~/utils/sendTransactionWithTracking";
import invariant from "invariant";
import {
  useIsMutating,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
import { TurnkeySuborgStamper } from "~/utils/turnkey";

const RouteParamsZ = z.object({
  address: z.string(),
});

const mutationKey = ["magic_link_recovery_migration"];

export default function MigrateEmailRecovery() {
  const queryClient = useQueryClient();
  const { wallet } = useActiveWallet();
  const { address } = RouteParamsZ.parse(useGlobalSearchParams());
  const oldKey = wallet.keys.recoveryKeys?.find(
    (key) => key.address === address
  );
  invariant(
    oldKey?.recoveryKeyType === "email",
    `invalid recovery key type: ${oldKey}`
  );

  const [control, setControl] = useState<TurnkeyEmailAuthControl | null>(null);

  useEffect(() => {
    walletAddEmailRecovery({
      walletKey: wallet.walletKey,
      email: oldKey.email,
    }).then((control) => {
      setControl(control);
    });
  }, [oldKey]);

  const migrationMutation = useMutation({
    mutationKey: mutationKey,
    mutationFn: async (stamper: TurnkeySuborgStamper) => {
      const newKey: RecoveryKey = {
        recoveryKeyType: "turnkeyEmail",
        address: stamper.details.publicKey,
        email: stamper.details.email,
        subOrganizationId: stamper.details.subOrganizationId,
      };

      const action = await prepareAction({
        type: "updateRecoveryKeys",
        details: { type: "changeKey", oldKey: oldKey.address, newKey },
      }).catch((e) => {
        toast.error(`Failed to update key`);
        throw e;
      });

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );

      router.dismissTo("/unlocked/root/security");

      await sendTransactionWithTrackingStatus({
        walletKey: wallet.walletKey,
        transaction: { type: "action", signedTransactions },
        confirmationFailureMessage: `Failed to confirm Recovery Key update`,
      }).finally(() =>
        refetchWallet(queryClient, { walletKey: wallet.walletKey })
      );
    },
  });

  if (control === null) {
    return (
      <Flex style={{ alignItems: "center", justifyContent: "center" }}>
        <Spinner />
      </Flex>
    );
  }

  return (
    <View style={styles.container}>
      <TurnkeyOTPConfirmation
        walletKey={wallet.walletKey}
        labels={{
          title: "Update\nRecovery Key",
          button: "Confirm",
        }}
        control={control}
        onSuccess={migrationMutation.mutateAsync}
      />
    </View>
  );
}

export function useIsMagicLinkKeyMigrating() {
  return useIsMutating({ mutationKey }) !== 0;
}
