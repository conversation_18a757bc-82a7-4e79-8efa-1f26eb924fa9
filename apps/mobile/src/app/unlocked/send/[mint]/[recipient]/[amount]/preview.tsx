import { View } from "~/components/Themed";
import { router, useLocalSearchParams } from "expo-router";
import { Button } from "~/components/Button";
import invariant from "invariant";
import { useActiveWallet } from "~/state/wallet";
import { refetchBalances, useBalances } from "~/state/balances";
import { z } from "zod";
import { RecipientRouteParamsZ } from "~/app/unlocked/send/[mint]/[recipient]/enter-amount";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import * as wallets from "~/services/wallets";
import { Action, TokenFee } from "~/services/wallets";
import { toast } from "~/components/Toaster";
import { ListSeparator } from "~/components/ListSeparator";
import * as Haptics from "expo-haptics";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { clientErrorMessage, isClientErrorReasonType } from "~/services/utils";
import {
  AddressRow,
  PropertyRow,
  SolanaFeesData,
  TokenTransferFeeRow,
  TransactionFeesRows,
} from "~/components/PropertyRow";
import { useSendTokenState } from "~/app/unlocked/send/_layout";
import {
  refetchSpendingLimits,
  useSpendingLimits,
} from "~/state/spendingLimits";
import { SpendingLimit } from "~/services/spendingLimits";
import { P3 } from "~/components/typography/P3";
import { SpendingLimitUsage } from "~/components/SpendingLimitUsage";
import { sendTransactionWithTrackingStatus } from "~/utils/sendTransactionWithTracking";
import { saveRecentAddress } from "~/components/account/AddressesList";
import { useInterval } from "~/hooks/useInterval";
import { TransferPartyDetails } from "~/components/send/TransferPartyDetails";
import { useEffect } from "react";
import { FusePlusActionBanner } from "~/components/FusePlusActionBanner";

const AmountRouteParamsZ = z.intersection(
  z.object({ amount: z.coerce.number() }),
  RecipientRouteParamsZ
);

export default function Preview() {
  const queryClient = useQueryClient();
  const { mint, recipient, amount } = AmountRouteParamsZ.parse(
    useLocalSearchParams()
  );

  const { wallet } = useActiveWallet();

  const balances = useBalances({
    address: wallet.defaultVault,
  });
  const balance = balances.find((b) =>
    mint === "SOL" ? b.mint === null : b.mint === mint
  );
  invariant(balance, "Invalid mint");

  const [state] = useSendTokenState();
  const { spendingLimits } = useSpendingLimits({
    walletKey: wallet.walletKey,
    vaultKey: wallet.defaultVault,
  });

  const spendingLimit = state.useSpendingLimit
    ? (spendingLimits.find((s) => (s.mint ?? "SOL") === mint) ?? null)
    : null;

  const createTransactionMutation = useMutation({
    mutationFn: async () => {
      const action: Action = spendingLimit
        ? {
            type: "spendingLimitUse",
            vaultIndex: 0,
            spendingLimitAddress: spendingLimit.address,
            amount: amount,
            recipient: recipient,
          }
        : mint === "SOL"
          ? {
              type: "sendSol",
              lamports: amount,
              vaultIndex: 0,
              to: recipient,
            }
          : {
              type: "sendToken",
              amount,
              vaultIndex: 0,
              to: recipient,
              tokenMint: mint,
              decimals: balance.decimals,
            };

      return wallets.prepareAction(action);
    },
    onError: (error) => {
      // Don't show an error if the user doesn't have enough funds to top up the fee buffer,
      // we handle this error in the screen itself.
      if (isClientErrorReasonType(error, "notEnoughSol")) return;

      toast.error("Transaction simulation failed");
    },
  });

  useEffect(() => {
    createTransactionMutation.mutate();
  }, []);

  //create a tx every minute to prevent tx failure due to blockhash expiration
  useInterval(() => {
    createTransactionMutation.mutate();
  }, 60_000);

  const sendMutation = useMutation({
    mutationFn: async () => {
      invariant(createTransactionMutation.data, "Invalid transaction");
      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        createTransactionMutation.data.transactions
      );

      saveRecentAddress(recipient);
      router.dismissTo("/unlocked/root/home");

      await sendTransactionWithTrackingStatus({
        walletKey: wallet.walletKey,
        transaction: { type: "action", signedTransactions },
        confirmationFailureMessage: (e) =>
          clientErrorMessage("Failed to confirm", e),
      }).finally(() => {
        Promise.all([
          refetchBalances({
            queryClient,
            address: wallet.defaultVault,
          }),
          spendingLimit &&
            refetchSpendingLimits(queryClient, {
              walletKey: wallet.walletKey,
              vaultKey: wallet.defaultVault,
            }),
        ]);
      });
    },
  });

  const tokenFee = createTransactionMutation.data?.platformFees?.find(
    (fee): fee is TokenFee =>
      fee.type === "tokenFee" && fee.mint === balance.mint
  );

  const amountFloat = amount / 10 ** balance.decimals;

  return (
    <View style={{ flex: 1, paddingHorizontal: 20, paddingVertical: 38 }}>
      <View
        style={{
          flex: 1,
          justifyContent: "space-between",
        }}
      >
        <View style={{ gap: 24 }}>
          <TransferPartyDetails
            token={{ mint, amountFloat: amountFloat }}
            destination={{ type: "onchain", address: recipient }}
          />

          <View style={{ gap: 16 }}>
            {spendingLimit && (
              <>
                <ListSeparator />
                <View style={{ gap: 12 }}>
                  <SpendingLimitRows
                    spendingLimit={spendingLimit}
                    amountToBeUsed={amount}
                  />
                </View>
              </>
            )}
            <ListSeparator />
            <View style={{ gap: 12 }}>
              <AddressRow label={"From"} address={wallet.defaultVault} />
              {tokenFee && (
                <TokenTransferFeeRow
                  key={tokenFee.mint}
                  amount={amount}
                  mint={tokenFee.mint}
                  feeBsp={tokenFee.feeBps}
                />
              )}

              <TransactionFeesRows
                data={SolanaFeesData.fromMutation(createTransactionMutation)}
              />
            </View>

            <FusePlusActionBanner />
          </View>
        </View>
        <Button
          disabled={
            createTransactionMutation.isError ||
            createTransactionMutation.isPending ||
            sendMutation.isPending
          }
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            sendMutation.mutate();
          }}
        >
          Send
        </Button>
      </View>
    </View>
  );
}

function SpendingLimitRows({
  spendingLimit,
  amountToBeUsed,
}: {
  spendingLimit: SpendingLimit;
  amountToBeUsed: number;
}) {
  return (
    <>
      <PropertyRow
        label={"Spending Limit"}
        value={
          <SpendingLimitUsage
            as={P3}
            spendingLimit={{
              mint: spendingLimit.mint,
              amount: spendingLimit.amount,
              remainingAmount: spendingLimit.remainingAmount - amountToBeUsed,
            }}
          />
        }
      />
      <PropertyRow
        label={"Timeframe"}
        value={
          <P3 style={{ textTransform: "capitalize" }}>
            {spendingLimit.period}
          </P3>
        }
      />
    </>
  );
}
