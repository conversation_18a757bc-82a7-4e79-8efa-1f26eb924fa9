import { View } from "~/components/Themed";
import { router, useLocalSearchParams } from "expo-router";
import { Button } from "~/components/Button";
import { getMaxAmount, useBalances } from "~/state/balances";
import { useActiveWallet } from "~/state/wallet";
import invariant from "invariant";
import { z } from "zod";
import { AddressZ } from "@squads/models/solana";
import { MintRouteParamsZ } from "~/app/unlocked/send/[mint]/choose-recipient";
import { useFormik } from "formik";
import { toFormikValidationSchema } from "zod-formik-adapter";
import { AmountZ } from "~/components/AmountDisplay";
import { NumPad } from "~/components/NumPad";
import * as Haptics from "expo-haptics";
import { useSendTokenState } from "~/app/unlocked/send/_layout";
import { useSpendingLimits } from "~/state/spendingLimits";
import { Flex } from "~/components/Grid";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { DashedListSeparator } from "~/components/ListSeparator";
import {
  AssetAmountInputBlock,
  TokenLabel,
  UseSpendingLimitRow,
} from "~/components/AssetAmountInputBlock";
import { AddressTag } from "~/components/send/AddressDetails";
import { numberToString } from "~/utils/conversions";

export const RecipientRouteParamsZ = z.intersection(
  z.object({ recipient: AddressZ }),
  MintRouteParamsZ
);

export default function EnterAmount() {
  const insets = useSafeAreaInsets();

  const { wallet } = useActiveWallet();

  const { mint, recipient } = RecipientRouteParamsZ.parse(
    useLocalSearchParams()
  );

  const [sendTokenFlowState, setSendTokenFlowState] = useSendTokenState();

  const { spendingLimits } = useSpendingLimits({
    walletKey: wallet.walletKey,
    vaultKey: wallet.defaultVault,
  });

  const availableSpendingLimit =
    spendingLimits.find((s) => (s.mint ?? "SOL") === mint) ?? null;

  const selectedSpendingLimit = sendTokenFlowState.useSpendingLimit
    ? availableSpendingLimit
    : null;

  const balances = useBalances({
    address: wallet.defaultVault,
  });

  const balance = balances.find((b) =>
    mint === "SOL" ? b.mint === null : b.mint === mint
  );
  invariant(balance, "Invalid mint");

  const maxAmount = getMaxAmount({
    balance,
    spendingLimit: selectedSpendingLimit,
  });

  const FormValuesZ = z.object({
    amount: AmountZ(maxAmount),
  });
  type FormValues = z.infer<typeof FormValuesZ>;

  const formik = useFormik<FormValues>({
    initialValues: {
      amount: "",
    },

    validationSchema: toFormikValidationSchema(FormValuesZ),

    onSubmit(values) {
      const amount = Number(values.amount);
      const decimalsAmount = Math.round(amount * 10 ** balance.decimals);

      Haptics.selectionAsync();
      router.push(
        `/unlocked/send/${mint}/${recipient}/${decimalsAmount}/preview`
      );
    },
  });

  return (
    <View
      style={{ flex: 1, paddingHorizontal: 20, paddingBottom: insets.bottom }}
    >
      <Flex justify={"flex-end"} gap={28}>
        <View>
          <View gap={8} style={{ marginBottom: 20 }}>
            <View gap={12} style={{ marginBottom: 12 }}>
              <TokenLabel token={balance.metadata} />
              <AssetAmountInputBlock
                amount={formik.values.amount}
                usdPrice={balance.usdcPrice}
                maxAmountQuery={{ status: "success", maxAmount }}
                onInstantChange={(amount) =>
                  formik.handleChange("amount")(numberToString(amount))
                }
              />
            </View>

            {availableSpendingLimit && (
              <View style={{ gap: 20 }}>
                <DashedListSeparator />
                <UseSpendingLimitRow
                  enabled={!!selectedSpendingLimit}
                  onSwitchChange={(enabled) => {
                    setSendTokenFlowState({ useSpendingLimit: enabled });
                  }}
                />
              </View>
            )}
          </View>

          <AddressTag address={recipient} />
        </View>

        <View style={{ gap: 20 }}>
          <NumPad
            value={formik.values.amount}
            onChange={formik.handleChange("amount")}
            decimals={balance.decimals}
          />
          <Button
            error={
              formik.errors.amount === "insufficientBalance"
                ? "Insufficient balance"
                : undefined
            }
            disabled={!formik.dirty || !formik.isValid}
            onPress={() => formik.handleSubmit()}
          >
            Review
          </Button>
        </View>
      </Flex>
    </View>
  );
}
