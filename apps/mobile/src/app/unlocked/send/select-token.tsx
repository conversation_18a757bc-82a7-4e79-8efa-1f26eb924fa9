import { View } from "~/components/Themed";
import { AssetsList } from "~/components/AssetsList";
import { useActiveWallet } from "~/state/wallet";
import { router, useGlobalSearchParams } from "expo-router";
import { nonZeroBalances, useBalances } from "~/state/balances";
import { Address } from "@squads/models/solana";
import { useCallback, useMemo, useState } from "react";
import useDebouncedValue from "~/hooks/useDebounceValue";
import { AssetsSearchInput } from "~/components/AssetsSearchInput";
import { FuseErrorBoundary } from "~/components/errors/FuseErrorBoundary";
import { ErrorFallbackView } from "~/components/ErrorFallbackView";

export default function SelectToken() {
  return (
    <FuseErrorBoundary FallbackComponent={ErrorFallbackView}>
      <SelectTokenInner />
    </FuseErrorBoundary>
  );
}

export function SelectTokenInner() {
  const { wallet } = useActiveWallet();
  const { address } = useGlobalSearchParams<{ address?: Address }>();
  const balances = nonZeroBalances(
    useBalances({ address: wallet.defaultVault })
  );

  const [searchInput, setSearchInput] = useState("");
  const debouncedInputValue = useDebouncedValue(searchInput);

  const filteredBalances = useMemo(() => {
    return balances.filter((token) => {
      const searchString = debouncedInputValue.toLowerCase();
      return (
        token.metadata.name.toLowerCase().includes(searchString) ||
        token.metadata.symbol.toLowerCase().includes(searchString) ||
        token.mint === searchString
      );
    });
  }, [debouncedInputValue]);

  const handleSelect = useCallback(
    (mint: Address | "SOL") => {
      if (address) {
        router.push(`/unlocked/send/${mint}/${address}/enter-amount`);
      } else {
        router.push(`/unlocked/send/${mint}/choose-recipient`);
      }
    },
    [address]
  );

  return (
    <View style={{ flex: 1, paddingHorizontal: 20, paddingVertical: 38 }}>
      <AssetsSearchInput value={searchInput} onChange={setSearchInput} />
      <AssetsList
        variant="position"
        tokens={filteredBalances}
        onSelect={handleSelect}
      />
    </View>
  );
}
