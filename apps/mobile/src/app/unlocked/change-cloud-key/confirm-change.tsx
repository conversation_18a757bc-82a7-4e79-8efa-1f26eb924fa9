import { Text, View } from "~/components/Themed";
import { KeyboardAvoidingButtons } from "~/components/KeyboardAvoidingButtons";
import { styles } from "~/app/unlocked/change-recovery/_layout";
import { Button } from "~/components/Button";
import { KeyDetails } from "~/components/KeyDetails";
import invariant from "invariant";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { CloudKey, prepareAction } from "~/services/wallets";
import { updateWallet, useActiveWallet } from "~/state/wallet";
import { toast } from "~/components/Toaster";
import { router } from "expo-router";
import { useChangeCloudKeyState } from "~/app/unlocked/change-cloud-key/_layout";
import * as Haptics from "expo-haptics";

import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { sendTransactionWithTrackingStatus } from "~/utils/sendTransactionWithTracking";
import { deleteCloudKey } from "~/state/cloudKey";
import { isClientErrorReasonType } from "~/services/utils";
import { showNotEnoughSolModal } from "~/components/modals/NotEnoughSolModal";

export default function ConfirmCloudKeyChange() {
  const queryClient = useQueryClient();

  const { wallet } = useActiveWallet();
  const [state] = useChangeCloudKeyState();

  const newKey = state?.newKey;
  invariant(newKey, "newKey should be set", { state });

  const cloudKeyChangeMutation = useMutation({
    mutationFn: async () => {
      const action = await prepareAction({
        type: "updateCloudKey",
        newKey,
      }).catch((e) => {
        toast.error("Failed to initiate transaction");
        throw e;
      });

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      ).catch((e) => {
        toast.error("Failed to sign transaction");
        throw e;
      });

      router.dismissTo("/unlocked/root/security");

      const keyName = CloudKey.getLabel(newKey);

      const currentCloudKey = wallet.keys.cloudKey.details;
      const isChanging = currentCloudKey.type === newKey.details.type;

      await sendTransactionWithTrackingStatus({
        walletKey: wallet.walletKey,
        transaction: { type: "action", signedTransactions },
        confirmationFailureMessage: `Failed to ${isChanging ? "change" : "change to"} ${keyName} Key`,
      });

      updateWallet(queryClient, {
        walletKey: wallet.walletKey,
        wallet: { keys: { ...wallet.keys, cloudKey: newKey } },
      });

      // cleanup iCloud key if needed
      if (currentCloudKey.type === "iCloud") {
        await deleteCloudKey(queryClient, "confirm-delete-cloud-key");
        console.debug("iCloud key deleted");
      }
    },
    onError: (error) => {
      if (isClientErrorReasonType(error, "notEnoughSol")) {
        showNotEnoughSolModal();
        return;
      }
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.headingContainer}>
        <Text variant="medium" style={styles.text}>
          Please review and confirm{"\n"}your new key to finish the process
        </Text>

        <KeyDetails
          title={CloudKey.getLabel(newKey)}
          image={newKey.details.type === "iCloud" ? "cloud" : "ledger"}
          address={CloudKey.getValue(newKey)}
        />
      </View>

      <KeyboardAvoidingButtons>
        <Button
          variant="primary"
          loading={cloudKeyChangeMutation.isPending}
          onPress={() => {
            Haptics.selectionAsync();
            cloudKeyChangeMutation.mutate();
          }}
        >
          Finish
        </Button>
      </KeyboardAvoidingButtons>
    </View>
  );
}
