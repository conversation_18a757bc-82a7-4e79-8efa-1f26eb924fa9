import { useActiveWallet } from "~/state/wallet";
import { RefObject, useId, useRef, useState } from "react";
import {
  BottomModal,
  BottomModalImperativeMethods,
  BottomModalMenuOption,
} from "~/components/BottomModal";
import { Text, View } from "~/components/Themed";
import {
  styles,
  useChangeCloudKeyState,
} from "~/app/unlocked/change-cloud-key/_layout";
import * as Haptics from "expo-haptics";
import { H1 } from "~/components/typography/H1";
import { Button } from "~/components/Button";
import { KeyboardAvoidingButtons } from "~/components/KeyboardAvoidingButtons";
import invariant from "invariant";
import {
  awaitRecoveryStatus,
  refetchWalletRecoveryState,
  reloadWalletRecoveryState,
  useWalletRecoveryState,
} from "~/state/walletRecovery";
import { KeyDetails } from "~/components/KeyDetails";
import {
  CloudKey,
  executeAction,
  prepareAction,
  RecoveryKey,
  Wallet,
} from "~/services/wallets";

import { FuseSuspense } from "~/components/FuseSuspense";
import { Spinner } from "~/components/Spinner";
import {
  QueryClient,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
import { router } from "expo-router";
import { FlatList } from "react-native";
import { ListSeparator } from "~/components/ListSeparator";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import { RecoveringKeyDetails } from "~/services/recovery";
import { signTransactionsWithFuseKeys } from "~/services/transactions";

export default function ResumeRecoveryScreen() {
  return (
    <FuseSuspense
      fallback={
        <View
          style={{ flex: 1, justifyContent: "center", alignItems: "center" }}
        >
          <Spinner size={24} colorToken={"text"} />
        </View>
      }
    >
      <ResumeRecoveryScreen_ />
    </FuseSuspense>
  );
}

export function ResumeRecoveryScreen_() {
  const { wallet } = useActiveWallet();
  const modalRef = useRef<BottomModalImperativeMethods>(null);

  const recoveryKeys = wallet.keys.recoveryKeys;
  invariant(recoveryKeys, "wallet recovery keys should be set");

  const walletRecovery = useWalletRecoveryState({
    walletKey: wallet.walletKey,
  });
  invariant(walletRecovery, "walletRecovery should be set");

  const newKey = walletRecovery.newKey;
  invariant(newKey.keyType === "cloudKey", "newKey should be a cloudKey");

  const queryClient = useQueryClient();
  const [_, setState] = useChangeCloudKeyState();

  const startRecoveryMutation = useMutation({
    mutationFn: async (recoveryKey: RecoveryKey) => {
      invariant(newKey, "newKey should be set");

      const activityId = await getOrInitCloudKeyRecovery(queryClient, {
        wallet,
        newKey: {
          keyType: "cloudKey",
          address: newKey.address,
          details: newKey.details,
        },
      });

      setState({ recovery: { key: recoveryKey, activityId } });

      router.push("/unlocked/change-cloud-key/recovery/recovery-in-progress");
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.headingContainer}>
        <H1>Resume{"\n"}2FA change</H1>

        <Text variant="medium" style={styles.text}>
          Continue adding{"\n"}of the following key:
        </Text>

        <KeyDetails
          title={CloudKey.getLabel(newKey)}
          image={"recovery"}
          address={CloudKey.getValue(newKey)}
        />
      </View>
      <KeyboardAvoidingButtons>
        <Button
          variant="primary"
          loading={startRecoveryMutation.isPending}
          onPress={() => {
            Haptics.selectionAsync();

            if (recoveryKeys.length === 1) {
              startRecoveryMutation.mutate(recoveryKeys[0]);
            } else {
              modalRef.current?.present();
            }
          }}
        >
          Continue
        </Button>
      </KeyboardAvoidingButtons>
      <ChooseRecoveryKeyModal
        recoveryKeys={recoveryKeys}
        modalRef={modalRef}
        onConfirm={(recoveryKey) =>
          startRecoveryMutation.mutateAsync(recoveryKey)
        }
      />
    </View>
  );
}

export function ChooseRecoveryKeyModal({
  recoveryKeys,
  modalRef,
  onConfirm,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
  recoveryKeys: Array<RecoveryKey>;
  onConfirm: (recoveryKey: RecoveryKey) => Promise<void>;
}) {
  const id = useId();
  const [selected, setSelected] = useState<RecoveryKey | null>(null);

  const onConfirmMutation = useMutation({
    mutationFn: onConfirm,
  });

  return (
    <BottomModal
      modalRef={modalRef}
      modalId={id}
      title={"Select Recovery Key"}
      body={
        <FlatList
          data={recoveryKeys}
          ItemSeparatorComponent={ListSeparator}
          renderItem={({ item: key }) => {
            switch (key.recoveryKeyType) {
              case "selfCustody": {
                return (
                  <BottomModalMenuOption
                    key={key.address}
                    selected={key === selected}
                    label={
                      <>
                        Wallet{" "}
                        <Text
                          colorToken="textSecondary"
                          style={{ fontSize: 15 }}
                        >
                          {"  •  "}
                          {abbreviateAddress(key.address)}
                        </Text>
                      </>
                    }
                    onSelect={() => {
                      setSelected(key);
                    }}
                  />
                );
              }

              case "turnkeyEmail":
              case "email": {
                return (
                  <BottomModalMenuOption
                    key={key.email}
                    selected={key === selected}
                    label={
                      <>
                        Email{" "}
                        <Text
                          colorToken="textSecondary"
                          style={{ fontSize: 15 }}
                        >
                          {"  •  "}
                          {key.email}
                        </Text>
                      </>
                    }
                    onSelect={() => {
                      setSelected(key);
                    }}
                  />
                );
              }

              case "keystone": {
                return (
                  <BottomModalMenuOption
                    key={key.address}
                    selected={key === selected}
                    label={
                      <>
                        Keystone{" "}
                        <Text
                          colorToken="textSecondary"
                          style={{ fontSize: 15 }}
                        >
                          {"  •  "}
                          {abbreviateAddress(key.address)}
                        </Text>
                      </>
                    }
                    onSelect={() => {
                      setSelected(key);
                    }}
                  />
                );
              }

              case "phone": {
                return null;
              }

              default:
                return key satisfies never;
            }
          }}
        />
      }
      footer={
        <Button
          variant="primary"
          loading={onConfirmMutation.isPending}
          disabled={!selected}
          onPress={async () => {
            selected && (await onConfirmMutation.mutateAsync(selected));
            modalRef.current?.dismiss();
          }}
        >
          Continue
        </Button>
      }
    />
  );
}

export async function getOrInitCloudKeyRecovery(
  queryClient: QueryClient,
  {
    wallet,
    newKey,
  }: { wallet: Wallet; newKey: RecoveringKeyDetails & { keyType: "cloudKey" } }
) {
  async function createAndSendRecoveryTx() {
    const action = await prepareAction({
      type: "recoveryInit",
      newKey,
    });

    const signedTransactions = await signTransactionsWithFuseKeys(
      wallet.keys,
      action.transactions
    );

    const { activityId } = await executeAction({
      signedTransactions,
    });

    await awaitRecoveryStatus(
      queryClient,
      { walletKey: wallet.walletKey, activityId },
      "active"
    );

    await refetchWalletRecoveryState(queryClient, {
      walletKey: wallet.walletKey,
    });

    return activityId;
  }

  const activeRecovery = await reloadWalletRecoveryState(queryClient, {
    walletKey: wallet.walletKey,
  });

  const hasRecoveryKeys = wallet.keys.recoveryKeys !== null;
  const expectedStatus = hasRecoveryKeys ? "active" : "approved";

  return activeRecovery &&
    activeRecovery.status === expectedStatus &&
    activeRecovery.newKey.keyType === "cloudKey"
    ? activeRecovery.activityId
    : await createAndSendRecoveryTx();
}
