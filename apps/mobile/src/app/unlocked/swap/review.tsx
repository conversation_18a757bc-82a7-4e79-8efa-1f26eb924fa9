import { Text, View } from "~/components/Themed";
import { useActiveWallet } from "~/state/wallet";
import { router, useLocalSearchParams } from "expo-router";
import { Button } from "~/components/Button";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Address } from "@squads/models/solana";
import * as wallets from "~/services/wallets";
import { PlatformFee, TokenFee } from "~/services/wallets";
import { Keypair } from "@solana/web3.js";
import { z } from "zod";
import { CoinLogo } from "~/components/CoinLogo";
import { useSwapState } from "~/components/SwapStateProvider";
import { useSuspenseToken, useTokenAmountFloat } from "~/state/tokens";
import { useLazyQuote } from "~/state/swaps";
import { ListSeparator } from "~/components/ListSeparator";
import { SwapQuote } from "~/services/swaps";
import { SlippageModal } from "~/components/SlippageModal";
import { useEffect, useRef, type JSX } from "react";
import { refetchBalances } from "~/state/balances";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { formatTokenAmount } from "@squads/utils/numberFormats";
import {
  PropertyRow,
  SolanaFeesData,
  TokenTransferFeeRow,
  TransactionFeesRows,
} from "~/components/PropertyRow";
import invariant from "invariant";
import { ContentSkeleton } from "~/components/Skeleton";
import { clientErrorMessage, isClientErrorReasonType } from "~/services/utils";
import {
  signTransactionsWithFuseKeys,
  signTransactionsWithKeypair,
} from "~/services/transactions";
import { Row } from "~/components/Grid";
import { sendTransactionWithTrackingStatus } from "~/utils/sendTransactionWithTracking";
import { PriceImpactWarningModal } from "~/components/swap/PriceImpactWarningModal";
import { SlippageSettingsControl } from "~/components/swap/SlippageSettingControl";
import { useToast } from "~/components/Toaster";
import { BottomModalImperativeMethods } from "~/components/BottomModal";
import { SlippageSetting, useSlippageSetting } from "~/state/slippageSetting";
import * as Haptics from "expo-haptics";
import { FusePlusActionBanner } from "~/components/FusePlusActionBanner";

export const RouteParamsZ = z.object({
  amount: z.coerce.number(),
});

export default function SwapScreen() {
  const queryClient = useQueryClient();
  const insets = useSafeAreaInsets();
  const { toast } = useToast();

  const [swapState] = useSwapState();
  const { amount } = RouteParamsZ.parse(useLocalSearchParams());
  const { wallet } = useActiveWallet();

  const { slippageSetting } = useSlippageSetting();

  const { data: quote } = useLazyQuote({
    walletKey: wallet.walletKey,
    quote: {
      inputMint: swapState.inputMint,
      outputMint: swapState.outputMint,
      amount: amount,
      slippageBps:
        slippageSetting.type === "manual"
          ? slippageSetting.bps
          : slippageSetting.maxBps,
    },
  });

  const initSwapMutation = useMutation({
    mutationFn: async (quote: SwapQuote) => {
      // Multileg swap
      let muleKeypair = Keypair.generate();

      let action = await wallets.prepareAction({
        type: "jupiterSwap",
        quote: {
          ...quote,
          slippageBps:
            slippageSetting.type === "manual"
              ? slippageSetting.bps
              : slippageSetting.maxBps,
        },
        vaultIndex: 0,
        mule: muleKeypair.publicKey.toBase58() as Address,
        dynamicSlippage:
          slippageSetting.type === "dynamic"
            ? {
                maxBps: slippageSetting.maxBps,
              }
            : null,
      });

      return { action, muleKeypair };
    },
    onError: (err) => {
      // The slippage error is handled in the UI and doesn't need a toast.
      if (isClientErrorReasonType(err, "slippageToleranceExceeded")) return;
      if (isClientErrorReasonType(err, "notEnoughSol")) return;

      toast.error("Swap simulation failed", {
        id: "swap-simulation-failed",
        opaque: true,
      });
    },
  });

  useEffect(() => {
    quote && initSwapMutation.mutate(quote);
  }, [quote]);

  const isSlippageExceeded = Boolean(
    initSwapMutation.error &&
      isClientErrorReasonType(
        initSwapMutation.error,
        "slippageToleranceExceeded"
      )
  );

  const quoteMutation = useMutation({
    mutationFn: async () => {
      invariant(initSwapMutation.data, "Invalid transaction");
      const { action, muleKeypair } = initSwapMutation.data;

      let signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );

      if (muleKeypair) {
        signedTransactions = await signTransactionsWithKeypair(
          signedTransactions,
          muleKeypair
        );
      }

      router.dismissTo("/unlocked/root/home");

      await sendTransactionWithTrackingStatus({
        walletKey: wallet.walletKey,
        transaction: { type: "action", signedTransactions },
        confirmationFailureMessage: (err) =>
          clientErrorMessage("Swap failed", err),
      }).finally(async () => {
        // Reset the mutation state to drop the mule keypair from memory.
        initSwapMutation.reset();

        Promise.all([
          refetchBalances({
            queryClient,
            address: wallet.defaultVault,
          }),
        ]);
      });
    },
  });

  const slippageModalRef = useRef<BottomModalImperativeMethods>(null);
  const priceImpactWarningModalRef = useRef<BottomModalImperativeMethods>(null);

  return (
    <>
      <PriceImpactWarningModal
        modalRef={priceImpactWarningModalRef}
        onConfirm={() => quoteMutation.mutate()}
      />
      <View
        style={{
          flex: 1,
          justifyContent: "space-between",
          padding: 20,
          paddingBottom: 20 + insets.bottom,
        }}
      >
        <View style={{ gap: 24 }}>
          <View style={{ gap: 18 }}>
            <TokenDisplay
              walletKey={wallet.walletKey}
              type="input"
              amount={Number(amount)}
              mint={swapState.inputMint}
            />
            {quote ? (
              <TokenDisplay
                walletKey={wallet.walletKey}
                type="output"
                amount={Number(quote.outAmount)}
                mint={swapState.outputMint}
              />
            ) : (
              <TokenDisplaySkeleton type="output" mint={swapState.outputMint} />
            )}
          </View>

          <View gap={16}>
            <ListSeparator />

            {quote ? (
              <QuoteDetails
                quote={quote}
                isSlippageExceeded={isSlippageExceeded}
                slippageSetting={slippageSetting}
                onSlippageControlPress={() => {
                  Haptics.selectionAsync();
                  slippageModalRef?.current?.present();
                }}
                solanaFeesData={SolanaFeesData.fromMutation({
                  isPending: initSwapMutation.isPending,
                  data: initSwapMutation.data?.action,
                  error: initSwapMutation.error,
                })}
                platformFees={
                  initSwapMutation.data?.action.platformFees ?? null
                }
              />
            ) : (
              <QuoteDetailsSkeleton />
            )}
          </View>
        </View>

        <Button
          error={isSlippageExceeded ? "Slippage exceeded" : undefined}
          iconName={isSlippageExceeded ? undefined : "faceid"}
          iconWeight={"bold"}
          disabled={
            initSwapMutation.isError ||
            initSwapMutation.isPending ||
            quoteMutation.isPending ||
            quote === undefined
          }
          onPress={() => {
            invariant(quote, "quote should be defined");
            const priceImpactPercentage = Number(quote.priceImpactPct);

            if (priceImpactPercentage > 10) {
              priceImpactWarningModalRef.current?.present();
            } else {
              quoteMutation.mutate();
            }
          }}
        >
          Confirm
        </Button>
      </View>
      <SlippageModal
        modalRef={slippageModalRef}
        suggestedSlippageBps={quote?.slippageBps ?? 0}
        isSlippageExceeded={isSlippageExceeded}
      />
    </>
  );
}

function TokenDisplay({
  walletKey,
  type,
  amount,
  mint,
}: {
  walletKey: Address;
  type: "input" | "output";
  amount: number;
  mint: Address;
}): JSX.Element {
  const token = useSuspenseToken({ mint });
  invariant(token, `Unknown token ${mint}`);
  const tokenAmount = useTokenAmountFloat({ mint, amount });
  invariant(tokenAmount, `Unknown token ${mint}`);

  return (
    <View style={{ gap: 20, flexDirection: "row", alignItems: "center" }}>
      <CoinLogo mint={mint} size={46} />
      <View style={{ flex: 1, gap: 4 }}>
        <Text variant="medium" colorToken="textTertiary">
          {type === "input" ? "Swap" : "To"}
        </Text>
        <Text variant="semibold" style={{ fontSize: 20 }}>
          {formatTokenAmount(tokenAmount, "", {
            maximumFractionDigits: 6,
          })}
          <Text
            variant="medium"
            colorToken="textSecondary"
            style={{ fontSize: 18 }}
          >
            {" "}
            {token.symbol}
          </Text>
        </Text>
      </View>
    </View>
  );
}

function TokenDisplaySkeleton({
  type,
  mint,
}: {
  type: "input" | "output";
  mint: Address;
}): JSX.Element {
  return (
    <View style={{ gap: 20, flexDirection: "row", alignItems: "center" }}>
      <CoinLogo mint={mint} size={54} />
      <View style={{ flex: 1, gap: 4 }}>
        <Text variant="medium" colorToken="textTertiary">
          {type === "input" ? "Swap" : "To"}
        </Text>
        <ContentSkeleton>
          <Text variant="medium" style={{ fontSize: 26 }}>
            100000
          </Text>
        </ContentSkeleton>
      </View>
    </View>
  );
}

function QuoteDetailsSkeleton() {
  return (
    <View gap={8}>
      <Row justify={"space-between"} style={{ minHeight: 20 }}>
        <ContentSkeleton>
          <Text variant="medium">0.00649 SOL</Text>
        </ContentSkeleton>
        <ContentSkeleton>
          <Text variant="medium">0.00649 SOL</Text>
        </ContentSkeleton>
      </Row>
    </View>
  );
}

function QuoteDetails({
  quote,
  slippageSetting,
  isSlippageExceeded,
  solanaFeesData,
  platformFees,
  onSlippageControlPress,
}: {
  quote: SwapQuote;
  slippageSetting: SlippageSetting;
  isSlippageExceeded: boolean;
  solanaFeesData: SolanaFeesData;
  platformFees: PlatformFee[] | null;
  onSlippageControlPress: () => void;
}) {
  const quoteOutAmount = Number(quote.outAmount);
  const quoteInAmount = Number(quote.inAmount);

  const outputPlatformFee = platformFees?.find(
    (fee): fee is TokenFee =>
      fee.type === "tokenFee" && fee.mint === quote.outputMint
  );
  const outputTransferFeeBsp = outputPlatformFee?.feeBps ?? 0;

  const priceImpactPercentage = Number(quote.priceImpactPct);

  const slippageBps =
    slippageSetting.type === "manual"
      ? slippageSetting.bps
      : slippageSetting.maxBps;

  const [first, ...rest] = quote.routePlan;
  const minReceiveAmount =
    quoteOutAmount -
    Math.round((quoteOutAmount / 10000) * slippageBps) -
    Math.round((quoteOutAmount / 10000) * outputTransferFeeBsp);

  const token = useSuspenseToken({ mint: quote.outputMint });
  const minReceiveAmountFloat = useTokenAmountFloat({
    mint: quote.outputMint,
    amount: minReceiveAmount,
  });
  const minReceiveString =
    minReceiveAmountFloat != null && token
      ? formatTokenAmount(minReceiveAmountFloat, " ", {
          maximumFractionDigits: 6,
        })
      : "-";

  return (
    <View style={{ gap: 16 }}>
      <View gap={12}>
        <PropertyRow
          label="Slippage"
          value={
            <SlippageSettingsControl
              level={
                isSlippageExceeded
                  ? "exceeded"
                  : slippageBps < quote.slippageBps
                    ? "below-recommended"
                    : "normal"
              }
              label={
                slippageSetting.type === "manual"
                  ? `${new Intl.NumberFormat("en", {
                      minimumFractionDigits: 1,
                      maximumFractionDigits: 1,
                    }).format(slippageBps / 100)}%`
                  : "Dynamic"
              }
              onPress={onSlippageControlPress}
            />
          }
        />

        <PropertyRow
          label="Receive at least"
          value={
            <Text variant="medium">
              {minReceiveString}
              <Text colorToken="textSecondary" variant={"medium"}>
                {token?.symbol || ""}
              </Text>
            </Text>
          }
        />

        <PropertyRow
          label={"Route"}
          value={
            <Text variant="medium">
              {first.swapInfo.label}{" "}
              {rest.length > 0 && (
                <Text variant="medium" colorToken={"textSecondary"}>
                  + {rest.length} more
                </Text>
              )}
            </Text>
          }
        />

        <PropertyRow
          label={"Price Impact"}
          value={
            <Text
              variant="medium"
              colorToken={
                priceImpactPercentage <= 1
                  ? "green"
                  : priceImpactPercentage <= 5
                    ? "orange"
                    : "systemRed"
              }
            >
              {priceImpactPercentage < 0.1
                ? "< 0.1 %"
                : formatTokenAmount(priceImpactPercentage, "%", {
                    maximumFractionDigits: 2,
                  })}
            </Text>
          }
        />
      </View>

      <ListSeparator />

      <View gap={12}>
        <PropertyRow
          label={"Platform fee"}
          value={
            <Text variant="medium" colorToken="green">
              Free
            </Text>
          }
        />

        {platformFees?.map((fee) => {
          if (fee.type !== "tokenFee") return;

          return (
            <TokenTransferFeeRow
              key={fee.mint}
              amount={
                fee.mint === quote.inputMint ? quoteInAmount : quoteOutAmount
              }
              mint={fee.mint}
              feeBsp={fee.feeBps}
            />
          );
        })}

        <TransactionFeesRows data={solanaFeesData} />
      </View>

      <FusePlusActionBanner />
    </View>
  );
}
