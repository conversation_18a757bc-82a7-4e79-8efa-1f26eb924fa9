import { View } from "~/components/Themed";
import { AssetsList } from "~/components/AssetsList";
import { router } from "expo-router";
import { Address } from "@squads/models/solana";
import { useSwapState } from "~/components/SwapStateProvider";
import { useTrustedTokens } from "~/state/tokens";
import { useCallback, useMemo, useState } from "react";
import { AssetsSearchInput } from "~/components/AssetsSearchInput";

import useDebouncedValue from "~/hooks/useDebounceValue";
import { NATIVE_MINT } from "@solana/spl-token";

export default function ChooseOutputTokenScreen() {
  const [searchInput, setSearchInput] = useState("");
  const debouncedInputValue = useDebouncedValue(searchInput);

  const [swapState] = useSwapState();
  const inputMint = swapState.inputMint;

  const allTokens = useTrustedTokens();

  const tokens = useMemo(() => {
    return Object.values(allTokens)
      .map((token) => {
        return {
          mint: token.mint,
          decimals: token.decimals,
          amount: 0,
          amount24h: 0,
          stakedAmount: 0,
          stakeAccounts: [],
          stakedAmount24h: 0,
          usdcPrice: null,
          usdcPrice24h: 0,
          metadata: {
            name: token.name,
            symbol: token.symbol,
            logoUri: token.logoUri,
          },
          driftEarn: null,
          luloProtectedEarn: null,
          kaminoEarn: null,
          isVerified: true,
        };
      })
      .filter((token) => {
        const searchString = debouncedInputValue.toLowerCase();
        return (
          token.mint !== inputMint &&
          (token.metadata.name.toLowerCase().includes(searchString) ||
            token.metadata.symbol.toLowerCase().includes(searchString) ||
            token.mint === debouncedInputValue)
        );
      });
  }, [allTokens, inputMint, debouncedInputValue]);

  const [, setSwapState] = useSwapState();

  const handleSelect = useCallback(
    (mintOrSol: Address | "SOL") => {
      const mint =
        mintOrSol === "SOL" ? (NATIVE_MINT.toBase58() as Address) : mintOrSol;

      setSwapState({ outputMint: mint });

      router.dismissTo(`/unlocked/swap/main`);
    },
    [setSwapState]
  );

  return (
    <View style={{ flex: 1, paddingHorizontal: 20, paddingVertical: 38 }}>
      <AssetsSearchInput value={searchInput} onChange={setSearchInput} />
      <AssetsList
        variant="tokenDetails"
        tokens={tokens}
        onSelect={handleSelect}
      />
    </View>
  );
}
