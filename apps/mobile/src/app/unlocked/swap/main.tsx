import { Text, useThemeColor, View } from "~/components/Themed";
import { useActiveWallet } from "~/state/wallet";
import { router, useLocalSearchParams } from "expo-router";
import { useBalance } from "~/state/balances";
import { Button } from "~/components/Button";
import { Address } from "@squads/models/solana";
import { z } from "zod";
import { useFormik } from "formik";
import { toFormikValidationSchema } from "zod-formik-adapter";
import * as Haptics from "expo-haptics";
import invariant from "invariant";
import { AmountZ, getMaxAvailableAmount } from "~/components/AmountDisplay";
import { Icon } from "~/components/Icon";
import { NumPad } from "~/components/NumPad";
import { CoinLogo } from "~/components/CoinLogo";
import { TouchableScale } from "~/components/TouchableScale";
import {
  formatAmountInputWithSeparators,
  formatUsdValue,
} from "@squads/utils/numberFormats";
import { useSwapState } from "~/components/SwapStateProvider";
import { useTokenMetadata } from "~/hooks/useTokenMetadata";
import { useLazyQuote } from "~/state/swaps";
import { useSuspenseToken, useTokenAmountFloat } from "~/state/tokens";
import { ReactNode } from "react";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import Animated, { FadeIn, FadeOut } from "react-native-reanimated";
import { DURATION_FAST } from "~/constants/animations";
import { useLoadingAnimation } from "~/hooks/useLoadingAnimation";
import { Flex } from "~/components/Grid";
import { MaxButton } from "~/components/swap/MaxButton";

export function SwapSecondaryRow({ children }: { children: ReactNode }) {
  return (
    <View
      style={{
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        gap: 12,
      }}
    >
      {children}
    </View>
  );
}

export const RouteParamsZ = z.object({
  amount: z.coerce.number().optional(),
});

export type MainSwapRouteParams = z.infer<typeof RouteParamsZ>;

export default function SwapScreen() {
  const insets = useSafeAreaInsets();
  const [swapState] = useSwapState();

  const { wallet } = useActiveWallet();
  const { amount } = RouteParamsZ.parse(useLocalSearchParams());

  const balance = useBalance({
    mint: swapState.inputMint,
    vault: wallet.defaultVault,
  });

  const token = useSuspenseToken({
    mint: swapState.inputMint,
  });
  invariant(token, `Unknown token ${swapState.inputMint}}`);

  const maxAmount = balance ? getMaxAvailableAmount(balance) : 0;

  const FormValuesZ = z.object({
    amount: AmountZ(maxAmount),
  });

  const hasInitialValue = amount !== undefined;
  const formik = useFormik<z.infer<typeof FormValuesZ>>({
    initialValues: {
      amount: amount ? String(amount / 10 ** token.decimals) : "",
    },
    validateOnMount: hasInitialValue,

    validationSchema: toFormikValidationSchema(FormValuesZ),

    onSubmit(values) {
      const amount = Math.round(Number(values.amount) * 10 ** token.decimals);
      router.push({
        pathname: `/unlocked/swap/review`,
        params: { amount },
      });
    },
  });

  const amountNumber = formik.values.amount
    ? Number(formik.values.amount)
    : null;
  const decimalsAmount =
    amountNumber && Math.round(amountNumber * 10 ** token.decimals);

  const {
    data: quote,
    isLoading,
    isError,
  } = useLazyQuote({
    walletKey: wallet.walletKey,
    quote: decimalsAmount
      ? {
          inputMint: swapState.inputMint,
          outputMint: swapState.outputMint,
          amount: decimalsAmount,
          slippageBps: 50,
        }
      : null,
  });

  const quoteLoading = !!decimalsAmount && isLoading;

  const outAmount =
    useTokenAmountFloat({
      mint: swapState.outputMint,
      amount: quote?.outAmount ? Number(quote?.outAmount) : 0,
    }) ?? 0;

  const inputUsdValue =
    amountNumber && balance
      ? formatUsdValue((balance.usdcPrice ?? 0) * amountNumber)
      : null;

  const outputUsdValue =
    amountNumber && balance
      ? formatUsdValue((balance.usdcPrice ?? 0) * amountNumber)
      : null;

  const error =
    formik.errors.amount === "insufficientBalance"
      ? "Insufficient balance"
      : isError
        ? "Route not available"
        : undefined;

  return (
    <View
      style={{
        flex: 1,
        justifyContent: "space-between",
        padding: 20,
        paddingBottom: 20 + insets.bottom,
      }}
    >
      <Flex justify={"space-between"} style={{ maxHeight: 264 }}>
        <SwapRowLabel>You pay</SwapRowLabel>
        <SwapTokenRow>
          <AmountDisplay amount={formik.values.amount || null} />
          <TokenSelector
            mint={swapState.inputMint}
            onPress={() => {
              Haptics.selectionAsync();
              router.push({
                pathname: `/unlocked/swap/choose-input`,
                params: {
                  exclude: swapState.outputMint,
                },
              });
            }}
          />
        </SwapTokenRow>
        <SwapSecondaryRow>
          <Flex>
            <Text
              numberOfLines={1}
              adjustsFontSizeToFit
              variant="medium"
              colorToken={"textSecondary"}
            >
              {inputUsdValue ?? " "}
            </Text>
          </Flex>

          <MaxButton
            balance={balance}
            onMaxPressed={(amount) =>
              formik.setFieldValue("amount", String(amount))
            }
          />
        </SwapSecondaryRow>
        <TokensSeparator onSwap={() => formik.setFieldValue("amount", "")} />
        <SwapRowLabel>You receive</SwapRowLabel>
        <SwapTokenRow>
          <Animated.View
            style={{ flex: 1 }}
            key={outAmount}
            entering={FadeIn.duration(DURATION_FAST)}
            exiting={FadeOut.duration(DURATION_FAST)}
          >
            {quoteLoading ? (
              <AmountDisplayLoading />
            ) : (
              <AmountDisplay amount={outAmount.toString()} />
            )}
          </Animated.View>
          <TokenSelector
            mint={swapState.outputMint}
            onPress={() => {
              Haptics.selectionAsync();
              router.push({
                pathname: `/unlocked/swap/choose-output`,
                params: {
                  inputMint: swapState.inputMint,
                },
              });
            }}
          />
        </SwapTokenRow>
        <SwapSecondaryRow>
          <Text variant="medium" colorToken={"textSecondary"}>
            {outputUsdValue ?? " "}
          </Text>
        </SwapSecondaryRow>
      </Flex>

      <View style={{ gap: 20 }}>
        <NumPad
          value={formik.values.amount}
          onChange={formik.handleChange("amount")}
          decimals={token.decimals}
        />
        <Button
          error={error}
          disabled={
            (!hasInitialValue && !formik.dirty) ||
            !formik.isValid ||
            quoteLoading
          }
          onPress={() => {
            Haptics.selectionAsync();
            formik.handleSubmit();
          }}
        >
          Review
        </Button>
      </View>
    </View>
  );
}

export function SwapRowLabel({ children }: { children: ReactNode }) {
  return (
    <Text variant="display-medium" style={{ fontSize: 15 }} colorToken="text">
      {children}
    </Text>
  );
}

export function SwapTokenRow({ children }: { children: ReactNode }) {
  return (
    <View
      style={{
        height: 54,
        gap: 12,
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
      }}
    >
      {children}
    </View>
  );
}

function TokensSeparator({ onSwap }: { onSwap: () => void }) {
  const [, setSwapState] = useSwapState();

  function onSwapPress() {
    setSwapState((state) => ({
      outputMint: state.inputMint,
      inputMint: state.outputMint,
    }));
    onSwap();
  }

  const separatorColor = useThemeColor({}, "listSeparator");

  return (
    <View
      style={{
        flexDirection: "row",
        alignItems: "center",
        gap: 12,
        paddingVertical: 12,
      }}
    >
      <View style={{ flex: 1, height: 1, backgroundColor: separatorColor }} />
      <TouchableScale onPress={onSwapPress} hitSlop={12}>
        <Icon
          name={"arrow.up.arrow.down"}
          size={12}
          rectSize={16}
          colorToken={"text"}
        />
      </TouchableScale>

      <View style={{ flex: 1, height: 1, backgroundColor: separatorColor }} />
    </View>
  );
}

export function AmountDisplay({ amount }: { amount: string | null }) {
  return (
    <View style={{ flex: 1, justifyContent: "center" }}>
      <Text
        adjustsFontSizeToFit={true}
        numberOfLines={1}
        colorToken={amount && amount !== "0" ? "text" : "textSecondary"}
        variant={"display-medium"}
        style={{
          fontSize: 45,
          paddingBottom: 0,
        }}
      >
        {amount ? formatAmountInputWithSeparators(amount) : "0"}
      </Text>
    </View>
  );
}

export function AmountDisplayLoading() {
  const animatedOpacityStyle = useLoadingAnimation();

  return (
    <Animated.View style={[{ flex: 1 }, animatedOpacityStyle]}>
      <AmountDisplay amount={"0"} />
    </Animated.View>
  );
}

export function TokenSelector({
  mint,
  onPress,
}: {
  mint: Address | null;
  onPress: () => void;
}) {
  const { symbol } = useTokenMetadata({ mint });
  return (
    <TouchableScale onPress={onPress}>
      <View
        background={"backgroundSecondary"}
        style={{
          flexDirection: "row",
          alignItems: "center",
          gap: 8,
          padding: 8,
          borderRadius: 32,
        }}
      >
        <CoinLogo size={24} mint={mint ?? "SOL"} />
        <Text style={{ fontSize: 18 }} variant="display-medium">
          {symbol}
        </Text>
        <Icon
          name={"chevron.right"}
          size={10}
          rectSize={24}
          weight="bold"
          colorToken={"textSecondary"}
        />
      </View>
    </TouchableScale>
  );
}
