import { View } from "~/components/Themed";
import { SelectOption } from "~/components/SelectOption";
import { LedgerIcon } from "~/components/icons/LedgerIcon";
import { Icon } from "~/components/Icon";
import { router } from "expo-router";
import {
  styles,
  useChangeRecoveryState,
} from "~/app/unlocked/change-recovery/_layout";
import * as Haptics from "expo-haptics";
import { FeatureList, FeatureListItem } from "~/components/FeatureList";

import { H1 } from "~/components/typography/H1";
import { KeystoneModal } from "~/components/modals/ConnectKeystoneModal";

export default function RecoveryOptionsScreen() {
  const [routeState] = useChangeRecoveryState();

  return (
    <View style={{ flex: 1, padding: 20 }}>
      <View style={styles.headingContainer}>
        <H1>
          {routeState.oldKey ? `Change\nRecovery Key` : "Add\nRecovery Key"}
        </H1>
        <FeatureList>
          <FeatureListItem
            colorToken={"textSecondary"}
            label={"Wallet recovery"}
            text={
              "A Recovery Key can restore access to your wallet when paired with your Device or 2FA Key. Fuse supports up to 3 Recovery Keys"
            }
          />
          <FeatureListItem
            colorToken={"textSecondary"}
            label={"Recovery only"}
            text={
              "Recovery Keys have limited rights and can never access your Smart Account without an associated Active Key"
            }
          />
        </FeatureList>
        <View style={{ gap: 8 }}>
          <SelectOption
            text={"Crypto wallet"}
            icon={
              <LedgerIcon
                colorToken={"textSecondary"}
                size={23}
                rectSize={28}
              />
            }
            onPress={() => {
              Haptics.selectionAsync();
              router.push("/unlocked/change-recovery/by-wallet");
            }}
          />
          <SelectOption
            text={"Email"}
            icon={
              <Icon
                colorToken={"textSecondary"}
                name={"envelope"}
                size={14}
                weight="medium"
                rectSize={28}
              />
            }
            onPress={() => {
              Haptics.selectionAsync();
              router.push("/unlocked/change-recovery/by-email");
            }}
          />

          {/*<SelectOption*/}
          {/*  text={"Keystone Pro"}*/}
          {/*  icon={*/}
          {/*    <Icon*/}
          {/*      colorToken={"textSecondary"}*/}
          {/*      name={"smartphone"}*/}
          {/*      size={14}*/}
          {/*      weight="medium"*/}
          {/*      rectSize={28}*/}
          {/*    />*/}
          {/*  }*/}
          {/*  onPress={async () => {*/}
          {/*    Haptics.selectionAsync();*/}
          {/*    const granted = hasPermission || (await requestPermission());*/}
          {/*    showKeystoneModal({ type: "addKeystone" });*/}
          {/*    if (!granted) {*/}
          {/*      Alert.alert(*/}
          {/*        "Camera is not available",*/}
          {/*        "Please make sure camera access is enabled",*/}
          {/*        [*/}
          {/*          { text: "Dismiss", style: "cancel" },*/}
          {/*          {*/}
          {/*            text: "Open Settings",*/}
          {/*            onPress: () => Linking.openURL("app-settings:"),*/}
          {/*            isPreferred: true,*/}
          {/*          },*/}
          {/*        ]*/}
          {/*      );*/}
          {/*      return;*/}
          {/*    }*/}
          {/*  }}*/}
          {/*/>*/}
        </View>
      </View>
      <KeystoneModal />
    </View>
  );
}
