import { Text, View } from "~/components/Themed";
import {
  styles,
  useChangeRecoveryState,
} from "~/app/unlocked/change-recovery/_layout";
import { Button } from "~/components/Button";
import { KeyDetails } from "~/components/KeyDetails";
import invariant from "invariant";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { prepareAction, RecoveryKey } from "~/services/wallets";
import { refetchWallet, useActiveWallet } from "~/state/wallet";
import { toast } from "~/components/Toaster";
import { router } from "expo-router";
import { Flex, Row } from "~/components/Grid";
import { CompactKeyDetails } from "~/app/unlocked/root/security";
import { KeyboardAvoidingButtons } from "~/components/KeyboardAvoidingButtons";
import { H1 } from "~/components/typography/H1";

import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { sendTransactionWithTrackingStatus } from "~/utils/sendTransactionWithTracking";
import { isClientErrorReasonType } from "~/services/utils";
import { showNotEnoughSolModal } from "~/components/modals/NotEnoughSolModal";

export default function ConfirmRecoveryKeyChange() {
  const queryClient = useQueryClient();
  const [routeState] = useChangeRecoveryState();

  const [state] = useChangeRecoveryState();
  const { wallet } = useActiveWallet();

  const newKey = state?.newKey;
  invariant(newKey, "newKey should be set");

  const recoveryKeyChangeMutation = useMutation({
    mutationFn: async () => {
      const details = state.oldKey
        ? { type: "changeKey" as const, oldKey: state.oldKey, newKey }
        : { type: "addKey" as const, newKey };

      const action = await prepareAction({
        type: "updateRecoveryKeys",
        details,
      });

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );

      router.dismissTo("/unlocked/root/security");

      await sendTransactionWithTrackingStatus({
        walletKey: wallet.walletKey,
        transaction: { type: "action", signedTransactions },
        confirmationFailureMessage: `Failed to confirm Recovery Key ${state.oldKey ? "update" : "add"}`,
      }).finally(() => {
        refetchWallet(queryClient, { walletKey: wallet.walletKey });
      });
    },
    onError: (error) => {
      if (isClientErrorReasonType(error, "notEnoughSol")) {
        showNotEnoughSolModal();
        return;
      }

      toast.error(`Failed to ${state.oldKey ? "update" : "add"} key`, {
        id: "tx-simulation-failed",
      });
    },
  });

  const oldRecoveryKey = state.oldKey
    ? wallet.keys.recoveryKeys!.find((key) => key.address === state.oldKey)
    : null;

  return (
    <View style={styles.container}>
      <View style={styles.headingContainer}>
        <H1>
          {routeState.oldKey ? `Change\nRecovery Key` : "Add\nRecovery Key"}
        </H1>
        <Text variant="medium" style={styles.text}>
          Please review your new key below{"\n"}and confirm to finish the
          process
        </Text>

        {oldRecoveryKey ? (
          <Row gap={20}>
            <Flex gap={16}>
              <Text
                variant="semibold"
                style={{ fontSize: 15 }}
                colorToken="textSecondary"
              >
                Old
              </Text>

              <CompactKeyDetails
                title={RecoveryKey.getLabel(oldRecoveryKey)}
                keyValue={RecoveryKey.getValue(oldRecoveryKey)}
                image={"recovery"}
              />
            </Flex>
            <Flex gap={16}>
              <Text
                variant="semibold"
                style={{ fontSize: 15 }}
                colorToken="textSecondary"
              >
                New
              </Text>
              <CompactKeyDetails
                title={RecoveryKey.getLabel(newKey)}
                keyValue={RecoveryKey.getValue(newKey)}
                image={"recovery"}
              />
            </Flex>
          </Row>
        ) : (
          <KeyDetails
            title={RecoveryKey.getLabel(newKey)}
            image={"recovery"}
            address={RecoveryKey.getValue(newKey)}
          />
        )}
      </View>

      <KeyboardAvoidingButtons>
        <Button
          variant="primary"
          loading={recoveryKeyChangeMutation.isPending}
          onPress={() => recoveryKeyChangeMutation.mutate()}
        >
          {state.oldKey ? "Change" : "Add"} Recovery Key
        </Button>
      </KeyboardAvoidingButtons>
    </View>
  );
}
