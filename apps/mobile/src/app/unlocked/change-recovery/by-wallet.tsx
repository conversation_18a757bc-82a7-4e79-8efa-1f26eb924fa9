import { Text, View } from "~/components/Themed";
import { KeyboardAvoidingButtons } from "~/components/KeyboardAvoidingButtons";
import { router } from "expo-router";
import { AddressInput } from "~/components/AddressInput";
import { Button } from "~/components/Button";
import {
  styles,
  useChangeRecoveryState,
} from "~/app/unlocked/change-recovery/_layout";
import { useActiveWallet } from "~/state/wallet";
import { WalletKeys } from "~/services/wallets";
import { validationError } from "~/utils/form";
import { useRecoveryByWalletForm } from "~/hooks/useRecoveryByWalletForm";
import { H1 } from "~/components/typography/H1";

export default function RecoveryOptionByWallet() {
  const { wallet } = useActiveWallet();
  const [, setState] = useChangeRecoveryState();

  const excludedKeys = WalletKeys.allKeys(wallet.keys);

  const formik = useRecoveryByWalletForm({
    excludedKeys,
    onSuccess: (key) => {
      setState({ newKey: key });
      router.push("/unlocked/change-recovery/confirm");
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.headingContainer}>
        <H1>Crypto Wallet</H1>
        <Text variant="medium" style={styles.text}>
          Enter a wallet address{"\n"}in the field below
        </Text>

        <View>
          <AddressInput
            placeholder={"Enter public key"}
            value={formik.values.recoveryKey}
            error={validationError(formik.errors.recoveryKey)}
            onChangeText={formik.handleChange("recoveryKey")}
            blurOnSubmit={true}
          />
        </View>
      </View>

      <KeyboardAvoidingButtons>
        <Button
          variant="primary"
          disabled={!formik.dirty || !formik.isValid}
          onPress={() => formik.handleSubmit()}
        >
          Next
        </Button>
      </KeyboardAvoidingButtons>
    </View>
  );
}
