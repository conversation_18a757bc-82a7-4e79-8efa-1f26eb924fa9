import { Stack, useLocalSearchParams } from "expo-router";
import { DURATION_FAST } from "~/constants/animations";
import { ModalHeader } from "~/components/ModalHeader";
import { z } from "zod";
import { Address, AddressZ } from "@squads/models/solana";
import { createContext, ReactNode, useContext, useMemo } from "react";
import { createStore, StoreApi, useStore } from "zustand";
import invariant from "invariant";

const RouteParamsZ = z.object({
  id: AddressZ,
  recipient: AddressZ.optional(),
});

export type SendNftRouteParams = z.infer<typeof RouteParamsZ>;

export default function NftSendLayout() {
  const data = useLocalSearchParams();
  const { id, recipient } = RouteParamsZ.parse(data);

  return (
    <SendNftProvider id={id} recipient={recipient}>
      <Stack
        screenOptions={{
          animation: "simple_push",
          animationDuration: DURATION_FAST,
          headerBackButtonMenuEnabled: false,
          headerShadowVisible: false,
          statusBarStyle: "inverted",
          header: Mo<PERSON><PERSON>eader,
        }}
      >
        <Stack.Screen
          name={"recipient"}
          options={{ title: "Choose recipient" }}
        />
        <Stack.Screen name={"review"} options={{ title: "Review" }} />
      </Stack>
    </SendNftProvider>
  );
}

export type SendNft = {
  id: Address;
  recipient?: Address;
};

const SendNftContext = createContext<StoreApi<SendNft> | null>(null);

export const SendNftProvider = ({
  children,
  id,
  recipient,
}: {
  children: ReactNode;
  id: Address;
  recipient?: Address;
}) => {
  const store = useMemo(
    () => createStore<SendNft>(() => ({ id, recipient })),
    []
  );

  return (
    <SendNftContext.Provider value={store}>{children}</SendNftContext.Provider>
  );
};

export const useSendNft = () => {
  const store = useContext(SendNftContext);
  invariant(store, "useSendNft must be used within a SendNftProvider");
  return [useStore(store), store.setState] as const;
};
