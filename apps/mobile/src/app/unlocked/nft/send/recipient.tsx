import { View } from "~/components/Themed";
import { router } from "expo-router";
import { Address } from "@squads/models/solana";
import * as Haptics from "expo-haptics";
import { useSendNft } from "~/app/unlocked/nft/send/_layout";
import { RecipientForm } from "~/components/account/RecipientForm";

export default function ChooseRecipient() {
  const [, updateState] = useSendNft();

  function selectRecipient(recipient: Address) {
    Haptics.selectionAsync();
    updateState({ recipient: recipient });
    router.push(`/unlocked/nft/send/review`);
  }

  return (
    <View style={{ flex: 1, paddingHorizontal: 20, paddingVertical: 38 }}>
      <RecipientForm onSelect={selectRecipient} />
    </View>
  );
}
