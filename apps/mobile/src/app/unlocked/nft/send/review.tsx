import { View } from "~/components/Themed";
import { ListSeparator } from "~/components/ListSeparator";
import { Button } from "~/components/Button";
import * as Haptics from "expo-haptics";
import { useActiveWallet } from "~/state/wallet";
import { useSendNft } from "~/app/unlocked/nft/send/_layout";
import { Redirect } from "~/components/Redirect";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import * as wallets from "~/services/wallets";
import invariant from "invariant";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { router } from "expo-router";
import {
  AddressRow,
  SolanaFeesData,
  TransactionFeesRows,
} from "~/components/PropertyRow";
import { useEffect } from "react";
import { removeNft, useDetailedNftById, useHiddenNfts } from "~/state/nfts";
import { sendTransactionWithTrackingStatus } from "~/utils/sendTransactionWithTracking";
import { saveRecentAddress } from "~/components/account/AddressesList";
import { isClientErrorReasonType } from "~/services/utils";
import { useInterval } from "~/hooks/useInterval";
import { NftTransferPartyDetails } from "~/components/send/TransferPartyDetails";
import { FusePlusActionBanner } from "~/components/FusePlusActionBanner";

export default function SendNftReviewScreen() {
  const queryClient = useQueryClient();

  const { removeHiddenNft } = useHiddenNfts();
  const { wallet } = useActiveWallet();
  const [{ id, recipient }] = useSendNft();
  const nft = useDetailedNftById({ id });

  if (!recipient) {
    return <Redirect href="/unlocked/nft/send/recipient" />;
  }

  const createTransactionMutation = useMutation({
    mutationFn: async () => {
      return wallets.prepareAction({
        type: "sendNft",
        id,
        vaultIndex: 0,
        to: recipient,
        amount: 1,
      });
    },
  });

  //create a tx every time the reservedForTopUp changes
  useEffect(() => {
    createTransactionMutation.mutate();
  }, []);

  //create a tx every minute to prevent tx failure due to blockhash expiration
  useInterval(() => {
    createTransactionMutation.mutate();
  }, 60_000);

  const sendMutation = useMutation({
    mutationFn: async () => {
      invariant(createTransactionMutation.data, "Invalid transaction");
      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        createTransactionMutation.data.transactions
      );

      saveRecentAddress(recipient);
      router.dismissTo("/unlocked/root/home");
      await sendTransactionWithTrackingStatus({
        walletKey: wallet.walletKey,
        transaction: { type: "action", signedTransactions },
        confirmationFailureMessage: `Sending NFT failed`,
      }).finally(() => {
        removeNft(queryClient, { vaultKey: wallet.defaultVault, id });
        removeHiddenNft(id);
      });
    },
  });

  const isUnsupportedNftFormat =
    createTransactionMutation.error &&
    !isClientErrorReasonType(createTransactionMutation.error, "notEnoughSol");

  return (
    <View style={{ flex: 1, paddingHorizontal: 20, paddingVertical: 38 }}>
      <View
        style={{
          flex: 1,
          justifyContent: "space-between",
        }}
      >
        <View style={{ gap: 48 }}>
          <NftTransferPartyDetails nft={nft} recipient={recipient} />
          <View style={{ gap: 24 }}>
            <View style={{ gap: 12 }}>
              <AddressRow label="From" address={wallet.defaultVault} />
              <ListSeparator />
              <TransactionFeesRows
                data={SolanaFeesData.fromMutation(createTransactionMutation)}
              />
            </View>
            <FusePlusActionBanner />
          </View>
        </View>
        <Button
          warning={
            isUnsupportedNftFormat ? "Not supported NFT format" : undefined
          }
          disabled={
            !createTransactionMutation.data ||
            createTransactionMutation.isError ||
            createTransactionMutation.isPending ||
            sendMutation.isPending
          }
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            sendMutation.mutate();
          }}
        >
          Send
        </Button>
      </View>
    </View>
  );
}
