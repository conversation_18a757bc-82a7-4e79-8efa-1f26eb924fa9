import { SafeAreaView, View } from "~/components/Themed";
import { useLocalSearchParams, useNavigation } from "expo-router";
import { Address, AddressZ } from "@squads/models/solana";
import { Suspense } from "react";
import { Flex, Row } from "~/components/Grid";
import { BottomInset } from "~/components/BottomInset";
import { ScrollView } from "react-native";
import { z } from "zod";
import { useDetailedNftById, useHiddenNfts, useNftById } from "~/state/nfts";
import { ContentSkeleton } from "~/components/Skeleton";
import { PropertyRow } from "~/components/PropertyRow";
import { P3 } from "~/components/typography/P3";
import { H3 } from "~/components/typography/H3";
import { useActiveWallet } from "~/state/wallet";
import { NftImage } from "~/components/NftImage";
import { SendNftButton } from "~/components/SendNftButton";
import { FloatingFooter } from "~/components/FloatingFooter";
import {
  HEADER_HEIGHT,
  StackHeader,
} from "~/components/navigation/StackHeader";
import Animated, { AnimatedRef, useAnimatedRef } from "react-native-reanimated";
import { MenuView } from "@react-native-menu/menu";
import { useLock } from "~/hooks/useLock";
import { Icon } from "~/components/Icon";
import { TouchableScale } from "~/components/TouchableScale";
import * as Haptics from "expo-haptics";
import { toast } from "~/components/Toaster";
import { CommonActions } from "@react-navigation/native";

const RouteParamsZ = z.object({
  id: AddressZ,
});

const HIDE_UNHIDE_TOAST_ID = "hide-unhide-nft";
export default function NFTScreen() {
  const { id } = RouteParamsZ.parse(useLocalSearchParams());
  const scrollViewRef = useAnimatedRef<Animated.ScrollView>();

  return (
    <SafeAreaView ignoreBottom style={{ flex: 1 }}>
      <Suspense>
        <NftScreenHeader id={id} scrollViewRef={scrollViewRef} />
      </Suspense>
      <View style={{ paddingTop: HEADER_HEIGHT + 20, flex: 1 }}>
        <ScrollView
          ref={scrollViewRef}
          style={{ overflow: "visible" }}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingHorizontal: 20, gap: 24 }}
        >
          <Suspense fallback={<NftContentSkeleton />}>
            <NftContent id={id} />
          </Suspense>
        </ScrollView>

        <FloatingFooter>
          <SendNftButton id={id} />
        </FloatingFooter>
      </View>
    </SafeAreaView>
  );
}

function NftMenueView({ nftAddress }: { nftAddress: string }) {
  const withLock = useLock();
  const { hiddenNftAddresses, addHiddenNft, removeHiddenNft } = useHiddenNfts();
  const navigation = useNavigation();

  const hideControlAction = hiddenNftAddresses.includes(nftAddress)
    ? ({
        id: "unhide-nft",
        title: "Unhide",
        image: "eye",
      } as const)
    : ({
        id: "hide-nft",
        title: "Hide",
        image: "eye.slash",
      } as const);

  return (
    <MenuView
      themeVariant="light"
      onPressAction={withLock(async ({ nativeEvent }) => {
        switch (nativeEvent.event as (typeof hideControlAction)["id"]) {
          case "hide-nft": {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Soft);
            addHiddenNft(nftAddress);
            toast.info("Added to Hidden", {
              id: HIDE_UNHIDE_TOAST_ID,
              iconName: "eye.slash.circle.fill",
            });
            break;
          }
          case "unhide-nft":
            {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Rigid);
              removeHiddenNft(nftAddress);
              const navigationState = navigation.getState();

              if (navigationState) {
                const previousScreenIndex = navigationState.index - 1;
                const routes = navigationState.routes;
                const routeParams = routes[previousScreenIndex].params as {
                  collection_id?: string;
                };

                if (
                  previousScreenIndex >= 0 &&
                  routeParams?.collection_id === "hidden" &&
                  hiddenNftAddresses.length <= 1
                ) {
                  navigation.dispatch((state) => {
                    const updatedRoutes = [...state.routes];
                    updatedRoutes.splice(previousScreenIndex, 1);

                    return CommonActions.reset({
                      ...state,
                      routes: updatedRoutes,
                      index: updatedRoutes.length - 1,
                    });
                  });
                }
              }
            }
            toast.info("Removed from Hidden", {
              id: HIDE_UNHIDE_TOAST_ID,
              iconName: "eye.circle.fill",
            });
            break;
        }
      })}
      actions={[hideControlAction]}
    >
      <TouchableScale onPress={() => Haptics.selectionAsync()}>
        <Icon name={"ellipsis"} size={12} rectSize={20} colorToken={"text"} />
      </TouchableScale>
    </MenuView>
  );
}

function NftScreenHeader({
  id,
  scrollViewRef,
}: {
  id: Address;
  scrollViewRef: AnimatedRef<Animated.ScrollView>;
}) {
  const { wallet } = useActiveWallet();
  const nft = useNftById({ id, vaultKey: wallet.defaultVault });

  let name = nft.name;
  if (name === null || name === undefined || name === "") {
    name = "Untitled NFT";
  }

  return (
    <StackHeader
      title={name}
      scrollViewRef={scrollViewRef}
      rightButton={<NftMenueView nftAddress={nft.address} />}
    />
  );
}

function NftContentSkeleton() {
  return (
    <>
      <ContentSkeleton>
        <NftHeader text="NFT" />
      </ContentSkeleton>
      <ContentSkeleton>
        <NftImage nft={{ imageUrl: null, videoUrl: null }} />
      </ContentSkeleton>
    </>
  );
}

function NftContent({ id }: { id: Address }) {
  const { wallet } = useActiveWallet();
  const nft = useNftById({ id, vaultKey: wallet.defaultVault });

  return (
    <Flex gap={24}>
      <NftImage nft={nft} borderRadius={24} />
      <Suspense fallback={<NftDetailsSkeleton />}>
        <NftDetails id={id} />
      </Suspense>
    </Flex>
  );
}

function NftHeader({ text }: { text: string }) {
  return (
    <Row>
      <H3>{text}</H3>
    </Row>
  );
}

function NftDetailsSkeleton() {
  return (
    <View gap={24}>
      <View gap={8}>
        <PropertyRow label={"Description"} value={null} />
        <ContentSkeleton>
          <P3>text text text text text text text text text</P3>
          <P3>text text text text text text text text text</P3>
        </ContentSkeleton>
      </View>
      <View gap={12}>
        <PropertyRow
          label={"Collection"}
          value={
            <ContentSkeleton>
              <P3>text text text</P3>
            </ContentSkeleton>
          }
        />
        <PropertyRow
          label={"Floor price"}
          value={
            <ContentSkeleton>
              <P3>text text text</P3>
            </ContentSkeleton>
          }
        />
        <PropertyRow
          label={"Last sale price"}
          value={
            <ContentSkeleton>
              <P3>text text text</P3>
            </ContentSkeleton>
          }
        />
        <PropertyRow
          label={"Total return"}
          value={
            <ContentSkeleton>
              <P3>text text text</P3>
            </ContentSkeleton>
          }
        />
      </View>
    </View>
  );
}

function NftDetails({ id }: { id: Address }) {
  const nft = useDetailedNftById({ id });

  return (
    <View gap={24}>
      {nft.description !== null && nft.description !== "" ? (
        <View gap={8}>
          <PropertyRow label={"Description"} value={null} />
          <P3>{nft.description}</P3>
        </View>
      ) : null}
      <View gap={12}>
        {nft.collection !== null ? (
          <PropertyRow
            label={"Collection"}
            value={<P3 numberOfLines={1}>{nft.collection.name}</P3>}
          />
        ) : null}
      </View>
    </View>
  );
}
