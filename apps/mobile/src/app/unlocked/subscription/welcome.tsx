import { Icon } from "~/components/Icon";
import { router } from "expo-router";
import { Button } from "~/components/Button";
import { Text, View } from "~/components/Themed";
import { ScreenLayout } from "./promo";
import Animated, {
  interpolate,
  SharedValue,
  useAnimatedStyle,
  useSharedValue,
  withD<PERSON>y,
  withSpring,
} from "react-native-reanimated";
import { ReactNode, useEffect } from "react";
import { Row } from "~/components/Grid";
import { DashedListSeparator } from "~/components/ListSeparator";
import { LinearGradient } from "expo-linear-gradient";
import { PlusIcon } from "~/components/icons/PlusIcon";
import { PlusBlur } from "~/components/icons/PlusBlur";
import { easeGradient } from "~/utils/easeGradient";
import { NetworkIcon } from "~/components/icons/NetworkIcon";
import { EuroDollarIcon } from "~/components/icons/EuroDollarIcon";
import { useActiveWallet } from "~/state/wallet";
import { FuseLogoDuoColor } from "~/components/icons/FuseLogoDuoColor";
import { SparklesIcon } from "~/components/icons/SparklesIcon";
import { StyleSheet } from "react-native";
import { setAppIcon } from "~/utils/appIcon";
import { Image } from "expo-image";
import circleBlur from "~assets/images/circle-blur.png";
import { hapticDismissBottomTray, hapticSelect } from "~/utils/haptics";

export default function SubscriptionWelcomeScreen() {
  const animationStage1Progress = useSharedValue(0);
  const animationStage2Progress = useSharedValue(0);
  useEffect(() => {
    animationStage1Progress.value = withSpring(
      1,
      {
        stiffness: 200,
        damping: 32,
      },
      () => {
        animationStage2Progress.value = withDelay(
          200,
          withSpring(1, {
            stiffness: 65,
            damping: 15,
          })
        );
      }
    );
  }, []);

  // Fade in from the bottom
  const contentAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(animationStage1Progress.value, [0, 1], [0, 1]),
      transform: [
        {
          translateY: interpolate(
            animationStage1Progress.value,
            [0, 1],
            [20, 0]
          ),
        },
      ],
    };
  });

  async function dismissModal() {
    hapticSelect();
    await setAppIcon("app-icon-plus").catch(console.error);
    router.back();
  }

  return (
    <ScreenLayout
      backgroundAnimationProgress={animationStage1Progress}
      gradientVariant={"low"}
      onClosePress={dismissModal}
      body={
        <View>
          {/* Heading section */}
          <Animated.View
            style={[{ top: -28, alignItems: "center" }, contentAnimatedStyle]}
          >
            <Text
              variant="medium"
              colorToken={"textSecondaryOpposite"}
              size={18}
            >
              Membership
            </Text>
            <Text
              variant="semibold"
              colorToken={"textOpposite"}
              size={24}
              style={{ lineHeight: 26, marginTop: 24 }}
            >
              Welcome to Fuse Plus
            </Text>
            <Text
              variant="medium"
              size={14}
              style={{ textAlign: "center", marginTop: 8 }}
              colorToken={"textSecondaryOpposite"}
            >
              Your membership to earn more, pay less,{"\n"}
              and access premium benefits.
            </Text>
          </Animated.View>

          <Animated.View
            style={[
              { paddingHorizontal: 24, marginTop: 12 },
              contentAnimatedStyle,
            ]}
          >
            <Card plusGlowAnimationProgress={animationStage2Progress} />
          </Animated.View>
        </View>
      }
      footer={
        <Animated.View style={[{ gap: 32 }, contentAnimatedStyle]}>
          <View
            gap={14}
            style={{ alignItems: "center", paddingHorizontal: 12 }}
          >
            <Text
              variant="medium"
              style={{ textAlign: "center" }}
              colorToken={"textSecondaryOpposite"}
              size={14}
            >
              Keep $100 or more in Fuse Earn{"\n"}to maintain your membership.
            </Text>
          </View>
          <Button
            size={"medium-new"}
            variant="secondary"
            onPress={dismissModal}
          >
            Got it!
          </Button>
        </Animated.View>
      }
    />
  );
}

const CARD_BORDER_GRADIENT_COLOR_STOPS = easeGradient({
  colorStops: {
    0: "rgba(224,230,234,0)",
    0.5: "rgba(224,230,234,0.4)",
    1: "rgba(224,230,234,0)",
  },
});

function Card({
  plusGlowAnimationProgress,
}: {
  plusGlowAnimationProgress: SharedValue<number>;
}) {
  return (
    <View
      style={{
        backgroundColor: "#181718",
        paddingHorizontal: 24,
        paddingVertical: 24,
        borderRadius: 32,
        borderCurve: "continuous",
      }}
    >
      <PlusMembershipCardContent
        plusGlowAnimationProgress={plusGlowAnimationProgress}
      />
    </View>
  );
}

export function PlusMembershipCardContent({
  plusGlowAnimationProgress,
}: {
  plusGlowAnimationProgress: SharedValue<number>;
}) {
  const { wallet } = useActiveWallet();
  const walletName = wallet.vaults.find((v) => v.index === 0)?.name ?? "Wallet";

  const glowAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(plusGlowAnimationProgress.value, [0, 1], [0, 1]),
    };
  });

  return (
    <>
      {/* Plus logo */}
      <View
        style={{
          alignItems: "center",
          justifyContent: "center",
          alignSelf: "flex-start",
          marginTop: 12,
        }}
      >
        <Animated.View style={[{ position: "absolute" }, glowAnimatedStyle]}>
          <PlusBlur size={24} />
        </Animated.View>
        <PlusIcon size={20} />
      </View>

      {/* Heading */}
      <View style={{ marginTop: 24 }}>
        <Text variant="semibold" size={28} colorToken={"textOpposite"}>
          Fuse Plus
        </Text>
        <Text
          variant="semibold"
          size={28}
          colorToken={"textSecondaryOpposite"}
          style={{ lineHeight: 30 }}
        >
          Member
        </Text>
      </View>

      {/* Access */}
      <View style={{ marginTop: 24 }}>
        <Text variant="medium" size={14} colorToken="textSecondaryOpposite">
          Access to
        </Text>
        {/* Benefits */}
        <View gap={8} style={{ marginTop: 12 }}>
          <Benefit icon={<NetworkIcon size={24} />} text="Free Transactions" />
          <Benefit
            icon={<EuroDollarIcon size={24} />}
            text={
              <Text variant="medium" colorToken="textOpposite">
                Free Off-Ramps{" "}
                <Text variant="medium" colorToken={"textSecondaryOpposite"}>
                  up to $25K/month
                </Text>
              </Text>
            }
          />
          <Benefit icon={<SparklesIcon size={24} />} text="Custom App Icon" />
        </View>

        <View style={{ marginVertical: 24 }}>
          <DashedListSeparator colorToken="textSecondaryOpposite" />
        </View>

        <Row justify="space-between">
          <View gap={2}>
            <Text variant="medium" size={14} colorToken="textSecondaryOpposite">
              Owner
            </Text>
            <Text variant="medium" size={14} colorToken={"textOpposite"}>
              {walletName}
            </Text>
          </View>

          <FuseLogoDuoColor />
        </Row>
      </View>

      {/* Shiny bottom border */}
      <Animated.View
        style={[
          {
            width: "100%",
            position: "absolute",
            left: 20,
            bottom: 0.5,
            opacity: 0.6,
          },
        ]}
      >
        <LinearGradient
          style={{
            width: "100%",
            height: 0.5,
          }}
          start={[0, 0]}
          end={[1, 0]}
          colors={
            CARD_BORDER_GRADIENT_COLOR_STOPS.colors as readonly [
              string,
              string,
              ...string[],
            ]
          }
          locations={
            CARD_BORDER_GRADIENT_COLOR_STOPS.locations as readonly [
              number,
              number,
              ...number[],
            ]
          }
        />
      </Animated.View>

      {/* Bottom background gradient */}
      <View
        style={{
          position: "absolute",
          bottom: 0,
          width: "100%",
          height: 96,
          alignSelf: "center",
          overflow: "hidden",
        }}
      >
        <Image
          source={circleBlur}
          style={{
            aspectRatio: 1,
            width: "100%",
            opacity: 0.1,
          }}
        ></Image>
      </View>
    </>
  );
}

function Benefit({
  icon,
  text,
}: {
  icon: ReactNode;
  text: string | ReactNode;
}) {
  return (
    <Row gap={12}>
      <View
        style={{
          borderRadius: 6,
          overflow: "hidden",
          borderColor: "#272727",
          borderWidth: StyleSheet.hairlineWidth,
        }}
      >
        {icon}
      </View>
      {typeof text === "string" ? (
        <Text variant="medium" size={14} colorToken={"textOpposite"}>
          {text}
        </Text>
      ) : (
        text
      )}
    </Row>
  );
}
