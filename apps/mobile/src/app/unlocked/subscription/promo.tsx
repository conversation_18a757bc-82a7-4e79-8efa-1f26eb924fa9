import { Icon } from "~/components/Icon";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import { router } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Button } from "~/components/Button";
import { Text, View } from "~/components/Themed";
import { StyleSheet, useWindowDimensions } from "react-native";
import { Canvas, RadialGradient, Rect, vec } from "@shopify/react-native-skia";
import { FusePlusText } from "~/components/FusePlusText";
import { ReactNode, useEffect, useMemo } from "react";
import { FusePlusLogo } from "~/components/icons/FusePlusLogo";
import { NetworkIcon } from "~/components/icons/NetworkIcon";
import { EuroDollarIcon } from "~/components/icons/EuroDollarIcon";
import { Row } from "~/components/Grid";
import { DashedListSeparator } from "~/components/ListSeparator";
import { LinearGradient } from "expo-linear-gradient";
import { easeGradient } from "~/utils/easeGradient";
import Animated, {
  interpolate,
  SharedValue,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withDelay,
  withSpring,
} from "react-native-reanimated";
import { hapticGoBack, hapticGoForward } from "~/utils/haptics";

const HIT_SLOP = 8;

export default function SubscriptionPromoScreen() {
  const animationStage1Progress = useSharedValue(0);
  const animationStage2Progress = useSharedValue(0);

  useEffect(() => {
    animationStage1Progress.value = withSpring(
      1,
      {
        stiffness: 200,
        damping: 32,
      },
      () => {
        animationStage2Progress.value = withDelay(
          200,
          withSpring(1, {
            stiffness: 65,
            damping: 15,
          })
        );
      }
    );
  }, []);

  // Fade in from the bottom
  const logoAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(animationStage1Progress.value, [0, 1], [0, 1]),
      transform: [
        {
          translateY: interpolate(
            animationStage1Progress.value,
            [0, 1],
            [40, 0]
          ),
        },
      ],
    };
  });

  // Fade in
  const headingAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(animationStage2Progress.value, [0, 1], [0, 1]),
    };
  });

  // Fade in from the bottom
  const contentAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(animationStage2Progress.value, [0, 1], [0, 1]),
      transform: [
        {
          translateY: interpolate(
            animationStage2Progress.value,
            [0, 1],
            [20, 0]
          ),
        },
      ],
    };
  });

  return (
    <ScreenLayout
      backgroundAnimationProgress={animationStage2Progress}
      body={
        <View>
          {/* Heading section */}
          <View style={{ alignItems: "center" }}>
            <Animated.View
              style={[{ padding: 10, marginBottom: 12 }, logoAnimatedStyle]}
            >
              <FusePlusLogo size={20} />
            </Animated.View>
            <Animated.View
              style={[
                { alignItems: "center", opacity: 0 },
                headingAnimatedStyle,
              ]}
            >
              <Text
                variant="semibold"
                size={24}
                colorToken={"textOpposite"}
                style={{ lineHeight: 26 }}
              >
                Fuse Plus
              </Text>
              <Text
                variant="semibold"
                size={24}
                colorToken={"textSecondaryOpposite"}
                style={{ lineHeight: 26 }}
              >
                Membership
              </Text>
            </Animated.View>
          </View>

          {/* Benefits */}
          <Animated.View
            style={[
              { marginTop: 32, paddingHorizontal: 24, gap: 12 },
              contentAnimatedStyle,
            ]}
          >
            <Card
              icon={<NetworkIcon />}
              title="Free Transactions"
              description="Use Fuse without paying Solana transaction fees."
            />
            <Card
              icon={<EuroDollarIcon />}
              title="Free Off-Ramps"
              description={
                "Off-ramp up to $25K/month to your bank\naccount with zero platform fees."
              }
            />
          </Animated.View>
        </View>
      }
      footer={
        <Animated.View style={[{ gap: 32 }, contentAnimatedStyle]}>
          {/* How to get */}
          <View
            gap={14}
            style={{ alignItems: "center", paddingHorizontal: 12 }}
          >
            <View style={{ alignItems: "center" }} gap={8}>
              <Text
                variant="medium"
                size={14}
                colorToken={"textSecondaryOpposite"}
              >
                How to get Fuse Plus
              </Text>
              <Row gap={8}>
                <Icon
                  name={"arrow.down.circle.dotted"}
                  colorToken={"textOpposite"}
                  size={14}
                  weight="semibold"
                />
                <Text
                  variant={"semibold"}
                  size={16}
                  colorToken={"textOpposite"}
                >
                  Deposit $100+ into Earn
                </Text>
              </Row>
            </View>
            <DashedListSeparator colorToken={"textSecondaryOpposite"} />
            <Text
              variant="medium"
              style={{ textAlign: "center" }}
              colorToken={"textSecondaryOpposite"}
              size={14}
            >
              Keep $100 or more in Earn{"\n"}to maintain your membership.
            </Text>
          </View>

          <Button
            size={"medium-new"}
            variant="primary"
            style={{ text: { top: 3 } }}
            onPress={() => {
              hapticGoForward();
              router.dismissTo("/unlocked/root/earn");
            }}
          >
            <FusePlusText
              colorToken={"textButtonPrimary"}
              prefix={"Deposit and get "}
              size={16}
            />
          </Button>
        </Animated.View>
      }
    />
  );
}

export function ScreenLayout({
  backgroundAnimationProgress,
  body,
  footer,
  gradientVariant,
  onClosePress = () => {
    hapticGoBack();
    router.back();
  },
}: {
  backgroundAnimationProgress: SharedValue<number>;
  body: ReactNode;
  footer: ReactNode;
  gradientVariant?: "high" | "low";
  onClosePress?: () => void;
}) {
  const insets = useSafeAreaInsets();

  return (
    <View style={{ flex: 1, position: "relative", overflow: "visible" }}>
      <AnimatedBackground
        animationProgress={backgroundAnimationProgress}
        gradientVariant={gradientVariant}
      />

      {/*Header*/}
      <View
        style={{
          marginTop: insets.top,
          zIndex: 1,
          flexDirection: "row",
          justifyContent: "flex-end",
          alignItems: "center",
          paddingHorizontal: 32,
        }}
      >
        <View
          style={{
            right: -HIT_SLOP,
          }}
        >
          <AnimatedTouchableScale
            pressedScale={0.95}
            hitSlop={HIT_SLOP}
            onPress={onClosePress}
          >
            <Icon
              name={"xmark"}
              weight="semibold"
              size={14}
              rectSize={16 + HIT_SLOP * 2}
              colorToken="textSecondaryOpposite"
            />
          </AnimatedTouchableScale>
        </View>
      </View>

      <View style={{ flex: 1 }}>{body}</View>
      <View style={{ paddingBottom: insets.bottom + 8, paddingHorizontal: 32 }}>
        {footer}
      </View>
    </View>
  );
}

const CARD_BORDER_GRADIENT_COLOR_STOPS = easeGradient({
  colorStops: {
    0: "rgba(224,230,234,0)",
    0.5: "rgba(224,230,234,0.4)",
    1: "rgba(224,230,234,0)",
  },
});

function Card({
  icon,
  title,
  description,
}: {
  icon: ReactNode;
  title: string;
  description: string;
}) {
  return (
    <View
      style={{
        backgroundColor: "#181718",
        paddingHorizontal: 20,
        paddingVertical: 24,
        borderRadius: 24,
        borderCurve: "continuous",
        alignItems: "flex-start",
      }}
    >
      <View
        style={{
          borderRadius: 8,
          overflow: "hidden",
        }}
      >
        {icon}
      </View>
      <View gap={4} style={{ marginTop: 16 }}>
        <Text variant={"semibold"} size={16} colorToken={"textOpposite"}>
          {title}
        </Text>
        <Text
          variant={"semibold"}
          size={14}
          colorToken={"textSecondaryOpposite"}
        >
          {description}
        </Text>
      </View>
      {/* Shiny bottom border */}
      <Animated.View
        style={[
          {
            width: "100%",
            position: "absolute",
            left: 20,
            bottom: 0.5,
            opacity: 0.6,
          },
        ]}
      >
        <LinearGradient
          style={{
            width: "100%",
            height: 0.5,
          }}
          start={[0, 0]}
          end={[1, 0]}
          colors={
            CARD_BORDER_GRADIENT_COLOR_STOPS.colors as readonly [
              string,
              string,
              ...string[],
            ]
          }
          locations={
            CARD_BORDER_GRADIENT_COLOR_STOPS.locations as readonly [
              number,
              number,
              ...number[],
            ]
          }
        />
      </Animated.View>
    </View>
  );
}

const INITIAL_TRANSLATE_Y = 200;

function AnimatedBackground({
  animationProgress,
  gradientVariant = "high",
}: {
  animationProgress: SharedValue<number>;
  gradientVariant?: "low" | "high";
}) {
  const { width, height } = useWindowDimensions();

  const GRADIENT_STOPS = useMemo(() => {
    return easeGradient({
      colorStops: {
        0: "#000101",
        0.35: "#000206",
        0.45: "#181818",
        [gradientVariant === "high" ? 0.6 : 0.7]: "#E0E6EA",
      },
    });
  }, [gradientVariant]);

  const gradientHeight = height * 2.2;
  const gradientCenterY = 30;
  const gradientCenterX = width / 2;

  // const translateY = useSharedValue(INITIAL_TRANSLATE_Y);
  const translateY = useDerivedValue(() => {
    return interpolate(
      animationProgress.value,
      [0, 1],
      [INITIAL_TRANSLATE_Y, 0]
    );
  });

  const animatedStyles = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  return (
    <View style={[StyleSheet.absoluteFill, { overflow: "hidden" }]}>
      <Animated.View style={[{ flex: 1 }, animatedStyles]}>
        <Canvas
          style={[
            {
              position: "absolute",
              top: -INITIAL_TRANSLATE_Y - 80,
              left: 0,
              right: 0,
              bottom: -20,
              backgroundColor: "#E0E6EA",
            },
          ]}
        >
          <Rect x={-width / 2} y={0} width={width * 2} height={gradientHeight}>
            <RadialGradient
              c={vec(gradientCenterX, gradientCenterY)}
              r={gradientHeight}
              positions={GRADIENT_STOPS.locations as number[]}
              colors={GRADIENT_STOPS.colors as string[]}
            />
          </Rect>
        </Canvas>
      </Animated.View>
    </View>
  );
}
