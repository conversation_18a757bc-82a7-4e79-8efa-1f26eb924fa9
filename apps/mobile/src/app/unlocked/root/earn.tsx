import { useColor, View } from "~/components/Themed";
import { AccessibleBlurView } from "~/components/AccessibleBlurView";
import { Row } from "~/components/Grid";
import { WalletInfo } from "~/components/WalletInfo";
import { ActivityButton } from "~/components/ActivityButton";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { RefreshControl, ScrollView } from "react-native";
import { useActiveWallet } from "~/state/wallet";
import { useEffect, useState } from "react";
import * as Haptics from "expo-haptics";
import { loadHistoricalBalances, queryKeys } from "~/state/balances";
import { useQueryClient } from "@tanstack/react-query";
import Animated, {
  LinearTransition,
  ReduceMotion,
} from "react-native-reanimated";
import { DURATION_MEDIUM } from "~/constants/animations";
import { EarnDashboard } from "~/components/earn/EarnDashboard";
import { EarnTiles } from "~/components/earn/EarnTiles";

export default function EarnScreen() {
  const { wallet } = useActiveWallet();

  const queryClient = useQueryClient();
  useEffect(() => {
    loadHistoricalBalances(queryClient, {
      vault: wallet.defaultVault,
      period: "year",
    });
  }, []);

  const insets = useSafeAreaInsets();
  const backgroundColor = useColor("backgroundTabBar");
  const fallbackBackgroundColor = useColor("background");

  const refreshControlColor = useColor("textTertiary");
  const [refreshing, setRefreshing] = useState(false);

  return (
    <View style={{ flex: 1 }}>
      <View
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1,
          overflow: "hidden",
        }}
      >
        <AccessibleBlurView
          intensity={60}
          tint="light"
          fallbackBackgroundColor={fallbackBackgroundColor}
          style={{
            gap: 20,
            backgroundColor,
            paddingHorizontal: 20,
            paddingBottom: 12,
            paddingTop: insets.top + 4,
          }}
        >
          <Row justify="space-between">
            <WalletInfo />
            <ActivityButton />
          </Row>
        </AccessibleBlurView>
      </View>
      <View
        style={{
          flex: 1,
          gap: 20,
          paddingHorizontal: 20,
          paddingTop: insets.top + 80,
        }}
      >
        <ScrollView
          style={{ overflow: "visible" }}
          contentContainerStyle={{
            paddingBottom: insets.bottom + 76,
            gap: 10,
          }}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              tintColor={refreshControlColor}
              refreshing={refreshing}
              onRefresh={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setRefreshing(true);

                queryClient
                  .refetchQueries({
                    queryKey: [
                      queryKeys.balancesByAddress(wallet.defaultVault),
                      "lulo_pools",
                      "drift_spot_markets",
                      queryKeys.historicalBalancesByAddressAndPeriod(
                        wallet.defaultVault,
                        "year"
                      ),
                    ],
                  })
                  .finally(() => setRefreshing(false));
              }}
            />
          }
        >
          <EarnDashboard />

          <Animated.View
            layout={LinearTransition.springify()
              .duration(DURATION_MEDIUM)
              .dampingRatio(1)
              .reduceMotion(ReduceMotion.Never)}
          >
            <EarnTiles />
          </Animated.View>
        </ScrollView>
      </View>
    </View>
  );
}
