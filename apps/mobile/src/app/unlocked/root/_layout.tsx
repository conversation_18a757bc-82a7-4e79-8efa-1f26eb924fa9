import { Tabs } from "expo-router/tabs";
import { HomeIcon } from "~/components/icons/HomeIcon";
import { SecurityIcon } from "~/components/icons/SecurityIcon";
import { SettingsIcon } from "~/components/icons/SettingsIcon";
import { Text, useColor, View } from "~/components/Themed";
import { DeFiIcon } from "~/components/icons/DeFiIcon";
import * as Haptics from "expo-haptics";
import { FuseSuspense } from "~/components/FuseSuspense";
import { AccessibleBlurView } from "~/components/AccessibleBlurView";
import { useLazySecurityWarning } from "~/hooks/useSecurityWarning";
import { useActiveWallet } from "~/state/wallet";
import { useIsMagicLinkKeyMigrating } from "~/app/unlocked/migrate-email-recovery";
import { IconWrapper } from "~/components/IconWrapper";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";

export default function UnlockedRootLayout() {
  const backgroundColor = useColor("backgroundTabBar");
  const fallbackBackgroundColor = useColor("background");

  return (
    <FuseSuspense>
      <Tabs
        screenOptions={{
          tabBarLabel: (props) => {
            return (
              <Text
                variant="medium"
                colorToken={props.focused ? "text" : "textSecondary"}
                style={{ fontSize: 10 }}
              >
                {props.children}
              </Text>
            );
          },
          tabBarStyle: { position: "absolute", bottom: 0 },
          tabBarBackground: () => (
            <AccessibleBlurView
              intensity={60}
              tint="light"
              fallbackBackgroundColor={fallbackBackgroundColor}
              style={{
                height: "100%",
                backgroundColor,
              }}
            ></AccessibleBlurView>
          ),
          tabBarButton: (props) => {
            //@ts-ignore
            return <AnimatedTouchableScale {...props} pressedScale={0.95} />;
          },
        }}
      >
        <Tabs.Screen
          name="home"
          options={{
            title: "Home",
            headerShown: false,
            tabBarIcon: ({ focused }) => (
              <HomeIcon style={{ opacity: focused ? 1 : 0.3 }} />
            ),
          }}
          listeners={{
            tabPress: () => {
              Haptics.selectionAsync();
            },
          }}
        />
        <Tabs.Screen
          name="earn"
          options={{
            title: "Earn",
            headerShown: false,
            tabBarIcon: ({ focused }) => (
              <DeFiIcon style={{ opacity: focused ? 1 : 0.3 }} />
            ),
          }}
          listeners={{
            tabPress: async (e) => {
              Haptics.selectionAsync();
            },
          }}
        />
        <Tabs.Screen
          name="security"
          options={{
            title: "Security",
            headerShown: false,
            tabBarIcon: SecurityTabView,
          }}
          listeners={{
            tabPress: () => {
              Haptics.selectionAsync();
            },
          }}
        />
        <Tabs.Screen
          name="settings"
          options={{
            title: "Settings",
            headerShown: false,
            tabBarIcon: ({ focused }) => (
              <SettingsIcon style={{ opacity: focused ? 1 : 0.3 }} />
            ),
          }}
          listeners={{
            tabPress: () => {
              Haptics.selectionAsync();
            },
          }}
        />
      </Tabs>
    </FuseSuspense>
  );
}

function SecurityTabView({ focused }: { focused: boolean }) {
  return (
    <View>
      <SecurityIcon style={{ opacity: focused ? 1 : 0.3 }} />
      <WarningIcon />
    </View>
  );
}

function WarningIcon() {
  const { showSecurityWarning } = useLazySecurityWarning();

  const { wallet } = useActiveWallet();
  const isMagicLinkMigrating = useIsMagicLinkKeyMigrating();
  const hasMagicLinkRecoveryKey =
    wallet.keys.recoveryKeys !== null &&
    wallet.keys.recoveryKeys.some((key) => key.recoveryKeyType === "email");

  const showMagicLinkWarning = hasMagicLinkRecoveryKey && !isMagicLinkMigrating;

  if (!showSecurityWarning && !showMagicLinkWarning) {
    return null;
  }

  return (
    <View style={{ position: "absolute", right: -2 }}>
      <IconWrapper size={12} backgroundColorToken={"background"}>
        <View
          background={"red"}
          style={{
            width: 9,
            height: 9,
            borderRadius: 999,
          }}
        />
      </IconWrapper>
    </View>
  );
}
