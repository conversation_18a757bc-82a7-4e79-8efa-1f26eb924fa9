import fuseLogoEbony from "~assets/images/fuse-logo-ebony.png";
import { Suspense, useRef, useState } from "react";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { WalletInfo } from "~/components/WalletInfo";
import { Text, useColor, View } from "~/components/Themed";
import { Image } from "expo-image";
import * as Application from "expo-application";
import * as Haptics from "expo-haptics";
import { openURL } from "expo-linking";
import { UserPreferencesKey, useUserPreference } from "~/state/userPreferences";
import { BottomModalImperativeMethods } from "~/components/BottomModal";
import { EditWalletRow } from "~/components/settings/EditWalletModal";
import { ScrollView } from "react-native";
import { isEmbeddedLaunch, updateId } from "expo-updates";
import { router } from "expo-router";
import { AccessibleBlurView } from "~/components/AccessibleBlurView";
import { NotificationsRow } from "~/components/settings/NotificationsSettingsModal";
import {
  Section,
  SectionHeading,
  SettingRow,
} from "~/components/settings/SettingRow";
import { DeleteWalletModal } from "~/components/settings/DeleteWalletModal";
import { useIsWalletOffline } from "~/hooks/useAssertWalletState";
import { FuseErrorBoundary } from "~/components/errors/FuseErrorBoundary";
import { FusePlusMembershipBanner } from "~/components/settings/FusePlusMembershipBanner";

export default function Settings() {
  const insets = useSafeAreaInsets();
  const backgroundColor = useColor("backgroundTabBar");
  const fallbackBackgroundColor = useColor("background");

  const explorerSettingModalRef = useRef<BottomModalImperativeMethods>(null);
  const [explorer, setExplorer] = useUserPreference(
    UserPreferencesKey.Explorer,
    undefined
  );
  const [transientExplorerValue, setTransientExplorerValue] = useState(
    explorer ?? "solana-explorer"
  );

  const deleteWalletModalRef = useRef<BottomModalImperativeMethods>(null);

  const isWalletOffline = useIsWalletOffline();

  return (
    <View
      style={{
        flex: 1,
        gap: 20,
      }}
    >
      <View
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1,
          overflow: "hidden",
        }}
      >
        <AccessibleBlurView
          intensity={60}
          tint="light"
          fallbackBackgroundColor={fallbackBackgroundColor}
          style={{ height: insets.top, backgroundColor, paddingBottom: 12 }}
        />
      </View>

      <ScrollView
        contentContainerStyle={{
          paddingTop: insets.top,
          paddingHorizontal: 32,
        }}
        showsVerticalScrollIndicator={false}
      >
        <View gap={32} style={{ marginBottom: 32 }}>
          <WalletInfo variant="vertical" />
          <FusePlusMembershipBanner />
        </View>

        <SectionHeading>General</SectionHeading>
        <Section>
          <EditWalletRow />

          <NotificationsRow />

          <SettingRow
            iconName="person"
            label="Address book"
            onPress={async () => {
              Haptics.selectionAsync();
              router.push("/unlocked/settings/address-book");
            }}
          />

          <SettingRow
            iconName="photo"
            label="NFTs"
            onPress={() => {
              Haptics.selectionAsync();
              router.push("/unlocked/settings/nfts");
            }}
          />
        </Section>
        <SectionHeading>About</SectionHeading>
        <Section>
          <SettingRow
            iconName="ellipsis.message"
            label="Contact support"
            onPress={() => {
              Haptics.selectionAsync();
              openURL(`https://t.me/+X2BzTAWsMN01ZTNk`);
            }}
          />
          <SettingRow
            iconName="twitter"
            label="Follow @fusewallet"
            onPress={() => {
              Haptics.selectionAsync();
              openURL(`https://x.com/fusewallet`);
            }}
          />
          {/*<SettingRow iconName="heart" label="Give feedback" />*/}
          <SettingRow
            iconName="checkmark.shield"
            label="Terms & Conditions"
            onPress={() => {
              Haptics.selectionAsync();
              openURL(`https://fusewallet.com/legal/terms-of-service`);
            }}
          />
          <SettingRow
            iconName="trash"
            color={"systemRed"}
            label="Delete Wallet"
            onPress={async () => {
              Haptics.selectionAsync();

              if (await isWalletOffline()) {
                return;
              }

              deleteWalletModalRef.current?.present();
            }}
          />
          <FuseErrorBoundary FallbackComponent={() => null}>
            <Suspense>
              <DeleteWalletModal modalRef={deleteWalletModalRef} />
            </Suspense>
          </FuseErrorBoundary>
        </Section>
        <View style={{ alignItems: "center", gap: 16, marginTop: 8 }}>
          <Image source={fuseLogoEbony} style={{ width: 50, height: 50 }} />
          <AppVersion />
        </View>

        {/* Bottom spacer */}
        <View style={{ height: insets.bottom + 62 }} />
      </ScrollView>
    </View>
  );
}

export function AppVersion() {
  return (
    <View style={{ gap: 4, alignItems: "center" }}>
      <Text
        variant="semibold"
        style={{ fontSize: 13 }}
        colorToken="textSecondary"
      >
        Version {Application.nativeApplicationVersion} (
        {Application.nativeBuildVersion})
      </Text>
      {!isEmbeddedLaunch && (
        <Text
          variant="semibold"
          style={{ fontSize: 12 }}
          colorToken="textSecondary"
        >
          {updateId}
        </Text>
      )}
    </View>
  );
}
