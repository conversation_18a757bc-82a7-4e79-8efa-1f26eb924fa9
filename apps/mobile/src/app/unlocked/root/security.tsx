import imageFaceId from "~assets/images/security-face-id.png";
import imageCloud from "~assets/images/security-cloud.png";
import recoveryOptions from "~assets/images/recovery-options.png";
import imageLedger from "~assets/images/ledger.png";
import { Text, useColor, View } from "~/components/Themed";
import { WalletInfo } from "~/components/WalletInfo";
import { useActiveWallet } from "~/state/wallet";
import { Image } from "expo-image";
import { ScrollView } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { MenuView } from "@react-native-menu/menu";
import { Icon } from "~/components/Icon";
import { router } from "expo-router";
import { CloudKey, RecoveryKey } from "~/services/wallets";
import { TouchableScale } from "~/components/TouchableScale";
import { ReactNode, Suspense, useRef } from "react";
import { Address, AddressZ } from "@squads/models/solana";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import { Grid, Row } from "~/components/Grid";
import { H4 } from "~/components/typography/H4";
import { ChangeRecoveryRouteParams } from "~/app/unlocked/change-recovery/_layout";
import * as Haptics from "expo-haptics";
import { DeleteRecoveryKeyModal } from "~/components/security/DeleteRecoveryKeyModal";
import { InfoRecoveryKeyModal } from "~/components/security/InfoRecoveryKeyModal";
import { DeviceKeyInfoModal } from "~/components/security/DeviceKeyInfoModal";
import { CloudKeyInfoModal } from "~/components/security/CloudKeyInfoModal";
import { BottomModalImperativeMethods } from "~/components/BottomModal";
import { useLazyWalletRecoveryState } from "~/state/walletRecovery";
import invariant from "invariant";
import { Cancel2FAChangeModal } from "~/components/security/Cancel2FAChangeModal";
import { showCloudKeyUnavailableModal } from "~/components/CloudKeyUnavailableModal";
import { StatusView } from "~/components/StatusView";
import { useMaybeCloudKey } from "~/state/cloudKey";
import { useLock } from "~/hooks/useLock";
import { AddSpendingLimitTile } from "~/components/security/AddSpendingLimitTile";
import { useSpendingLimits } from "~/state/spendingLimits";
import {
  SpendingLimitTile,
  SpendingLimitTileSkeleton,
} from "~/components/security/SpendingLimitTile";
import { mintsSorting } from "~/constants/tokens";
import { P2 } from "~/components/typography/P2";
import { SpendingLimitInfoModal } from "~/components/security/SpendingLimitInfoModal";
import { P3 } from "~/components/typography/P3";
import { SpendingLimitUnavailableModal } from "~/components/security/SpendingLimitUnavailableModal";
import { getAddressExplorerLink } from "~/utils/explorer";
import { openURL } from "expo-linking";
import { useIsWalletInactive } from "~/hooks/useAssertWalletState";
import { AccessibleBlurView } from "~/components/AccessibleBlurView";
import { ActivityButton } from "~/components/ActivityButton";
import { showTwoFAKeyWarningModal } from "~/components/security/2FAKeyWarningModal";
import { AddRecoveryKeyHint } from "~/components/security/AddRecoveryKeyHint";
import { UpdateEmailRecoveryKeyHint } from "~/components/security/UpdateEmailRecoveryKeyHint";
import { FuseErrorBoundary } from "~/components/errors/FuseErrorBoundary";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";
import { hapticOpenBottomTray, hapticSelect } from "~/utils/haptics";

export default function SecurityScreen() {
  const insets = useSafeAreaInsets();
  const backgroundColor = useColor("backgroundTabBar");
  const fallbackBackgroundColor = useColor("background");

  return (
    <View style={{ flex: 1 }}>
      <View
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1,
          overflow: "hidden",
        }}
      >
        <AccessibleBlurView
          intensity={60}
          tint="light"
          fallbackBackgroundColor={fallbackBackgroundColor}
          style={{
            gap: 20,
            backgroundColor,
            paddingHorizontal: 20,
            paddingBottom: 12,
            paddingTop: insets.top + 4,
          }}
        >
          <Row justify="space-between">
            <WalletInfo />
            <ActivityButton />
          </Row>
        </AccessibleBlurView>
      </View>
      <View
        style={{
          flex: 1,
          paddingHorizontal: 20,
          paddingBottom: 20,
          gap: 20,
        }}
      >
        <ScrollView
          contentContainerStyle={{ paddingTop: insets.top + 76 }}
          showsVerticalScrollIndicator={false}
        >
          <SecurityMain />
          <View style={{ height: insets.bottom + 62 }} />
        </ScrollView>
      </View>
    </View>
  );
}

function SecurityMain() {
  const { wallet, deviceKey } = useActiveWallet();

  return (
    <View style={{ gap: 20 }}>
      <Row gap={20}>
        <View style={{ flex: 1 }}>
          <DeviceKeyDetails address={deviceKey} />
        </View>
        <View style={{ flex: 1 }}>
          <CloudKeyDetails />
        </View>
      </Row>
      <Text variant="semibold" style={{ fontSize: 15 }}>
        Recovery
      </Text>

      {wallet.keys.recoveryKeys !== null ? (
        <>
          <RecoveryKeysGrid recoveryKeys={wallet.keys.recoveryKeys} />
          <UpdateEmailRecoveryKeyHint recoveryKeys={wallet.keys.recoveryKeys} />
          <SpendingLimitsSection />
        </>
      ) : (
        <>
          <AddRecoveryKey variant={"long"} />
          <AddRecoveryKeyHint />
        </>
      )}
    </View>
  );
}

function RecoveryKeysGrid({ recoveryKeys }: { recoveryKeys: RecoveryKey[] }) {
  const sortedRecoveryKeys = recoveryKeys.sort((a, b) =>
    RecoveryKey.getValue(a) > RecoveryKey.getValue(b) ? 1 : -1
  );

  return (
    <Grid
      gap={20}
      columns={2}
      items={[
        ...sortedRecoveryKeys,
        ...(sortedRecoveryKeys.length < 3 ? ["addRecoveryKey" as const] : []),
      ]}
      renderItem={(item) =>
        item === "addRecoveryKey" ? (
          <View style={{ flex: 1 }}>
            <AddRecoveryKey />
          </View>
        ) : (
          <View style={{ flex: 1 }}>
            <RecoveryKeyDetails key={item!.address} recoveryKey={item!} />
          </View>
        )
      }
    />
  );
}

function SpendingLimitsSection() {
  const { wallet } = useActiveWallet();
  const hasICloudKey = wallet.keys.cloudKey.details.type === "iCloud";

  return (
    <View style={{ gap: 20 }}>
      <SpendingLimitsHeader showInfo={hasICloudKey} />

      <View gap={10}>
        <FuseErrorBoundary FallbackComponent={SpendingLimitTileSkeleton}>
          <Suspense fallback={<SpendingLimitTileSkeleton />}>
            <SpendingLimitsList disabled={hasICloudKey} />
            {!hasICloudKey && <AddSpendingLimitTile />}
          </Suspense>
        </FuseErrorBoundary>
      </View>
    </View>
  );
}

function SpendingLimitsHeader({ showInfo }: { showInfo?: boolean }) {
  const infoModalRef = useRef<BottomModalImperativeMethods>(null);

  return (
    <>
      <Row justify="space-between">
        <P2>Spending Limits</P2>

        {showInfo && (
          <TouchableScale
            hitSlop={8}
            onPress={() => {
              Haptics.selectionAsync();
              infoModalRef.current?.present();
            }}
          >
            <Row gap={4}>
              <P3 colorToken={"textSecondary"}>Set up Ledger 2FA</P3>
              <Icon
                name={"info.circle"}
                colorToken={"textSecondary"}
                size={12}
                rectSize={18}
              />
            </Row>
          </TouchableScale>
        )}
      </Row>
      <SpendingLimitInfoModal modalRef={infoModalRef} />
    </>
  );
}

function SpendingLimitsList({ disabled }: { disabled: boolean }) {
  const { wallet } = useActiveWallet();

  const walletKey = wallet.walletKey;
  const vaultKey = wallet.defaultVault;
  const deviceKey = wallet.keys.deviceKey.address;

  const { spendingLimits } = useSpendingLimits({ walletKey, vaultKey });

  const modalRef = useRef<BottomModalImperativeMethods>(null);

  return (
    <>
      {spendingLimits.sort(mintsSorting()).map((spendingLimit) => (
        <SpendingLimitTile
          onPress={disabled ? () => modalRef.current?.present() : undefined}
          key={spendingLimit.address}
          deviceKey={deviceKey}
          spendingLimit={spendingLimit}
        />
      ))}
      <SpendingLimitUnavailableModal modalRef={modalRef} />
    </>
  );
}

function DeviceKeyDetails({ address }: { address: Address }) {
  const infoModalRef = useRef<BottomModalImperativeMethods>(null);

  const withLock = useLock();

  return (
    <>
      <CompactKeyDetails
        title={"Device"}
        status={<StatusView variant={"success"} title={"Active"} />}
        image={"device"}
        keyValue={address}
        titleAction={
          <MenuView
            themeVariant="light"
            onPressAction={withLock(async ({ nativeEvent }) => {
              switch (nativeEvent.event) {
                case "more-info": {
                  Haptics.selectionAsync();
                  infoModalRef.current?.present();
                  break;
                }

                case "see-in-explorer": {
                  openURL(getAddressExplorerLink(address));
                  break;
                }
              }
            })}
            actions={[
              {
                id: "more-info",
                title: "More info",
                image: "questionmark.circle",
              },
              {
                id: "see-in-explorer",
                title: "Explorer",
                image: "safari",
              },
            ]}
            shouldOpenOnLongPress={false}
          >
            <TouchableScale onPress={() => Haptics.selectionAsync()}>
              <Icon
                name={"ellipsis"}
                size={12}
                rectSize={20}
                colorToken={"textSecondary"}
              />
            </TouchableScale>
          </MenuView>
        }
      />
      <DeviceKeyInfoModal modalRef={infoModalRef} />
    </>
  );
}

function CloudKeyDetails() {
  const { wallet } = useActiveWallet();
  const walletKey = wallet.walletKey;
  const hasRecoveryKeys = wallet.keys.recoveryKeys !== null;

  const maybeCloudKey = useMaybeCloudKey();

  const { data: activeRecovery } = useLazyWalletRecoveryState({
    walletKey: walletKey,
  });

  const hasActiveCloudKeyRecovery =
    activeRecovery === undefined
      ? undefined
      : (activeRecovery?.newKey?.keyType === "cloudKey" &&
          activeRecovery?.status === "active") ||
        activeRecovery?.status === "approved";

  const isWalletInactive = useIsWalletInactive();
  const isWalletInactiveNoSecurityCheck = useIsWalletInactive({
    simplifiedSecurityCheck: false,
  });

  const infoModalRef = useRef<BottomModalImperativeMethods>(null);
  const cancel2FAChangeModalRef = useRef<BottomModalImperativeMethods>(null);
  const cloudKey = wallet.keys.cloudKey;

  const hasICloudKey = wallet.keys.cloudKey.details.type === "iCloud";

  const withLock = useLock();

  return (
    <>
      <CompactKeyDetails
        title={CloudKey.getLabel(cloudKey)}
        status={
          hasICloudKey && maybeCloudKey === null ? (
            <TouchableScale
              hitSlop={4}
              onPress={() => showCloudKeyUnavailableModal()}
            >
              <StatusView variant={"danger"} title={"Inactive"} />
            </TouchableScale>
          ) : (
            <StatusView variant={"success"} title={"Active"} />
          )
        }
        image={hasICloudKey ? "cloud" : "ledger"}
        titleAction={
          <MenuView
            themeVariant="light"
            onPressAction={withLock(async ({ nativeEvent }) => {
              switch (nativeEvent.event) {
                case "second-key-switch":
                  if (await isWalletInactiveNoSecurityCheck()) {
                    return;
                  }

                  if (wallet.keys.recoveryKeys === null) {
                    showTwoFAKeyWarningModal();
                    return;
                  }

                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
                  if (hasICloudKey) {
                    router.push("/unlocked/change-cloud-key/options");
                  } else {
                    router.push(
                      "/unlocked/change-cloud-key/cloud/instructions"
                    );
                  }
                  break;

                case "more-info":
                  Haptics.selectionAsync();
                  infoModalRef.current?.present();
                  break;

                case "2fa-change":
                  Haptics.selectionAsync();

                  if (await isWalletInactive()) {
                    return;
                  }

                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
                  router.push({
                    pathname: "/unlocked/change-cloud-key/options",
                    params: {
                      change: "true",
                    },
                  });
                  break;

                case "2fa-change-complete":
                  invariant(activeRecovery, "activeRecovery should be set");
                  Haptics.selectionAsync();

                  if (await isWalletInactive()) {
                    return;
                  }

                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
                  const proposalStatus = activeRecovery?.status;
                  if (activeRecovery && proposalStatus == "approved") {
                    router.push({
                      pathname:
                        "/unlocked/change-cloud-key/recovery/confirm-recovery",
                    });
                  } else {
                    router.push({
                      pathname:
                        "/unlocked/change-cloud-key/recovery/resume-recovery",
                    });
                  }
                  break;

                case "2fa-change-cancel":
                  invariant(activeRecovery, "activeRecovery should be set");

                  Haptics.selectionAsync();

                  if (await isWalletInactive()) {
                    return;
                  }

                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
                  cancel2FAChangeModalRef.current?.present();

                  break;

                case "see-in-explorer": {
                  openURL(getAddressExplorerLink(cloudKey.address));
                  break;
                }

                case "export-cloud-key": {
                  Haptics.selectionAsync();
                  router.push("/unlocked/security/export-cloud-key");
                  break;
                }
              }
            })}
            actions={[
              ...(hasActiveCloudKeyRecovery === undefined
                ? //no actions while loading
                  []
                : hasActiveCloudKeyRecovery
                  ? [
                      {
                        id: "2fa-change-complete",
                        title: "Complete 2FA change",
                        image: "checkmark.gobackward",
                        attributes: {
                          hidden: hasICloudKey,
                        },
                      },
                      {
                        id: "2fa-change-cancel",
                        title: "Cancel 2FA change",
                        image: "xmark.circle",
                        attributes: {
                          destructive: true,
                          hidden: hasICloudKey,
                        },
                      },
                    ]
                  : [
                      {
                        id: "second-key-switch",
                        title: hasICloudKey
                          ? "Change 2FA Key"
                          : "Change to Cloud Key",
                        image: hasICloudKey
                          ? "key.radiowaves.forward"
                          : "key.icloud",
                      },
                      {
                        id: "2fa-change",
                        title: "Change 2FA Key",
                        image: "arrow.triangle.2.circlepath",
                        attributes: {
                          hidden: hasICloudKey,
                        },
                      },
                    ]),
              {
                id: "more-info",
                title: "More info",
                image: "questionmark.circle",
              },
              {
                id: "see-in-explorer",
                title: "Explorer",
                image: "safari",
              },
              {
                id: "export-cloud-key",
                title: "Back up key",
                image: "key.viewfinder",
                attributes: {
                  hidden: !hasICloudKey,
                  disabled: maybeCloudKey === null,
                },
              },
            ]}
            shouldOpenOnLongPress={false}
          >
            <TouchableScale onPress={() => Haptics.selectionAsync()}>
              <Icon
                name={"ellipsis"}
                size={12}
                rectSize={20}
                colorToken={"textSecondary"}
              />
            </TouchableScale>
          </MenuView>
        }
        keyValue={cloudKey.address}
      />
      <CloudKeyInfoModal
        keyType={wallet.keys.cloudKey.details.type}
        modalRef={infoModalRef}
      />
      {activeRecovery && activeRecovery.newKey.keyType === "cloudKey" && (
        <Cancel2FAChangeModal modalRef={cancel2FAChangeModalRef} />
      )}
    </>
  );
}

function AddRecoveryKey({
  variant = "square",
}: {
  variant?: "long" | "square";
}) {
  const isWalletInactive = useIsWalletInactive({
    simplifiedSecurityCheck: false,
  });
  const borderColor = useColor("border");
  const recoveryMoreInfoModalRef = useRef<BottomModalImperativeMethods>(null);
  const withLock = useLock();

  return (
    <>
      <AnimatedTouchableScale
        onPress={withLock(async () => {
          if (await isWalletInactive()) {
            return;
          }

          hapticOpenBottomTray();
          router.push("/unlocked/change-recovery/options");
        })}
      >
        <View
          style={{
            borderRadius: 16,
            borderWidth: 1,
            borderStyle: "dashed",
            borderColor,
            flex: 1,
            padding: 16,
            justifyContent: "space-between",
            height: variant === "square" ? 110 : undefined,
            flexDirection: variant === "long" ? "row" : undefined,
          }}
        >
          <Row
            justify={variant === "square" ? "space-between" : undefined}
            gap={8}
          >
            <P2 colorToken={"textSecondary"}>Add key</P2>
            <TouchableScale
              hitSlop={8}
              onPress={() => {
                recoveryMoreInfoModalRef.current?.present();
                hapticSelect();
              }}
            >
              <Icon
                name={"info.circle"}
                colorToken={"textSecondary"}
                size={12}
              />
            </TouchableScale>
          </Row>
          <Row style={{ justifyContent: "flex-end" }}>
            <Icon name={"plus"} colorToken={"textSecondary"} size={12} />
          </Row>
        </View>
      </AnimatedTouchableScale>
      <InfoRecoveryKeyModal modalRef={recoveryMoreInfoModalRef} />
    </>
  );
}

function RecoveryKeyDetails({ recoveryKey }: { recoveryKey: RecoveryKey }) {
  const { wallet } = useActiveWallet();
  const recoveryKeyValue = RecoveryKey.getValue(recoveryKey);

  const recoveryMoreInfoModalRef = useRef<BottomModalImperativeMethods>(null);
  const deleteRecoveryKeyModalRef = useRef<BottomModalImperativeMethods>(null);
  const withLock = useLock();
  const isWalletInactive = useIsWalletInactive();

  return (
    <>
      <CompactKeyDetails
        title={RecoveryKey.getLabel(recoveryKey)}
        status={<StatusView variant={"success"} title={"Active"} />}
        image={"recovery"}
        keyValue={recoveryKeyValue}
        titleAction={
          <MenuView
            themeVariant="light"
            onPressAction={withLock(async ({ nativeEvent }) => {
              Haptics.selectionAsync();
              switch (nativeEvent.event) {
                case "change-key":
                  if (await isWalletInactive()) {
                    return;
                  }

                  router.push({
                    pathname: "/unlocked/change-recovery/options",
                    params: {
                      oldKey: recoveryKey.address,
                    } satisfies ChangeRecoveryRouteParams,
                  });
                  break;

                case "more-info":
                  recoveryMoreInfoModalRef.current?.present();
                  break;

                case "delete-key": {
                  if (await isWalletInactive()) {
                    return;
                  }

                  deleteRecoveryKeyModalRef.current?.present();
                  break;
                }

                case "see-in-explorer": {
                  openURL(getAddressExplorerLink(recoveryKey.address));
                  break;
                }
              }
            })}
            actions={[
              {
                id: "change-key",
                title: "Change key",
                image: "arrow.triangle.2.circlepath",
              },
              {
                id: "more-info",
                title: "More info",
                image: "questionmark.circle",
              },
              {
                id: "see-in-explorer",
                title: "Explorer",
                image: "safari",
              },
              {
                id: "delete-key",
                title: "Delete key",
                image: "trash",
                attributes: {
                  destructive: true,
                  hidden:
                    wallet.keys.recoveryKeys !== null &&
                    wallet.keys.recoveryKeys.length === 1,
                },
              },
            ]}
            shouldOpenOnLongPress={false}
          >
            <TouchableScale onPress={() => Haptics.selectionAsync()}>
              <Icon
                name={"ellipsis"}
                size={12}
                rectSize={20}
                colorToken={"textSecondary"}
              />
            </TouchableScale>
          </MenuView>
        }
      />
      <InfoRecoveryKeyModal modalRef={recoveryMoreInfoModalRef} />

      <DeleteRecoveryKeyModal
        modalRef={deleteRecoveryKeyModalRef}
        recoveryKey={recoveryKey}
      />
    </>
  );
}

export function CompactKeyDetails({
  image,
  keyValue,
  title,
  titleAction,
  status,
}: {
  image: "device" | "cloud" | "ledger" | "recovery";
  title: string;
  keyValue: Address | string;
  titleAction?: ReactNode;
  status?: ReactNode;
}) {
  return (
    <View
      background={"backgroundSecondary"}
      style={{
        borderRadius: 16,
        overflow: "hidden",
        height: 110,
      }}
    >
      <View style={{ padding: 16, gap: 8 }}>
        <View style={{ gap: 4 }}>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <H4>{title}</H4>

            {titleAction}
          </View>
        </View>
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
          }}
        >
          <View style={{ gap: 16, paddingRight: 20 }}>
            <KeyLabel label={keyValue} />
            {status}
          </View>
        </View>
      </View>
      {image === "device" && (
        <Image
          source={imageFaceId}
          style={{
            position: "absolute",
            width: 51,
            height: 51,
            bottom: 2,
            right: 2,
          }}
        />
      )}
      {image === "cloud" && (
        <Image
          source={imageCloud}
          style={{
            position: "absolute",
            width: 51,
            height: 51,
            bottom: 0,
            right: 8,
          }}
        />
      )}
      {image === "ledger" && (
        <Image
          source={imageLedger}
          style={{
            position: "absolute",
            width: 51,
            height: 51,
            bottom: 0,
            right: 0,
          }}
        />
      )}
      {image === "recovery" && (
        <Image
          source={recoveryOptions}
          style={{
            position: "absolute",
            width: 51,
            height: 51,
            bottom: -8,
            right: 0,
          }}
        />
      )}
    </View>
  );
}

function KeyLabel({ label }: { label: Address | string }) {
  const parsedAddress = AddressZ.safeParse(label);

  if (!parsedAddress.success) {
    return (
      <Text
        variant="medium"
        colorToken={"textSecondary"}
        style={{ fontSize: 15 }}
        numberOfLines={1}
      >
        {label}
      </Text>
    );
  }

  return <AddressView address={parsedAddress.data} />;
}

function AddressView({ address }: { address: Address }) {
  return (
    <View style={{ flexDirection: "row", alignItems: "center", gap: 8 }}>
      <Text
        variant="medium"
        colorToken={"textSecondary"}
        style={{ fontSize: 15 }}
      >
        {abbreviateAddress(address, 4)}
      </Text>
    </View>
  );
}
