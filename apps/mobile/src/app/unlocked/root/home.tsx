import { useColor, View } from "~/components/Themed";
import { useActiveWallet } from "~/state/wallet";
import { PropsWithChildren, useEffect, useRef } from "react";
import { WalletInfo } from "~/components/WalletInfo";
import { Dashboard } from "~/components/Dashboard";
import { Coins } from "~/components/Coins";
import { useSharedValue } from "react-native-reanimated";
import { NavTabs } from "~/components/NavTabs";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { PagerImperativeMethods, PagerView } from "~/components/PagerView";
import { AccessibleBlurView } from "~/components/AccessibleBlurView";
import { Row } from "~/components/Grid";
import { ActivityButton } from "~/components/ActivityButton";
import { SponsoredActionInfoModal } from "~/components/modals/SponsoredActionInfoModal";
import { BottomModalImperativeMethods } from "~/components/BottomModal";
import {
  membershipWelcomeSeen,
  navigateToMembershipWelcomeScreen,
  useSubscription,
} from "~/state/subscription";
import { Settings } from "react-native";
import { sleep } from "~/utils/promise";
import { hapticOpenBottomTray, hapticSuccess } from "~/utils/haptics";

const HOME_HEADER_HEIGHT = 165;

export default function HomeScreen() {
  return (
    <>
      <SponsorshipPromo />
      <HomeContent />
    </>
  );
}

function SponsorshipPromo() {
  const subscriptionQuery = useSubscription();
  const modalRef = useRef<BottomModalImperativeMethods>(null);

  useEffect(() => {
    const promoKey = "SPONSORSHIP_PROMO_SEEN";

    if (!subscriptionQuery.data) {
      return;
    }

    sleep(1_000).then(() => {
      const subscription = subscriptionQuery.data;
      const hasMembership = subscription.status === "active";

      const promoSeen = Settings.get(promoKey);

      if (hasMembership) {
        if (!membershipWelcomeSeen()) {
          hapticSuccess();
          navigateToMembershipWelcomeScreen();
        }
      } else {
        if (!promoSeen) {
          if (modalRef.current) {
            hapticOpenBottomTray();
            modalRef.current.present();
            Settings.set({ [promoKey]: true });
          }
        }
      }
    });
  }, [subscriptionQuery.data]);

  return (
    <SponsoredActionInfoModal
      modalRef={modalRef}
      actionsLeft={5}
      actionsUsed={0}
    />
  );
}

function HomeContent() {
  const { wallet } = useActiveWallet();

  const pagerViewRef = useRef<PagerImperativeMethods>(null);
  const initialPageIndex = 0;
  const pagePosition = useSharedValue(initialPageIndex);

  const insets = useSafeAreaInsets();

  const backgroundColor = useColor("backgroundTabBar");
  const fallbackBackgroundColor = useColor("background");

  return (
    <View style={{ flex: 1 }}>
      <View
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1,
          overflow: "hidden",
        }}
      >
        <AccessibleBlurView
          intensity={60}
          tint="light"
          fallbackBackgroundColor={fallbackBackgroundColor}
          style={{
            gap: 18,
            backgroundColor,
            paddingHorizontal: 20,
            paddingBottom: 12,
            paddingTop: insets.top + 4,
          }}
        >
          <Row justify="space-between">
            <WalletInfo />
            <ActivityButton />
          </Row>
          <NavTabs
            pagePosition={pagePosition}
            onSelectView={(index) => pagerViewRef.current?.setPage(index)}
          />
        </AccessibleBlurView>
      </View>

      <PagerView
        pagerRef={pagerViewRef}
        style={{ flex: 1 }}
        pagePosition={pagePosition}
        initialPageIndex={initialPageIndex}
      >
        <Page key={0}>
          <Dashboard wallet={wallet} />
        </Page>
        <Page key={1} xPadding>
          <Coins wallet={wallet} />
        </Page>
      </PagerView>
    </View>
  );
}

function Page({
  children,
  xPadding,
}: PropsWithChildren<{ xPadding?: boolean }>) {
  return (
    <View
      style={{
        flex: 1,
        paddingTop: HOME_HEADER_HEIGHT,
        paddingHorizontal: xPadding ? 20 : 0,
      }}
    >
      {children}
    </View>
  );
}
