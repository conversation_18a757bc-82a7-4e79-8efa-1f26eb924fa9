import { Stack } from "expo-router";
import { DURATION_FAST } from "~/constants/animations";
import { ModalHeader } from "~/components/ModalHeader";

export default function StakeLayout() {
  return (
    <Stack
      screenOptions={{
        animation: "simple_push",
        animationDuration: DURATION_FAST,
        headerBackButtonMenuEnabled: false,
        headerShadowVisible: false,
        statusBarStyle: "inverted",
        header: ModalHeader,
      }}
    >
      <Stack.Screen name={"deposit"} options={{ title: "Stake SOL" }} />
    </Stack>
  );
}
