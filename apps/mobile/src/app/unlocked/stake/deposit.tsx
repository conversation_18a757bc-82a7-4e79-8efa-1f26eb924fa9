import { SafeAreaView, Text, View } from "~/components/Themed";
import { useGlobalSearchParams, useNavigation } from "expo-router";
import { Button } from "~/components/Button";
import { useBalance, useSolBalance } from "~/state/balances";
import { useActiveWallet } from "~/state/wallet";
import invariant from "invariant";
import { z } from "zod";
import { Address } from "@squads/models/solana";
import { useFormik } from "formik";
import { toFormikValidationSchema } from "zod-formik-adapter";
import { AmountZ, getMaxAvailableAmount } from "~/components/AmountDisplay";
import { NumPad } from "~/components/NumPad";
import * as Haptics from "expo-haptics";
import {
  AmountDisplay,
  SwapRowLabel,
  SwapSecondaryRow,
  SwapTokenRow,
} from "~/app/unlocked/swap/main";
import { formatTokenAmount, formatUsdValue } from "@squads/utils/numberFormats";
import { Action, prepareAction } from "~/services/wallets";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { useToast } from "~/components/Toaster";
import { useTokenMetadata } from "~/hooks/useTokenMetadata";
import { CoinLogo } from "~/components/CoinLogo";
import { mints } from "~/constants/tokens";
import { SQUADS_VALIDATOR } from "~/services/nativeStake";
import { Suspense } from "react";
import { Flex, Row } from "~/components/Grid";
import { useValidators } from "~/state/nativeStake";
import { ValidatorIcon } from "~/components/icons/ValidatorIcon";
import { P3 } from "~/components/typography/P3";
import { H4 } from "~/components/typography/H4";
import { useFuseSolApy, useLstPrice } from "~/state/liquidStake";
import { FUSE_SOL_MINT } from "~/services/liquidStake";
import { tokenUsdValue } from "~/utils/tokens";
import { ContentSkeleton } from "~/components/Skeleton";
import { LAMPORTS_PER_SOL } from "@solana/web3.js";
import { sendTransactionWithTrackingStatus } from "~/utils/sendTransactionWithTracking";
import { MaxButton } from "~/components/swap/MaxButton";
import { FuseErrorBoundary } from "~/components/errors/FuseErrorBoundary";

const MIN_STAKE_AMOUNT = 0.02;

const RouteParamsZ = z.object({
  stakeType: z.union([z.literal("native"), z.literal("liquid")]),
});

export type DepositStakeRouteParams = z.infer<typeof RouteParamsZ>;

export default function StakeSol() {
  const { stakeType } = RouteParamsZ.parse(useGlobalSearchParams());
  const navigation = useNavigation();

  const { wallet } = useActiveWallet();
  const { toast } = useToast();

  const balance = useBalance({
    mint: null,
    vault: wallet.defaultVault,
  });
  invariant(balance, "Sol balance not found");

  const maxAmount = getMaxAvailableAmount(balance);

  const formik = useFormik({
    initialValues: { amount: "" },

    validationSchema: toFormikValidationSchema(
      z.object({ amount: AmountZ(maxAmount, MIN_STAKE_AMOUNT) })
    ),

    async onSubmit(values) {
      Haptics.selectionAsync();
      const amount = Number(values.amount);
      const decimalsAmount = Math.round(amount * 10 ** balance.decimals);

      const actionRequest: Action =
        stakeType === "native"
          ? ({
              type: "stake",
              details: {
                type: "deposit",
                vaultIndex: 0,
                lamports: decimalsAmount,
                voteKey: SQUADS_VALIDATOR,
              },
            } as const)
          : stakeType === "liquid"
            ? ({
                type: "liquidStakeDeposit",
                vaultIndex: 0,
                lamports: decimalsAmount,
              } as const)
            : (stakeType satisfies never);

      const action = await prepareAction(actionRequest).catch((e) => {
        toast.error("Failed to simulate transaction", {
          id: "tx-simulation-failed",
        });
        throw e;
      });

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );

      navigation.getParent()?.goBack();

      await sendTransactionWithTrackingStatus({
        walletKey: wallet.walletKey,
        transaction: { type: "action", signedTransactions },
        confirmationFailureMessage: `Stake failed`,
      });
    },
  });

  const amountNumber = formik.values.amount
    ? Number(formik.values.amount)
    : null;

  const inputUsdValue =
    amountNumber && balance
      ? formatUsdValue((balance.usdcPrice ?? 0) * amountNumber)
      : null;

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View
        style={{
          justifyContent: "space-between",
          flex: 1,
          padding: 20,
          gap: 24,
        }}
      >
        <Flex style={{ justifyContent: "space-between" }}>
          <View style={{ gap: 8 }}>
            <SwapRowLabel>You stake</SwapRowLabel>
            <SwapTokenRow>
              <AmountDisplay amount={formik.values.amount || null} />
              <TokenLabel mint={null} />
            </SwapTokenRow>
            <SwapSecondaryRow>
              <Flex>
                <Text
                  numberOfLines={1}
                  adjustsFontSizeToFit
                  variant="medium"
                  colorToken={"textSecondary"}
                >
                  {inputUsdValue ?? " "}
                </Text>
              </Flex>
              <MaxButton
                balance={balance}
                onMaxPressed={(amount) => {
                  formik.setFieldValue("amount", String(amount));
                }}
              />
            </SwapSecondaryRow>
          </View>
          {stakeType === "native" && (
            <FuseErrorBoundary FallbackComponent={() => null}>
              <Suspense>
                <ValidatorDetailsView validator={SQUADS_VALIDATOR} />
              </Suspense>
            </FuseErrorBoundary>
          )}
          {stakeType === "liquid" && (
            <FuseErrorBoundary FallbackComponent={() => null}>
              <Suspense fallback={<LiquidStakeDetailsSkeleton />}>
                <LiquidStakeDetails solAmount={Number(formik.values.amount)} />
              </Suspense>
            </FuseErrorBoundary>
          )}
        </Flex>

        <View style={{ gap: 20 }}>
          <NumPad
            value={formik.values.amount}
            onChange={formik.handleChange("amount")}
            decimals={balance.decimals}
          />
          <Button
            error={
              formik.errors.amount === "insufficientBalance"
                ? "Insufficient balance"
                : formik.errors.amount?.startsWith("minAmountExceeded")
                  ? `Minimum stake is ${MIN_STAKE_AMOUNT} SOL`
                  : undefined
            }
            loading={formik.isSubmitting}
            disabled={!formik.dirty || !formik.isValid}
            onPress={() => formik.handleSubmit()}
          >
            Stake
          </Button>
        </View>
      </View>
    </SafeAreaView>
  );
}

export function TokenLabel({ mint }: { mint: Address | null }) {
  const { symbol } = useTokenMetadata({ mint });
  return (
    <View
      background={"backgroundSecondary"}
      style={{
        flexDirection: "row",
        alignItems: "center",
        gap: 8,
        padding: 8,
        paddingHorizontal: 12,
        borderRadius: 32,
      }}
    >
      <CoinLogo size={24} mint={mint ?? "SOL"} />
      <Text style={{ fontSize: 18 }} variant="display-medium">
        {symbol}
      </Text>
    </View>
  );
}

function ValidatorDetailsView({ validator }: { validator: Address }) {
  const validators = useValidators();
  const squadsValidator = validators.find((v) => v.address === validator);

  if (!squadsValidator) {
    return null;
  }

  return (
    <Row justify={"space-between"} style={{}}>
      <Row gap={16}>
        <ValidatorIcon validator={validator} />
        <View style={{ gap: 4 }}>
          <Text variant={"semibold"} style={{ fontSize: 18 }}>
            {squadsValidator.name}
          </Text>
          <Text
            colorToken={"textSecondary"}
            variant={"semibold"}
            style={{ fontSize: 16 }}
          >
            {formatTokenAmount(squadsValidator.totalStake, "SOL")}
          </Text>
        </View>
      </Row>
      <Row>
        <View style={{ gap: 4 }}>
          <Text
            colorToken={"green"}
            variant={"semibold"}
            style={{ fontSize: 18 }}
          >
            {squadsValidator.apy}%
          </Text>
        </View>
      </Row>
    </Row>
  );
}

function LiquidStakeDetails({ solAmount }: { solAmount: number }) {
  const { wallet } = useActiveWallet();
  const solBalance = useSolBalance({ address: wallet.defaultVault });
  const fuseSolApy = useFuseSolApy();

  const { price: priceLamports } = useLstPrice({ mint: FUSE_SOL_MINT });
  const priceSol = priceLamports / 10 ** 9;

  const lstAmount = solAmount / priceSol;

  const usdValue = tokenUsdValue({
    amount: solAmount * LAMPORTS_PER_SOL,
    decimals: solBalance.solBalance.decimals,
    usdcPrice: solBalance.solBalance.usdcPrice,
  });

  return (
    <LiquidStakeDetailsView
      lstAmount={lstAmount}
      usdValue={usdValue}
      fuseSolApy={fuseSolApy}
    />
  );
}

function LiquidStakeDetailsSkeleton() {
  return <LiquidStakeDetailsView isLoading lstAmount={0} usdValue={0} />;
}

function LiquidStakeDetailsView({
  lstAmount,
  usdValue,
  isLoading = false,
  fuseSolApy = 0,
}: {
  isLoading?: boolean;
  lstAmount: number;
  usdValue: number;
  fuseSolApy?: number;
}) {
  return (
    <View gap={16}>
      <P3>You receive</P3>
      <Row justify={"space-between"} gap={12}>
        <Row gap={16}>
          <CoinLogo mint={mints.fuseSol} size={42} />
          <View gap={4}>
            <H4>Fuse Staked SOL</H4>
            <P3 colorToken={"green"}>{fuseSolApy.toFixed(2)}%</P3>
          </View>
        </Row>

        {isLoading ? (
          <ContentSkeleton>
            <View style={{ gap: 4 }}>
              <P3>1 FuseSOL</P3>
              <P3 colorToken={"textSecondary"}>$0.00</P3>
            </View>
          </ContentSkeleton>
        ) : (
          <View style={{ flex: 1, gap: 4, alignItems: "flex-end" }}>
            <P3 adjustsFontSizeToFit numberOfLines={1}>
              {formatTokenAmount(lstAmount, "fuseSOL")}
            </P3>
            <P3
              adjustsFontSizeToFit
              numberOfLines={1}
              colorToken={"textSecondary"}
            >
              {formatUsdValue(usdValue)}
            </P3>
          </View>
        )}
      </Row>
    </View>
  );
}
