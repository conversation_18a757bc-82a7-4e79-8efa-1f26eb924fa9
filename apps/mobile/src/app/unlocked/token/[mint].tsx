import { Text, View } from "~/components/Themed";
import { useActiveWallet } from "~/state/wallet";
import { useLocalSearchParams } from "expo-router";
import { Address, AddressZ } from "@squads/models/solana";
import { mints } from "~/constants/tokens";
import { useBalance, useSolBalance } from "~/state/balances";
import {
  useLazyHistoricalPrices,
  usePeriodTokenPrice,
  useSuspenseToken,
  useTokenDetails,
} from "~/state/tokens";
import { Flex, Row } from "~/components/Grid";
import invariant from "invariant";
import {
  abbreviateAmount,
  formatEpoch,
  formatTokenAmount,
  formatUsdValue,
} from "@squads/utils/numberFormats";
import { tokenUsdValue } from "~/utils/tokens";
import { LAMPORTS_PER_SOL } from "@solana/web3.js";
import { TouchableScale } from "~/components/TouchableScale";
import { ValidatorUnstakeModal } from "~/components/security/ValidatorUnstakeModal";
import { ReactNode, useEffect, useMemo, useRef, useState } from "react";
import { BottomModalImperativeMethods } from "~/components/BottomModal";
import { CoinLogo } from "~/components/CoinLogo";
import { MarinadeIcon } from "~/components/icons/MarinadeIcon";
import { ScrollView } from "react-native";
import { Period, TokenBalance } from "~/services/balances";
import { DateTime, Duration } from "luxon";
import {
  Chart,
  ChartPeriods,
  SelectedChartPoint,
} from "~/components/BalanceChart";
import { BottomInset } from "~/components/BottomInset";
import Animated, {
  FadeInDown,
  FadeOutDown,
  ReduceMotion,
  useAnimatedRef,
} from "react-native-reanimated";
import { DURATION_FAST } from "~/constants/animations";
import { SendButton } from "~/components/SendButton";
import { SwapButton } from "~/components/SwapButton";
import { z } from "zod";
import { ContentSkeleton, TextSkeleton } from "~/components/Skeleton";
import { StakeButton } from "~/components/StakeButton";
import { ListSeparator } from "~/components/ListSeparator";
import Ticker from "~/components/Ticker";
import { FuseErrorBoundary } from "~/components/errors/FuseErrorBoundary";
import { FuseSuspense } from "~/components/FuseSuspense";
import {
  InitializedStakeAccount,
  StakeAccount,
  StakeRewards,
  Validator,
} from "~/services/nativeStake";
import {
  preloadValidators,
  useEpochRemainingTime,
  useRewards,
  useValidators,
} from "~/state/nativeStake";
import { ValidatorIcon } from "~/components/icons/ValidatorIcon";
import { useQueryClient } from "@tanstack/react-query";
import {
  HEADER_HEIGHT,
  StackHeader,
} from "~/components/navigation/StackHeader";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import {
  HIDDEN_BALANCE_TEXT,
  useBalanceSettings,
} from "~/state/balanceSettings";
import { Icon } from "~/components/Icon";
import { abbreviateAddress } from "~/vendor/squads/utils/abbreviateAddress";
import {
  LabelValueRow,
  LabelValueRowSkeleton,
} from "~/components/LabelValueRow";
import {
  ActivitiesListSkeleton,
  CoinActivitiesList,
} from "~/components/Activities";
import { openURL } from "expo-linking";
import { PropertyRow } from "~/components/PropertyRow";
import { useFuseSolApy } from "~/state/liquidStake";
import { AnimatedTouchableScale } from "~/components/AnimatedTouchableScale";

const RouteParamsZ = z.object({
  mint: AddressZ,
});

export default function TokenScreen() {
  const { mint } = RouteParamsZ.parse(useLocalSearchParams());
  const { wallet } = useActiveWallet();

  const queryClient = useQueryClient();
  useEffect(() => {
    if (mint === mints.sol) {
      preloadValidators({ queryClient });
    }
  }, [mint]);

  const scrollViewRef = useAnimatedRef<Animated.ScrollView>();
  const insets = useSafeAreaInsets();

  return (
    <View
      style={{
        flex: 1,
        paddingBottom: insets.bottom,
      }}
    >
      <StackHeader
        title={(size) => <TokenHeader mint={mint} size={size} />}
        scrollViewRef={scrollViewRef}
      />
      <Flex
        style={{
          paddingHorizontal: 20,
          paddingTop: insets.top + HEADER_HEIGHT + 16,
          overflow: "visible",
        }}
      >
        <ScrollView
          ref={scrollViewRef}
          showsVerticalScrollIndicator={false}
          style={{ overflow: "visible" }}
          contentContainerStyle={{ gap: 24 }}
        >
          <TokenDetails mint={mint} vault={wallet.defaultVault} />
          <Row gap={6}>
            <SendButton mint={mint} />
            <SwapButton vault={wallet.defaultVault} inputMint={mint} />

            {mint === mints.sol && <StakeButton vault={wallet.defaultVault} />}
          </Row>
          {mint === mints.sol && (
            <View style={{ gap: 36 }}>
              <StakingOverview vaultKey={wallet.defaultVault} />
              <FuseErrorBoundary FallbackComponent={() => null}>
                <FuseSuspense fallback={<StakingAccountsSkeleton />}>
                  <StackingAccounts vaultKey={wallet.defaultVault} />
                </FuseSuspense>
              </FuseErrorBoundary>
            </View>
          )}
          {(mint === mints.usdc ||
            mint === mints.pyusd ||
            mint === mints.usds) && (
            <EarnOverview vault={wallet.defaultVault} mint={mint} />
          )}
          <ListSeparator />
          <TokenPriceDetails
            walletKey={wallet.walletKey}
            mint={mint}
            vault={wallet.defaultVault}
          />
          <ListSeparator />

          <FuseErrorBoundary FallbackComponent={() => null}>
            <FuseSuspense
              fallback={
                <LabelValueRowSkeleton rowsCount={3} gap={16} fontSize={15} />
              }
            >
              <TokenMarketDetails mint={mint} />
              <ListSeparator />
            </FuseSuspense>
          </FuseErrorBoundary>

          <FuseErrorBoundary FallbackComponent={() => null}>
            <FuseSuspense fallback={<ActivitiesListSkeleton />}>
              <CoinActivitiesList mint={mint} wallet={wallet} />
            </FuseSuspense>
          </FuseErrorBoundary>

          <BottomInset />
        </ScrollView>
      </Flex>
    </View>
  );
}

export function TokenHeader({ mint, size }: { mint: Address; size: number }) {
  const token = useSuspenseToken({
    mint: mint === mints.sol ? "SOL" : mint,
  });

  // Vova please refactor me...
  return (
    <Row gap={size / 2} style={{ height: "100%", paddingTop: 4 }}>
      <CoinLogo mint={mint} size={size * 1.35} />
      <Text
        adjustsFontSizeToFit={true}
        numberOfLines={1}
        colorToken={"text"}
        variant={"semibold"}
        style={{ fontSize: size, flexShrink: 1 }}
      >
        {token?.name ?? "Unknown coin"}
      </Text>
    </Row>
  );
}

export function HeaderText({ children }: { children: ReactNode }) {
  return (
    <Text
      adjustsFontSizeToFit={true}
      numberOfLines={1}
      colorToken={"text"}
      variant={"medium"}
      style={{ fontSize: 18 }}
    >
      {children}
    </Text>
  );
}

export function TokenDetails({
  mint,
  vault,
}: {
  mint: Address;
  vault: Address;
}) {
  const token = useBalance({
    mint: mint === mints.sol ? null : mint,
    vault,
  });
  invariant(token, `Unknown balance for ${mint}`);

  const totalAmount = TokenBalance.total(token);
  const amountString = formatTokenAmount(
    totalAmount / 10 ** token.decimals,
    ""
  );

  const usdAmountString = formatUsdValue(
    tokenUsdValue({
      amount: totalAmount,
      usdcPrice: token.usdcPrice,
      decimals: token.decimals,
    })
  );

  const { displayBalance } = useBalanceSettings();

  return (
    <View style={{ gap: 4 }}>
      <Text
        variant="semibold"
        colorToken={"textSecondary"}
        style={{ fontSize: 15 }}
      >
        Balance
      </Text>
      <View style={{ gap: 4 }}>
        <Text
          adjustsFontSizeToFit={true}
          numberOfLines={1}
          colorToken={amountString ? "text" : "textSecondary"}
          variant={"semibold"}
          style={{
            fontSize: 26,
          }}
        >
          {displayBalance(amountString || "0")}{" "}
          <Text variant={"medium"} colorToken={"textSecondary"}>
            {token.metadata?.symbol ?? ""}
          </Text>
        </Text>
        <Text variant={"medium"} colorToken={"textSecondary"}>
          {displayBalance(usdAmountString)}
        </Text>
      </View>
    </View>
  );
}

export function StakingOverview({ vaultKey }: { vaultKey: Address }) {
  const { solBalance } = useSolBalance({ address: vaultKey });

  const availableAmount = solBalance.amount;
  const stakedAmount = solBalance.stakedAmount ?? 0;

  if (availableAmount + stakedAmount === 0) {
    return null;
  }

  return (
    <View style={{ gap: 16 }}>
      <Text variant="semibold" style={{ fontSize: 15 }}>
        Overview
      </Text>
      <FuseErrorBoundary FallbackComponent={() => null}>
        <FuseSuspense fallback={<BreakdownSkeleton />}>
          <SolBreakdown vault={vaultKey} />
        </FuseSuspense>
      </FuseErrorBoundary>
      <View style={{ gap: 8 }}>
        <Row justify={"space-between"}>
          <Row gap={10}>
            <View
              background={"text"}
              style={{
                width: 8,
                height: 8,
                borderRadius: 999,
              }}
            />
            <Text variant={"medium"}>Available</Text>
          </Row>
          <UnstakedAmount vaultKey={vaultKey} />
        </Row>
        <Row justify={"space-between"}>
          <Row gap={10}>
            <View
              background={"textSecondary"}
              style={{
                width: 8,
                height: 8,
                borderRadius: 999,
              }}
            />
            <Text variant={"medium"}>Total Staked</Text>
          </Row>
          <StakedAmount vaultKey={vaultKey} />
        </Row>
        <Row justify={"space-between"}>
          <Row gap={10}>
            <View
              background={"green"}
              style={{
                width: 8,
                height: 8,
                borderRadius: 999,
              }}
            />
            <Text variant={"medium"}>Total Earned</Text>
          </Row>
          <FuseErrorBoundary
            FallbackComponent={() => <TextSkeleton size={10} />}
          >
            <FuseSuspense fallback={<TextSkeleton size={10} />}>
              <TotalEarned vaultKey={vaultKey} />
            </FuseSuspense>
          </FuseErrorBoundary>
        </Row>
      </View>
    </View>
  );
}

function BreakdownSkeleton() {
  return (
    <ContentSkeleton>
      <View
        background={"text"}
        style={{
          width: 20,
          height: 4,
          borderRadius: 999,
        }}
      />
    </ContentSkeleton>
  );
}

function SolBreakdown({ vault }: { vault: Address }) {
  const { solBalance } = useSolBalance({ address: vault });
  const rewards = useRewards({ vaultKey: vault });

  const availableAmount = solBalance.amount;
  const rewardsAmount = StakeRewards.totalRewards(rewards);
  // Originally staked. Sans rewards.
  const stakedAmount = Math.max(
    (solBalance.stakedAmount ?? 0) - rewardsAmount,
    0
  );

  const total = availableAmount + stakedAmount + rewardsAmount;
  const availablePercent = total && (availableAmount / total) * 100;
  const stakedPercent = total && (stakedAmount / total) * 100;
  const rewardsPercent = total && (rewardsAmount / total) * 100;

  const width = availableAmount === 0 || stakedAmount === 0 ? 0 : 7;

  const height = 4;
  return (
    <Row gap={4} style={{ flex: 1 }}>
      <View
        background={"text"}
        style={{
          flexGrow: availablePercent,
          width: width,
          height,
          borderRadius: 999,
        }}
      />
      {stakedPercent !== 0 && (
        <View
          background={"textTertiary"}
          style={{
            flexGrow: stakedPercent,
            width: width,
            height,
            borderRadius: 999,
          }}
        />
      )}
      {rewardsPercent !== 0 && (
        <View
          background={"green"}
          style={{
            flexGrow: rewardsPercent,
            width: 7,
            height,
            borderRadius: 999,
          }}
        />
      )}
    </Row>
  );
}

function EarnOverview({ vault, mint }: { vault: Address; mint: Address }) {
  const balance = useBalance({ vault, mint });
  const { displayBalance } = useBalanceSettings();

  if (!balance) {
    return null;
  }

  const decimals = balance.decimals ?? 0;
  const availableAmount = (balance.amount ?? 0) / Math.pow(10, decimals);
  const depositAmount = TokenBalance.earning(balance) / Math.pow(10, decimals);
  const symbol = balance?.metadata.symbol ?? "";

  if (depositAmount === 0) {
    return null;
  }

  return (
    <View style={{ gap: 16 }}>
      <Text variant="semibold" style={{ fontSize: 15 }}>
        Overview
      </Text>
      <FuseErrorBoundary FallbackComponent={() => null}>
        <FuseSuspense fallback={<BreakdownSkeleton />}>
          <EarnBreakdown vault={vault} mint={mint} />
        </FuseSuspense>
      </FuseErrorBoundary>
      <View style={{ gap: 8 }}>
        <Row justify={"space-between"}>
          <Row gap={10}>
            <View
              background={"blue"}
              style={{
                width: 8,
                height: 8,
                borderRadius: 999,
              }}
            />
            <Text variant={"medium"}>Available</Text>
          </Row>
          <Text variant={"medium"}>
            {displayBalance(formatTokenAmount(availableAmount, symbol))}
          </Text>
        </Row>
        <Row justify={"space-between"}>
          <Row gap={10}>
            <View
              background={"textTertiary"}
              style={{
                width: 8,
                height: 8,
                borderRadius: 999,
              }}
            />
            <Text variant={"medium"}>Deposited</Text>
          </Row>
          <Text variant={"medium"}>
            {displayBalance(formatTokenAmount(depositAmount, symbol))}
          </Text>
        </Row>
      </View>
    </View>
  );
}

function EarnBreakdown({ vault, mint }: { vault: Address; mint: Address }) {
  const balance = useBalance({ vault, mint });

  const availableAmount = balance?.amount ?? 0;
  const depositAmount = balance ? TokenBalance.earning(balance) : 0;

  const total = availableAmount + depositAmount;
  const availablePercent = total && (availableAmount / total) * 100;
  const depositPercent = total && (depositAmount / total) * 100;

  const height = 4;
  const width = availableAmount === 0 || depositAmount === 0 ? 0 : 7;
  return (
    <Row gap={4} style={{ flex: 1 }}>
      <View
        background={"blue"}
        style={{
          flexGrow: availablePercent,
          width,
          height,
          borderRadius: 999,
        }}
      />
      {depositPercent !== 0 && (
        <View
          background={"textTertiary"}
          style={{
            flexGrow: depositPercent,
            width,
            height,
            borderRadius: 999,
          }}
        />
      )}
    </Row>
  );
}

function UnstakedAmount({ vaultKey }: { vaultKey: Address }) {
  const { solBalance } = useSolBalance({ address: vaultKey });
  const { displayBalance } = useBalanceSettings();

  return (
    <Text variant={"medium"}>
      {displayBalance(
        formatTokenAmount(solBalance.amount / LAMPORTS_PER_SOL, "SOL")
      )}
    </Text>
  );
}

function StakedAmount({ vaultKey }: { vaultKey: Address }) {
  const { solBalance } = useSolBalance({ address: vaultKey });

  const stakedAmount = solBalance.stakedAmount
    ? solBalance.stakedAmount / LAMPORTS_PER_SOL
    : null;

  const { isBalanceHidden } = useBalanceSettings();
  if (isBalanceHidden) {
    return <Text variant={"medium"}>{HIDDEN_BALANCE_TEXT}</Text>;
  }

  if (!stakedAmount) {
    return (
      <Text variant={"medium"} colorToken={"textSecondary"}>
        {formatTokenAmount(0, "SOL")}
      </Text>
    );
  }

  return (
    <Text variant={"medium"}>{formatTokenAmount(stakedAmount, "SOL")}</Text>
  );
}

function TotalEarned({ vaultKey }: { vaultKey: Address }) {
  const rewards = useRewards({ vaultKey });
  const totalRewardsAmount =
    StakeRewards.totalRewards(rewards) / LAMPORTS_PER_SOL;

  const { isBalanceHidden } = useBalanceSettings();
  if (isBalanceHidden)
    return <Text variant={"medium"}>{HIDDEN_BALANCE_TEXT}</Text>;

  if (totalRewardsAmount === 0) {
    return (
      <Text variant={"medium"} colorToken={"textSecondary"}>
        {formatTokenAmount(totalRewardsAmount, "SOL")}
      </Text>
    );
  }

  return (
    <Text variant={"medium"} colorToken={"green"}>
      +{formatTokenAmount(totalRewardsAmount, "SOL")}
    </Text>
  );
}

function StakingAccountsSkeleton() {
  return (
    <View style={{ gap: 16 }}>
      <Text variant="semibold" style={{ fontSize: 15 }}>
        Staking
      </Text>
      <View style={{ gap: 20 }}>
        <ContentSkeleton>
          <ValidatorDetailsView
            key={mints.sol}
            label={"validator"}
            icon={<MarinadeIcon />}
            lamports={LAMPORTS_PER_SOL}
          />
        </ContentSkeleton>
      </View>
    </View>
  );
}

export function StackingAccounts({ vaultKey }: { vaultKey: Address }) {
  const epochRemainingTime = useEpochRemainingTime();
  const { solBalance } = useSolBalance({ address: vaultKey });

  const stakeAccounts = solBalance.stakeAccounts.filter(
    (account): account is InitializedStakeAccount =>
      account.type === "initialized"
  );

  const stakeAccountsGroupedByValidator = useMemo(() => {
    return stakeAccounts.reduce(
      (acc, stakeAccount) => {
        const validator = stakeAccount.validator ?? "none";

        if (!acc[validator]) {
          acc[validator] = [];
        }
        acc[validator].push(stakeAccount);
        return acc;
      },
      {} as Record<string, typeof stakeAccounts>
    );
  }, [stakeAccounts]);

  const validators = useValidators();

  if (!stakeAccounts.length) {
    return null;
  }

  return (
    <View style={{ gap: 16 }}>
      <Row justify={"space-between"}>
        <Text variant="semibold" style={{ fontSize: 15 }}>
          Staking
        </Text>
        <Text
          variant="medium"
          colorToken={"textSecondary"}
          style={{ fontSize: 13 }}
        >
          Next Epoch in{" "}
          {Duration.fromObject({
            seconds: epochRemainingTime.seconds,
          }).toFormat(formatEpoch(epochRemainingTime.seconds))}
        </Text>
      </Row>
      <View style={{ gap: 20 }}>
        {Object.entries(stakeAccountsGroupedByValidator).map(
          ([validatorAddress, stakeAccounts]) => {
            const validator = validators?.find(
              (validator) => validator.address === validatorAddress
            );

            //todo handle unknown validators?
            if (!validator) {
              console.log("Unknown validator", validatorAddress);
              return null;
            }

            return (
              <ValidatorStakeAccountsRow
                key={validatorAddress}
                vaultKey={vaultKey}
                validator={validator}
                stakeAccounts={stakeAccounts}
              />
            );
          }
        )}
      </View>
    </View>
  );
}

function ValidatorStakeAccountsRow({
  vaultKey,
  validator,
  stakeAccounts,
}: {
  vaultKey: Address;
  validator: Validator;
  stakeAccounts: InitializedStakeAccount[];
}) {
  const modalRef = useRef<BottomModalImperativeMethods>(null);

  const totalStaked = StakeAccount.totalStaked(stakeAccounts);

  return (
    <>
      <TouchableScale onPress={() => modalRef.current?.present()}>
        <ValidatorDetailsView
          label={validator.name}
          icon={<ValidatorIcon validator={validator.address} />}
          lamports={totalStaked}
        />
      </TouchableScale>
      <ValidatorUnstakeModal
        vaultKey={vaultKey}
        validator={validator}
        stakeAccounts={stakeAccounts}
        modalRef={modalRef}
      />
    </>
  );
}

function ValidatorDetailsView({
  icon,
  label,
  lamports,
}: {
  icon: ReactNode;
  label: string;
  lamports: number;
}) {
  const amount = formatTokenAmount(lamports / LAMPORTS_PER_SOL, "SOL");

  const { displayBalance } = useBalanceSettings();

  return (
    <Row justify={"space-between"} style={{}}>
      <Row gap={16}>
        {icon}
        <View style={{ gap: 4 }}>
          <Text variant={"semibold"} style={{ fontSize: 18 }}>
            {label}
          </Text>
          <Text variant={"medium"}>{displayBalance(amount)}</Text>
        </View>
      </Row>
    </Row>
  );
}

function TokenPriceDetails({
  walletKey,
  mint,
  vault,
}: {
  walletKey: Address;
  mint: Address;
  vault: Address;
}) {
  const [period, setPeriod] = useState<Period>("day");
  const balance = useBalance({ mint, vault });

  const [selectedPoint, setSelectedPoint] = useState<SelectedChartPoint>({
    active: false,
    point: null,
  });

  const { data: historicalPrices, isLoading } = useLazyHistoricalPrices({
    walletKey,
    mint,
    period,
  });

  const points = useMemo(() => {
    return historicalPrices?.map((price) => {
      return {
        date: DateTime.fromSeconds(price.timestamp).toJSDate(),
        value: price.usdcPrice,
      };
    });
  }, [historicalPrices]);

  const point = selectedPoint.active ? selectedPoint.point : null;

  if (!balance || balance.usdcPrice === null) {
    return null;
  }

  const price = point?.value ?? balance.usdcPrice;

  return (
    <View style={{ gap: 16 }}>
      <Text variant="semibold" style={{ fontSize: 15 }}>
        {balance.metadata?.symbol}
        {"  •  "}
        USD
      </Text>
      <View style={{ gap: 4 }}>
        <Row justify={"space-between"} style={{ overflow: "hidden" }}>
          <Ticker
            value={formatUsdValue(price, {
              maximumFractionDigits: price < 1 ? 8 : 2,
            })}
            fontSize={26}
            wholeNumbersColorToken={"text"}
            decimalsColorToken={"textSecondary"}
          />

          <FuseSuspense fallback={<PercentageViewSkeleton />}>
            <TokenDetailsPercentageView
              mint={mint}
              vault={vault}
              period={period}
            />
          </FuseSuspense>
        </Row>
        <Row>
          {point ? (
            <Animated.View
              entering={FadeInDown.duration(DURATION_FAST).reduceMotion(
                ReduceMotion.Never
              )}
              exiting={FadeOutDown.duration(DURATION_FAST).reduceMotion(
                ReduceMotion.Never
              )}
            >
              <Text colorToken={"textSecondary"}>
                {DateTime.fromJSDate(point.date).toLocaleString(
                  DateTime.DATETIME_MED
                )}
              </Text>
            </Animated.View>
          ) : (
            <Text> </Text>
          )}
        </Row>
      </View>
      <View style={{ marginHorizontal: -20 }}>
        <Chart
          height={200}
          points={points}
          isLoading={isLoading}
          onPointSelected={(point) => {
            setSelectedPoint(point);
          }}
        />
      </View>
      <ChartPeriods selected={period} onSelect={setPeriod} />
    </View>
  );
}

function FuseSolApyText() {
  const fuseSolApy = useFuseSolApy();
  return (
    <Text variant={"medium"} colorToken="green" style={{ fontSize: 15 }}>
      {fuseSolApy.toFixed(2)}%
    </Text>
  );
}

function TokenMarketDetails({ mint }: { mint: Address }) {
  const tokenDetails = useTokenDetails({ mint });

  if (!tokenDetails) {
    return null;
  }

  return (
    <View style={{ gap: 12 }}>
      {mints.fuseSol === mint && (
        <PropertyRow
          key={"fuse_sol_apy"}
          label={"APY"}
          value={
            <FuseErrorBoundary FallbackComponent={() => null}>
              <FuseSuspense fallback={<TextSkeleton size={15} />}>
                <FuseSolApyText />
              </FuseSuspense>
            </FuseErrorBoundary>
          }
        />
      )}
      {tokenDetails.marketCap !== null && (
        <LabelValueRow
          key={"market_cap"}
          label={"Market Cap"}
          value={`$${abbreviateAmount(tokenDetails.marketCap, { type: "usd" })}`}
          fontSize={15}
        />
      )}

      {tokenDetails.volume24h !== null && (
        <LabelValueRow
          key={"volume_24h"}
          label={"Volume 24h"}
          value={`$${abbreviateAmount(tokenDetails.volume24h, { type: "usd" })}`}
          fontSize={15}
        />
      )}

      <LabelValueRow
        key={"mint"}
        label={"Mint Address"}
        value={abbreviateAddress(tokenDetails.mint)}
        copyValue={tokenDetails.mint}
        fontSize={15}
      />

      <AnimatedTouchableScale
        pressedScale={0.98}
        onPress={() => openURL(`https://birdeye.so/token/${mint}?chain=solana`)}
      >
        <Text variant={"medium"} style={{ fontSize: 15, alignSelf: "center" }}>
          See more{"  "}
          <Icon
            colorToken={"text"}
            name="arrow.up.forward"
            size={9}
            weight="bold"
            style={{ top: 1 }}
          />
        </Text>
      </AnimatedTouchableScale>
    </View>
  );
}

function PercentageViewSkeleton() {
  return (
    <ContentSkeleton borderRadius={6}>
      <Text variant={"semibold"}>$0.00 • 0.00%</Text>
    </ContentSkeleton>
  );
}

function TokenDetailsPercentageView({
  mint,
  vault,
  period,
}: {
  mint: Address;
  vault: Address;
  period: Period;
}) {
  const balance = useBalance({ vault, mint });
  const periodPrice = usePeriodTokenPrice({ mint, period });

  if (!balance || balance.usdcPrice === null) {
    return null;
  }

  const priceDiffUsd =
    periodPrice?.price != null ? balance.usdcPrice - periodPrice.price : 0;

  const priceDiffPercent = periodPrice?.price
    ? (priceDiffUsd / periodPrice.price) * 100
    : 0;

  const usdValueString = formatUsdValue(Math.abs(priceDiffUsd));
  const percentageChangeString = Math.abs(priceDiffPercent).toFixed(2) + "%";

  const isChanged =
    Math.abs(priceDiffPercent) >= 0.01 || Math.abs(priceDiffUsd) >= 0.01;

  return (
    <View style={{ flexDirection: "row", alignItems: "center", gap: 4 }}>
      {isChanged ? (
        <Icon
          name={priceDiffUsd >= 0 ? "arrow.up.right" : "arrow.down.right"}
          size={8}
          colorToken={priceDiffUsd >= 0 ? "green" : "red"}
          weight={"heavy"}
        />
      ) : null}
      <Text
        variant={"semibold"}
        colorToken={priceDiffPercent >= 0 ? "green" : "red"}
      >
        {usdValueString} • {percentageChangeString}
      </Text>
    </View>
  );
}
