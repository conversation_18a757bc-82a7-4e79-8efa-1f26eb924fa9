import { Text, View } from "~/components/Themed";
import { NumPad } from "~/components/NumPad";
import { useCallback, useMemo } from "react";
import { z } from "zod";
import { AmountZ } from "~/components/AmountDisplay";
import {
  refetchBalances,
  refetchHistoricalBalances,
  useBalances,
} from "~/state/balances";
import { AddressZ } from "@squads/models/solana";
import { useActiveWallet } from "~/state/wallet";
import invariant from "invariant";
import { useFormik } from "formik";
import { toFormikValidationSchema } from "zod-formik-adapter";
import * as Haptics from "expo-haptics";
import { router, useLocalSearchParams } from "expo-router";
import { useToast } from "~/components/Toaster";
import { useQueryClient } from "@tanstack/react-query";
import { mints } from "~/constants/tokens";
import { Action, prepareAction } from "~/services/wallets";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { sendTransactionWithTrackingStatus } from "~/utils/sendTransactionWithTracking";
import { Flex, Row } from "~/components/Grid";
import { Button } from "~/components/Button";
import {
  AssetAmountInputBlock,
  TokenLabel,
} from "~/components/AssetAmountInputBlock";
import { EarnPeriodsZ, earnPeriodToPeriod } from "~/services/balances";
import { EarnProviderZ } from "~/services/earn";
import { EarnProviderIcon } from "~/components/earn/EarnProviderIcon";
import { assertNever } from "~/utils/assertNever";
import { isClientErrorReasonType } from "~/services/utils";
import { showNotEnoughSolModal } from "~/components/modals/NotEnoughSolModal";
import { fetchSubscription } from "~/state/subscription";
import { getAppIcon, setAppIcon } from "~/utils/appIcon";

const RouteParamsZ = z.object({
  earnMint: AddressZ,
  provider: EarnProviderZ,
});

export type EarnWithdrawRouteParams = z.infer<typeof RouteParamsZ>;

export default function LendDepositWithdrawAmountScreen() {
  const { earnMint: mint, provider } = RouteParamsZ.parse(
    useLocalSearchParams()
  );

  const { wallet } = useActiveWallet();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const balances = useBalances({ address: wallet.defaultVault });

  const balance = balances.find((b) => b.mint === mint);
  invariant(balance, "Invalid mint");

  const maxAmount =
    provider === "drift"
      ? (balance.driftEarn?.total ?? 0) / 10 ** balance.decimals
      : provider === "lulo"
        ? (balance.luloProtectedEarn?.total ?? 0) / 10 ** balance.decimals
        : provider === "kamino"
          ? (balance.kaminoEarn?.total ?? 0) / 10 ** balance.decimals
          : assertNever(provider);

  const formik = useFormik({
    initialValues: { amount: "" },

    validationSchema: toFormikValidationSchema(
      z.object({ amount: AmountZ(maxAmount) })
    ),

    async onSubmit(values) {
      Haptics.selectionAsync();

      const amount = Number(values.amount);
      const decimalsAmount = Math.round(amount * 10 ** balance.decimals);

      let actionRequest: Action;
      if (provider === "drift") {
        let type;
        switch (balance.mint) {
          case mints.usdc:
            type = "usdc";
            break;
          case mints.pyusd:
            type = "pyusd";
            break;
          case mints.usds:
            type = "usds";
            break;
          default:
            throw new Error("Unsupported mint");
        }

        actionRequest = {
          type: "driftEarnWithdraw",
          vaultIndex: 0,
          deposit: {
            type,
            amount: decimalsAmount,
            decimals: balance.decimals,
          },
        };
      } else if (provider === "lulo") {
        actionRequest = {
          type: "luloProtectedEarnWithdraw",
          vaultIndex: 0,
          withdrawal: {
            type: "usdc",
            amount: decimalsAmount,
            decimals: balance.decimals,
          },
        };
      } else if (provider === "kamino") {
        actionRequest = {
          type: "kaminoEarnWithdraw",
          vaultIndex: 0,
          withdrawal: {
            type: "usdc",
            amount: decimalsAmount,
            decimals: balance.decimals,
          },
        };
      } else {
        assertNever(provider);
      }

      const action = await prepareAction(actionRequest).catch((error) => {
        if (isClientErrorReasonType(error, "notEnoughSol")) {
          showNotEnoughSolModal();
        } else {
          toast.error("Failed to simulate transaction", {
            id: "tx-simulation-failed",
          });
        }

        throw error;
      });

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );

      router.dismissTo("/unlocked/root/earn");

      await sendTransactionWithTrackingStatus({
        walletKey: wallet.walletKey,
        transaction: { type: "action", signedTransactions },
        confirmationFailureMessage: `Withdraw failed`,
      }).finally(() => {
        refetchBalances({ queryClient, address: wallet.defaultVault });
        // Refetch historical balances for the periods used for the Earn chart.
        for (const period of EarnPeriodsZ.options) {
          refetchHistoricalBalances({
            queryClient,
            vault: wallet.defaultVault,
            period: earnPeriodToPeriod(period),
          });
        }
      });

      const newSubscription = await fetchSubscription(queryClient);
      if (
        newSubscription.status === "none" &&
        getAppIcon() === "app-icon-plus"
      ) {
        await setAppIcon("icon");
      }
    },
  });

  const onAmountInputChange = useCallback(
    (amount: number) => {
      formik.setFieldValue("amount", String(amount));
    },
    [formik]
  );

  const maxAmountQuery = useMemo(() => {
    return { status: "success", maxAmount } as const;
  }, [maxAmount]);

  return (
    <Flex
      justify="space-between"
      gap={24}
      style={{
        paddingHorizontal: 20,
        paddingBottom: 38,
      }}
    >
      <Flex style={{ justifyContent: "space-between", alignItems: "center" }}>
        <Row gap={8}>
          <EarnProviderIcon provider={provider} size={20} />
          <Text variant="medium" size={14} colorToken="textSecondary">
            {provider === "lulo" ? "Rebalancing Yield" : "Lending Yield"}
          </Text>
        </Row>
        <View gap={12}>
          <TokenLabel token={balance.metadata} />
          <AssetAmountInputBlock
            amount={formik.values.amount}
            usdPrice={null}
            maxAmountQuery={maxAmountQuery}
            onInstantChange={onAmountInputChange}
            showSlider
          />
        </View>
      </Flex>

      <View style={{ gap: 20 }}>
        <NumPad
          value={formik.values.amount}
          onChange={formik.handleChange("amount")}
          decimals={balance.decimals}
        />
        <Button
          error={
            formik.errors.amount === "insufficientBalance"
              ? "Insufficient balance"
              : undefined
          }
          loading={formik.isSubmitting}
          loadingText={"Withdrawing"}
          disabled={!formik.dirty || !formik.isValid}
          iconName="faceid"
          iconWeight="medium"
          onPress={() => formik.handleSubmit()}
        >
          Withdraw
        </Button>
      </View>
    </Flex>
  );
}
