import { Text, View } from "~/components/Themed";
import { NumPad } from "~/components/NumPad";
import { useCallback, useMemo } from "react";
import { z } from "zod";
import {
  AmountError,
  AmountZ,
  getMaxAvailableAmount,
} from "~/components/AmountDisplay";
import {
  refetchBalances,
  refetchHistoricalBalances,
  useBalances,
} from "~/state/balances";
import { AddressZ } from "@squads/models/solana";
import { useActiveWallet } from "~/state/wallet";
import invariant from "invariant";
import { useFormik } from "formik";
import { toFormikValidationSchema } from "zod-formik-adapter";
import * as Haptics from "expo-haptics";
import { Flex, Row } from "~/components/Grid";
import { Button } from "~/components/Button";
import { router, useLocalSearchParams } from "expo-router";
import { Action, prepareAction } from "~/services/wallets";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { sendTransactionWithTrackingStatus } from "~/utils/sendTransactionWithTracking";
import { useToast } from "~/components/Toaster";
import { useQueryClient } from "@tanstack/react-query";
import { mints } from "~/constants/tokens";
import {
  AssetAmountInputBlock,
  TokenLabel,
} from "~/components/AssetAmountInputBlock";
import { EarnPeriodsZ, earnPeriodToPeriod } from "~/services/balances";
import { EarnProviderZ } from "~/services/earn";
import { EarnProviderIcon } from "~/components/earn/EarnProviderIcon";
import { assertNever } from "~/utils/assertNever";
import { useTokenMetadata } from "~/hooks/useTokenMetadata";
import { isClientErrorReasonType } from "~/services/utils";
import { showNotEnoughSolModal } from "~/components/modals/NotEnoughSolModal";
import {
  fetchSubscription,
  navigateToMembershipWelcomeScreen,
  useSubscription,
} from "~/state/subscription";
import { EarnBalance, useEarnBalances } from "~/state/earn";
import { sleep } from "~/utils/promise";
import { hapticSuccess } from "~/utils/haptics";

const MIN_DEPOSIT = 5;

const RouteParamsZ = z.object({
  provider: EarnProviderZ,
  earnMint: AddressZ,
});

export type EarnDepositRouteParams = z.infer<typeof RouteParamsZ>;

export default function LendDepositAmountScreen() {
  const { provider, earnMint: mint } = RouteParamsZ.parse(
    useLocalSearchParams()
  );

  const { wallet } = useActiveWallet();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const subscriptionQuery = useSubscription();
  const earnBalances = useEarnBalances({ address: wallet.defaultVault });
  const currentEarnBalanceUsd = EarnBalance.totalBalanceUsd(
    earnBalances.flatMap((e) => e.assets)
  );

  const balances = useBalances({ address: wallet.defaultVault });

  const balance = balances.find((b) => b.mint === mint);
  invariant(balance, `Balance not found for mint: ${mint}`);
  const { symbol: tokenSymbol } = useTokenMetadata({ mint });

  const minAmount = MIN_DEPOSIT;

  const maxAmount = getMaxAvailableAmount(balance);

  const formik = useFormik({
    initialValues: { amount: String(maxAmount) },

    validationSchema: toFormikValidationSchema(
      z.object({ amount: AmountZ(maxAmount, minAmount) })
    ),
    validateOnMount: true,

    async onSubmit(values) {
      Haptics.selectionAsync();

      const amount = Number(values.amount);
      const decimalsAmount = Math.round(amount * 10 ** balance.decimals);

      let type;
      switch (balance.mint) {
        case mints.usdc:
          type = "usdc";
          break;
        case mints.pyusd:
          type = "pyusd";
          break;
        case mints.usds:
          type = "usds";
          break;
        default:
          throw new Error("Unsupported mint");
      }

      const actionRequest: Action = (() => {
        switch (provider) {
          case "drift":
            return {
              type: "driftEarnDeposit",
              vaultIndex: 0,
              deposit: {
                type,
                amount: decimalsAmount,
                decimals: balance.decimals,
              },
            };
          case "lulo":
            return {
              type: "luloProtectedEarnDeposit",
              vaultIndex: 0,
              deposit: {
                type: "usdc",
                amount: decimalsAmount,
                decimals: balance.decimals,
              },
            };
          case "kamino":
            return {
              type: "kaminoEarnDeposit",
              vaultIndex: 0,
              deposit: {
                type: "usdc",
                amount: decimalsAmount,
                decimals: balance.decimals,
              },
            };
          default:
            return assertNever(provider);
        }
      })();

      const action = await prepareAction(actionRequest).catch((error) => {
        if (isClientErrorReasonType(error, "notEnoughSol")) {
          showNotEnoughSolModal();
        } else {
          toast.error("Failed to simulate transaction", {
            id: "tx-simulation-failed",
          });
        }

        throw error;
      });

      const isGettingSubscription =
        subscriptionQuery.data?.status === "none" &&
        currentEarnBalanceUsd + amount >=
          subscriptionQuery.data.minHoldAmountUsd;

      const signedTransactions = await signTransactionsWithFuseKeys(
        wallet.keys,
        action.transactions
      );

      if (!isGettingSubscription) {
        router.dismissTo("/unlocked/root/earn");
      }

      await sendTransactionWithTrackingStatus({
        walletKey: wallet.walletKey,
        transaction: { type: "action", signedTransactions },
        confirmationFailureMessage: `Deposit failed`,
        sendSuccessMessage: false,
      }).finally(() => {
        fetchSubscription(queryClient);
        refetchBalances({ queryClient, address: wallet.defaultVault });
        // Refetch historical balances for the periods used for the Earn chart.
        for (const period of EarnPeriodsZ.options) {
          refetchHistoricalBalances({
            queryClient,
            vault: wallet.defaultVault,
            period: earnPeriodToPeriod(period),
          });
        }
      });

      if (isGettingSubscription) {
        router.dismissTo("/unlocked/root/earn");
        await sleep(300);

        navigateToMembershipWelcomeScreen();

        hapticSuccess();
      }
    },
  });

  const onAmountInputChange = useCallback(
    (amount: number) => {
      formik.setFieldValue("amount", String(amount));
    },
    [formik]
  );

  const maxAmountQuery = useMemo(() => {
    return { status: "success", maxAmount } as const;
  }, [maxAmount]);

  return (
    <Flex
      justify="space-between"
      gap={24}
      style={{
        paddingHorizontal: 20,
        paddingBottom: 38,
      }}
    >
      <Flex style={{ justifyContent: "space-between", alignItems: "center" }}>
        <Row gap={8}>
          <EarnProviderIcon provider={provider} size={20} />
          <Text variant="medium" size={14} colorToken="textSecondary">
            {provider === "lulo" ? "Rebalancing Yield" : "Lending Yield"}
          </Text>
        </Row>

        <View gap={12}>
          <TokenLabel token={balance.metadata} />
          <AssetAmountInputBlock
            amount={formik.values.amount}
            usdPrice={null}
            maxAmountQuery={maxAmountQuery}
            onInstantChange={onAmountInputChange}
            showSlider
          />
        </View>
      </Flex>

      <View style={{ gap: 20 }}>
        <NumPad
          value={formik.values.amount}
          onChange={formik.handleChange("amount")}
          decimals={balance.decimals}
        />
        <Button
          error={getErrorMessage(
            formik.errors.amount as AmountError | undefined,
            tokenSymbol
          )}
          loading={formik.isSubmitting}
          loadingText={"Depositing"}
          disabled={!formik.isValid}
          onPress={() => formik.handleSubmit()}
          iconName="faceid"
          iconWeight="medium"
          size="xlarge"
        >
          Deposit
        </Button>
      </View>
    </Flex>
  );
}

function getErrorMessage(error: AmountError | undefined, tokenSymbol: string) {
  if (error === "insufficientBalance") return "Insufficient balance";
  if (error?.startsWith("minAmountExceeded")) {
    const minAmount = error.split(":")[1];
    return `Minimum deposit is ${minAmount} ${tokenSymbol}`;
  }
  return undefined;
}
