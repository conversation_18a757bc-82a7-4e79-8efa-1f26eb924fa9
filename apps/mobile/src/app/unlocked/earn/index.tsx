import { Text, View } from "~/components/Themed";
import { Flex, Row } from "~/components/Grid";
import { CoinLogo } from "~/components/CoinLogo";
import { useSuspenseToken } from "~/state/tokens";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import {
  useBalance,
  useBalances,
  useLazyHistoricalBalances,
  useStablesForEarnBalance,
} from "~/state/balances";
import { useActiveWallet } from "~/state/wallet";
import { formatPercent, formatUsdValue } from "@squads/utils/numberFormats";
import { DriftSpotMarket, EarnProviderZ, LendingApy } from "~/services/earn";
import {
  EarnBalance,
  useEarnBalances,
  useSuspenseDriftSpotMarkets,
  useSuspenseKaminoVault,
  useSuspenseLuloPools,
} from "~/state/earn";
import { ProvidedBy } from "~/components/earn/ProvidedBy";
import { FloatingFooter } from "~/components/FloatingFooter";
import { DriftEarnWithdrawButton } from "~/components/earn/DriftEarnWithdrawButton";
import { DriftEarnDepositButton } from "~/components/earn/DriftEarnDepositButton";
import { Address } from "@squads/models/solana";
import { BottomInset } from "~/components/BottomInset";
import { P3 } from "~/components/typography/P3";
import { Link, useLocalSearchParams } from "expo-router";
import { useBalanceSettings } from "~/state/balanceSettings";
import { EarnChart } from "~/components/EarnChart";
import { TextFadeCents } from "~/components/typography/TextFadeCents";
import { StackScreenHeader } from "~/components/headers/StackScreenHeader";
import Animated from "react-native-reanimated";
import { useHeaderHeight } from "@react-navigation/elements";
import { useHeaderAnimatedValues } from "~/hooks/useHeaderAnimatedValues";
import { InteractiveShowcaseChart } from "~/components/earn/InteractiveShowcaseChart";
import { mints } from "~/constants/tokens";
import { z } from "zod";
import { calculateEarnedAtPointInTime } from "~/services/balances";
import { LuloProtectedEarnDepositButton } from "~/components/earn/LuloProtectedEarnDepositButton";
import { LuloProtectedEarnWithdrawButton } from "~/components/earn/LuloProtectedEarnWithdrawButton";
import { ActivityIndicator, Alert, useWindowDimensions } from "react-native";
import { KaminoEarnDepositButton } from "~/components/earn/KaminoEarnDepositButton";
import { KaminoEarnWithdrawButton } from "~/components/earn/KaminoEarnWithdrawButton";

const RouteParamsZ = z.object({
  provider: EarnProviderZ,
});
export type EarnProviderRouteParams = z.infer<typeof RouteParamsZ>;

export default function LendScreen() {
  const { provider } = RouteParamsZ.parse(useLocalSearchParams());
  const { height: screenHeight } = useWindowDimensions();

  const headerHeight = useHeaderHeight();
  const { animatedScrollRef, headerBackgroundOpacity } =
    useHeaderAnimatedValues({ headerHeight });

  const { wallet } = useActiveWallet();
  const earnBalances = useEarnBalances({ address: wallet.defaultVault });
  const providerEarnBalances =
    earnBalances.find((b) => b.provider === provider)?.assets ?? [];

  const totalEarnBalance = EarnBalance.totalBalanceUsd(providerEarnBalances);

  const historicalBalances = useLazyHistoricalBalances({
    vault: wallet.defaultVault,
    period: "year",
  });
  const loading = historicalBalances.status === "pending";

  const calculateLifetimeProviderEarn = () => {
    const lifetimeproviderEarned = calculateEarnedAtPointInTime(
      provider,
      "now",
      totalEarnBalance,
      historicalBalances.data ?? []
    );
    return lifetimeproviderEarned ?? 0;
  };
  let lifetimeproviderEarned;
  try {
    lifetimeproviderEarned = calculateLifetimeProviderEarn();
  } catch (e) {
    Alert.alert(JSON.stringify(historicalBalances.data).slice(0, 300));
    throw e;
  }

  const isSimulation =
    !loading && totalEarnBalance < 0.01 && lifetimeproviderEarned < 0.01;

  const weightedApy = EarnBalance.weightedApy(providerEarnBalances);

  const { spotMarkets } = useSuspenseDriftSpotMarkets();
  const driftUsdcMarket = spotMarkets.find(
    (market) => market.mint === mints.usdc
  );
  const luloPools = useSuspenseLuloPools();
  const kaminoVault = useSuspenseKaminoVault();

  const luloProtectedApy = luloPools.protected.apy;
  const simulatedApy =
    provider === "drift"
      ? driftUsdcMarket
        ? LendingApy.total(driftUsdcMarket.apy)
        : 6
      : provider === "lulo"
        ? luloProtectedApy
        : kaminoVault.apy;
  const totalStablesBalance = useStablesForEarnBalance({
    address: wallet.defaultVault,
  });

  return (
    <View background="background" style={{ flex: 1 }}>
      <StackScreenHeader
        headerTitle={
          provider === "lulo" ? "Rebalancing Yield" : "Lending Yield"
        }
        backgroundOpacity={headerBackgroundOpacity}
      />

      <Animated.ScrollView
        ref={animatedScrollRef}
        contentInsetAdjustmentBehavior="automatic"
        showsVerticalScrollIndicator={false}
        style={{ overflow: "visible", paddingHorizontal: 20 }}
        contentContainerStyle={{ gap: 40 }}
      >
        <View
          style={{
            height: loading ? screenHeight - headerHeight : undefined,
          }}
        >
          <Row style={{ width: "100%", justifyContent: "center" }}>
            <ProvidedBy provider={provider} />
          </Row>

          {loading && (
            <View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <ActivityIndicator />
            </View>
          )}
        </View>

        {!loading && (
          <>
            {isSimulation ? (
              <>
                <InteractiveShowcaseChart
                  data={{
                    apy: simulatedApy,
                    usdBalance: Math.max(1000, totalStablesBalance),
                  }}
                />
                {provider === "drift" && <DriftSpotMarkets />}
                {provider === "lulo" && <LuloPools />}
                {provider === "kamino" && <KaminoPools />}
              </>
            ) : (
              <>
                <EarnChart
                  totalEarnBalance={totalEarnBalance}
                  weightedApy={weightedApy}
                  provider={provider}
                />

                {provider === "drift" && (
                  <DriftSpotPositions vault={wallet.defaultVault} />
                )}
              </>
            )}
          </>
        )}

        {!loading && (
          <>
            {provider === "drift" && <AboutDrift />}
            {provider === "lulo" && <AboutLulo />}
            {provider === "kamino" && <AboutKamino />}
          </>
        )}

        <BottomInset offset={12} />
      </Animated.ScrollView>

      {!loading && (
        <FloatingFooter>
          {isSimulation ? (
            <>
              {provider === "drift" && <DriftEarnDepositButton />}
              {provider === "lulo" && <LuloProtectedEarnDepositButton />}
              {provider === "kamino" && <KaminoEarnDepositButton />}
            </>
          ) : (
            <>
              {provider === "drift" && <DriftEarnButtons />}
              {provider === "lulo" && <LuloProtectedButtons />}
              {provider === "kamino" && <KaminoEarnButtons />}
            </>
          )}
        </FloatingFooter>
      )}
    </View>
  );
}

function DriftEarnButtons() {
  return (
    <Row justify="space-between" gap={12}>
      <Flex>
        <DriftEarnWithdrawButton />
      </Flex>
      <Flex>
        <DriftEarnDepositButton />
      </Flex>
    </Row>
  );
}

function LuloProtectedButtons() {
  return (
    <Row justify="space-between" gap={12}>
      <Flex>
        <LuloProtectedEarnWithdrawButton />
      </Flex>
      <Flex>
        <LuloProtectedEarnDepositButton />
      </Flex>
    </Row>
  );
}

function KaminoEarnButtons() {
  return (
    <Row justify="space-between" gap={12}>
      <Flex>
        <KaminoEarnWithdrawButton />
      </Flex>
      <Flex>
        <KaminoEarnDepositButton />
      </Flex>
    </Row>
  );
}

export function DriftSpotPositions({ vault }: { vault: Address }) {
  const { spotMarkets } = useSuspenseDriftSpotMarkets();

  const mints = spotMarkets.map((spotMarket) => spotMarket.mint).sort();
  const balances = useBalances({ address: vault });
  const balancesByMint = mints.reduce(
    (acc, mint) => {
      const token = useSuspenseToken({ mint });
      if (token === null) return acc;

      const balance = balances.find((balance) => balance.mint === mint);
      if (balance) {
        acc[mint] = (balance.driftEarn?.total ?? 0) / 10 ** token.decimals;
      }
      return acc;
    },
    {} as Record<string, number>
  );
  const totalMintBalance = Object.values(balancesByMint).reduce(
    (acc, balance) => acc + balance,
    0
  );
  if (totalMintBalance === 0) return null;

  return (
    <View gap={24}>
      <Text variant="semibold" style={{ fontSize: 16 }}>
        Breakdown
      </Text>
      {spotMarkets.map((spotMarket) => (
        <SpotPositionTile
          key={spotMarket.mint}
          spotMarket={spotMarket}
          vault={vault}
        />
      ))}
    </View>
  );
}

export function SpotPositionTile({
  spotMarket,
  vault,
}: {
  spotMarket: DriftSpotMarket;
  vault: Address;
}) {
  const mint = spotMarket.mint;
  const token = useSuspenseToken({ mint });
  const balance = useBalance({ vault, mint });

  const { displayBalance } = useBalanceSettings();

  if (token === null) return null;
  const totalAmount = (balance?.driftEarn?.total ?? 0) / 10 ** token.decimals;
  if (totalAmount === 0) return null;

  return (
    <View
      style={{
        borderRadius: 28,
        borderCurve: "continuous",
      }}
    >
      <Row justify="space-between">
        <Row gap={8}>
          <CoinLogo mint={mint} size={32} />
          <View>
            <Row gap={2}>
              <Text colorToken="systemGreen" size={12}>
                {formatPercent(LendingApy.total(spotMarket.apy))}%
              </Text>
              <Text colorToken="textSecondary" size={12}>
                APY
              </Text>
            </Row>
            <Text variant="semibold" size={14}>
              {token?.symbol ?? abbreviateAddress(mint)}
            </Text>
          </View>
        </Row>
        <View style={{ alignItems: "flex-end" }}>
          <TextFadeCents size={14} variant="semibold">
            {displayBalance(formatUsdValue(totalAmount))}
          </TextFadeCents>
        </View>
      </Row>
    </View>
  );
}

export function AboutDrift() {
  return (
    <View gap={12}>
      <Text variant="semibold" style={{ fontSize: 16 }}>
        About
      </Text>
      <P3 colorToken="textSecondary">
        Drift is an exchange and decentralized money markets protocol that
        supports the borrowing and lending of assets. Fuse integrates with
        Drift's Lend/Borrow product where your assets are deposited to Drift and
        automatically earn yield based on supply and borrower demand. Learn more
        about borrowing and lending on Drift{" "}
        <Link
          href="https://docs.drift.trade/lend-borrow/lend-borrow-faq"
          target="_blank"
        >
          <P3
            colorToken="textSecondary"
            style={{ textDecorationLine: "underline" }}
          >
            here
          </P3>
        </Link>
        .{"\n\n"}Note that deposited funds are subject to protocol and smart
        contract risks that could result in loss of funds. Learn more about
        these potential risks{" "}
        <Link
          href="https://fusewallet.com/support/understanding-risk-with-defi"
          target="_blank"
        >
          <P3
            colorToken="textSecondary"
            style={{ textDecorationLine: "underline" }}
          >
            here
          </P3>
        </Link>
        .
      </P3>
    </View>
  );
}

export function AboutLulo() {
  return (
    <View gap={12}>
      <Text variant="semibold" style={{ fontSize: 16 }}>
        About
      </Text>
      <P3 colorToken="textSecondary">
        Lulo dynamically allocates funds across Kamino, Drift, MarginFi, and
        Save based on current yields and market conditions. This integration
        uses Protected Deposits within Lulo’s dual deposit structure.{"\n\n"}
        Protected Deposits are called “protected” because Boosted Deposits serve
        as first-loss capital in the event of smart contract exploits, oracle
        failures, and bad debt events directly impacting the underlying
        protocols that Lulo rebalances across.{"\n\n"}These mitigations do not
        apply to stablecoin depegs, Solana network failures, multiple underlying
        protocols failing simultaneously, failures within Lulo’s own smart
        contracts, or any losses that exceed the protections provided by Boosted
        Deposits.{"\n\n"}Use these links to learn more about{" "}
        <Link href="https://docs.lulo.fi/protect" target="_blank">
          <P3
            colorToken="textSecondary"
            style={{ textDecorationLine: "underline" }}
          >
            Lulo Protect
          </P3>
        </Link>{" "}
        and{" "}
        <Link
          href="https://fusewallet.com/support/understanding-risk-with-defi"
          target="_blank"
        >
          <P3
            colorToken="textSecondary"
            style={{ textDecorationLine: "underline" }}
          >
            DeFi risks
          </P3>
        </Link>
        .
      </P3>
    </View>
  );
}

export function AboutKamino() {
  return (
    <View gap={12}>
      <Text variant="semibold" style={{ fontSize: 16 }}>
        About
      </Text>
      <P3 colorToken="textSecondary">
        Kamino is a money markets protocol for trading, borrowing, and lending
        assets. Fuse integrates with Kamino Earn to deposit your assets into
        yield-generating vaults, with returns based on supply and borrower
        demand. Learn more about Kamino's lending platform{" "}
        <Link href="https://docs.kamino.finance" target="_blank">
          <P3
            colorToken="textSecondary"
            style={{ textDecorationLine: "underline" }}
          >
            here
          </P3>
        </Link>
        .{"\n\n"}
        Note that deposited funds are subject to protocol and smart contract
        risks that could result in loss of funds. Learn more about these
        potential risks{" "}
        <Link
          href="https://fusewallet.com/support/understanding-risk-with-defi"
          target="_blank"
        >
          <P3
            colorToken="textSecondary"
            style={{ textDecorationLine: "underline" }}
          >
            here
          </P3>
        </Link>
        .
      </P3>
    </View>
  );
}

function DriftSpotMarkets() {
  const { spotMarkets } = useSuspenseDriftSpotMarkets();

  return (
    <View gap={20}>
      <Text variant="semibold" style={{ fontSize: 16 }}>
        Available markets
      </Text>
      {spotMarkets.map((spotMarket) => (
        <SpotMarketTile key={spotMarket.mint} spotMarket={spotMarket} />
      ))}
    </View>
  );
}

function SpotMarketTile({ spotMarket }: { spotMarket: DriftSpotMarket }) {
  const mint = spotMarket.mint;
  const token = useSuspenseToken({ mint });

  if (token === null) {
    return null;
  }

  return (
    <View
      style={{
        borderRadius: 28,
        borderCurve: "continuous",
      }}
    >
      <Row justify="space-between">
        <Row gap={8}>
          <CoinLogo mint={mint} size={32} />
          <View>
            <Text variant="semibold" style={{ fontSize: 14 }}>
              {token?.symbol ?? abbreviateAddress(mint)}
            </Text>
          </View>
        </Row>
        <View style={{ alignItems: "flex-end" }}>
          <Row gap={2}>
            <Text colorToken="textSecondary" variant="medium">
              APY
            </Text>
            <Text colorToken="systemGreen" variant="medium">
              {formatPercent(LendingApy.total(spotMarket.apy))}%
            </Text>
          </Row>
        </View>
      </Row>
    </View>
  );
}

function LuloPools() {
  const luloPools = useSuspenseLuloPools();

  return (
    <View gap={20}>
      <Text variant="semibold" style={{ fontSize: 16 }}>
        Available pools
      </Text>

      <PoolTile mint={mints.usdc} apy={luloPools.protected.apy} />
    </View>
  );
}

function KaminoPools() {
  const kaminoVault = useSuspenseKaminoVault();

  return (
    <View gap={20}>
      <Text variant="semibold" style={{ fontSize: 16 }}>
        Available vaults
      </Text>

      <PoolTile mint={mints.usdc} apy={kaminoVault.apy} />
    </View>
  );
}

function PoolTile({ mint, apy }: { mint: Address; apy: number }) {
  const token = useSuspenseToken({ mint });

  if (token === null) {
    return null;
  }

  return (
    <View
      style={{
        borderRadius: 28,
        borderCurve: "continuous",
      }}
    >
      <Row justify="space-between">
        <Row gap={8}>
          <CoinLogo mint={mint} size={32} />
          <View>
            <Text variant="semibold" style={{ fontSize: 14 }}>
              {token?.symbol ?? abbreviateAddress(mint)}
            </Text>
          </View>
        </Row>
        <View style={{ alignItems: "flex-end" }}>
          <Row gap={2}>
            <Text colorToken="textSecondary" variant="medium">
              APY
            </Text>
            <Text colorToken="systemGreen" variant="medium">
              {formatPercent(apy)}%
            </Text>
          </Row>
        </View>
      </Row>
    </View>
  );
}
