import { Redirect } from "expo-router";
import { useWalletStatus } from "~/state/wallet";
export { ErrorBoundary } from "~/components/errors/RootErrorBoundary";

export default function Root() {
  const walletStatus = useWalletStatus();

  // return <Redirect href={"/unlocked/bridge/card/onboarding/introduction"} />;

  switch (walletStatus.status) {
    case "deviceKeyError":
      return <Redirect href="/locked/device-key-unavailable" />;

    case "active":
      return <Redirect href="/unlocked/root/home" />;

    case "inactive":
      return <Redirect href="/welcome" />;

    default:
      return walletStatus satisfies never;
  }
}
