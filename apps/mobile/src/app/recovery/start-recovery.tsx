import {
  SafeAreaView,
  Text,
  ThemeOverrideContext,
  useColor,
  View,
} from "~/components/Themed";
import { Button } from "~/components/Button";
import { Alert, StyleSheet } from "react-native";
import { router } from "expo-router";
import { useRef } from "react";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import { executeAction, prepareAction, RecoveryKey } from "~/services/wallets";
import { Address } from "@squads/models/solana";
import {
  awaitRecoveryStatus,
  reloadWalletRecoveryState,
  useWalletRecoveryState,
} from "~/state/walletRecovery";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { WELCOME_HEADER_HEIGHT } from "~/constants/sizes";
import { useUsdBalance } from "~/state/balances";
import { formatUsdValue } from "@squads/utils/numberFormats";

import { useGenerateDeviceKey, useMaybeDeviceKey } from "~/state/deviceKey";

import * as Clipboard from "expo-clipboard";
import { useToast } from "~/components/Toaster";
import { TouchableScale } from "~/components/TouchableScale";
import { RecoveryInitiateModal } from "~/components/RecoveryInitiateModal";
import * as Device from "expo-device";
import { KeysView } from "~/components/KeysView";
import { BottomModalImperativeMethods } from "~/components/BottomModal";
import * as Haptics from "expo-haptics";
import { useRecoveryState } from "~/app/recovery/_layout";
import { useGenerateCloudKey, useMaybeCloudKey } from "~/state/cloudKey";
import { RecoveringKeyDetails } from "~/services/recovery";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import invariant from "invariant";

export default function StartRecoveryScreen() {
  const [{ recovery, wallet }, setRecoveryState] = useRecoveryState();
  const queryClient = useQueryClient();

  const walletKey = wallet.walletKey;
  const hasRecoveryKeys = wallet.keys.recoveryKeys !== null;

  const generateCloudKey = useGenerateCloudKey();
  const generateDeviceKey = useGenerateDeviceKey();

  const maybeDeviceKey = useMaybeDeviceKey();
  const maybeCloudKey = useMaybeCloudKey();

  const activeRecovery = useWalletRecoveryState({
    walletKey: wallet.walletKey,
  });

  const createAndSendRecoveryTx = async (newKey: RecoveringKeyDetails) => {
    const action = await prepareAction({
      type: "recoveryInit",
      newKey,
    });

    const signedTransactions = await signTransactionsWithFuseKeys(
      wallet.keys,
      action.transactions
    );

    const { activityId } = await executeAction({
      signedTransactions,
    });

    await awaitRecoveryStatus(
      queryClient,
      { walletKey, activityId },
      hasRecoveryKeys ? "active" : "approved"
    );

    return activityId;
  };

  const recoveryMutation = useMutation({
    mutationFn: async (recoveryKey: RecoveryKey | null) => {
      Haptics.selectionAsync();

      if (hasRecoveryKeys) {
        invariant(recoveryKey !== null, "Recovery key needs to be selected");
      }

      const newKey: RecoveringKeyDetails =
        recovery.recoveredKeyType === "deviceKey"
          ? {
              keyType: "deviceKey",
              address:
                maybeDeviceKey ??
                ((await generateDeviceKey()).toBase58() as Address),
              deviceName: Device.deviceName ?? "Unknown device",
              modelName: Device.modelName ?? "Unknown device",
            }
          : {
              keyType: "cloudKey",
              address:
                maybeCloudKey ??
                ((await generateCloudKey()).toBase58() as Address),
              details: { type: "iCloud" },
            };

      // Check if there's an active recovery proposal already created.
      const activeRecovery = await reloadWalletRecoveryState(queryClient, {
        walletKey,
      });

      const expectedStatus = hasRecoveryKeys ? "active" : "approved";
      const activityId =
        activeRecovery &&
        activeRecovery.status === expectedStatus &&
        activeRecovery.newKey.keyType === recovery.recoveredKeyType
          ? activeRecovery.activityId
          : await createAndSendRecoveryTx(newKey);

      setRecoveryState({ activityId, recoveryKey: recoveryKey?.address });

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      if (hasRecoveryKeys) {
        return router.push("/recovery/recovery-in-progress");
      } else {
        return router.replace("/recovery/complete-recovery");
      }
    },
  });

  const key =
    recovery.recoveredKeyType === "deviceKey"
      ? recovery.cloudKey.details.type === "iCloud"
        ? "Cloud"
        : "Ledger"
      : "Device";

  const recoveryKeys = wallet.keys.recoveryKeys;

  const recoveryInitiateModalRef = useRef<BottomModalImperativeMethods>(null);

  return (
    <ThemeOverrideContext.Provider value={"dark"}>
      <SafeAreaView style={{ flex: 1 }}>
        <View style={styles.container}>
          <View style={styles.headingContainer}>
            <Text variant="display-bold" style={styles.heading}>
              Start your recovery process
            </Text>
            <Text
              variant="medium"
              style={styles.subheading}
              colorToken="textSecondary"
            >
              Associated Fuse wallet{"\n"}
              with your {key} key
            </Text>
          </View>
          <View style={styles.keyContainer}>
            <WalletRecoveryView vaultKey={wallet.defaultVault} />
            <KeysView
              variant={{
                deviceKey:
                  recovery.recoveredKeyType === "deviceKey"
                    ? "default"
                    : "darken",
                cloudKey:
                  recovery.recoveredKeyType === "cloudKey"
                    ? "default"
                    : "darken",
                recoveryKey: "darken",
              }}
              deviceKey={
                recovery.recoveredKeyType === "deviceKey"
                  ? "recovery"
                  : recovery.deviceKey.address
              }
              cloudKey={
                recovery.recoveredKeyType === "cloudKey"
                  ? "recovery"
                  : recovery.cloudKey.address
              }
              cloudKeyLabel={
                recovery.recoveredKeyType === "cloudKey"
                  ? "Cloud"
                  : recovery.cloudKey.details.type === "ledger"
                    ? "2FA Ledger"
                    : "Cloud"
              }
              recoveryKeys={recoveryKeys}
            />
          </View>
          <View style={styles.buttons}>
            <Button
              variant="primary"
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                recoveryInitiateModalRef.current?.present();
              }}
              loading={recoveryMutation.isPending}
            >
              Start Recovery
            </Button>
          </View>
        </View>

        <RecoveryInitiateModal
          modalRef={recoveryInitiateModalRef}
          recoveredKeyType={"deviceKey"}
          recoveryKeys={recoveryKeys}
          onConfirm={(recoveryKey) => {
            recoveryInitiateModalRef.current?.close();
            recoveryMutation.mutateAsync(recoveryKey).catch((err) => {
              Alert.alert("Failed to start recovery", String(err));
            });
          }}
        />
      </SafeAreaView>
    </ThemeOverrideContext.Provider>
  );
}

function WalletRecoveryView({ vaultKey }: { vaultKey: Address }) {
  const { toast } = useToast();
  const borderColor = useColor("border");
  const usdcBalance = useUsdBalance({ address: vaultKey });

  const formattedValue = formatUsdValue(usdcBalance, {
    renderCurrency: false,
    minimumFractionDigits: 2,
  });

  return (
    <View
      style={{
        padding: 20,
        borderRadius: 20,
        borderColor,
        borderWidth: 1,
        gap: 12,
      }}
    >
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <TouchableScale
          onPress={() => {
            Clipboard.setStringAsync(vaultKey);
            toast.info("Copied to clipboard", { id: "copy-address" });
          }}
        >
          <Text variant="medium" style={{ fontSize: 18 }}>
            {abbreviateAddress(vaultKey)}
          </Text>
        </TouchableScale>
        <Text
          variant="medium"
          style={{ fontSize: 18 }}
          colorToken={"textSecondary"}
        >
          Wallet 1
        </Text>
      </View>
      <Text variant="semibold" style={{ fontSize: 26 }}>
        ${formattedValue}
      </Text>
    </View>
  );
}

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    paddingTop: WELCOME_HEADER_HEIGHT,
    gap: 24,
  },
  headingContainer: {
    paddingTop: 20,
    gap: 24,
  },
  heading: {
    fontSize: 45,
    letterSpacing: -1.5,
    lineHeight: 45,
  },
  subheading: {
    fontSize: 18,
    lineHeight: 21,
  },
  keyContainer: {
    flex: 1,
    justifyContent: "space-between",
    gap: 8,
  },
  buttons: {
    gap: 8,
  },
});
