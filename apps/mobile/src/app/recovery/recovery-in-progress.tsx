import {
  SafeAreaView,
  Text,
  ThemeOverrideContext,
  View,
} from "~/components/Themed";
import { Button } from "~/components/Button";
import Colors from "~/constants/Colors";
import { StyleSheet } from "react-native";
import { useEffect } from "react";
import { TouchableScale } from "~/components/TouchableScale";
import * as Clipboard from "expo-clipboard";
import { useToast } from "~/components/Toaster";
import { Icon } from "~/components/Icon";
import { useLazyRecoveryStatus } from "~/state/walletRecovery";
import { router } from "expo-router";
import { WELCOME_HEADER_HEIGHT } from "~/constants/sizes";
import { WalletQrCode } from "~/components/WalletQrCode";
import { Address } from "@squads/models/solana";
import { useMutation } from "@tanstack/react-query";
import { RecoveryKey } from "~/services/wallets";
import { sendRecoveryEmail } from "~/services/magicLink";
import { appConfig } from "~/state/config";
import invariant from "invariant";
import MagicLinkCodeConfirmation from "~/components/MagicLinkCodeConfirmation";
import { Spinner } from "~/components/Spinner";
import { useRecoveryState } from "~/app/recovery/_layout";
import { sendTurnkeyRecoveryEmail } from "~/state/turnkey";
import TurnkeyOTPConfirmation from "~/components/TurnkeyOTPConfirmation";
import { useCheckRecoveryStatusEffect } from "~/hooks/useCheckRecoveryStatusEffect";
import { RecoveryByKeystone } from "~/components/SignWithKeystone";
import { hapticSelect, hapticSuccess } from "~/utils/haptics";

export default function RecoveryInProgressScreen() {
  const [{ wallet, recoveryKey: recoveryKeyAddress, activityId }] =
    useRecoveryState();

  invariant(recoveryKeyAddress, "recoveryKeyAddress is not set");

  const recoveryKeys = wallet.keys.recoveryKeys;
  invariant(recoveryKeys, "expected recovery keys");

  const recoveryKey = recoveryKeys.find(
    (key) => key.address === recoveryKeyAddress
  );
  invariant(recoveryKey, "invalid recovery key address");

  //checking if the recovery is cancelled
  useCheckRecoveryStatusEffect({ walletKey: wallet.walletKey, activityId });

  return (
    <ThemeOverrideContext.Provider value={"dark"}>
      <SafeAreaView style={{ flex: 1 }}>
        {recoveryKey.recoveryKeyType === "selfCustody" ? (
          <RecoveryByPublicKey walletKey={wallet.walletKey} />
        ) : recoveryKey.recoveryKeyType === "email" ? (
          <RecoveryByMagicLink recoveryKey={recoveryKey} />
        ) : recoveryKey.recoveryKeyType === "turnkeyEmail" ? (
          <RecoveryTurnkeyEmail
            walletKey={wallet.walletKey}
            recoveryKey={recoveryKey}
          />
        ) : recoveryKey.recoveryKeyType === "keystone" ? (
          <RecoveryByKeystoneView recoveryKey={recoveryKey} />
        ) : null}
      </SafeAreaView>
    </ThemeOverrideContext.Provider>
  );
}

function RecoveryByKeystoneView({
  recoveryKey,
}: {
  recoveryKey: RecoveryKey & { recoveryKeyType: "keystone" };
}) {
  const [{ activityId }] = useRecoveryState();
  invariant(activityId, "activityId is not set");

  return (
    <View
      gap={24}
      style={{ flex: 1, padding: 20, paddingTop: WELCOME_HEADER_HEIGHT }}
    >
      <RecoveryByKeystone
        recoveryKey={recoveryKey}
        activityId={activityId}
        onSuccessRedirect={"/recovery/complete-recovery"}
        themeOverride="dark"
      />
    </View>
  );
}

function RecoveryByMagicLink({
  recoveryKey,
}: {
  recoveryKey: RecoveryKey & { recoveryKeyType: "email" };
}) {
  const mutation = useMutation({
    mutationFn: async () => {
      return sendRecoveryEmail(recoveryKey.email);
    },
  });

  useEffect(() => {
    mutation.mutate();
  }, []);

  if (!mutation.data) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
        <Spinner size={24} colorToken={"text"} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <MagicLinkCodeConfirmation
        control={mutation.data}
        onSuccess={() => {
          hapticSuccess();
          router.replace("/recovery/complete-recovery");
        }}
      />
    </View>
  );
}

function RecoveryTurnkeyEmail({
  walletKey,
  recoveryKey,
}: {
  walletKey: Address;
  recoveryKey: RecoveryKey & { recoveryKeyType: "turnkeyEmail" };
}) {
  const [, updateRecoveryState] = useRecoveryState();
  const sendRecoveryEmailMutation = useMutation({
    mutationFn: async () => {
      return sendTurnkeyRecoveryEmail({ walletKey, recoveryKey });
    },
  });

  useEffect(() => {
    sendRecoveryEmailMutation.mutate();
  }, []);

  if (!sendRecoveryEmailMutation.data) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
        <Spinner size={24} colorToken={"text"} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <TurnkeyOTPConfirmation
        walletKey={walletKey}
        control={sendRecoveryEmailMutation.data}
        onSuccess={(stamper) => {
          hapticSuccess();
          updateRecoveryState({ stamper });

          router.replace("/recovery/complete-recovery");
        }}
      />
    </View>
  );
}

function RecoveryByPublicKey({ walletKey }: { walletKey: Address }) {
  const [{ activityId }] = useRecoveryState();
  invariant(activityId, "activityId is not set");

  const { data: proposal } = useLazyRecoveryStatus({
    walletKey: walletKey,
    activityId: activityId,
  });
  const isApproved = proposal?.status === "approved";
  const url = `${appConfig.fuseWebUrl}/${walletKey}/${activityId}`;

  return (
    <View style={styles.container}>
      <View style={styles.headingContainer}>
        <Text variant="display-bold" style={styles.heading}>
          Confirm Recovery
        </Text>
        <Text variant={"medium"} style={styles.subheading}>
          Use your Recovery Key to confirm{"\n"}
          recovery transaction by following{"\n"}
          the link or scanning QR code below
        </Text>
      </View>
      <CopyableText label={"Link"} text={url} />
      <View style={styles.keyContainer}>
        <View style={{ padding: 10, borderRadius: 10 }}>
          <WalletQrCode value={url} logo={"dark"} size={4} />
        </View>
      </View>
      <View style={styles.buttons}>
        <Button
          disabled={!isApproved}
          variant="primary"
          onPress={() => {
            hapticSelect();
            router.replace("/recovery/complete-recovery");
          }}
        >
          Next
        </Button>
      </View>
    </View>
  );
}

export function CopyableText({
  label,
  text,
}: {
  label?: string;
  text: string;
}) {
  const { toast } = useToast();

  return (
    <TouchableScale
      activeScale={0.98}
      onPress={() => {
        Clipboard.setStringAsync(text);
        toast.info("Copied to clipboard", { id: "copy-address" });
      }}
      hitSlop={12}
      style={{ gap: 8 }}
    >
      {label && (
        <Text
          darkColor={Colors.dark.textSecondary}
          lightColor={Colors.dark.textSecondary}
        >
          {label}
        </Text>
      )}
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Text
          darkColor={Colors.dark.text}
          lightColor={Colors.dark.text}
          variant="medium"
          style={{ fontSize: 18, maxWidth: "90%" }}
          numberOfLines={1}
        >
          {text}
        </Text>
        <Icon
          color={Colors.dark.text}
          name="doc.on.doc"
          size={11}
          weight="medium"
        />
      </View>
    </TouchableScale>
  );
}

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    paddingTop: WELCOME_HEADER_HEIGHT,
    gap: 24,
  },
  headingContainer: {
    paddingTop: 20,
    gap: 24,
  },
  heading: {
    fontSize: 45,
    letterSpacing: -1.5,
    lineHeight: 45,
  },
  subheading: {
    fontSize: 15,
    lineHeight: 20,
  },
  keyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 8,
  },
  buttons: {
    gap: 8,
  },
});
