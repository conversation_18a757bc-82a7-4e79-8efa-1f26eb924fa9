import { Stack } from "expo-router";
import { DURATION_FAST } from "~/constants/animations";

import { useWalletStatus } from "~/state/wallet";
import { Redirect } from "~/components/Redirect";

export const unstable_settings = {
  initialRouteName: "index",
};

export default function WelcomeLayout() {
  const walletStatus = useWalletStatus();

  if (walletStatus.status === "active") {
    return <Redirect href="/unlocked/root/home" />;
  }

  return (
    <>
      <Stack
        screenOptions={{
          headerShown: false,
          animation: "simple_push",
          animationDuration: DURATION_FAST,
          statusBarStyle: "light",
        }}
      >
        <Stack.Screen name="index" options={{ title: "Welcome to Fuse" }} />
      </Stack>
    </>
  );
}
