import { router } from "expo-router";
import * as Haptics from "expo-haptics";
import {
  SafeAreaView,
  Text,
  ThemeOverrideContext,
  View,
} from "~/components/Themed";
import { Button } from "~/components/Button";
import Colors from "~/constants/Colors";
import { FuseLogo } from "~/components/icons/FuseLogo";
import { StyleSheet } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useWalletByAddress, useWalletByMember } from "~/state/wallet";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { loadBalances } from "~/state/balances";
import Animated, {
  Easing,
  FadeInDown,
  interpolate,
  ReduceMotion,
  useAnimatedProps,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";
import { DURATION_SLOW } from "~/constants/animations";
import { loadICloudStatus, useMaybeCloudKey } from "~/state/cloudKey";
import { useMaybeDeviceKey } from "~/state/deviceKey";
import { RecoveryDetails, RecoveryRouteParams } from "~/app/recovery/_layout";
import { reloadWalletRecoveryState } from "~/state/walletRecovery";
import { ReactNode, Suspense, useEffect, useRef } from "react";
import { TouchableScale } from "~/components/TouchableScale";
import {
  authenticateDeviceKey,
  authenticateICloudKey,
  authenticateLedgerKey,
} from "~/state/auth";
import { Recovery2FAKeyModal } from "~/components/Recovery2FAKeyModal";
import { BottomModalImperativeMethods } from "~/components/BottomModal";
import { ICloudNotAvailableModal } from "~/components/ICloudNotAvailableModal";
import { RecoveryWithoutICloudConsentModal } from "~/components/RecoveryWithoutICloudConsentModal";
import { WelcomeScreen3dScene } from "~/components/welcome/WelcomeScreen3dScene";
import { ErrorBoundary } from "react-error-boundary";
import { isDevice } from "expo-device";
import { Spinner } from "~/components/Spinner";

const AnimatedText = Animated.createAnimatedComponent(Text);

export default function WelcomeScreen() {
  const stepProgress = useSharedValue(0);

  function setStep(s: 1 | 2) {
    stepProgress.value = withTiming(s, {
      easing: Easing.inOut(Easing.ease),
      duration: 800,
    });
  }

  // On mount, animate into the "welcome" step
  useEffect(() => {
    stepProgress.value = withTiming(1, {
      duration: 2000,
    });
  }, []);

  const welcomeAnimatedStyles = useAnimatedStyle(() => {
    return {
      opacity: interpolate(stepProgress.value, [0, 1, 1.5], [0, 1, 0]),
    };
  });
  const welcomeAnimatedProps = useAnimatedProps(() => {
    return {
      pointerEvents:
        stepProgress.value >= 1.5 ? ("none" as const) : ("auto" as const),
    };
  });

  // const enterInviteCodeAnimatedStyles = useAnimatedStyle(() => {
  //   return {
  //     opacity: interpolate(stepProgress.value, [1.5, 2], [0, 1]),
  //   };
  // });
  // const enterInviteCodeAnimatedProps = useAnimatedProps(() => {
  //   return {
  //     pointerEvents:
  //       stepProgress.value < 1.5 ? ("none" as const) : ("auto" as const),
  //   };
  // });

  // const inviteCodeFromDeepLink = useInviteCodeFromDeepLink();

  return (
    <ThemeOverrideContext.Provider value={"dark"}>
      <SafeAreaView style={{ flex: 1 }}>
        <View style={StyleSheet.absoluteFill}>
          <ErrorBoundary
            onError={(err) =>
              console.error("Error in WelcomeScreen3dScene", err)
            }
            FallbackComponent={() => null}
          >
            <Suspense fallback={null}>
              {/* disable 3d for simulator */}
              {isDevice ? <WelcomeScreen3dScene step={stepProgress} /> : null}
            </Suspense>
          </ErrorBoundary>
        </View>
        <LinearGradient
          colors={[
            "transparent",
            Colors.dark.background,
            Colors.dark.background,
          ]}
          locations={[0, 0.7, 1]}
          style={{
            position: "absolute",
            bottom: 0,
            left: 0,
            right: 0,
            height: 360,
          }}
        />
        <View style={{ flex: 1 }}>
          {/*Welcome*/}
          <Animated.View
            animatedProps={welcomeAnimatedProps}
            style={[StyleSheet.absoluteFill, welcomeAnimatedStyles]}
          >
            <Welcome
              onCreateNewWalletPress={async () => {
                // if (inviteCodeFromDeepLink.status === "valid") {
                //   router.replace("/onboarding/how-it-works");
                //   return;
                // }
                //
                // setStep(2);

                router.push("/onboarding/device-key");
              }}
            />
          </Animated.View>
          {/*Enter Invite Code*/}
          {/*<Animated.View*/}
          {/*  animatedProps={enterInviteCodeAnimatedProps}*/}
          {/*  style={[StyleSheet.absoluteFill, enterInviteCodeAnimatedStyles]}*/}
          {/*>*/}
          {/*  <EnterInviteCode*/}
          {/*    inviteCodeFromDeepLink={*/}
          {/*      inviteCodeFromDeepLink.status === "valid"*/}
          {/*        ? inviteCodeFromDeepLink.value*/}
          {/*        : undefined*/}
          {/*    }*/}
          {/*    stepProgress={stepProgress}*/}
          {/*    onBackPress={() => {*/}
          {/*      setStep(1);*/}
          {/*    }}*/}
          {/*  />*/}
          {/*</Animated.View>*/}
        </View>
      </SafeAreaView>
    </ThemeOverrideContext.Provider>
  );
}

async function authenticateWallet(recovery: RecoveryDetails) {
  if (recovery.recoveredKeyType !== "deviceKey") {
    return await authenticateDeviceKey();
  }

  if (recovery.cloudKey.details.type === "iCloud") {
    return await authenticateICloudKey();
  }

  if (recovery.cloudKey.details.type === "ledger") {
    return await authenticateLedgerKey({
      ledgerKey: {
        address: recovery.cloudKey.address,
        derivationPath: recovery.cloudKey.details.derivationPath,
        deviceId: recovery.cloudKey.details.deviceId,
      },
    });
  }

  throw new Error(`Unsupported recovery key: ${recovery.recoveredKeyType}`);
}

function Welcome({
  onCreateNewWalletPress,
}: {
  onCreateNewWalletPress: () => Promise<void>;
}) {
  const cloudKeyModalRef = useRef<BottomModalImperativeMethods>(null);
  const iCloudNotAvailableModalRef = useRef<BottomModalImperativeMethods>(null);
  const recoveryWithoutICloudConsentModalRef =
    useRef<BottomModalImperativeMethods>(null);

  const queryClient = useQueryClient();
  const maybeDeviceKey = useMaybeDeviceKey();
  const { walletKey: walletKeyByDeviceKey } = useWalletByMember({
    memberKey: maybeDeviceKey,
  });
  const { wallet: walletByDeviceKey } = useWalletByAddress({
    walletKey: walletKeyByDeviceKey,
  });

  const maybeCloudKey = useMaybeCloudKey();
  const { walletKey: walletKeyByCloudKey } = useWalletByMember({
    memberKey: maybeCloudKey,
  });

  const isRecoveryByCloudKey =
    walletKeyByDeviceKey === null && walletKeyByCloudKey !== null;

  const isCloudRecoveryByDeviceKey =
    walletByDeviceKey !== null &&
    walletByDeviceKey.keys.cloudKey.details.type === "iCloud" &&
    walletKeyByCloudKey !== walletKeyByDeviceKey;

  const textEntryAnimation = FadeInDown.duration(DURATION_SLOW)
    .delay(100)
    .easing(Easing.inOut(Easing.ease))
    .reduceMotion(ReduceMotion.Never);

  const createWalletMutation = useMutation({
    mutationFn: onCreateNewWalletPress,
  });

  const startRecoveryMutation = useMutation({
    mutationFn: async (recovery: RecoveryDetails) => {
      console.debug(`starting recovery for ${recovery.recoveredKeyType}`);
      const wallet = await authenticateWallet(recovery);

      await loadBalances({
        address: wallet.defaultVault,
        queryClient,
      });

      const maybeNewKey =
        recovery.recoveredKeyType === "deviceKey"
          ? maybeDeviceKey
          : maybeCloudKey;

      const walletKey = wallet.walletKey;
      const [activeRecovery] = await Promise.all([
        reloadWalletRecoveryState(queryClient, { walletKey }),
      ]);

      const proposalStatus = activeRecovery?.status;
      console.debug(
        "starting recovery",
        JSON.stringify({ maybeNewKey, activeRecovery, proposalStatus }, null, 2)
      );

      // Recovery tx is already approved by third-party key
      if (
        activeRecovery &&
        activeRecovery.status == "approved" &&
        activeRecovery.newKey.keyType === recovery.recoveredKeyType
      ) {
        router.replace({
          pathname: "/recovery/complete-recovery",
          params: {
            args: JSON.stringify({
              recovery,
              activityId: activeRecovery.activityId,
            } satisfies RecoveryRouteParams),
          },
        });
      } else {
        router.push({
          pathname: "/recovery/start-recovery",
          params: {
            args: JSON.stringify({ recovery } satisfies RecoveryRouteParams),
          },
        });
      }
    },
  });

  return (
    <>
      <View style={[styles.container]}>
        <View style={styles.logoWrapper}>
          <FuseLogo size={32} color={Colors.dark.text} />
        </View>
        <View style={styles.headingContainer}>
          <View gap={8}>
            <Text
              variant="semibold"
              style={{
                textAlign: "center",
                letterSpacing: -0.3,
              }}
              size={18}
              darkColor={Colors.dark.textSecondary}
              lightColor={Colors.dark.textSecondary}
            >
              Welcome to
            </Text>
            <Text
              style={{
                textAlign: "center",
                fontSize: 64,
                letterSpacing: -2,
                lineHeight: 64,
              }}
              darkColor={Colors.dark.text}
              lightColor={Colors.dark.text}
              variant="heavy"
            >
              Fuse
            </Text>
          </View>
        </View>

        <View style={styles.buttons}>
          {isRecoveryByCloudKey ? (
            <View style={{ gap: 20 }}>
              <View>
                <AnimatedText
                  variant="medium"
                  colorToken="textSecondary"
                  style={{
                    fontSize: 15,
                    alignSelf: "center",
                    textAlign: "center",
                  }}
                  entering={textEntryAnimation}
                >
                  We have found a Cloud Key
                  {"\n"}associated with an existing Fuse wallet
                </AnimatedText>
              </View>

              <Button
                loading={startRecoveryMutation.isPending}
                onPress={() => {
                  Haptics.selectionAsync();
                  startRecoveryMutation.mutate({
                    recoveredKeyType: "deviceKey",
                    cloudKey: {
                      address: maybeCloudKey!,
                      details: { type: "iCloud" },
                    },
                  });
                }}
                variant="primary"
              >
                Recover Existing Wallet
              </Button>
            </View>
          ) : isCloudRecoveryByDeviceKey ? (
            <View style={{ gap: 20 }}>
              <View>
                <AnimatedText
                  variant="medium"
                  colorToken="textSecondary"
                  style={{
                    fontSize: 15,
                    alignSelf: "center",
                    textAlign: "center",
                  }}
                  entering={textEntryAnimation}
                >
                  {maybeCloudKey
                    ? `Your iCloud account is not associated with this Fuse wallet. Proceed with recovery to update Access keys.`
                    : `We have found an existing\nFuse Wallet associated with this device`}
                </AnimatedText>
              </View>

              <Button
                loading={startRecoveryMutation.isPending}
                onPress={() => {
                  Haptics.selectionAsync();
                  startRecoveryMutation.mutate({
                    recoveredKeyType: "cloudKey",
                    deviceKey: {
                      address: maybeDeviceKey!,
                    },
                  });
                }}
                variant="primary"
              >
                Recover Existing Wallet
              </Button>
            </View>
          ) : (
            <View gap={20}>
              {!startRecoveryMutation.isPending && (
                <Button
                  variant="primary"
                  loading={createWalletMutation.isPending}
                  onPress={async () => {
                    Haptics.selectionAsync();

                    const iCloudStatus = await loadICloudStatus({
                      queryClient,
                    });
                    if (iCloudStatus !== "available") {
                      iCloudNotAvailableModalRef.current?.present();
                      return;
                    }

                    createWalletMutation.mutate();
                  }}
                >
                  Create New Wallet
                </Button>
              )}
              <TransparentButton
                loading={startRecoveryMutation.isPending}
                onPress={async () => {
                  Haptics.selectionAsync();

                  const iCloudStatus = await loadICloudStatus({ queryClient });
                  if (iCloudStatus !== "available") {
                    recoveryWithoutICloudConsentModalRef.current?.present();
                    return;
                  }

                  cloudKeyModalRef?.current?.present();
                }}
              >
                Recover Existing Wallet
              </TransparentButton>
            </View>
          )}
        </View>
        <Recovery2FAKeyModal
          modalRef={cloudKeyModalRef}
          onConfirm={(cloudKey) => {
            cloudKeyModalRef?.current?.dismiss();
            startRecoveryMutation.mutate({
              recoveredKeyType: "deviceKey",
              cloudKey,
            });
          }}
        />
      </View>
      <ICloudNotAvailableModal modalRef={iCloudNotAvailableModalRef} />
      <RecoveryWithoutICloudConsentModal
        modalRef={recoveryWithoutICloudConsentModalRef}
        onDismiss={() => {
          cloudKeyModalRef?.current?.present();
        }}
      />
    </>
  );
}

function TransparentButton({
  onPress,
  children,
  loading,
}: {
  onPress: () => void;
  children: ReactNode;
  loading?: boolean;
}) {
  return (
    <TouchableScale
      onPress={onPress}
      disabled={loading}
      style={{
        alignSelf: "center",
        justifyContent: "center",
        paddingHorizontal: 18,
        height: 32,
      }}
    >
      {loading ? (
        <Spinner colorToken={"textSecondary"} size={24} />
      ) : (
        <Text
          colorToken={"textSecondary"}
          variant="medium"
          style={{ fontSize: 15 }}
        >
          {children}
        </Text>
      )}
    </TouchableScale>
  );
}

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    paddingTop: 40,
  },
  logoWrapper: {
    alignItems: "center",
  },
  headingContainer: {
    flex: 1,
    justifyContent: "center",
    paddingBottom: 60,
  },
  subheading: {
    fontSize: 15,
    lineHeight: 20,
    maxWidth: 250,
  },
  inputContainer: {
    flex: 1,
    paddingTop: 32,
  },
  buttons: {
    gap: 8,
  },
});
