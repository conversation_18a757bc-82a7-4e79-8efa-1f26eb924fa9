import { SafeAreaView, Text, View } from "~/components/Themed";
import { Button } from "~/components/Button";
import {
  FC,
  PropsWithChildren,
  RefObject,
  useCallback,
  useId,
  useRef,
  useState,
} from "react";
import {
  CloudKeyData,
  cloudQueryKey,
  useMaybeCloudKey,
  useTryCloudKey,
} from "~/state/cloudKey";
import {
  DeviceKeyData,
  deviceQueryKey,
  useDeviceKey,
  useMaybeDeviceKey,
} from "~/state/deviceKey";
import { Alert, Keyboard, Linking, ScrollView, Settings } from "react-native";
import { FuseSuspense } from "~/components/FuseSuspense";
import {
  QueryClient,
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import * as FuseKeyring from "@sqds/expo-fuse-keyring";
import * as Clipboard from "expo-clipboard";
import { toast } from "~/components/Toaster";
import { TouchableScale } from "~/components/TouchableScale";
import { useWalletByMember } from "~/state/wallet";
import { appConfig, iCloudContainerId } from "~/state/config";
import { Flex, Row } from "~/components/Grid";
import * as SecureStore from "expo-secure-store";
import { deleteAuthToken } from "~/state/auth-store";
import {
  ACTION_CARD_TYPES,
  setActionCardVisibilityPreference,
} from "~/components/home/<USER>";
import { useURL } from "expo-linking";
import { Icon } from "~/components/Icon";
import { H3 } from "~/components/typography/H3";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import { Address, AddressZ } from "@squads/models/solana";
import { P3 } from "~/components/typography/P3";
import { z } from "zod";
import { refetchBalances, useSolBalance } from "~/state/balances";
import {
  BottomModal,
  BottomModalImperativeMethods,
} from "~/components/BottomModal";
import { validationError } from "~/utils/form";
import { useFormik } from "formik";
import { toFormikValidationSchema } from "zod-formik-adapter";
import * as Haptics from "expo-haptics";
import { executeAction, prepareAction } from "~/services/wallets";
import { confirmActivity } from "~/services/activities";
import { TextInput } from "~/components/TextInput";
import { RecipientForm } from "~/components/account/RecipientForm";
import { saveRecentAddress } from "~/components/account/AddressesList";
import { formatSolAmount } from "~/utils/tokens";
import { FuseErrorBoundary } from "~/components/errors/FuseErrorBoundary";
import { authenticateDeviceKey, authenticateICloudKey } from "~/state/auth";
import { queryClient } from "~/state/queryClient";
import { replaceAll } from "~/utils/router";
import { generateDeviceToken } from "@sqds/expo-fuse-app-attest";

export default function Playground() {
  const deepLinkedUrl = useURL();

  if (
    appConfig.environment === "production" &&
    !deepLinkedUrl?.includes("dangerous-i-know-what-im-doing")
  ) {
    return null;
  }

  return (
    <FuseSuspense fallback={<Text>loading...</Text>}>
      <Welcome />
    </FuseSuspense>
  );
}

function WalletView() {
  const maybeDeviceKey = useMaybeDeviceKey();
  const { walletKey: walletKeyByDeviceKey } = useWalletByMember({
    memberKey: maybeDeviceKey,
  });

  const maybeCloudKey = useMaybeCloudKey();
  const { walletKey: walletKeyByCloudKey } = useWalletByMember({
    memberKey: maybeCloudKey,
  });

  const walletKey = walletKeyByDeviceKey ?? walletKeyByCloudKey;
  const { data: wallet } = useSuspenseQuery({
    queryKey: ["playground/wallet", walletKey],
    queryFn: () => {
      if (walletKeyByDeviceKey) {
        return authenticateDeviceKey();
      } else if (walletKeyByCloudKey) {
        return authenticateICloudKey();
      }
      return null;
    },
  });

  const relayerKey = wallet?.keys?.relayerKey?.address ?? null;

  const withdrawRelayerModalRef = useRef<BottomModalImperativeMethods>(null);

  return (
    <>
      <Row justify={"space-between"}>
        <P3 colorToken={"textSecondary"}>Wallet key</P3>
        {walletKey ? (
          <CopiableAddress address={walletKey} />
        ) : (
          <P3>No Wallet</P3>
        )}
      </Row>
      <View gap={12}>
        <Row justify={"space-between"}>
          <P3 colorToken={"textSecondary"}>Relayer key</P3>
          {relayerKey ? (
            <CopiableAddress address={relayerKey} />
          ) : (
            <P3>No Relayer</P3>
          )}
        </Row>
        {relayerKey && (
          <Row justify={"space-between"}>
            <Balance address={relayerKey} />
            <Button
              size={"medium"}
              variant={"primary"}
              onPress={() => withdrawRelayerModalRef.current?.present()}
            >
              Withdraw
            </Button>
            <WithdrawRelayerModal
              relayerKey={relayerKey}
              modalRef={withdrawRelayerModalRef}
            />
          </Row>
        )}
      </View>
    </>
  );
}

function Balance({ address }: { address: Address }) {
  const {
    solBalance: { amount: relayerBalance },
  } = useSolBalance({ address });

  return (
    <P3 colorToken={"textSecondary"}>{formatSolAmount(relayerBalance)}</P3>
  );
}

function CopiableAddress({ address }: { address: Address }) {
  return (
    <TouchableScale
      onPress={() => {
        Clipboard.setStringAsync(address);
        toast.info("Copied to clipboard", { id: "copy-address" });
      }}
    >
      <P3>{abbreviateAddress(address)}</P3>
    </TouchableScale>
  );
}

export function Welcome() {
  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView>
        <View
          style={{
            flex: 1,
            padding: 20,
            gap: 16,
          }}
        >
          {/*header*/}
          <Row>
            <Flex style={{ alignItems: "center" }}>
              <Text variant="medium" style={{ fontSize: 24 }}>
                Control panel
              </Text>
            </Flex>
            <TouchableScale
              onPress={() => replaceAll("/")}
              style={{
                position: "absolute",
                alignItems: "flex-start",
                justifyContent: "center",
              }}
            >
              <Icon
                name={"arrow.backward"}
                size={16}
                rectSize={32}
                weight="medium"
              />
            </TouchableScale>
          </Row>

          {/*body*/}
          <H3>Keys</H3>
          <Row justify="space-between">
            <P3 colorToken={"textSecondary"}>Device Key</P3>
            <FuseSuspense fallback={null}>
              <DeviceKeyActions />
            </FuseSuspense>
          </Row>

          <Row justify="space-between" gap={12}>
            <P3 colorToken={"textSecondary"}>Cloud Key</P3>
            <FuseSuspense fallback={null}>
              <CloudKeyActions />
            </FuseSuspense>
          </Row>

          <View style={{ paddingVertical: 12 }}>
            <ResetBothKeys />
          </View>

          <FuseErrorBoundary
            FallbackComponent={({ error }) => <P3>{String(error)}</P3>}
          >
            <FuseSuspense fallback={<P3>loading...</P3>}>
              <WalletView />
            </FuseSuspense>
          </FuseErrorBoundary>

          <H3>Actions</H3>
          <Button
            size={"medium"}
            variant="secondary"
            onPress={() => Linking.openURL("app-settings:")}
          >
            Open app settings
          </Button>

          <Row>
            <Flex>
              <Button
                size={"medium-new"}
                variant="primary"
                onPress={async () => {
                  const deviceCheckToken = await generateDeviceToken();
                  if (deviceCheckToken.ok) {
                    Clipboard.setStringAsync(deviceCheckToken.value);
                  } else {
                    Alert.alert("failed to generate device check token");
                  }
                }}
              >
                Copy device check token
              </Button>
            </Flex>
          </Row>

          <Row>
            <Flex>
              <Button
                size={"medium-new"}
                variant="primary"
                onPress={() => {
                  Settings.set({ SPONSORSHIP_PROMO_SEEN: false });
                }}
              >
                Reset fuse plus promo modal
              </Button>
            </Flex>
            <Flex>
              <Button
                size={"medium-new"}
                variant="primary"
                onPress={() => {
                  Settings.set({ CARD_INTRO_ANIMATION_SEEN: false });
                }}
              >
                Reset card animation
              </Button>
            </Flex>
          </Row>

          <Row gap={8}>
            <Flex>
              <Button
                size={"medium-new"}
                variant="danger"
                onPress={() => {
                  ACTION_CARD_TYPES.forEach((type) =>
                    setActionCardVisibilityPreference(type, true)
                  );
                }}
              >
                Reset action cards
              </Button>
            </Flex>
            <Flex>
              <Button
                size={"medium-new"}
                variant="danger"
                onPress={() => queryClient.clear()}
              >
                Reset query cache
              </Button>
            </Flex>
          </Row>

          <Row gap={8}>
            <Flex>
              <Button
                size={"medium-new"}
                variant="secondary"
                onPress={async () => {
                  const jwt = await SecureStore.getItem("AUTH_TOKEN");
                  jwt && Clipboard.setStringAsync(jwt);
                }}
              >
                Copy jwt token
              </Button>
            </Flex>
            <Flex>
              <Button
                size={"medium-new"}
                variant="danger"
                onPress={async () => {
                  Alert.alert(
                    "Reset jwt token",
                    "Confirm resetting jwt token",
                    [
                      { text: "Dismiss", style: "cancel" },
                      {
                        text: "Confirm",
                        onPress: async () => {
                          await SecureStore.deleteItemAsync("AUTH_TOKEN");
                        },
                        isPreferred: true,
                      },
                    ]
                  );
                }}
              >
                Reset jwt token
              </Button>
            </Flex>
          </Row>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

function ResetBothKeys() {
  const queryClient = useQueryClient();

  return (
    <ActionableButton
      variant={"danger"}
      action={async () => {
        Alert.alert(
          "Reset Device & Cloud Keys",
          "Resetting Device & Cloud Keys. This operation cannot be undone.",
          [
            { text: "Dismiss", style: "cancel" },
            {
              text: "Confirm",
              onPress: async () => {
                await resetCloudKey(queryClient);
                await resetDeviceKey(queryClient);
                await deleteAuthToken();

                replaceAll("/");
              },
              isPreferred: true,
            },
          ]
        );
      }}
    >
      Reset Device & Cloud Keys
    </ActionableButton>
  );
}

const ActionableButton: FC<
  PropsWithChildren<{
    variant?: "primary" | "secondary" | "danger";
    action: () => Promise<any>;
  }>
> = ({ variant, action, children }) => {
  const [isLoading, setLoading] = useState(false);

  const onPress = () => {
    setLoading(true);
    action().finally(() => setLoading(false));
  };

  return (
    <Button size={"medium-new"} variant={variant} onPress={onPress}>
      {isLoading ? "loading..." : children}
    </Button>
  );
};

const DeviceKeyActions = () => {
  const deviceKey = useDeviceKey();
  const resetDeviceKey = useResetDeviceKey();

  if (deviceKey) {
    return (
      <>
        <CopiableAddress address={deviceKey} />
        <ActionableButton action={resetDeviceKey}>Reset</ActionableButton>
      </>
    );
  }

  return <Text>No Device Key</Text>;
};

const CloudKeyActions = () => {
  const maybeCloudKey = useTryCloudKey();
  const resetCloudKey = useResetCloudKey();

  if (maybeCloudKey.type === "error") {
    return (
      <Flex>
        <Text>iCloud is not available: {String(maybeCloudKey.error)}</Text>
      </Flex>
    );
  }

  if (maybeCloudKey.key) {
    return (
      <>
        <CopiableAddress address={maybeCloudKey.key} />
        <ActionableButton action={resetCloudKey.mutateAsync}>
          Reset
        </ActionableButton>
      </>
    );
  }
  return <Text>No Cloud Key</Text>;
};

async function resetCloudKey(queryClient: QueryClient) {
  await FuseKeyring.deleteCloudKey(iCloudContainerId);
  queryClient.setQueryData<CloudKeyData>(cloudQueryKey, {
    type: "success",
    key: null,
  });
}

export const useResetCloudKey = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      Alert.alert(
        "Reset Cloud Key",
        "Confirm resetting Cloud Key. This operation cannot be undone.",
        [
          { text: "Dismiss", style: "cancel" },
          {
            text: "Confirm",
            onPress: async () => {
              await resetCloudKey(queryClient);
              await deleteAuthToken();
            },
            isPreferred: true,
          },
        ]
      );
    },
  });
};

async function resetDeviceKey(queryClient: QueryClient) {
  await FuseKeyring.deleteDeviceKey();
  queryClient.setQueryData<DeviceKeyData>(deviceQueryKey, {
    type: "success",
    key: null,
  });
}

export function useResetDeviceKey() {
  const queryClient = useQueryClient();

  return useCallback(async () => {
    Alert.alert(
      "Reset Device Key",
      "Confirm resetting Device Key. This operation cannot be undone.",
      [
        { text: "Dismiss", style: "cancel" },
        {
          text: "Confirm",
          onPress: async () => {
            await resetDeviceKey(queryClient);
            await deleteAuthToken();
          },
          isPreferred: true,
        },
      ]
    );
  }, [queryClient]);
}

export function WithdrawRelayerModal({
  modalRef,
  relayerKey,
}: {
  modalRef: RefObject<BottomModalImperativeMethods | null>;
  relayerKey: Address;
}) {
  const id = useId();
  const queryClient = useQueryClient();

  const formik = useFormik({
    initialValues: {
      address: "" as Address,
    },

    validationSchema: toFormikValidationSchema(
      z.object({
        address: AddressZ,
      })
    ),
    validateOnChange: true,
    validateOnBlur: true,

    async onSubmit({ address }) {
      Keyboard.dismiss();

      const action = await prepareAction({
        type: "relayerWithdraw",
        to: address,
      });

      const { activityId } = await executeAction({
        signedTransactions: action.transactions,
      });

      const status = await confirmActivity({ activityId });
      if (status.type !== "confirmed") {
        toast.error("Failed to withdraw from relayer");
        return;
      }

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      await refetchBalances({ queryClient, address: relayerKey });
      saveRecentAddress(address);
      modalRef.current?.dismiss();
    },
  });

  return (
    <BottomModal
      modalRef={modalRef}
      modalId={id}
      title="Withdraw Relayer"
      body={
        <Flex gap={32}>
          {formik.values.address ? (
            <TextInput
              readOnly
              placeholder={"Recipient"}
              autoCorrect={false}
              spellCheck={false}
              error={validationError(formik.errors.address)}
              right={
                <TouchableScale
                  style={{ paddingHorizontal: 14 }}
                  hitSlop={{ top: 16, bottom: 16 }}
                  onPress={async () => {
                    formik.setFieldValue("address", "");
                  }}
                >
                  <P3>Edit</P3>
                </TouchableScale>
              }
              value={formik.values.address}
            />
          ) : (
            <RecipientForm
              showScan={false}
              onSelect={(address) => {
                formik.setFieldValue("address", address);
              }}
            />
          )}
        </Flex>
      }
      footer={
        <Button
          disabled={!formik.isValid}
          loading={formik.isSubmitting}
          variant="primary"
          onPress={formik.handleSubmit}
        >
          Withdraw
        </Button>
      }
    />
  );
}
