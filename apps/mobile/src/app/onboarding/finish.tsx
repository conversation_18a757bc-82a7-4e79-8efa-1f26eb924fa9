import { H1 } from "~/components/typography/H1";
import { View } from "~/components/Themed";
import * as Haptics from "expo-haptics";
import { OnboardingContainer } from "~/components/onboarding/OnboardingContainer";
import { useState } from "react";
import { Button } from "~/components/Button";
import { Image } from "expo-image";
import { resetRecoveryKey } from "~/state/recoveryKey";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useDeviceKey } from "~/state/deviceKey";
import { useCloudKey } from "~/state/cloudKey";
import invariant from "invariant";
import * as wallets from "~/services/wallets";
import { createInitV2 } from "~/services/wallets";
import { updateAuthenticatedWallet } from "~/state/auth";
import { preloadWalletData } from "~/utils/preload";
import { toast } from "~/components/Toaster";
import { Message } from "~/components/Message";
import * as Device from "expo-device";
import { signTransactionsWithFuseKeys } from "~/services/transactions";
import { isClientErrorReasonType } from "~/services/utils";
import { refetchWalletByKeys } from "~/state/wallet";
import { fetchCreateKey, removeCreateKeyFromCache } from "~/state/createKey";
import image from "~assets/images/onboarding/finish.png";
import { H5 } from "~/components/typography/H5";
import { P3 } from "~/components/typography/P3";
import { replaceAll } from "~/utils/router";
import { generateDeviceToken } from "@sqds/expo-fuse-app-attest";
import { reportError } from "~/utils/errors";

const statuses = [
  "Initiating...",
  "Setting up smart account...",
  "Confirming transaction...",
  "Finalizing...",
] as const;

type Status = (typeof statuses)[number];

export default function FinishOnboardingScreen() {
  const queryClient = useQueryClient();

  const deviceKey = useDeviceKey();
  const cloudKey = useCloudKey();
  // const [recoveryKey] = useState(getOnboardingRecoveryKey());

  const [status, setStatus] = useState<Status | null>(null);

  invariant(deviceKey, "deviceKey is not set");
  invariant(cloudKey, "cloudKey is not set");
  // invariant(recoveryKey, "recoveryKey is not set");

  const { mutate: onFinish, isPending } = useMutation({
    mutationFn: async () => {
      Haptics.selectionAsync();

      setStatus("Initiating...");
      // If this step takes longer than 10 seconds, we update the status to indicate that something is happening.
      const takingLongerTimeout = setTimeout(
        () => setStatus("Setting up smart account..."),
        10_000
      );

      const { createKey, keyId, assertion } = await fetchCreateKey({
        queryClient,
      });

      const deviceCheckToken = Device.isDevice
        ? await generateDeviceToken()
        : ({ ok: true, value: "not_used" } as const);

      if (!deviceCheckToken.ok) {
        toast.error("Device check failed. Please reach out to support.", {
          id: "create_wallet_failed",
        });

        reportError(queryClient, new Error(deviceCheckToken.error), {});
        return;
      }

      // Get the transactions for wallet creation.
      const { transactions } = await createInitV2({
        keyId,
        createKey,
        assertion,
        deviceKey: {
          address: deviceKey,
          deviceName: Device.deviceName || "Unknown device",
          modelName: Device.modelName || "Unknown model",
        },
        cloudKey: {
          address: cloudKey,
        },
        deviceCheckToken: deviceCheckToken.value,
        // recoveryKey,
      });

      clearTimeout(takingLongerTimeout);

      const signedTransactions = await signTransactionsWithFuseKeys(
        {
          cloudKey: { address: cloudKey, details: { type: "iCloud" } },
          deviceKey: { address: deviceKey },
        },
        transactions
      );

      setStatus("Confirming transaction...");
      setTimeout(() => setStatus("Finalizing..."), 10_000);
      const { wallet, token } = await wallets.createConfirmV2({
        keyId,
        createKey,
        assertion,
        transactions: signedTransactions,
      });

      removeCreateKeyFromCache({ queryClient });
      await updateAuthenticatedWallet(deviceKey, token, wallet);
      await preloadWalletData({ queryClient, wallet });

      resetRecoveryKey();

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      replaceAll("/unlocked/root/home");
    },
    onError: async (error) => {
      setStatus(null);

      if (isClientErrorReasonType(error, "walletsLimitReached")) {
        toast.error(error.toMessage(), {
          id: "create_wallet_failed",
        });
        return;
      }

      toast.error("Failed to create wallet. Please try again.", {
        id: "create_wallet_failed",
      });

      if (isClientErrorReasonType(error, "walletExists")) {
        await refetchWalletByKeys(queryClient);
        replaceAll("/welcome");
      }
    },
  });

  return (
    <OnboardingContainer>
      <View gap={32}>
        <H1 style={{ textAlign: "center" }}>You're all set!</H1>
        <H5 style={{ textAlign: "center" }}>
          You have successfully generated your Device and 2FA Key! These keys
          control access to your wallet.
        </H5>
      </View>

      <Image style={{ flex: 1 }} contentFit="contain" source={image} />

      <P3
        colorToken="textSecondary"
        style={{ textAlign: "center", paddingBottom: 20 }}
      >
        Create your wallet and add Recovery Keys to enable multifactor
        authentication and advanced recovery.
      </P3>

      <View gap={12}>
        <Message
          colorToken="textSecondary"
          style={{ textAlign: "center", fontSize: 15 }}
          variant="medium"
        >
          {status ?? " "}
        </Message>
        <Button
          variant="primary"
          onPress={() => onFinish()}
          loading={isPending}
        >
          Create Smart Account
        </Button>
      </View>
    </OnboardingContainer>
  );
}
