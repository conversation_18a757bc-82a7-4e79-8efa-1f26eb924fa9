import { DefaultTheme } from "@react-navigation/native";

const tintColorLight = "#000";
const tintColorDark = "#fff";

export type ColorName = keyof typeof Colors.light & keyof typeof Colors.dark;

const Colors = {
  light: {
    transparent: "transparent",
    purple: "#B271FF",
    blue: "#007AFF",
    red: "#FF7272",
    systemRed: "#eb4e34",
    green: "#41BE6B",
    systemGreen: "#34C759",
    yellow: "#FFCC00",
    newYellow: "#F5BD46",
    orange: "#FF9500",
    newRed: "#FF7272",

    text: "#1F1E1E",
    textOpposite: "#FFFFFF",
    textSecondary: "rgba(31,30,30,0.4)",
    textSecondaryOpposite: "rgba(255, 255, 255, 0.4)",
    textSecondaryV2: hexToRgba("#1F1E1E", 0.3),
    textTertiary: "rgba(31,30,30,0.25)",
    textDisabled: "rgba(31,30,30,0.10)",
    textError: "#FF7272",
    textWarning: "#E5AD41",
    textButtonPrimary: "#fff",
    textButtonSecondary: "#000",
    textButtonDanger: "#fff",
    textButtonSuccess: "#fff",
    textButtonPrimaryOpposite: "#000",
    textButtonSecondaryOpposite: "#fff",
    textToastInfo: "#1F1E1E",
    // textToastInfo: "#fff",
    textToastSuccess: "#41BE6B",
    textToastDanger: "#FF7272",
    textToastWarning: "#E5AD41",

    iconDanger: "#FF7272",
    iconWarning: "#E5AD41",

    background: "#F3F3F3", // rgb(243,243,243)
    backgroundCard: "#181718",
    backgroundCardSecondary: "rgba(245,245,245,0.2)",
    backgroundSecondary: "#EBEBEB",
    backgroundTertiary: "#F7F7F7",
    backgroundBlue: "#EAEEF2",
    backgroundNewYellow: "#F3F0EA",
    backgroundRed: "#F4EDED",
    backgroundBanner: "#fff",
    backgroundProgressBar: "#fff",
    backgroundProgressBarLevel: "#C7C7C7",
    backgroundButtonPrimary: "#000",
    backgroundButtonPrimaryOpposite: "#fff",
    backgroundButtonSecondary: "#EBEBEB",
    backgroundButtonDanger: "#FF7272",
    backgroundButtonSuccess: "#41BE6B",
    backgroundButtonSecondaryOpposite: "rgba(243, 243, 243, 0.1)",
    backgroundTabBar: "rgba(239,239,239,0.6)",
    backgroundToastInfo: "rgba(31,30,30,0.07)",
    backgroundToastInfoOpaque: "#EBEBEB",
    backgroundToastSuccess: "rgba(65,190,107,0.2)",
    backgroundToastSuccessOpaque: "#D0E9D8",
    backgroundToastDanger: "rgba(255,114,114,0.2)",
    backgroundToastDangerOpaque: "#F6DADA",
    backgroundToastWarning: "rgba(229,173,65,0.1)",
    backgroundToastWarningOpaque: "#F1E6D0",
    backgroundIconDanger: "#F6DADA",
    backgroundIconWarning: "#F1E6D0",
    backgroundBottomSheet: "#F3F3F3",

    shadowModal: "#000",
    shadowToastInfo: "#838383",
    shadowToastSuccess: "#41be6b",
    shadowToastDanger: "#FF7272",

    tint: tintColorLight,
    tabIconDefault: "#ccc",
    tabIconSelected: tintColorLight,

    border: "#d8d8d8",
    borderHard: "#A5A5A5",
    borderSecondary: "#EAEBE6",
    borderError: "#FF7272",
    borderWarning: "#E5AD41",

    listSeparator: "#EAEBE6",
    dashedListSeparator: "#DEDEDE",
    dashedListSeparatorOpposite: "#333333",
  } as const,
  dark: {
    transparent: "transparent",
    purple: "#CAB2F5",
    blue: "#2775CA",
    red: "#DA637A",
    systemRed: "#eb4e34",
    green: "#41BE6B",
    systemGreen: "#30D158",
    yellow: "#FFD60A",
    newYellow: "#F5BD46",
    orange: "#FF9F0A",
    newRed: "#FF7272",

    text: "#F3F3F3",
    textOpposite: "#1F1E1E",
    textSecondary: "rgba(255, 255, 255, 0.5)",
    textSecondaryOpposite: "rgba(0, 0, 0, 0.6)",
    textSecondaryV2: hexToRgba("#F3F3F3", 0.5),
    textTertiary: "rgba(255, 255, 255, 0.25)",
    textDisabled: "rgba(255, 255, 255, 0.15)",
    textError: "#FF7272",
    textWarning: "#E5AD41",
    textButtonPrimary: "#000",
    textButtonPrimaryOpposite: "#fff",
    textButtonSecondary: "#fff",
    textButtonDanger: "#fff",
    textButtonSuccess: "#fff",
    textButtonSecondaryOpposite: "#000",
    textToastInfo: "#1F1E1E",
    textToastSuccess: "#41BE6B",
    textToastDanger: "#FF7272",
    textToastWarning: "#E5AD41",

    iconDanger: "#FF7272",
    iconWarning: "#E5AD41",

    background: "#0C0B0B",
    backgroundCard: "#fff",
    backgroundCardSecondary: "rgba(245,245,245,0.8)",
    backgroundSecondary: "#2D2D2D",
    backgroundTertiary: "rgba(255,255,255,0.1)",
    backgroundBlue: "#1A1D21",
    backgroundNewYellow: "#2A2825",
    backgroundRed: "#2A1F1F",
    backgroundBanner: "#000",
    backgroundProgressBar: "#000",
    backgroundProgressBarLevel: "#404040",
    backgroundButtonPrimary: "#fff",
    backgroundButtonPrimaryOpposite: "#000",
    backgroundButtonSecondary: "#2D2D2D",
    backgroundButtonDanger: "#FF7272",
    backgroundButtonSuccess: "#41BE6B",
    backgroundButtonSecondaryOpposite: "#EAEBE6",
    backgroundTabBar: "rgba(45,45,45,0.3)",
    backgroundToastInfo: "#F3F3F3",
    backgroundToastInfoOpaque: "#F3F3F3",
    backgroundToastSuccess: "#17301F",
    backgroundToastSuccessOpaque: "#17301F",
    backgroundToastDanger: "#3D2121",
    backgroundToastDangerOpaque: "#3D2121",
    backgroundToastWarning: "#382D17",
    backgroundToastWarningOpaque: "#382D17",
    backgroundIconDanger: "#3D2121",
    backgroundIconWarning: "#3d3421",
    backgroundBottomSheet: "#232222",

    shadowModal: "#000",
    shadowToastInfo: "#838383",
    shadowToastSuccess: "#41BE6B",
    shadowToastDanger: "#FF7272",

    tint: tintColorDark,
    tabIconDefault: "#ccc",
    tabIconSelected: tintColorDark,

    border: "#2D2F33",
    borderHard: "#A5A5A5", // FIXME: NOT ADAPTED
    borderSecondary: "rgba(243, 243, 243, 0.25)",
    borderError: "#FF7272",
    borderWarning: "#E5AD41",

    listSeparator: "#333333",
    dashedListSeparator: "#333333",
    dashedListSeparatorOpposite: "#DEDEDE",
  } as const,
} as const;

export const NavigationTheme = {
  dark: {
    dark: true,
    colors: {
      primary: tintColorDark,
      background: Colors.dark.background,
      card: Colors.dark.background,
      text: Colors.dark.text,
      border: Colors.dark.border,
      notification: "rgb(255, 69, 58)",
    },
    fonts: DefaultTheme.fonts,
  },
  light: {
    dark: false,
    colors: {
      primary: tintColorLight,
      background: Colors.light.background,
      card: Colors.light.background,
      text: Colors.light.text,
      border: Colors.light.border,
      notification: "rgb(255, 59, 48)",
    },
    fonts: DefaultTheme.fonts,
  },
};

export default Colors;

export function hexToRgba(hex: string, alpha: number): string {
  hex = hex.replace(/^#/, "");

  let r = parseInt(hex.substring(0, 2), 16);
  let g = parseInt(hex.substring(2, 4), 16);
  let b = parseInt(hex.substring(4, 6), 16);

  return `rgba(${r},${g},${b},${alpha})`;
}
