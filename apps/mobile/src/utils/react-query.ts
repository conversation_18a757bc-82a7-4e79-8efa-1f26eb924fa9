import { useCallback, useRef } from "react";
import { useFocusEffect } from "expo-router";

export function useFocusNotifyOnChangeProps() {
  const focusedRef = useRef(true);

  useFocusEffect(
    useCallback(() => {
      focusedRef.current = true;

      return () => {
        focusedRef.current = false;
      };
    }, [])
  );

  //don't notify on any changes if not focused
  if (!focusedRef.current) {
    return [];
  }

  //keep the default notification behavior
  return undefined;
}

export function useIsFocused() {
  const focusedRef = useRef(true);

  useFocusEffect(
    useCallback(() => {
      focusedRef.current = true;

      return () => {
        focusedRef.current = false;
      };
    }, [])
  );

  return focusedRef.current;
}
