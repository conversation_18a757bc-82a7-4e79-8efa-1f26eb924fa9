import { z, ZodDiscriminatedUnion, ZodDiscriminatedUnionOption } from "zod";
import { DateTime } from "luxon";

export const DateTimeZ = z
  .string()
  .transform((val) => DateTime.fromISO(val))
  .brand<"DateTime">();

export const IsoDateTime = z.string().datetime({ offset: true });

export type IsoDateTime = z.infer<typeof IsoDateTime>;

export function zodArrayIgnoreUnknown<
  Discriminator extends string,
  Options extends ZodDiscriminatedUnionOption<Discriminator>[],
>(zodUnion: ZodDiscriminatedUnion<Discriminator, Options>) {
  const isKnownItem = (item: unknown) => {
    const isSuccess = zodUnion.safeParse(item).success;
    if (!isSuccess) {
      console.warn("Unknown item in array", item);
    }
    return isSuccess;
  };

  return z.preprocess(
    (val) => toSafeArray(val).filter(isKnownItem),
    z.array(zodUnion)
  );
}

function toSafeArray(item: unknown): Array<unknown> {
  if (isArray(item)) {
    return item;
  }
  return [item];
}

function isArray<T>(item: unknown): item is Array<T> {
  return Array.isArray(item);
}
