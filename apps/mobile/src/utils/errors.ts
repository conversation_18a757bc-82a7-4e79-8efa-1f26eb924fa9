import * as Sentry from "@sentry/react-native";
import { isEmbeddedLaunch, updateId } from "expo-updates";
import { Wallet } from "~/services/wallets";
import { QueryClient, useQueryClient } from "@tanstack/react-query";

// eventId by error
export const seenErrors = new WeakMap<Error, string>();

export function reportError(
  queryClient: QueryClient,
  error: Error,
  extra: Record<string, unknown> = {}
) {
  console.error("[reportError]", error, extra);

  const isErrorObject = typeof error === "object" && error !== null;

  const seenEventId = isErrorObject ? seenErrors.get(error) : null;
  if (seenEventId) {
    console.warn("Skipping reporting for duplicate error", error);
    return seenEventId;
  }

  const dataByKey = queryClient.getQueriesData({
    predicate: (query) =>
      query.queryKey[0] === "wallet" && query.queryKey[1] !== null,
  });
  const wallet = (dataByKey.at(0)?.[1] as { wallet: Wallet | null } | null)
    ?.wallet;

  const tags = {
    isEmbeddedLaunch,
    updateId,
    wallet: wallet?.walletKey,
    vault: wallet?.defaultVault,
  };

  const eventId = Sentry.captureException(error, { extra, tags });

  // WeakMap can only store objects as keys.
  if (isErrorObject) {
    seenErrors.set(error, eventId);
  }

  return eventId;
}

export function useReportError() {
  const queryClient = useQueryClient();

  function reportErrorWrapper(
    error: Error,
    extra: Record<string, unknown> = {}
  ) {
    return reportError(queryClient, error, extra);
  }

  return reportErrorWrapper;
}
