import { Animated, Easing, EasingFunction } from "react-native";

export type ColorStops = {
  [location: number]: string;
};

type GradientParams = {
  colorStops: ColorStops;
  extraColorStopsPerTransition?: number;
  easing?: EasingFunction;
};

const easeInOut = Easing.bezier(0.42, 0, 0.58, 1);

/**
 * Lifted from https://github.com/phamfoo/react-native-easing-gradient/blob/154828a3f339a15c54cea394d331a68774a540a8/src/index.ts
 */
export function easeGradient({
  colorStops,
  easing = easeInOut,
  extraColorStopsPerTransition = 12,
}: GradientParams): {
  colors: readonly string[];
  locations: readonly number[];
} {
  const colors: string[] = [];
  const locations: number[] = [];

  const initialLocations = Object.keys(colorStops)
    .map((key) => Number(key))
    .sort();

  const totalColorStops = initialLocations.length;

  for (
    let currentStopIndex = 0;
    currentStopIndex < totalColorStops - 1;
    currentStopIndex++
  ) {
    const startLocation = initialLocations[currentStopIndex];
    const endLocation = initialLocations[currentStopIndex + 1];

    const startColor = colorStops[startLocation];
    const endColor = colorStops[endLocation];

    const colorScale = createInterpolation({
      inputRange: [0, 1],
      outputRange: [startColor, endColor],
      easing: easing,
    });

    const currentTransitionLength = endLocation - startLocation;
    const stepSize = 1 / (extraColorStopsPerTransition + 1);

    for (
      let stepIndex = 0;
      stepIndex <= extraColorStopsPerTransition + 1;
      stepIndex++
    ) {
      const progress = stepIndex * stepSize;
      const color = colorScale(progress);
      colors.push(color);
      locations.push(startLocation + currentTransitionLength * progress);
    }
  }

  return { colors, locations };
}

// @ts-expect-error
const AnimatedInterpolation = Animated.Interpolation;

type ColorInterpolateFunction = (input: number) => string;

function createInterpolation(
  config: Animated.InterpolationConfigType
): ColorInterpolateFunction {
  if (AnimatedInterpolation.__createInterpolation) {
    return AnimatedInterpolation.__createInterpolation(config);
  }

  return (input) => {
    const interpolation = new AnimatedInterpolation(
      { __getValue: () => input },
      config
    );

    return interpolation.__getValue();
  };
}
