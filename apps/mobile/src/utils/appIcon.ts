import { getAppIconName, setAlternateAppIcon } from "expo-alternate-app-icons";
import { z } from "zod";

// Note: these must be synced with the icons configured in app.config.ts
export const AppIconId = z.enum(["icon", "app-icon-plus"]).nullable();
export type AppIconId = z.infer<typeof AppIconId>;

export { supportsAlternateIcons } from "expo-alternate-app-icons";

export function getAppIcon() {
  const currentIcon = getAppIconName();
  return AppIconId.parse(currentIcon);
}

export async function setAppIcon(icon: AppIconId) {
  return setAlternateAppIcon(icon);
}
