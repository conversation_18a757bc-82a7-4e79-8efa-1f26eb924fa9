/**
 * Takes a number and converts it to string, usually for use with formik forms.
 *
 * It is not just `String(num)` because when there are many front zeroes (like 0.000000001), the number is converted to scientific notation, and the string ends up being "1e-9", because javascript.
 * Info on the bug here: https://github.com/Squads-Protocol/fuse/pull/199
 */
export function numberToString(num: number): string {
  return num.toLocaleString("en-US", {
    useGrouping: false,
    maximumSignificantDigits: 21, // that's the maximum number javascript supports. https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/NumberFormat#maximumsignificantdigits
  });
}
