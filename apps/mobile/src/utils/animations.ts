import {
  BaseAnimationBuilder,
  FadeIn,
  FadeOut,
  ReduceMotion,
  useReducedMotion,
} from "react-native-reanimated";

/**
 * Returns an entry layout animation that is accessible for users with reduced motion enabled.
 * If the user has reduced motion enabled, the animation will be replaced by `FadeIn`.
 */
export function useAccessibleEntryAnimation(
  layoutAnimation: BaseAnimationBuilder
): BaseAnimationBuilder {
  const reduceMotion = useReducedMotion();

  if (reduceMotion) {
    return layoutAnimation.durationV != null
      ? FadeIn.duration(layoutAnimation.durationV).reduceMotion(
          ReduceMotion.Never
        )
      : FadeIn.createInstance();
  } else {
    return layoutAnimation;
  }
}

/**
 * Returns an exit layout animation that is accessible for users with reduced motion enabled.
 * If the user has reduced motion enabled, the animation will be replaced by `FadeOut`.
 */
export function useAccessibleExitAnimation(
  layoutAnimation: BaseAnimationBuilder
): BaseAnimationBuilder {
  const reduceMotion = useReducedMotion();

  if (reduceMotion) {
    return layoutAnimation.durationV != null
      ? FadeOut.duration(layoutAnimation.durationV).reduceMotion(
          ReduceMotion.Never
        )
      : FadeIn.createInstance();
  } else {
    return layoutAnimation;
  }
}

export function snapPoint(
  value: number,
  velocity: number,
  points: ReadonlyArray<number>
): number {
  "worklet";
  const point = value + 0.2 * velocity;
  const deltas = points.map((p) => Math.abs(point - p));
  const minDelta = Math.min.apply(null, deltas);
  return points.filter((p) => Math.abs(point - p) === minDelta)[0];
}
