import { QueryClient } from "@tanstack/react-query";
import { Wallet } from "~/services/wallets";
import { loadBalances, loadHistoricalBalances } from "~/state/balances";
import { loadTrustedTokens } from "~/state/tokens";
import { loadActivities } from "~/state/activities";
import { loadSpendingLimits } from "~/state/spendingLimits";
import {
  prefetchDriftSpotMarkets,
  prefetchKaminoVault,
  prefetchLuloPools,
} from "~/state/earn";
import {
  ensureCard,
  ensureVirtualAccountsNeedMigration,
  loadBridgeAccount,
  loadVirtualAccount,
} from "~/state/bridge";
import { ensureSubscription } from "~/state/subscription";

export async function preloadWalletData({
  queryClient,
  wallet,
}: {
  queryClient: QueryClient;
  wallet: Wallet;
}) {
  const walletKey = wallet.walletKey;

  await Promise.allSettled([
    loadBalances({
      queryClient,
      address: wallet.defaultVault,
    }),
    prefetchDriftSpotMarkets(queryClient),
    prefetchLuloPools(queryClient),
    prefetchKaminoVault(queryClient),
    //
    loadTrustedTokens({ queryClient }),
    loadActivities({ queryClient, walletKey }),
    loadHistoricalBalances(queryClient, {
      vault: wallet.defaultVault,
      period: "day",
    }),
    loadSpendingLimits(queryClient, {
      walletKey,
      vaultKey: wallet.defaultVault,
    }),
    loadBridgeAccount({ queryClient }),
    loadVirtualAccount(queryClient, { currency: "usd" }),
    loadVirtualAccount(queryClient, { currency: "eur" }),
    ensureCard({ queryClient }),
    ensureSubscription(queryClient),
    ensureVirtualAccountsNeedMigration(queryClient),
  ]);
}
