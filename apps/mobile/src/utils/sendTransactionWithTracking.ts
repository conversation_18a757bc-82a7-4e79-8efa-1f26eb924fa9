import {
  resetActiveTransaction,
  setActiveTransaction,
  updateActiveTransaction,
} from "~/hooks/useActiveTransaction";
import { Base64, executeAction } from "~/services/wallets";
import { toast } from "~/components/Toaster";
import { confirmActivity } from "~/services/activities";
import { sleep } from "~/utils/promise";
import { Address } from "@squads/models/solana";
import { refetchActivities, updateActivity } from "~/state/activities";
import { queryClient } from "~/state/queryClient";

export async function sendTransactionWithTrackingStatus({
  walletKey,
  transaction,
  sendSuccessMessage = "Transaction initiated",
  sendFailureMessage = "Failed to initiate transaction",
  confirmationFailureMessage,
  opaqueToasts = false,
}: {
  walletKey: Address;
  transaction: { type: "action"; signedTransactions: Base64[] };
  sendSuccessMessage?: string | false;
  sendFailureMessage?: string | ((err: Error) => string);
  confirmationFailureMessage: string | ((err: Error) => string);
  opaqueToasts?: boolean;
}) {
  let activityId: string;
  let toastId: string = "sendTransaction";
  try {
    setActiveTransaction();
    const responseData = await executeAction({
      signedTransactions: transaction.signedTransactions,
    });
    activityId = responseData.activityId;
    if (sendSuccessMessage) {
      toastId = toast.info(sendSuccessMessage, {
        duration: 2000,
        opaque: opaqueToasts,
      });
    }

    // Don't block
    const _ = refetchActivities({ queryClient, walletKey });
    // onSendSuccess?.();
  } catch (e) {
    toast.error(
      typeof sendFailureMessage === "string"
        ? sendFailureMessage
        : sendFailureMessage(e as Error),
      {
        id: toastId,
        opaque: opaqueToasts,
      }
    );
    resetActiveTransaction();
    return;
  }

  try {
    const status = await confirmActivity({ activityId });
    updateActivity(queryClient, {
      activityId,
      walletKey,
      activity: (activity) => {
        //do not modify swap status optimistically as there might be out amount updated
        if (activity.details.type === "swap") {
          return activity;
        }
        return { ...activity, status };
      },
    });

    if (status.type === "failed") {
      //fast forward to the catch block
      throw new Error(`Activity failed: ${status.reason}`);
    }

    if (status.type === "confirmed" || status.type === "cancelled") {
      updateActiveTransaction({ type: "confirmed" });
      return;
    }

    console.warn("Unexpected activity status type", status);
  } catch (e) {
    toast.error(
      typeof confirmationFailureMessage === "string"
        ? confirmationFailureMessage
        : confirmationFailureMessage(e as Error),
      {
        id: toastId,
        opaque: opaqueToasts,
      }
    );
    updateActiveTransaction({ type: "failed" });
    throw e;
  } finally {
    sleep(3000).then(() => resetActiveTransaction());
  }
}
