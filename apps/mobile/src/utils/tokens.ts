import { Address } from "@squads/models/solana";
import { mints } from "~/constants/tokens";
import { formatTokenAmount } from "@squads/utils/numberFormats";
import { LAMPORTS_PER_SOL } from "@solana/web3.js";

export function tokenUsdValue({
  amount,
  decimals,
  usdcPrice,
}: {
  decimals: number;
  amount?: number | null;
  usdcPrice?: number | null;
}) {
  return amount && usdcPrice ? (amount / 10 ** decimals) * usdcPrice : 0;
}

export function isStablecoin(mint: Address) {
  return [mints.usdc, mints.usdt].includes(mint);
}

export function getUnitAmount({
  amount,
  decimals,
}: {
  amount: number;
  decimals: number;
}) {
  return amount / 10 ** decimals;
}

export function formatSolAmount(
  lamports: number,
  options: { maximumFractionDigits?: number } = {}
) {
  const totalAmount = lamports / LAMPORTS_PER_SOL;

  return formatTokenAmount(totalAmount, "SOL", {
    maximumFractionDigits:
      options.maximumFractionDigits ?? getMaximumFractionDigits(totalAmount),
  });
}

export function formatSPLAmount({
  amount,
  decimals,
  symbol,
}: {
  amount: number;
  decimals: number;
  symbol?: string;
}) {
  const totalAmount = amount / 10 ** decimals;

  return formatTokenAmount(totalAmount, symbol, {
    maximumFractionDigits: getMaximumFractionDigits(totalAmount),
  });
}

export function getMaximumFractionDigits(totalAmount: number) {
  if (totalAmount === 0) {
    return 2;
  }

  return totalAmount < 1
    ? Math.max(-Math.floor(Math.log10(Math.abs(totalAmount))), 5)
    : Math.max(4 - Math.floor(Math.log10(Math.abs(totalAmount))), 0);
}
