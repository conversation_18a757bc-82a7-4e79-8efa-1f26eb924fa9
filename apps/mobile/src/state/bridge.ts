import {
  QueryClient,
  useInfiniteQuery,
  useQuery,
  useSuspenseQuery,
  UseSuspenseQueryOptions,
} from "@tanstack/react-query";
import {
  BridgeAccount,
  BridgeAccountCountry,
  Card,
  createBridgeAccount as sendCreateBridgeAccount,
  createExternalAccount as sendCreateExternalAccount,
  CreateExternalAccountRequest,
  createVirtualAccount as sendCreateVirtualAccount,
  Currency,
  deleteExternalAccount as sendDeleteExternalAccount,
  ExternalAccount,
  freezeCard as sendFreezeCard,
  getBridgeAccount,
  getBridgeFees,
  getCard,
  getCardBalance,
  getCardEvents,
  getCardSpending,
  getCardTransactionsByMonth,
  getCountriesList,
  getExchangeRate,
  getExternalAccounts,
  getVirtualAccount,
  getVirtualAccountsNeedMigration,
  KycEndorsement,
  requestCard as sendRequestCard,
  unfreezeCard as sendUnfreezeCard,
  VirtualAccount,
} from "~/services/bridge";
import { Settings } from "react-native";
import { updateWallet } from "~/state/wallet";
import { Address } from "@squads/models/solana";

type MaybeBridgeAccount = BridgeAccount | null;

const bridgeAccountQuery = ({
  refetchInterval,
}: {
  refetchInterval?: number;
} = {}) => {
  return {
    queryKey: ["bridge/bridge_account"],
    queryFn: async () => {
      const response = await getBridgeAccount();
      return response.account;
    },
    refetchInterval,
  } satisfies UseSuspenseQueryOptions<MaybeBridgeAccount>;
};

export function useBridgeAccount({
  refetchInterval,
}: {
  refetchInterval?: number;
} = {}) {
  return useSuspenseQuery(bridgeAccountQuery({ refetchInterval })).data;
}

export function useLazyBridgeAccount() {
  return useQuery(bridgeAccountQuery());
}

export async function loadBridgeAccount({
  queryClient,
}: {
  queryClient: QueryClient;
}) {
  return queryClient.ensureQueryData<MaybeBridgeAccount>(bridgeAccountQuery());
}

export async function reloadBridgeAccount(queryClient: QueryClient) {
  return await queryClient.fetchQuery<MaybeBridgeAccount>(bridgeAccountQuery());
}

export async function createBridgeAccount({
  queryClient,
  email,
  endorsements,
}: {
  queryClient: QueryClient;
  email: string;
  endorsements?: KycEndorsement[];
}) {
  const { account } = await sendCreateBridgeAccount({ email, endorsements });
  queryClient.setQueryData(bridgeAccountQuery().queryKey, account);
  return account;
}

type MaybeVirtualAccount = VirtualAccount | null;

const virtualAccountKeys = {
  all: ["bridge/virtual_account"],
  byCurrency: (currency: "usd" | "eur") => ["bridge/virtual_account", currency],
};

function virtualAccountQuery(params: { currency: "usd" | "eur" }) {
  return {
    queryKey: virtualAccountKeys.byCurrency(params.currency),
    queryFn: async () => {
      const response = await getVirtualAccount(params);
      return response.virtual_account;
    },
  } satisfies UseSuspenseQueryOptions<MaybeVirtualAccount>;
}

export function useVirtualAccount(params: { currency: "usd" | "eur" }) {
  return useQuery(virtualAccountQuery(params));
}

export async function loadVirtualAccount(
  queryClient: QueryClient,
  params: {
    currency: "usd" | "eur";
  }
) {
  const query = virtualAccountQuery(params);
  return (
    queryClient.getQueryData<MaybeVirtualAccount>(query.queryKey) ??
    (await queryClient.fetchQuery<MaybeVirtualAccount>(query))
  );
}

export async function refetchVirtualAccounts(queryClient: QueryClient) {
  return await queryClient.refetchQueries({ queryKey: virtualAccountKeys.all });
}

export async function createVirtualAccount(
  queryClient: QueryClient,
  params: { currency: "eur" | "usd" }
) {
  const query = virtualAccountQuery(params);
  const { virtual_account } = await sendCreateVirtualAccount(params);
  queryClient.setQueryData(query.queryKey, virtual_account);
  return virtual_account;
}

function updateKycCompletedTimestamp(customerId: string, timestamp: number) {
  Settings.set({ [`KYC_COMPLETED_TIMESTAMP_${customerId}`]: timestamp });
}

function getKycCompletedTimestamp(customerId: string) {
  return Settings.get(`KYC_COMPLETED_TIMESTAMP_${customerId}`) ?? null;
}

export function isKycJustCompleted(bridgeAccount: BridgeAccount, seconds = 60) {
  const customerId = bridgeAccount.customer_id;
  const timestamp = getKycCompletedTimestamp(customerId);
  if (timestamp === null) {
    return false;
  }

  const now = Date.now();
  return now - timestamp < seconds * 1000;
}

export type ExtendedKycStatus = "just_completed" | BridgeAccount["kyc_status"];

export function getKycStatus(bridgeAccount: BridgeAccount): ExtendedKycStatus {
  const kycStatus = bridgeAccount.kyc_status;
  if (isKycJustCompleted(bridgeAccount)) {
    console.debug(
      `overriding kyc status with under_review, original status ${kycStatus}`
    );
    return "just_completed";
  }

  if (kycStatus === "approved") {
    return kycStatus;
  }

  return kycStatus;
}

export function markKycCompleted(customerId: string) {
  updateKycCompletedTimestamp(customerId, Date.now());
}

const externalAccountsQuery = {
  queryKey: ["bridge/external_accounts"],
  queryFn: async () => {
    const response = await getExternalAccounts();
    return response.externalAccounts;
  },
} satisfies UseSuspenseQueryOptions<ExternalAccount[]>;

export function useExternalAccounts() {
  return useSuspenseQuery(externalAccountsQuery).data;
}

export async function loadExternalAccounts({
  queryClient,
}: {
  queryClient: QueryClient;
}) {
  return queryClient.ensureQueryData<ExternalAccount[]>(externalAccountsQuery);
}

export async function createExternalAccount(
  queryClient: QueryClient,
  params: CreateExternalAccountRequest
) {
  const { externalAccount } = await sendCreateExternalAccount(params);

  queryClient.setQueryData<ExternalAccount[]>(
    externalAccountsQuery.queryKey,
    (data) => (data ? [...data, externalAccount] : [externalAccount])
  );

  return externalAccount;
}

const countriesListQuery = {
  queryKey: ["bridge/countries_list"],
  queryFn: () => {
    return getCountriesList();
  },
};

export function useCountriesList() {
  return useSuspenseQuery(countriesListQuery).data;
}

export function loadCountriesList(queryClient: QueryClient) {
  return queryClient.ensureQueryData(countriesListQuery);
}

const bridgeFeesQuery = {
  queryKey: ["bridge/fees"],
  queryFn: () => {
    return getBridgeFees();
  },
};

export function useBridgeFeesLazy() {
  return useQuery(bridgeFeesQuery);
}

export function useBridgeFees() {
  return useSuspenseQuery(bridgeFeesQuery).data;
}

export function useExchangeRateLazy(params: { from: Currency; to: Currency }) {
  return useQuery({
    queryKey: [`bridge/exchange_rate/${params.from}/${params.to}`],
    queryFn: () => {
      return getExchangeRate(params);
    },
  });
}

export async function deleteExternalAccount(
  queryClient: QueryClient,
  params: {
    externalAccountId: string;
  }
) {
  await sendDeleteExternalAccount(params);

  queryClient.setQueryData<ExternalAccount[]>(
    externalAccountsQuery.queryKey,
    (data) =>
      data ? data.filter((e) => e.id !== params.externalAccountId) : data
  );
}

type MaybeCard = Card | null;

const cardQuery = {
  queryKey: ["bridge/card"],
  queryFn: async () => {
    const response = await getCard();
    return response.card;
  },
} satisfies UseSuspenseQueryOptions<MaybeCard>;

export function useSuspenseCard() {
  return useSuspenseQuery(cardQuery).data;
}

export function useLazyCard() {
  return useQuery(cardQuery);
}

export async function ensureCard({
  queryClient,
}: {
  queryClient: QueryClient;
}) {
  return queryClient.ensureQueryData(cardQuery);
}

export async function refetchCard({
  queryClient,
}: {
  queryClient: QueryClient;
}) {
  return queryClient.fetchQuery(cardQuery);
}

export async function freezeCard({
  queryClient,
}: {
  queryClient: QueryClient;
}) {
  const card = await sendFreezeCard();
  queryClient.setQueryData<Card>(cardQuery.queryKey, card);
}

export async function unfreezeCard({
  queryClient,
}: {
  queryClient: QueryClient;
}) {
  const card = await sendUnfreezeCard();
  queryClient.setQueryData<Card>(cardQuery.queryKey, card);
}

export function useCardEvents() {
  return useInfiniteQuery({
    queryKey: ["bridge/card/events-paginated"],
    queryFn: async ({ pageParam }) => {
      return getCardEvents({ page: pageParam });
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => lastPage.nextPage,
  });
}

export function refetchCardEvents(queryClient: QueryClient) {
  return queryClient.refetchQueries({
    queryKey: ["bridge/card/events-paginated"],
  });
}

export function isCardAvailable(country: BridgeAccountCountry) {
  console.debug("Checking card availability", { country });
  if (
    country.country === "USA" &&
    !["New York", "Alaska"].includes(country.region)
  ) {
    return true;
  }

  if (country.country === "ARG") {
    return true;
  }

  return false;
}

export function isThirdPartyPayoutAvailable(country: BridgeAccountCountry) {
  if (["Texas", "Pennsylvania"].includes(country.region)) {
    return false;
  }

  return true;
}

export async function requestCard(
  queryClient: QueryClient,
  { walletKey }: { walletKey: Address }
) {
  const { requestedAt } = await sendRequestCard();

  updateWallet(queryClient, {
    walletKey: walletKey,
    wallet: { card: { status: "requested", requestedAt } },
  });
}

function cardBalanceQuery({
  refetchInterval,
}: {
  refetchInterval?: number;
} = {}) {
  return {
    queryKey: ["bridge/card/balance"],
    queryFn: async () => {
      return getCardBalance();
    },
    refetchInterval: refetchInterval,
  };
}

export function useSuspenseCardBalance() {
  return useSuspenseQuery(cardBalanceQuery({ refetchInterval: 10_000 })).data;
}

export function refetchCardBalance(queryClient: QueryClient) {
  return queryClient.refetchQueries(cardBalanceQuery());
}

function virtualAccountsNeedMigrationQuery() {
  return {
    queryKey: ["bridge/virtual_accounts_migration"],
    queryFn: async () => {
      return getVirtualAccountsNeedMigration();
    },
  };
}

export function useVirtualAccountsNeedMigration() {
  return useQuery(virtualAccountsNeedMigrationQuery());
}

export function ensureVirtualAccountsNeedMigration(queryClient: QueryClient) {
  return queryClient.ensureQueryData(virtualAccountsNeedMigrationQuery());
}

export function refetchVirtualAccountsNeedMigration(queryClient: QueryClient) {
  return queryClient.refetchQueries({
    queryKey: virtualAccountsNeedMigrationQuery().queryKey,
  });
}

function cardSpendingQuery() {
  return {
    queryKey: ["bridge/card/spending"],
    queryFn: async () => {
      return getCardSpending();
    },
  };
}

export function useCardSpending() {
  return useQuery(cardSpendingQuery());
}

function cardTransactionsByMonthQuery({ month }: { month: number }) {
  return {
    queryKey: ["bridge/card/transactions_by_month", month],
    queryFn: async () => {
      return getCardTransactionsByMonth({ month });
    },
  };
}

export function useCardTransactionsByMonth({ month }: { month: number }) {
  return useQuery(cardTransactionsByMonthQuery({ month }));
}
