import { QueryClient, useQuery } from "@tanstack/react-query";
import { getSubscription } from "~/services/subscription";
import { router } from "expo-router";
import { Settings } from "react-native";

const subscriptionQuery = {
  queryKey: ["subscription"],
  queryFn: async () => {
    return getSubscription();
  },
};

export function useSubscription() {
  return useQuery(subscriptionQuery);
}

export async function fetchSubscription(queryClient: QueryClient) {
  return await queryClient.fetchQuery(subscriptionQuery);
}

export async function ensureSubscription(queryClient: QueryClient) {
  return await queryClient.ensureQueryData(subscriptionQuery);
}

const welcomeKey = "MEMBERSHIP_WELCOME_SEEN";

export function navigateToMembershipWelcomeScreen() {
  Settings.set({ [welcomeKey]: true });
  router.push("/unlocked/subscription/welcome");
}

export function membershipWelcomeSeen() {
  return Boolean(Settings.get(welcomeKey));
}
