import {
  getBalance,
  getHistoricalBalances,
  getPeriodBalances,
  HistoricalBalance,
  Period,
  TokenBalance,
  TokenPeriodBalances,
} from "~/services/balances";
import {
  QueryClient,
  useQuery,
  useQueryClient,
  UseQueryOptions,
  useSuspenseQuery,
  UseSuspenseQueryOptions,
} from "@tanstack/react-query";
import invariant from "invariant";
import { useMemo } from "react";
import { Address } from "@squads/models/solana";
import { mints, mintsSorting } from "~/constants/tokens";
import { getUnitAmount, tokenUsdValue } from "~/utils/tokens";
import { SpendingLimit } from "~/services/spendingLimits";
import { getMaxAvailableAmount } from "~/components/AmountDisplay";
import { Token } from "~/services/tokens";
import { tokenQueryKey } from "~/state/tokens";
import { reportError } from "~/utils/errors";
import { queryClient } from "./queryClient";

const FIVE_MIN_IN_SECONDS = 5 * 60 * 1000;
const SIX_HOURS_IN_SECONDS = 6 * 60 * 60 * 1000;
const TWELVE_HOURS_IN_SECONDS = 12 * 60 * 60 * 1000;
const DAY_IN_SECONDS = 24 * 60 * 60 * 1000;

type Data = TokenBalance[];

const keys = {
  balancesByAddress: (address: Address) => ["balances", address] as const,
  historicalBalancesByAddressAndPeriod: (address: Address, period: Period) =>
    ["historical_balances", address, period] as const,
  periodBalancesByAddressAndPeriod: (address: Address, period: Period) =>
    ["period_balances", address, period] as const,
  reservedForTopUp: () => ["reserved_for_top_up"] as const,
};
export const queryKeys = keys;

function balancesQuery(
  queryClient: QueryClient,
  { address, refetchInterval }: { address: Address; refetchInterval?: number }
) {
  return {
    queryKey: keys.balancesByAddress(address),
    queryFn: async () => {
      const balances = await getBalance({ address: address });

      balances.forEach((balance) => {
        if (balance.mint) {
          queryClient.setQueryData<Token>(tokenQueryKey(balance.mint), {
            name: balance.metadata.name,
            symbol: balance.metadata.symbol,
            decimals: balance.decimals,
            mint: balance.mint,
            logoUri: balance.metadata.logoUri,
          });
        }
      });

      return balances;
    },
    refetchInterval: refetchInterval ?? 7_000,
  } satisfies UseSuspenseQueryOptions<Data>;
}

export function useBalances({
  address,
  refetchInterval,
}: {
  address: Address;
  refetchInterval?: number;
}): TokenBalance[] {
  const queryClient = useQueryClient();
  const { data } = useSuspenseQuery(
    balancesQuery(queryClient, { address, refetchInterval })
  );
  return data;
}

export function useLazyBalances({ address }: { address: Address }) {
  const queryClient = useQueryClient();
  return useQuery(balancesQuery(queryClient, { address }));
}

function periodBalancesQuery({
  address,
  period,
}: {
  address: Address;
  period: Period;
}) {
  const staleTime = (() => {
    switch (period) {
      case "day":
        return FIVE_MIN_IN_SECONDS;
      case "week":
        return SIX_HOURS_IN_SECONDS;
      case "month":
        return TWELVE_HOURS_IN_SECONDS;
      case "halfYear":
        return TWELVE_HOURS_IN_SECONDS;
      case "year":
        return DAY_IN_SECONDS;
    }
  })();

  return {
    queryKey: keys.periodBalancesByAddressAndPeriod(address, period),
    queryFn: async () => getPeriodBalances({ address, period }),
    refetchInterval: period === "day" ? FIVE_MIN_IN_SECONDS : undefined,
    staleTime,
  } satisfies UseSuspenseQueryOptions<TokenPeriodBalances[], Error>;
}

export function usePeriodBalances({
  address,
  period,
}: {
  address: Address;
  period: Period;
}) {
  const { data } = useSuspenseQuery(periodBalancesQuery({ address, period }));
  return data;
}

export function useSolBalance({ address }: { address: Address }) {
  const balances = useBalances({ address: address });
  const solBalance = balances.find((balance) => balance.mint === null);
  invariant(solBalance, "sol balance must exist");

  return { solBalance };
}

export function useLazySolBalance({ address }: { address: Address }) {
  const queryClient = useQueryClient();
  const { data: balances } = useQuery(balancesQuery(queryClient, { address }));
  const solBalance = balances?.find((balance) => balance.mint === null);
  return { solBalance };
}

export function useUsdBalance({ address }: { address: Address }) {
  const { verifiedBalances } = useSuspenseVerifiedBalances({ address });
  return calculateUsdBalance(verifiedBalances);
}

export function useLazyUsdBalance({ address }: { address: Address }) {
  const result = useLazyBalances({ address });
  if (result.data) {
    return { ...result, data: calculateUsdBalance(result.data) };
  } else {
    return result;
  }
}

function calculateUsdBalance(balances: TokenBalance[]) {
  if (!Array.isArray(balances)) {
    reportError(
      queryClient,
      new Error("balances.ts: balances is not an array"),
      {
        balances,
      }
    );
    return 0;
  }

  // todo: delete after we debug the issue
  if (balances && !balances.find) {
    reportError(
      queryClient,
      new Error("balances.ts: balances.find is not a function"),
      {
        balances,
      }
    );
    return 0;
  }

  const solBalance = balances.find((balance) => balance.mint === null);
  invariant(solBalance, "sol balance must exist");

  return balances
    .map((balance) =>
      tokenUsdValue({
        amount: TokenBalance.total(balance),
        usdcPrice: balance.usdcPrice,
        decimals: balance.decimals,
      })
    )
    .reduce((a, b) => a + b, 0);
}

export function useStablesForEarnBalance({ address }: { address: Address }) {
  const stablesForEarn = [mints.usdc]; // only usdc, as this is the most used one in fuse

  const balances = useBalances({ address: address });
  const stablesBalances = balances.filter((balance) =>
    stablesForEarn.includes(balance.mint as Address)
  );

  const stablesBalance = stablesBalances
    .map((balance) =>
      tokenUsdValue({
        amount: TokenBalance.total(balance),
        usdcPrice: balance.usdcPrice,
        decimals: balance.decimals,
      })
    )
    .reduce((a, b) => a + b, 0);

  return stablesBalance;
}

export function useBalance({
  mint,
  vault,
}: {
  mint: Address | null;
  vault: Address;
}) {
  const mintOrSol = mint === mints.sol ? null : mint;
  const balances = useBalances({ address: vault });
  const tokenBalance = balances.find((balance) => balance.mint === mintOrSol);

  if (tokenBalance === undefined) return null;

  return tokenBalance;
}

export function usePeriodBalance({
  mint,
  address,
  period,
}: {
  mint: Address | null;
  address: Address;
  period: Period;
}) {
  const mintOrSol = mint === mints.sol ? null : mint;
  const periodBalances = usePeriodBalances({ address, period });
  const tokenBalance = periodBalances.find(
    (balance) => balance.mint === mintOrSol
  );

  if (tokenBalance === undefined) {
    return {
      mint: mint,
      amount: 0,
      stakedAmount: null,
      usdPrice: null,
      earnTotal: null,
    };
  }

  return tokenBalance;
}

export function useCoinUsdValue({
  mint,
  vault,
}: {
  mint: Address | null;
  vault: Address;
}) {
  const mintOrSol = mint === mints.sol ? null : mint;
  const balances = useBalances({ address: vault });
  const balance = balances.find((balance) => balance.mint === mintOrSol);

  if (balance === undefined) {
    return null;
  }

  return tokenUsdValue({
    amount: balance.amount,
    usdcPrice: balance.usdcPrice,
    decimals: balance.decimals,
  });
}

export async function refetchBalances({
  queryClient,
  address,
}: {
  queryClient: QueryClient;
  address: Address;
}) {
  const query = balancesQuery(queryClient, { address: address });
  await queryClient.refetchQueries({ queryKey: query.queryKey });
}

export async function loadBalances({
  queryClient,
  address,
}: {
  queryClient: QueryClient;
  address: Address;
}) {
  const query = balancesQuery(queryClient, { address: address });
  return (
    queryClient.getQueryData<Data>(query.queryKey) ??
    (await queryClient.fetchQuery<Data>(query))
  );
}

export const priorityOrder = [mints.sol, mints.fuseSol];

export function nonZeroBalances(
  balances: TokenBalance[],
  useTotalBalance = false
) {
  return balances
    .filter((balance) => {
      if (balance.mint === null) {
        return true;
      }

      const amount = useTotalBalance
        ? TokenBalance.total(balance)
        : balance.amount;
      return amount > 0;
    })
    .sort(
      mintsSorting(priorityOrder, (a, b) => {
        const usdComparison =
          TokenBalance.usdValue(b) - TokenBalance.usdValue(a);

        if (usdComparison === 0) {
          const nameA = a.metadata.name.toLowerCase();
          const nameB = b.metadata.name.toLowerCase();
          return nameA.localeCompare(nameB);
        } else {
          return usdComparison;
        }
      })
    );
}

export const balanceFilters = {
  nonZero: (balance: TokenBalance) =>
    balance.mint === null ? true : balance.amount > 0,
  withSearch: (search: string) => (balance: TokenBalance) => {
    if (!search) {
      return true;
    }

    const normalizedSearch = search.toLowerCase();
    const nameIncludes = balance.metadata?.name
      ?.toLowerCase()
      ?.includes(normalizedSearch);

    const symbolIncludes = balance.metadata?.symbol
      ?.toLowerCase()
      ?.includes(normalizedSearch);

    const matchesMint = balance.mint === search;

    return nameIncludes || symbolIncludes || matchesMint;
  },
};

function historicalBalancesQuery(params: { vault: Address; period: Period }) {
  return {
    queryKey: keys.historicalBalancesByAddressAndPeriod(
      params.vault,
      params.period
    ),
    queryFn: async () => {
      return getHistoricalBalances(params);
    },
    refetchInterval: 60_000,
  } satisfies UseQueryOptions<HistoricalBalance[]>;
}

export function useLazyHistoricalBalances(params: {
  vault: Address;
  period: Period;
}) {
  return useQuery(historicalBalancesQuery(params));
}

export async function loadHistoricalBalances(
  queryClient: QueryClient,
  params: {
    vault: Address;
    period: Period;
  }
) {
  const query = historicalBalancesQuery(params);
  return (
    queryClient.getQueryData<HistoricalBalance[]>(query.queryKey) ??
    (await queryClient.fetchQuery<HistoricalBalance[]>(query))
  );
}

export async function refetchHistoricalBalances({
  queryClient,
  ...params
}: {
  queryClient: QueryClient;
  vault: Address;
  period: Period;
}) {
  const query = historicalBalancesQuery(params);
  return queryClient.refetchQueries({ queryKey: query.queryKey });
}

export function getMaxAmount({
  balance,
  spendingLimit,
}: {
  balance: {
    mint: Address | null;
    amount: number;
    decimals: number;
  };
  spendingLimit: SpendingLimit | null;
}): number {
  const maxSendAmount = getMaxAvailableAmount(balance);

  return spendingLimit
    ? Math.min(
        getUnitAmount({
          amount: spendingLimit.remainingAmount,
          decimals: balance.decimals,
        }),
        maxSendAmount
      )
    : maxSendAmount;
}

/**
 * Separates tokens into verified and unverified groups
 * @param balances - Array of token balances to process
 * @returns Object containing verified and unverified token arrays
 */
function getVerifiedAndUnverifiedBalances(balances: TokenBalance[]) {
  return balances.reduce<{
    verifiedBalances: TokenBalance[];
    unverifiedBalances: TokenBalance[];
  }>(
    (acc, balance) => {
      if (balance.isVerified) {
        return { ...acc, verifiedBalances: [...acc.verifiedBalances, balance] };
      }

      return {
        ...acc,
        unverifiedBalances: [...acc.unverifiedBalances, balance],
      };
    },
    { verifiedBalances: [], unverifiedBalances: [] }
  );
}

type VerifiedBalances = {
  allBalances: TokenBalance[];
  verifiedBalances: TokenBalance[];
  unverifiedBalances: TokenBalance[];
};

export function useSuspenseVerifiedBalances({
  address,
}: {
  address: Address;
}): VerifiedBalances {
  const balances = useBalances({ address });

  return useMemo(() => {
    const { verifiedBalances, unverifiedBalances } =
      getVerifiedAndUnverifiedBalances(nonZeroBalances(balances, true));
    const allBalances = [...verifiedBalances, ...unverifiedBalances];

    return {
      allBalances,
      verifiedBalances,
      unverifiedBalances,
    };
  }, [balances]);
}
