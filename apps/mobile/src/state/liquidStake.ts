import { getFuseSolApy, getLstPrice } from "~/services/liquidStake";
import { useSuspenseQuery } from "@tanstack/react-query";
import { Address } from "@squads/models/solana";

function lstPriceQuery(params: { mint: Address }) {
  return {
    queryKey: ["liquid_stake", "lstPrice", params.mint],
    queryFn: async () => {
      return getLstPrice(params);
    },
  };
}

export function useLstPrice(params: { mint: Address }) {
  const { data } = useSuspenseQuery(lstPriceQuery(params));
  return data;
}

function fuseSolApyQuery() {
  return {
    queryKey: ["liquid_stake", "fuse_sol_apy"],
    staleTime: 60 * 60 * 1000,
    queryFn: async () => {
      return getFuseSolApy();
    },
  };
}
export function useFuseSolApy() {
  const { data } = useSuspenseQuery(fuseSolApyQuery());
  return data.apy * 100;
}
