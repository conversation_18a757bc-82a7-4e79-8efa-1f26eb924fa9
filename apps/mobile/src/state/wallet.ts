import { Address } from "@squads/models/solana";
import {
  QueryClient,
  useMutation,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
  UseSuspenseQueryOptions,
} from "@tanstack/react-query";
import invariant from "invariant";
import { useTryDeviceKey } from "~/state/deviceKey";
import { useTryCloudKey } from "~/state/cloudKey";
import {
  getByMember,
  getWallet,
  getWalletReferral,
  GetWalletResponse,
  updateVaultName,
  Wallet,
  WalletByMember,
} from "~/services/wallets";
import { useFocusNotifyOnChangeProps } from "~/utils/react-query";

const queryKeys = {
  walletByAddress: (walletKey: Address | null) =>
    ["wallet", walletKey] as const,
};

export function walletByKeyQuery(walletKey: Address | null) {
  return {
    queryKey: queryKeys.walletByAddress(walletKey),
    queryFn: () => {
      if (walletKey === null) {
        return { wallet: null };
      }
      return getWallet({ walletKey });
    },
  } satisfies UseSuspenseQueryOptions<GetWalletResponse>;
}

export function useWalletByAddress({
  walletKey,
}: {
  walletKey: Address | null;
}) {
  const query = walletByKeyQuery(walletKey);
  const result = useSuspenseQuery(query);
  return result.data;
}

export function updateWallet(
  queryClient: QueryClient,
  { walletKey, wallet }: { walletKey: Address; wallet: Partial<Wallet> }
) {
  const queryKey = queryKeys.walletByAddress(walletKey);
  queryClient.setQueryData<GetWalletResponse>(queryKey, (currentWallet) => {
    if (!currentWallet || !currentWallet.wallet) {
      return currentWallet;
    }

    return { wallet: { ...currentWallet.wallet, ...wallet } };
  });

  queryClient.invalidateQueries({ queryKey });
}

export async function loadWalletByKey({
  queryClient,
  walletKey,
}: {
  queryClient: QueryClient;
  walletKey: Address;
}) {
  const query = walletByKeyQuery(walletKey);
  return (
    queryClient.getQueryData<GetWalletResponse>(query.queryKey) ??
    (await queryClient.fetchQuery<GetWalletResponse>(query))
  );
}

type Data = WalletByMember;

const WALLET_BY_MEMBER_KEY_PREFIX = "wallet_by_member";

export function walletByMemberQuery(
  memberKey: Address | null,
  notifyOnChangeProps?: any
) {
  return {
    queryKey: [WALLET_BY_MEMBER_KEY_PREFIX, memberKey],
    queryFn: () => {
      if (memberKey === null) {
        return { walletKey: null };
      }
      return getByMember({ memberKey });
    },
    notifyOnChangeProps,
  } satisfies UseSuspenseQueryOptions<Data>;
}

export async function loadWalletByMember(
  queryClient: QueryClient,
  { memberKey }: { memberKey: Address | null }
): Promise<Data> {
  const query = walletByMemberQuery(memberKey);
  return (
    queryClient.getQueryData<Data>(query.queryKey) ??
    (await queryClient.fetchQuery<Data>(query))
  );
}

export function useWalletByMember({
  memberKey,
}: {
  memberKey: Address | null;
}): {
  walletKey: Address | null;
} {
  const notifyOnChangeProps = useFocusNotifyOnChangeProps();
  const query = walletByMemberQuery(memberKey, notifyOnChangeProps);
  const result = useSuspenseQuery(query);

  return result.data;
}

export async function refetchWallet(
  queryClient: QueryClient,
  { walletKey }: { walletKey: Address }
) {
  await queryClient.refetchQueries({
    queryKey: walletByKeyQuery(walletKey).queryKey,
  });
}

export function refetchWalletByKeys(queryClient: QueryClient) {
  return queryClient.refetchQueries({
    predicate: (query) => query.queryKey[0] === WALLET_BY_MEMBER_KEY_PREFIX,
  });
}

export type WalletStatus =
  | {
      status: "active";
      deviceKey: Address;
      wallet: Wallet;
    }
  | {
      status: "inactive";
    }
  | {
      status: "deviceKeyError";
      error: any;
    };

export function useWalletStatus(): WalletStatus {
  const tryDeviceKey = useTryDeviceKey();
  const tryCloudKey = useTryCloudKey();

  const maybeDeviceKey =
    tryDeviceKey.type === "success" ? tryDeviceKey.key : null;

  const { walletKey } = useWalletByMember({
    memberKey: maybeDeviceKey,
  });

  const { wallet } = useWalletByAddress({ walletKey });

  if (tryDeviceKey.type === "error") {
    return {
      status: "deviceKeyError",
      error: tryDeviceKey.error,
    };
  }

  const deviceKey = tryDeviceKey.key;

  const hasDeviceKeyAndWallet = deviceKey !== null && wallet !== null;
  if (!hasDeviceKeyAndWallet) {
    return { status: "inactive" };
  }

  const isICloudKey = wallet.keys.cloudKey.details.type === "iCloud";
  if (isICloudKey) {
    if (tryCloudKey.type === "error") {
      //wallet is active in read-only mode, offering to switch to 2FA or retry iCloud connection
      return { status: "active", deviceKey, wallet };
    }

    //otherwise route user to recovery flow
    if (tryCloudKey.key !== wallet.keys.cloudKey.address) {
      return { status: "inactive" };
    }
  }

  return { status: "active", deviceKey, wallet };
}

export function useActiveWallet() {
  const walletStatus = useWalletStatus();
  invariant(
    walletStatus.status === "active",
    `wallet status is not active: ${walletStatus.status}`
  );

  const { wallet } = walletStatus;

  return {
    deviceKey: walletStatus.deviceKey,
    relayerKey: walletStatus.wallet.keys.relayerKey.address,
    wallet,
  };
}

export function useUpdateVaultName() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      walletKey,
      vaultKey,
      vaultName,
    }: {
      walletKey: Address;
      vaultKey: Address;
      vaultName: string;
    }) => {
      await updateVaultName({ walletKey, vaultKey, vaultName });
    },

    // Perform optimistic update
    onMutate({ walletKey, vaultKey, vaultName }) {
      const queryKey = walletByKeyQuery(walletKey).queryKey;

      let prevData: GetWalletResponse;

      queryClient.setQueryData<GetWalletResponse>(queryKey, (prev) => {
        prevData = prev!;
        const prevWallet = prev!.wallet!;

        return {
          wallet: {
            ...prevWallet,
            vaults: prevWallet!.vaults.map((v) => {
              if (v.key !== vaultKey) return v;

              return { ...v, name: vaultName };
            }),
          },
        };
      });

      return { queryKey, prevData: prevData! };
    },

    // Revert optimistic update
    onError(_err, _vars, context) {
      invariant(context, "expected context in onError");
      queryClient.setQueryData(context.queryKey, context.prevData);
    },

    // Invalidate queries after optimistic update
    onSettled(_data, _err, { walletKey }) {
      const queryKey = walletByKeyQuery(walletKey).queryKey;
      queryClient.invalidateQueries({ queryKey: [queryKey] });
    },
  });
}

export function useLazyWalletReferral({ walletKey }: { walletKey: Address }) {
  return useQuery({
    queryKey: ["walletReferral", walletKey],
    queryFn: () => {
      return getWalletReferral({ walletKey });
    },
    refetchOnMount: "always",
  });
}
