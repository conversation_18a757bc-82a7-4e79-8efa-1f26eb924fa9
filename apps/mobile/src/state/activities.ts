import {
  QueryClient,
  useSuspenseQuery,
  UseSuspenseQueryOptions,
} from "@tanstack/react-query";
import { Address } from "@squads/models/solana";
import {
  Activity,
  getActivities,
  isActivityRelatedToMint,
} from "~/services/activities";
import { Wallet } from "~/services/wallets";

type Data = Activity[];

function activitiesQuery({ walletKey }: { walletKey: Address }) {
  return {
    queryKey: [`activities`, walletKey],
    queryFn: async () => {
      const activities = await getActivities({ walletKey: walletKey });
      return activities.filter((activity) => {
        if (activity.details.type === "receiveSol") {
          return activity.details.lamports > 10_000;
        }
        return true;
      });
    },
    refetchInterval: 10 * 1000,
  } satisfies UseSuspenseQueryOptions<Data>;
}

export function useActivities({ wallet }: { wallet: Address }) {
  const { data } = useSuspenseQuery(activitiesQuery({ walletKey: wallet }));
  return data;
}

export function useActivitiesByMint({
  wallet,
  mint,
}: {
  wallet: Wallet;
  mint: Address;
}) {
  const activities = useActivities({ wallet: wallet.walletKey });
  return activities.filter((activity) =>
    isActivityRelatedToMint(activity, mint, wallet.defaultVault)
  );
}

export async function refetchActivities({
  queryClient,
  walletKey,
}: {
  queryClient: QueryClient;
  walletKey: Address;
}) {
  const query = activitiesQuery({ walletKey: walletKey });
  await queryClient.refetchQueries({ queryKey: query.queryKey });
}

export async function loadActivities({
  queryClient,
  walletKey,
}: {
  queryClient: QueryClient;
  walletKey: Address;
}) {
  const query = activitiesQuery({ walletKey });
  return (
    queryClient.getQueryData<Data>(query.queryKey) ??
    (await queryClient.fetchQuery<Data>(query))
  );
}

export function updateActivity(
  queryClient: QueryClient,
  {
    activityId,
    walletKey,
    activity,
  }: {
    walletKey: Address;
    activityId: string;
    activity: Partial<Activity> | ((activity: Activity) => Activity);
  }
) {
  const { queryKey } = activitiesQuery({ walletKey });
  queryClient.setQueryData<Data>(queryKey, (current) => {
    if (!current) {
      return current;
    }

    return current.map((a) =>
      a.activityId === activityId
        ? typeof activity === "function"
          ? activity(a)
          : { ...a, ...activity }
        : a
    );
  });

  queryClient.invalidateQueries({ queryKey });
}
