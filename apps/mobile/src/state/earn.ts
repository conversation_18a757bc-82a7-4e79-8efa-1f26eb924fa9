import {
  DriftSpotMarkets,
  EarnProvider,
  getDriftSpotMarkets,
  getKaminoVault,
  getLuloPools,
  KaminoVault,
  LendingApy,
  LuloPools,
} from "~/services/earn";
import {
  QueryClient,
  UseQueryOptions,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { Address } from "@squads/models/solana";
import { useBalances } from "~/state/balances";
import { mints, mintsSorting } from "~/constants/tokens";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";

const driftSpotMarketsQuery = {
  queryKey: ["drift_spot_markets"],
  queryFn: async () => {
    return getDriftSpotMarkets();
  },
} satisfies UseQueryOptions<DriftSpotMarkets>;

export function useSuspenseDriftSpotMarkets() {
  const { data } = useSuspenseQuery(driftSpotMarketsQuery);
  return data;
}

export async function prefetchDriftSpotMarkets(queryClient: QueryClient) {
  return queryClient.prefetchQuery(driftSpotMarketsQuery);
}

const luloPoolsQuery = {
  queryKey: ["lulo_pools"],
  queryFn: async () => {
    return getLuloPools();
  },
} satisfies UseQueryOptions<LuloPools>;

export function useSuspenseLuloPools() {
  const { data } = useSuspenseQuery(luloPoolsQuery);
  return data;
}

export async function prefetchLuloPools(queryClient: QueryClient) {
  return queryClient.prefetchQuery(luloPoolsQuery);
}

export function useEarnBalances({
  address,
}: {
  address: Address;
}): Array<{ provider: EarnProvider; assets: EarnBalance[] }> {
  const { spotMarkets } = useSuspenseDriftSpotMarkets();
  const luloPools = useSuspenseLuloPools();
  const kaminoVault = useSuspenseKaminoVault();

  const balances = useBalances({ address });

  const result: Array<{ provider: EarnProvider; assets: EarnBalance[] }> = [];

  const driftEarnBalances: EarnBalance[] = [];
  const luloEarnBalances: EarnBalance[] = [];
  const kaminoEarnBalances: EarnBalance[] = [];
  balances.forEach((balance) => {
    const driftEarn = balance.driftEarn;
    const driftMarket =
      driftEarn && spotMarkets.find((market) => market.mint === balance.mint);

    if (driftEarn && driftMarket) {
      const usdBalance = driftEarn.total / 10 ** balance.decimals;

      if (usdBalance > 0) {
        const mint = driftMarket.mint;
        const symbol =
          mint === mints.usdc
            ? "USDC"
            : mint === mints.pyusd
              ? "PYUSD"
              : mint === mints.usds
                ? "USDS"
                : abbreviateAddress(mint);

        driftEarnBalances.push({
          apy: LendingApy.total(driftMarket.apy),
          mint,
          symbol,
          usdBalance,
        });
      }
    }

    const luloEarn = balance.luloProtectedEarn;
    if (luloEarn) {
      const usdBalance = luloEarn.total / 10 ** balance.decimals;

      if (usdBalance > 0) {
        luloEarnBalances.push({
          apy: luloPools.protected.apy,
          mint: mints.usdc,
          symbol: "USDC",
          usdBalance,
        });
      }
    }

    const kaminoEarn = balance.kaminoEarn;
    if (kaminoEarn) {
      const usdBalance = kaminoEarn.total / 10 ** balance.decimals;
      if (usdBalance > 0) {
        kaminoEarnBalances.push({
          apy: kaminoVault.apy,
          mint: mints.usdc,
          symbol: "USDC",
          usdBalance,
        });
      }
    }
  });

  // Sort both arrays using mintsSorting
  const sorter = mintsSorting();
  driftEarnBalances.sort(sorter);
  luloEarnBalances.sort(sorter);
  kaminoEarnBalances.sort(sorter);

  if (driftEarnBalances.length > 0) {
    result.push({ provider: "drift", assets: driftEarnBalances });
  }
  if (luloEarnBalances.length > 0) {
    result.push({ provider: "lulo", assets: luloEarnBalances });
  }
  if (kaminoEarnBalances.length > 0) {
    result.push({ provider: "kamino", assets: kaminoEarnBalances });
  }

  return result;
}

export type EarnBalance = {
  apy: number;
  mint: Address;
  usdBalance: number;
  symbol: string;
};

export const EarnBalance = {
  totalBalanceUsd: (assets: EarnBalance[]) =>
    assets.reduce((a, b) => a + b.usdBalance, 0),

  weightedApy: (assets: EarnBalance[]) => {
    const totalBalance = EarnBalance.totalBalanceUsd(assets);

    if (totalBalance === 0) return 0;

    return assets.reduce((total, asset) => {
      const proportion = asset.usdBalance / totalBalance;
      return total + proportion * asset.apy;
    }, 0);
  },
  MIN_EARN_BALANCE_TO_SHOW: 0.01,
};

const kaminoVaultQuery = {
  queryKey: ["kamino_vault"],
  queryFn: async () => {
    const { vault } = await getKaminoVault();
    return vault;
  },
} satisfies UseQueryOptions<KaminoVault>;

export function useSuspenseKaminoVault() {
  const { data } = useSuspenseQuery(kaminoVaultQuery);
  return data;
}

export async function prefetchKaminoVault(queryClient: QueryClient) {
  return queryClient.prefetchQuery(kaminoVaultQuery);
}
