import {
  QueryClient,
  useQuery,
  UseQueryOptions,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { Settings } from "react-native";
import { Base64, Base64Z } from "~/services/wallets";
import * as FuseAppAttest from "@sqds/expo-fuse-app-attest";
import { CryptoDigestAlgorithm, digest } from "expo-crypto";
import {
  getAttestationChallenge,
  isKeyAttested,
  saveAttestedKey,
} from "~/services/appAttest";
import * as Device from "expo-device";
import { queryClient } from "~/state/queryClient";

const APP_ATTEST_KEY_ID_LOCAL_STORAGE_KEY = "APP_ATTEST_KEY_ID_DEVELOPMENT";

type AppAttestKeyId = Base64;

function appAttestKeyIdQuery() {
  return {
    queryKey: [`appAttestKey`],
    staleTime: 60 * 60 * 1000,
    async queryFn() {
      if (!Device.isDevice) {
        return "dGhpcyBpcyB0ZXN0IHN0cmluZyB2YWx1ZQ==" as Base64;
      }

      // Load `appAttestKeyId` from local storage if exists or generate a new one.
      const existingAppAttestKeyIdResult = Base64Z.safeParse(
        Settings.get(APP_ATTEST_KEY_ID_LOCAL_STORAGE_KEY)
      );

      let appAttestKeyId: Base64;
      if (existingAppAttestKeyIdResult.success) {
        appAttestKeyId = existingAppAttestKeyIdResult.data;
      } else {
        const result = await FuseAppAttest.generateKeyPair();
        if (!result.ok) {
          throw new Error(result.error);
        }
        appAttestKeyId = result.value as Base64;

        // Persist the key id in local storage.
        Settings.set({
          [APP_ATTEST_KEY_ID_LOCAL_STORAGE_KEY]: appAttestKeyId,
        });
      }

      // Make sure the key is attested.
      const { attested } = await isKeyAttested({ keyId: appAttestKeyId });

      if (!attested) {
        const { challenge: attestationChallenge, timestamp } =
          await fetchAttestationChallenge({ queryClient });
        console.debug({ attestationChallenge, timestamp });

        const attestationChallengeHash = await digest(
          CryptoDigestAlgorithm.SHA256,
          new TextEncoder().encode(attestationChallenge)
        );
        const attestationChallengeHashBase64 = Buffer.from(
          attestationChallengeHash
        ).toString("base64");
        const attestationResult = await FuseAppAttest.attestKey(
          appAttestKeyId,
          attestationChallengeHashBase64
        );
        if (!attestationResult.ok) {
          if (attestationResult.error === "invalidKey") {
            // If the attestation fails because the key is invalid, e.g. when cached and stale,
            // we delete the key id from local storage.
            Settings.set({ [APP_ATTEST_KEY_ID_LOCAL_STORAGE_KEY]: undefined });
          }
          throw new Error(attestationResult.error);
        }

        await saveAttestedKey({
          keyId: appAttestKeyId,
          challenge: attestationChallenge,
          attestation: attestationResult.value as Base64,
        });
      } else {
        console.debug("Key is already attested");
      }

      return appAttestKeyId;
    },
  } satisfies UseQueryOptions<AppAttestKeyId>;
}

export async function prefetchAppAttestKeyId({
  queryClient,
}: {
  queryClient: QueryClient;
}) {
  const query = appAttestKeyIdQuery();
  return queryClient.prefetchQuery<AppAttestKeyId>(query);
}

export async function fetchAppAttestKeyId({
  queryClient,
}: {
  queryClient: QueryClient;
}) {
  return queryClient.fetchQuery<AppAttestKeyId>(appAttestKeyIdQuery());
}

export function useAppAttestKey(): AppAttestKeyId {
  const { data } = useSuspenseQuery(appAttestKeyIdQuery());

  return data;
}

export function useLazyAppAttestKeyId() {
  return useQuery(appAttestKeyIdQuery());
}

type AttestationChallengeData = {
  challenge: string;
  timestamp: number;
};

function attestationChallengeQuery() {
  return {
    queryKey: ["attestationChallenge"],
    staleTime: 4 * 60 * 1000, // 4 minutes (below the 5-minute server limit)
    async queryFn() {
      const { challenge } = await getAttestationChallenge();
      return {
        challenge,
        timestamp: Date.now(),
      };
    },
  } satisfies UseQueryOptions<AttestationChallengeData>;
}

/** If fetches and caches the attestation challenge. If the challenge is not yet stale, returns one from the cache. */
export async function fetchAttestationChallenge({
  queryClient,
}: {
  queryClient: QueryClient;
}) {
  const query = attestationChallengeQuery();
  return await queryClient.fetchQuery(query);
}
