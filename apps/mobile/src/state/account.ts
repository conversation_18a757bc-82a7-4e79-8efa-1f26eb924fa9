import { useQuery, useSuspenseQuery } from "@tanstack/react-query";
import {
  AccountActivity,
  DomainLookup,
  getAccountActivity,
  getDomainLookup,
} from "~/services/account";
import { Address } from "@squads/models/solana";
import { create } from "zustand";

export function useDomainLookup(
  { domain }: { domain: string },
  options?: { enabled: boolean }
) {
  return useQuery<DomainLookup>({
    queryKey: ["domainLookup", domain],
    queryFn: async () => {
      const domainLookup = await getDomainLookup(domain);

      if (domainLookup.address) {
        useDomainMap.setState((state) => {
          domainLookup.address &&
            state.domainMap.set(domainLookup.address, domain.toLowerCase());

          return state;
        });
      }

      return domainLookup;
    },
    enabled: options?.enabled,
  });
}

const useDomainMap = create<{
  domainMap: Map<Address, string>;
}>(() => ({
  domainMap: new Map(),
}));

export function useDomain(address: Address) {
  const { domainMap } = useDomainMap();
  return domainMap.get(address);
}

export function useAccountActivity({ address }: { address: Address }) {
  const { data } = useSuspenseQuery<AccountActivity>({
    queryKey: ["accountActivity", address],
    queryFn: async () => {
      return getAccountActivity(address);
    },
  });

  return data;
}
