import {
  UseBaseQueryOptions,
  useQuery,
  useSuspenseQuery,
} from "@tanstack/react-query";
import invariant from "invariant";
import { getQuote, QuoteRequest, SwapQuote } from "~/services/swaps";
import { Address } from "@squads/models/solana";

type Data = SwapQuote;

function quoteQuery({
  walletKey,
  quote,
}: {
  walletKey: Address;
  quote: QuoteRequest | null;
}) {
  return {
    queryKey: [
      `swap/quote`,
      walletKey,
      quote?.inputMint,
      quote?.outputMint,
      quote?.amount,
      quote?.slippageBps,
    ],
    queryFn: async () => {
      if (quote === null) return null;
      return getQuote({ walletKey, quote });
    },
    enabled: quote !== null,
    refetchInterval: 20_000,
    retry: 1,
  } satisfies UseBaseQueryOptions<Data | null>;
}

export function useQuote({
  walletKey,
  quote,
}: {
  walletKey: Address;
  quote: QuoteRequest;
}) {
  const { data } = useSuspenseQuery(quoteQuery({ walletKey, quote }));

  invariant(data !== null, "Quote not available");
  return data;
}

export function useLazyQuote({
  walletKey,
  quote,
}: {
  walletKey: Address;
  quote: QuoteRequest | null;
}) {
  return useQuery(quoteQuery({ walletKey, quote }));
}
