import { useState, useEffect, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { getAddressAutocomplete, PlaceSuggestion } from "~/services/addressAutocomplete";
import { debounce } from "~/utils/debounce";

const keys = {
  addressAutocomplete: (queryString: string) =>
    ["address_autocomplete", queryString] as const,
};

export const queryKeys = keys;

export function useAddressAutocomplete() {
  const [query, setQuery] = useState("");
  const [debouncedQuery, setDebouncedQuery] = useState("");

  const debouncedSetQuery = useCallback(
    debounce((value: string) => {
      setDebouncedQuery(value);
    }, 300),
    []
  );

  useEffect(() => {
    debouncedSetQuery(query);
    return () => {
      debouncedSetQuery.clear();
    };
  }, [query, debouncedSetQuery]);

  const {
    data: places = [],
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: keys.addressAutocomplete(debouncedQuery),
    queryFn: () => getAddressAutocomplete(debouncedQuery),
    enabled: debouncedQuery.trim().length >= 3,
  });

  return {
    query,
    setQuery,
    places: places as PlaceSuggestion[],
    loading,
    error,
  };
}
