import Constants from "expo-constants";
import { z } from "zod";

const AppConfigZ = z.object({
  name: z.string(),
  environment: z.union([
    z.literal("development"),
    z.literal("staging"),
    z.literal("production"),
  ]),
  identifier: z.string(),
  easProjectId: z.string(),
  apiUrl: z.string().url(),
  associatedDomain: z.string(),
  icloud: z.object({
    environment: z.union([z.literal("Development"), z.literal("Production")]),
    containerId: z.string(),
  }),
  magicLink: z.object({
    recovery: z.object({
      publicKey: z.string(),
    }),
  }),
  fuseWebUrl: z.string().url(),
});

export type AppConfig = z.infer<typeof AppConfigZ>;

export const appConfig: AppConfig = AppConfigZ.parse(
  Constants.expoConfig?.extra?.appConfig
);

if (__DEV__) {
  console.log(JSON.stringify(appConfig, null, 2));
}

export const iCloudContainerId = appConfig.icloud.containerId;
