import { z } from "zod";
import { API_BASE_URL } from "~/constants/apiBaseUrl";
import { getOrRefreshAuth } from "~/services/auth-utils";
import { assertOk } from "~/services/utils";

export const GetSubscriptionResponseZ = z.discriminatedUnion("status", [
  z.object({
    status: z.literal("active"),
  }),
  z.object({
    status: z.literal("none"),
    minHoldAmountUsd: z.number(),
  }),
]);

export async function getSubscription() {
  const response = await fetch(`${API_BASE_URL}/subscription`, {
    headers: {
      Authorization: await getOrRefreshAuth(),
    },
  });

  await assertOk(response);

  const data = await response.json();
  return GetSubscriptionResponseZ.parse(data);
}
