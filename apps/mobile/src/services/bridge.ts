import { API_BASE_URL } from "~/constants/apiBaseUrl";
import { assertOk } from "~/services/utils";
import { z } from "zod";
import { getOrRefreshAuth } from "~/services/auth-utils";
import { AddressZ } from "@squads/models/solana";
import { ActivityZ, CardSLChangeZ, CardWithdrawZ } from "~/services/activities";
import { DateTime, Duration } from "luxon";
import { Fields, Inquiry } from "react-native-persona";
import { Alert } from "react-native";
import { newResolvablePromise } from "~/utils/promise";
import { zodArrayIgnoreUnknown } from "~/utils/zod";

const RejectionReasonZ = z.object({
  developer_reason: z.string(),
  reason: z.string(),
});

export const BridgeAccountCountryZ = z.object({
  country: z.string(),
  region: z.string(),
});

export type BridgeAccountCountry = z.infer<typeof BridgeAccountCountryZ>;

export const KycEndorsementZ = z.union([
  z.literal("base"),
  z.literal("sepa"),
  z.literal("cards"),
  z.literal("unknown"),
]);

export type KycEndorsement = z.infer<typeof KycEndorsementZ>;

export const KycEndorsementStatusZ = z.union([
  z.literal("incomplete"),
  z.literal("approved"),
  z.literal("revoked"),
]);

export type KycEndorsementStatus = z.infer<typeof KycEndorsementStatusZ>;

const BridgeAccountZ = z.object({
  email: z.string(),
  kyc_link_id: z.string(),
  kyc_link: z.string(),
  //not_started pending incomplete awaiting_ubo manual_review under_review approved rejected
  kyc_status: z.union([
    // z.literal("pending"), // deprecated in favour of not_started
    z.literal("not_started"),
    z.literal("incomplete"),
    // z.literal("awaiting_ubo"), // irrelevant, used for KYB
    // z.literal("manual_review"), deprecated in favour of under_review
    z.literal("under_review"),
    z.literal("approved"),
    z.literal("rejected"),
  ]),
  customer_id: z.string(),
  rejection_reasons: z.array(RejectionReasonZ).nullable(),
  virtual_account_id: z.string().nullable(),
  country: BridgeAccountCountryZ.nullable(),
  endorsements: z.array(
    z.object({
      name: KycEndorsementZ,
      status: KycEndorsementStatusZ,
      requirements: z.object({
        pending: z.array(z.string()),
      }),
    })
  ),
});

export type BridgeAccount = z.infer<typeof BridgeAccountZ>;

export const BridgeAccount = {
  endorsement(account: BridgeAccount, endorsement: KycEndorsement) {
    return account.endorsements.find((e) => e.name === endorsement);
  },
};

export const CreateBridgeAccountResponseZ = z.object({
  account: BridgeAccountZ,
});

export async function createBridgeAccount({
  email,
  endorsements,
}: {
  email: string;
  endorsements?: KycEndorsement[];
}) {
  const response = await fetch(`${API_BASE_URL}/bridge/account`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
    body: JSON.stringify({ email, endorsements }),
  });

  await assertOk(response);

  const data = await response.json();

  return CreateBridgeAccountResponseZ.parse(data);
}

export const BridgeAccountResponseZ = z.object({
  account: BridgeAccountZ.nullable(),
});

export async function getBridgeAccount() {
  const response = await fetch(`${API_BASE_URL}/bridge/account`, {
    headers: {
      Authorization: await getOrRefreshAuth(),
    },
  });

  await assertOk(response);

  const data = await response.json();
  return BridgeAccountResponseZ.parse(data);
}

export const UpdateKycResponseZ = z.object({
  kycLink: z.string(),
});

export async function updateKycLink({
  endorsement,
}: {
  endorsement: KycEndorsement;
}) {
  const response = await fetch(`${API_BASE_URL}/bridge/kyc/update`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
    body: JSON.stringify({ endorsement }),
  });

  await assertOk(response);

  const data = await response.json();
  return UpdateKycResponseZ.parse(data);
}

const DepositInstructionsZ = z.discriminatedUnion("currency", [
  z.object({
    currency: z.literal("eur"),
    bank_name: z.string(),
    bank_address: z.string(),

    iban: z.string(),
    bic: z.string(),
    account_holder_name: z.string(),
  }),
  z.object({
    currency: z.literal("usd"),
    bank_name: z.string(),
    bank_address: z.string(),

    bank_routing_number: z.string(),
    bank_account_number: z.string(),
    bank_beneficiary_name: z.string(),
    bank_beneficiary_address: z.string().nullable(),
  }),
]);

export type DepositInstructions = z.infer<typeof DepositInstructionsZ>;

const VirtualAccountZ = z.object({
  deposit_instructions: DepositInstructionsZ,
});

export type VirtualAccount = z.infer<typeof VirtualAccountZ>;

export const GetVirtualAccountResponseZ = z.object({
  virtual_account: VirtualAccountZ.nullable(),
});

export async function getVirtualAccount({
  currency,
}: {
  currency: "eur" | "usd";
}) {
  const searchParams = new URLSearchParams({
    currency,
  });

  const response = await fetch(
    `${API_BASE_URL}/bridge/virtual_account?${searchParams}`,
    {
      headers: {
        Authorization: await getOrRefreshAuth(),
      },
    }
  );

  await assertOk(response);

  const data = await response.json();
  return GetVirtualAccountResponseZ.parse(data);
}

export const CreateVirtualAccountResponseZ = z.object({
  virtual_account: VirtualAccountZ,
});

export async function createVirtualAccount({
  currency,
}: {
  currency: "eur" | "usd";
}) {
  const response = await fetch(`${API_BASE_URL}/bridge/virtual_account`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
    body: JSON.stringify({ currency }),
  });

  await assertOk(response);

  const data = await response.json();

  return CreateVirtualAccountResponseZ.parse(data);
}

const BeneficialAddressZ = z.object({
  streetLine1: z.string().min(1),
  country: z.string().min(1),
  city: z.string().min(1),
  state: z.string().min(1),
  postalCode: z.string().min(1),
});

type BeneficialAddress = z.infer<typeof BeneficialAddressZ>;

export const BankAccountZ = z.discriminatedUnion("type", [
  z.object({
    type: z.literal("us"),
    bankName: z.string().min(1),
    accountNumber: z.string().min(1),
    routingNumber: z.string().min(1),
    checkingOrSavings: z.union([z.literal("checking"), z.literal("savings")]),
  }),
  z.object({
    type: z.literal("iban"),
    bankName: z.string().min(1),
    accountNumber: z.string().min(1),
  }),
]);

export type BankAccount = z.infer<typeof BankAccountZ>;

export type CreateExternalAccountRequest = {
  owner: {
    firstName: string;
    lastName: string;
    address: BeneficialAddress | null;
  };
  bankAccount: BankAccount;
};

const CurrencyZ = z.union([z.literal("usd"), z.literal("eur")]);
export type Currency = z.infer<typeof CurrencyZ>;

const ExternalAccountZ = z.object({
  id: z.string(),
  accountOwnerName: z.string(),
  bankName: z.string(),
  accountNumber: z.string(),
  currency: CurrencyZ,
});

export type ExternalAccount = z.infer<typeof ExternalAccountZ>;

export const CreateExternalAccountResponseZ = z.object({
  externalAccount: ExternalAccountZ,
});

export async function createExternalAccount(
  account: CreateExternalAccountRequest
) {
  const response = await fetch(`${API_BASE_URL}/bridge/external_accounts`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
    body: JSON.stringify(account),
  });

  await assertOk(response);
  const data = await response.json();

  return CreateExternalAccountResponseZ.parse(data);
}

const GetExternalAccountsResponseZ = z.object({
  externalAccounts: z.array(ExternalAccountZ),
});

export async function getExternalAccounts() {
  const response = await fetch(`${API_BASE_URL}/bridge/external_accounts`, {
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
  });

  await assertOk(response);

  return GetExternalAccountsResponseZ.parse(await response.json());
}

const CountryZ = z.object({
  name: z.string(),
  alpha3: z.string(),
  postal_code_format: z.string().nullable(),
  subdivisions: z.array(z.object({ name: z.string(), code: z.string() })),
});

const GetCountriesListResponseZ = z.object({
  us: CountryZ,
  eu: z.array(CountryZ),
});

export async function getCountriesList() {
  const response = await fetch(`${API_BASE_URL}/bridge/countries_list`, {
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
  });

  await assertOk(response);

  return GetCountriesListResponseZ.parse(await response.json());
}

const BridgeFeesForRailsZ = z.object({
  offRamp: z.object({
    railsFee: z.object({
      sepa: z.number(),
      achSameDay: z.number(),
    }),
    fuseFeePercent: z.number(),
    freeAmountLimitUsd: z.number(),
    usedAmountUsd: z.number(),
  }),
});

type BridgeFeesForRails = z.infer<typeof BridgeFeesForRailsZ>;

export type BridgeFees = {
  railsFee: number;
  fuseFeePercent: number;
  rails: "sepa" | "achSameDay";
  remainingFreeAmountUsd: number | null;
};

export const BridgeFees = {
  forCurrency: (
    offRamp: BridgeFeesForRails["offRamp"],
    currency: "usd" | "eur"
  ): BridgeFees => {
    const rails = currency === "eur" ? "sepa" : "achSameDay";

    const remainingFreeAmountUsd =
      offRamp.freeAmountLimitUsd > 0
        ? offRamp.freeAmountLimitUsd - offRamp.usedAmountUsd
        : null;

    return {
      rails,
      railsFee: offRamp.railsFee[rails],
      fuseFeePercent: offRamp.fuseFeePercent,
      remainingFreeAmountUsd,
    };
  },
  getAmountFees({
    fees,
    amountFloat,
  }: {
    fees: BridgeFees;
    amountFloat: number;
  }) {
    const totalFee = toFixed2(
      fees.railsFee + amountFloat * (fees.fuseFeePercent / 100)
    );
    const remainingFreeAmountUsd = fees.remainingFreeAmountUsd;

    if (
      remainingFreeAmountUsd !== null &&
      amountFloat <= remainingFreeAmountUsd
    ) {
      return { type: "onlyRailsFee", value: toFixed2(fees.railsFee) } as const;
    }

    return {
      type: "totalFee",
      value: totalFee,
      remainingFreeAmountUsd,
    } as const;
  },
  addFees({
    fees,
    amountFloat,
  }: {
    fees: BridgeFees;
    amountFloat: number;
  }): number {
    return amountFloat + BridgeFees.getAmountFees({ fees, amountFloat }).value;
  },
};

function toFixed2(amount: number) {
  return Math.trunc(amount * 100) / 100;
}

export async function getBridgeFees() {
  const response = await fetch(`${API_BASE_URL}/bridge/fees`, {
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
  });

  await assertOk(response);

  return BridgeFeesForRailsZ.parse(await response.json());
}

const ExchangeRateResponseZ = z.object({
  midmarketRate: z.number(),
  buyRate: z.number(),
  sellRate: z.number(),
});

export async function getExchangeRate({
  from,
  to,
}: {
  from: Currency;
  to: Currency;
}) {
  const params = new URLSearchParams({ from, to });

  const response = await fetch(
    `${API_BASE_URL}/bridge/exchange_rate?${params}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: await getOrRefreshAuth(),
      },
    }
  );

  await assertOk(response);

  return ExchangeRateResponseZ.parse(await response.json());
}

export async function deleteExternalAccount({
  externalAccountId,
}: {
  externalAccountId: string;
}) {
  const response = await fetch(
    `${API_BASE_URL}/bridge/delete_external_account`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: await getOrRefreshAuth(),
      },
      body: JSON.stringify({ externalAccountId }),
    }
  );

  await assertOk(response);
}

const CardPendingActionZ = z.object({
  activityId: z.string().uuid(),
  approvedAt: z.number(),
  details: z.discriminatedUnion("type", [CardWithdrawZ, CardSLChangeZ]),
});

export type CardPendingAction = z.infer<typeof CardPendingActionZ>;

export const CardPendingAction = {
  isSLChange(
    pendingAction: CardPendingAction
  ): pendingAction is CardPendingAction & {
    details: { type: "cardSLChange" };
  } {
    return pendingAction.details.type === "cardSLChange";
  },

  isWithdrawal(
    pendingAction: CardPendingAction
  ): pendingAction is CardPendingAction & {
    details: { type: "cardWithdraw" };
  } {
    return pendingAction.details.type === "cardWithdraw";
  },
};

const CardZ = z.object({
  cardMultisigKey: AddressZ,
  cardVaultAddress: AddressZ,
  cardImageUrl: z.string(),
  timeLockSeconds: z.number(),
  status: z.string(),
  pendingAction: CardPendingActionZ.nullable(),
  spendingLimits: z.record(
    AddressZ,
    z.object({
      amount: z.number(),
      remainingAmount: z.number(),
      lastReset: z.number(),
    })
  ),
  details: z.object({
    last4: z.string(),
  }),
  referral: z.object({
    inviteCode: z.string(),
    used: z.number(),
    remaining: z.number(),
  }),
});

export type Card = z.infer<typeof CardZ>;

export const Card = {
  pendingActivityTimeLock(card: Card) {
    const activity = card.pendingAction;
    if (!activity) {
      return undefined;
    }

    const timeLockStarted = DateTime.fromSeconds(activity.approvedAt);
    const timeLockDuration = Duration.fromObject({
      seconds: card.timeLockSeconds,
    });
    const timeLockElapsed = DateTime.now().diff(timeLockStarted);
    const timeLockRemaining = timeLockDuration.minus(timeLockElapsed);

    const isReady = timeLockRemaining.as("seconds") < 1;

    return {
      isReady,
      timeLockRemaining,
    };
  },
};

export const GetCardResponseZ = z.object({
  card: CardZ.nullable(),
});

export async function getCard() {
  const response = await fetch(`${API_BASE_URL}/bridge/card`, {
    headers: {
      Authorization: await getOrRefreshAuth(),
    },
  });

  await assertOk(response);

  const data = await response.json();
  return GetCardResponseZ.parse(data);
}

export async function freezeCard() {
  const response = await fetch(`${API_BASE_URL}/bridge/card/freeze`, {
    method: "POST",
    headers: {
      Authorization: await getOrRefreshAuth(),
    },
  });

  await assertOk(response);

  const data = await response.json();
  return CardZ.parse(data);
}

export async function unfreezeCard() {
  const response = await fetch(`${API_BASE_URL}/bridge/card/unfreeze`, {
    method: "POST",
    headers: {
      Authorization: await getOrRefreshAuth(),
    },
  });

  await assertOk(response);

  const data = await response.json();
  return CardZ.parse(data);
}

export const CardTransactionCategoryZ = z.union([
  z.literal("other"),
  z.literal("shopping"),
  z.literal("bills"),
  z.literal("healthcare"),
  z.literal("groceries"),
  z.literal("eatingOut"),
  z.literal("entertainment"),
  z.literal("personalCare"),
  z.literal("fuel"),
  z.literal("education"),
  z.literal("transport"),
  z.literal("trips"),
]);

export type CardTransactionCategory = z.infer<typeof CardTransactionCategoryZ>;

export const CardTransactionCategory = {
  toString: (category: CardTransactionCategory) => {
    switch (category) {
      case "other":
        return "General";
      case "shopping":
        return "Shopping";
      case "bills":
        return "Bills";
      case "healthcare":
        return "Healthcare";
      case "groceries":
        return "Groceries";
      case "eatingOut":
        return "Coffee & Restaurants";
      case "entertainment":
        return "Entertainment";
      case "personalCare":
        return "Personal Care";
      case "fuel":
        return "Fuel";
      case "education":
        return "Education";
      case "transport":
        return "Transport";
      case "trips":
        return "Trips";
      default:
        category satisfies never;
        return "Unknown";
    }
  },
};

const CardTransactionTypeZ = z.union([
  z.literal("adjustment"),
  z.literal("fee"),
  z.literal("purchase"),
  z.literal("refund"),
  z.literal("cryptoFunding"),
  z.literal("cryptoReturn"),
  z.literal("cryptoWithdrawal"),
  z.literal("unknown"),
]);

const CardTransactionZ = z.object({
  id: z.string(),
  category: CardTransactionCategoryZ,
  type: CardTransactionTypeZ,
  amount: z.number(),
  postedAt: z.number(),
  authorizedAt: z.number().nullable(),
  description: z.string(),
  merchant: z
    .object({
      merchantName: z.string(),
      merchantLocation: z.string(),
    })
    .nullable(),
  localTransactionDetails: z
    .object({
      amount: z.string(),
      currency: z.string(),
      exchangeRate: z.string(),
    })
    .nullable(),
});

export type CardTransaction = z.infer<typeof CardTransactionZ>;

const CardAuthorizationZ = z.object({
  id: z.string(),
  category: CardTransactionCategoryZ,
  billingAmount: z.number(),
  amount: z.number(),
  authorizedAt: z.number(),
  description: z.string(),
});

export type CardAuthorization = z.infer<typeof CardAuthorizationZ>;

const CardDeclineZ = z.object({
  id: z.string(),
  category: CardTransactionCategoryZ,
  amount: z.number(),
  declinedAt: z.number(),
  description: z.string(),
});

const CardEventZ = z.discriminatedUnion("type", [
  z.object({ type: z.literal("onchainActivity"), activity: ActivityZ }),
  z.object({ type: z.literal("transaction"), transaction: CardTransactionZ }),
  z.object({
    type: z.literal("authorization"),
    authorization: CardAuthorizationZ,
  }),
  z.object({ type: z.literal("decline"), decline: CardDeclineZ }),
]);

export type CardEvent = z.infer<typeof CardEventZ>;

export const CardEvent = {
  timestamp: (event: CardEvent) => {
    switch (event.type) {
      case "onchainActivity":
        const executeTx = event.activity.transactions.find(
          (tx) => tx.transactionType === "execute"
        );
        return executeTx?.timestamp ?? event.activity.timestamp;
      case "transaction":
        return event.transaction.authorizedAt ?? event.transaction.postedAt;
      case "authorization":
        return event.authorization.authorizedAt;
      case "decline":
        return event.decline.declinedAt;
      default:
        event satisfies never;
        return 0;
    }
  },
  id: (event: CardEvent) => {
    switch (event.type) {
      case "onchainActivity":
        return event.activity.activityId;
      case "authorization":
        return event.authorization.id;
      case "transaction":
        return event.transaction.id;
      case "decline":
        return event.decline.id;
      default:
        event satisfies never;
        return "0";
    }
  },
  description: (event: CardEvent) => {
    switch (event.type) {
      case "authorization":
        return trimSpaces(event.authorization.description);

      case "onchainActivity":
        switch (event.activity.details.type) {
          case "cardWithdraw":
            return "Withdrawal";
          case "cardTopUp":
            return "Deposit";
          default:
            return null;
        }

      case "transaction":
        return trimSpaces(
          event.transaction.merchant?.merchantName ??
            event.transaction.description
        );

      case "decline":
        return trimSpaces(event.decline.description);

      default:
        event satisfies never;
        return null;
    }
  },
};

function trimSpaces(str: string) {
  return str.replace(/\s+/g, " ");
}

export const CardEventsZ = z.object({
  events: zodArrayIgnoreUnknown(CardEventZ),
  nextPage: z.number().nullable(),
});

export type CardEvents = z.infer<typeof CardEventsZ>;

export async function getCardEvents({ page }: { page: number }) {
  const response = await fetch(
    `${API_BASE_URL}/bridge/card/events?page=${page}`,
    {
      headers: {
        Authorization: await getOrRefreshAuth(),
      },
    }
  );

  await assertOk(response);

  const data = await response.json();

  return CardEventsZ.parse(data);
}

export const RequestCardResponseZ = z.object({
  requestedAt: z.number(),
});

export async function requestCard() {
  const response = await fetch(`${API_BASE_URL}/bridge/card/request`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
  });

  await assertOk(response);
  const data = await response.json();

  return RequestCardResponseZ.parse(data);
}

export async function getBankStatement({ date }: { date: string }) {
  const response = await fetch(`${API_BASE_URL}/bridge/card/statement`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
    body: JSON.stringify({ date }),
  });

  await assertOk(response);

  return response.arrayBuffer();
}

const kycLinkSchema = z.union([
  z.object({ "inquiry-id": z.string() }),
  z.object({
    "inquiry-template-id": z.string(),
    "reference-id": z.string(),
    "environment-id": z.string().optional(),
    "fields[iqt_token]": z.string(),
    "fields[developer_id]": z.string(),
  }),
]);

export function openKycFlow({
  kycLink,
}: {
  kycLink: string;
}): Promise<{ status: "completed" | "cancelled" }> {
  const queryParams = new URL(kycLink).searchParams;

  const kycLinkParams = kycLinkSchema.parse(
    Object.fromEntries(queryParams.entries())
  );

  let builder;
  if ("inquiry-id" in kycLinkParams) {
    builder = Inquiry.fromInquiry(kycLinkParams["inquiry-id"]);
  } else {
    builder = Inquiry.fromTemplate(kycLinkParams["inquiry-template-id"]);

    if (kycLinkParams["environment-id"]) {
      builder.environmentId(kycLinkParams["environment-id"]);
    }

    builder
      .referenceId(kycLinkParams["reference-id"])
      .fields(
        Fields.builder()
          .string("developer_id", kycLinkParams["fields[developer_id]"])
          .string("iqt_token", kycLinkParams["fields[iqt_token]"])
          .build()
      );
  }

  const promise = newResolvablePromise<{ status: "completed" | "cancelled" }>();
  builder
    .onComplete((inquiryId, status, fields) => {
      promise.resolve({ status: "completed" });
    })
    .onCanceled((inquiryId, sessionToken) => {
      promise.resolve({ status: "cancelled" });
    })
    .onError((error) => {
      promise.reject(error);
      Alert.alert("Failed to handle kyc", error.message);
    })
    .build()
    .start();

  return promise;
}

const InviteCodeResponseZ = z.discriminatedUnion("type", [
  z.object({ type: z.literal("valid") }),
  z.object({ type: z.literal("notFound") }),
  z.object({ type: z.literal("quotaExceeded") }),
]);

export async function verifyInviteCode(code: string) {
  const response = await fetch(`${API_BASE_URL}/bridge/card/invite_code`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
    body: JSON.stringify({ code }),
  });
  await assertOk(response);
  const data = await response.json();

  return InviteCodeResponseZ.parse(data);
}

export const GetCardBalanceResponseZ = z.object({
  balances: z.record(
    AddressZ,
    z.object({
      available: z.number(),
      hold: z.number(),
      onchain: z.number(),
      total: z.number(),
      billing: z.number(),
      reserved: z.number(),
    })
  ),
  authLimits: z.array(
    z.object({
      period: z.string(),
      remaining: z.number(),
      total: z.number(),
      currency: z.string(),
      nextResetAt: z.number(),
    })
  ),
});

export type CardBalance = z.infer<typeof GetCardBalanceResponseZ>;

export async function getCardBalance() {
  const response = await fetch(`${API_BASE_URL}/bridge/card/balance`, {
    headers: {
      Authorization: await getOrRefreshAuth(),
    },
  });

  await assertOk(response);

  const data = await response.json();
  return GetCardBalanceResponseZ.parse(data);
}

export const CreatePinLinkResponseZ = z.object({
  path: z.string(),
});

export async function createPinLink() {
  const response = await fetch(`${API_BASE_URL}/bridge/card/pin_link`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
  });

  await assertOk(response);

  const data = await response.json();
  return CreatePinLinkResponseZ.parse(data);
}

export async function sendComplaint({
  name,
  email,
  reason,
  description,
}: {
  name: string;
  email: string;
  reason: string;
  description: string;
}) {
  const response = await fetch(`${API_BASE_URL}/bridge/card/complaint`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
    body: JSON.stringify({ name, email, reason, description }),
  });

  await assertOk(response);
}

export async function cardProvisioningRequestDetails() {
  return {
    url: `${API_BASE_URL}/bridge/card/provisioning`,
    headers: {
      Authorization: await getOrRefreshAuth(),
    },
  };
}

export const GetVirtualAccountsNeedMigrationResponseZ = z.object({
  needMigration: z.boolean(),
});

export async function getVirtualAccountsNeedMigration() {
  const response = await fetch(
    `${API_BASE_URL}/bridge/virtual_accounts_need_migration`,
    {
      headers: {
        Authorization: await getOrRefreshAuth(),
      },
    }
  );

  await assertOk(response);
  const data = await response.json();

  return GetVirtualAccountsNeedMigrationResponseZ.parse(data);
}

export async function migrateVirtualAccounts() {
  const response = await fetch(
    `${API_BASE_URL}/bridge/migrate_virtual_accounts`,
    {
      method: "POST",
      headers: {
        Authorization: await getOrRefreshAuth(),
      },
    }
  );

  await assertOk(response);
}

const MonthlyCardSpendingZ = z.object({
  month: z.number(),
  byCategory: z.array(
    z.object({
      category: CardTransactionCategoryZ,
      amount: z.number(),
    })
  ),
});

export type MonthlyCardSpending = z.infer<typeof MonthlyCardSpendingZ>;

const GetCardSpendingResponseZ = z.object({
  byMonth: z.array(MonthlyCardSpendingZ),
});

export type CardSpending = z.infer<typeof GetCardSpendingResponseZ>;

export async function getCardSpending() {
  const response = await fetch(`${API_BASE_URL}/bridge/card/spending`, {
    headers: {
      Authorization: await getOrRefreshAuth(),
    },
  });

  await assertOk(response);
  const data = await response.json();

  return GetCardSpendingResponseZ.parse(data);
}

const CardTransactionsByMonthResponseZ = z.object({
  transactions: z.array(CardTransactionZ),
});

export async function getCardTransactionsByMonth({ month }: { month: number }) {
  const response = await fetch(
    `${API_BASE_URL}/bridge/card/transactions_by_month`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: await getOrRefreshAuth(),
      },
      body: JSON.stringify({ month }),
    }
  );

  await assertOk(response);
  const data = await response.json();

  return CardTransactionsByMonthResponseZ.parse(data);
}
