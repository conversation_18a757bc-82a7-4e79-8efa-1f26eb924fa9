import { API_BASE_URL } from "~/constants/apiBaseUrl";
import { getOrRefreshAuth } from "~/services/auth-utils";
import { assertOk } from "~/services/utils";
import { z } from "zod";
import { Address } from "@squads/models/solana";

export const FUSE_SOL_MINT =
  "fuseYvhNJbSzdDByyTCrLcogsoNwAviB1WeewhbqgFc" as Address;

const LstPriceResponseZ = z.object({
  price: z.number(),
});

const FuseSolApyResponseZ = z.object({
  apy: z.number(),
});

export async function getLstPrice({ mint }: { mint: string }) {
  const response = await fetch(
    `${API_BASE_URL}/tokens/liquid/${mint}/price_sol`,
    {
      headers: { Authorization: await getOrRefreshAuth() },
    }
  );

  await assertOk(response);

  return LstPriceResponseZ.parse(await response.json());
}

export async function getFuseSolApy() {
  const response = await fetch(`${API_BASE_URL}/tokens/fuseSol/apy`, {
    headers: { Authorization: await getOrRefreshAuth() },
  });

  await assertOk(response);

  return FuseSolApyResponseZ.parse(await response.json());
}
