import { AddressZ } from "@squads/models/solana";
import { API_BASE_URL } from "~/constants/apiBaseUrl";
import { getOrRefreshAuth } from "~/services/auth-utils";
import { assertOk } from "~/services/utils";
import { z } from "zod";

const LendingApyZ = z.object({
  lending: z.number(),
  rewards: z.number(),
});

export type LendingApy = z.infer<typeof LendingApyZ>;

export const LendingApy = {
  total: (apy: LendingApy) => {
    return apy.lending + apy.rewards;
  },
};

const DriftSpotMarketZ = z.object({
  mint: AddressZ,
  apy: LendingApyZ,
  tvl: z.number(),
});

export type DriftSpotMarket = z.infer<typeof DriftSpotMarketZ>;

const DriftSpotMarketsZ = z.object({
  spotMarkets: z.array(DriftSpotMarketZ),
});

export type DriftSpotMarkets = z.infer<typeof DriftSpotMarketsZ>;

export async function getDriftSpotMarkets() {
  const response = await fetch(`${API_BASE_URL}/earn/drift/spot_markets`, {
    headers: { Authorization: await getOrRefreshAuth() },
  });

  await assertOk(response);

  const data = await response.json();
  return DriftSpotMarketsZ.parse(data);
}

const LuloProtectedPoolZ = z.object({
  apy: z.number(),
  openCapacity: z.number(),
  price: z.number(),
});

export const LuloPoolsZ = z.object({
  protected: LuloProtectedPoolZ,
});
export type LuloPools = z.infer<typeof LuloPoolsZ>;

const LuloPoolsResponseZ = z.object({
  pools: LuloPoolsZ,
});

export async function getLuloPools() {
  const response = await fetch(`${API_BASE_URL}/earn/lulo/pools`, {
    headers: { Authorization: await getOrRefreshAuth() },
  });

  await assertOk(response);

  const data = await response.json();

  return LuloPoolsResponseZ.parse(data).pools;
}

export const EarnProviderZ = z.union([
  z.literal("kamino"),
  z.literal("drift"),
  z.literal("lulo"),
]);
export type EarnProvider = z.infer<typeof EarnProviderZ>;

export const EARN_PROVIDERS = EarnProviderZ.options.map((p) => p.value);

export function earnProviderName(provider: EarnProvider) {
  switch (provider) {
    case "drift":
      return "Drift";
    case "lulo":
      return "Lulo";
    case "kamino":
      return "Kamino";
    default:
      return provider satisfies never;
  }
}

const KaminoVaultZ = z.object({
  apy: z.number(),
});

export type KaminoVault = z.infer<typeof KaminoVaultZ>;

const GetKaminoVaultsResponseZ = z.object({
  vault: KaminoVaultZ,
});

export async function getKaminoVault() {
  const response = await fetch(`${API_BASE_URL}/earn/kamino/vault`, {
    headers: { Authorization: await getOrRefreshAuth() },
  });

  await assertOk(response);

  const data = await response.json();
  return GetKaminoVaultsResponseZ.parse(data);
}
