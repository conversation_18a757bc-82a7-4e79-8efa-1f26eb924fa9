import { z } from "zod";
import * as <PERSON><PERSON> from "@squads/models/solana";
import { Address, AddressZ } from "@squads/models/solana";
import { Base58 } from "@squads/models/encoding";
import { API_BASE_URL } from "~/constants/apiBaseUrl";
import { SwapQuote } from "~/services/swaps";
import { assertOk } from "~/services/utils";
import { RecoveringKeyDetails } from "~/services/recovery";
import { abbreviateAddress } from "@squads/utils/abbreviateAddress";
import { getOrRefreshAuth } from "~/services/auth-utils";
import { IsoDateTime } from "~/utils/zod";
import { SpendingLimitPeriod } from "~/services/spendingLimits";

export const Base64Z = z
  .string()
  .refine((string) => {
    try {
      return !!Buffer.from(string, "base64");
    } catch {
      return false;
    }
  })
  .brand("Base64");

export type Base64 = z.infer<typeof Base64Z>;

const RelayerKeyZ = z.object({
  address: AddressZ,
});
export const DeviceKeyZ = z.object({
  address: AddressZ,
  modelName: z.string(),
  deviceName: z.string(),
});

export type DeviceKey = z.infer<typeof DeviceKeyZ>;

export const CloudKeyDetailsZ = z.union([
  z.object({
    type: z.literal("iCloud"),
  }),
  z.object({
    type: z.literal("ledger"),
    derivationPath: z.string(),
    deviceId: z.string(),
  }),
  z.object({
    type: z.literal("email"),
    email: z.string(),
  }),
]);
export const CloudKeyZ = z.object({
  address: AddressZ,
  details: CloudKeyDetailsZ,
});

export type CloudKey = z.infer<typeof CloudKeyZ>;

export const CloudKey = {
  getLabel(cloudKey: CloudKey) {
    switch (cloudKey.details.type) {
      case "email":
        return "Email";
      case "iCloud":
        return "Cloud";
      case "ledger":
        return "Ledger";
    }
  },

  getValue(cloudKey: CloudKey): string | Address {
    switch (cloudKey.details.type) {
      case "email":
        return cloudKey.details.email;
      case "iCloud":
        return cloudKey.address;
      case "ledger":
        return cloudKey.address;
    }
  },
};

export const RecoveryKeyZ = z.discriminatedUnion("recoveryKeyType", [
  z.object({
    address: AddressZ,
    recoveryKeyType: z.literal("selfCustody"),
  }),
  z.object({
    address: AddressZ,
    recoveryKeyType: z.literal("email"),
    email: z.string(),
  }),
  z.object({
    address: AddressZ,
    recoveryKeyType: z.literal("phone"),
    phone: z.string(),
  }),
  z.object({
    address: AddressZ,
    recoveryKeyType: z.literal("turnkeyEmail"),
    email: z.string(),
    subOrganizationId: z.string(),
  }),
  z.object({
    address: AddressZ,
    recoveryKeyType: z.literal("keystone"),
    derivationPath: z.string(),
    masterFingerprint: z.string(),
  }),
]);

export type RecoveryKey = z.infer<typeof RecoveryKeyZ>;

export const RecoveryKey = {
  getLabel(recoveryKey: RecoveryKey) {
    switch (recoveryKey.recoveryKeyType) {
      case "selfCustody":
        return "Wallet";
      case "email":
      case "turnkeyEmail":
        return "Email";
      case "phone":
        return "Phone";
      case "keystone":
        return "Keystone";
      default:
        return recoveryKey satisfies never;
    }
  },

  getValue(recoveryKey: RecoveryKey): string | Address {
    switch (recoveryKey.recoveryKeyType) {
      case "selfCustody":
      case "keystone":
        return recoveryKey.address;
      case "email":
      case "turnkeyEmail":
        return recoveryKey.email;
      case "phone":
        return recoveryKey.phone;
      default:
        return recoveryKey satisfies never;
    }
  },

  getStringValue(recoveryKey: RecoveryKey) {
    switch (recoveryKey.recoveryKeyType) {
      case "selfCustody":
      case "keystone":
        return abbreviateAddress(recoveryKey.address, 4);
      case "email":
      case "turnkeyEmail":
        return recoveryKey.email;
      case "phone":
        return recoveryKey.phone;
      default:
        return recoveryKey satisfies never;
    }
  },
};

const WalletKeysZ = z.object({
  relayerKey: RelayerKeyZ,
  deviceKey: DeviceKeyZ,
  cloudKey: CloudKeyZ,
  recoveryKeys: z.array(RecoveryKeyZ).nullable(),
});

export type WalletKeys = z.infer<typeof WalletKeysZ>;

export const WalletKeys = {
  allKeys(walletKeys: WalletKeys) {
    return [
      walletKeys.relayerKey.address,
      walletKeys.deviceKey.address,
      walletKeys.cloudKey.address,
      ...(walletKeys.recoveryKeys ?? [])?.map((key) => key.address),
    ].flat();
  },
};

export const VaultZ = z.object({
  index: z.number(),
  key: Solana.AddressZ,
  name: z.string().nullable(),
});
export type Vault = z.infer<typeof VaultZ>;

const WalletCardZ = z.discriminatedUnion("status", [
  z.object({
    status: z.literal("requested"),
    requestedAt: z.number(),
  }),
  z.object({
    status: z.literal("issued"),
    cardVaultAddress: AddressZ,
    last4: z.string(),
  }),
]);

export type WalletCard = z.infer<typeof WalletCardZ>;

export const WalletCard = {
  label(card: WalletCard & { status: "issued" }) {
    return `Visa  · · · ${card.last4}`;
  },
};

export const WalletZ = z.object({
  walletKey: Solana.AddressZ,
  defaultVault: Solana.AddressZ,
  vaults: z.array(VaultZ),
  keys: WalletKeysZ,
  createdAt: IsoDateTime,
  pushNotificationsSetUp: z.boolean(),
  pushNotificationsEnabled: z.boolean(),
  payFeesWithFuseSol: z.boolean(),
  card: WalletCardZ.nullable(),
});

export type Wallet = z.infer<typeof WalletZ>;

const CreateInitResponseZ = z.object({
  transactions: Base64Z.array(),
});

export async function createInitV2({
  createKey,
  keyId,
  assertion,
  deviceKey,
  cloudKey,
  recoveryKey,
  deviceCheckToken,
}: {
  createKey: Base58;
  keyId: Base64;
  assertion: Base64;
  deviceKey: {
    address: Address;
    deviceName: string;
    modelName: string;
  };
  cloudKey: {
    address: Address;
  };
  recoveryKey?: RecoveryKey;
  deviceCheckToken: String;
}) {
  const response = await fetch(`${API_BASE_URL}/wallets/create/init_v2`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      createKey,
      keyId,
      assertion,
      deviceKey,
      cloudKey,
      recoveryKey,
      deviceCheckToken,
    }),
  });

  await assertOk(response);

  return CreateInitResponseZ.parse(await response.json());
}

const CreateConfirmResponseZ = z.object({
  walletKey: Solana.AddressZ,
  token: z.string(),
  wallet: WalletZ,
});

export async function createConfirmV2({
  createKey,
  keyId,
  assertion,
  transactions,
  referralCode,
}: {
  createKey: Base58;
  keyId: Base64;
  assertion: Base64;
  transactions: Base64[];
  referralCode?: string;
}) {
  const response = await fetch(`${API_BASE_URL}/wallets/create/confirm_v2`, {
    headers: { "Content-Type": "application/json" },
    method: "POST",
    body: JSON.stringify({
      createKey,
      keyId,
      assertion,
      transactions,
      referralCode,
    }),
  });

  await assertOk(response);

  return CreateConfirmResponseZ.parse(await response.json());
}

const GetWalletResponseZ = z.object({
  wallet: WalletZ.nullable(),
});

export type GetWalletResponse = z.infer<typeof GetWalletResponseZ>;

export async function getWallet({ walletKey }: { walletKey: Address }) {
  const response = await fetch(`${API_BASE_URL}/wallets/${walletKey}`, {
    headers: { Authorization: await getOrRefreshAuth() },
  });

  await assertOk(response);

  return GetWalletResponseZ.parse(await response.json());
}

const GetWalletByMemberResponseZ = z.object({
  walletKey: AddressZ.nullable(),
});

export type WalletByMember = z.infer<typeof GetWalletByMemberResponseZ>;

export async function getByMember({ memberKey }: { memberKey: Address }) {
  const response = await fetch(
    `${API_BASE_URL}/wallets/by_member/${memberKey}`
  );

  await assertOk(response);

  return GetWalletByMemberResponseZ.parse(await response.json());
}

export async function updateVaultName({
  walletKey,
  vaultKey,
  vaultName,
}: {
  walletKey: Address;
  vaultKey: Address;
  vaultName: string;
}) {
  const response = await fetch(`${API_BASE_URL}/wallets/update_vault_name`, {
    headers: {
      Authorization: await getOrRefreshAuth(),
      "Content-Type": "application/json",
    },
    method: "POST",
    body: JSON.stringify({
      walletKey,
      vaultKey,
      vaultName,
    }),
  });

  await assertOk(response);

  return GetWalletResponseZ.parse(await response.json());
}

export async function updateLedgerDeviceId({ deviceId }: { deviceId: string }) {
  const response = await fetch(
    `${API_BASE_URL}/wallets/update_ledger_device_id`,
    {
      headers: {
        Authorization: await getOrRefreshAuth(),
        "Content-Type": "application/json",
      },
      method: "POST",
      body: JSON.stringify({ deviceId }),
    }
  );

  await assertOk(response);
}

export async function setPushNotificationsToken({ token }: { token: string }) {
  const response = await fetch(
    `${API_BASE_URL}/wallets/set_push_notifications_token`,
    {
      headers: {
        Authorization: await getOrRefreshAuth(),
        "Content-Type": "application/json",
      },
      method: "POST",
      body: JSON.stringify({ pushNotificationsToken: token }),
    }
  );

  await assertOk(response);
}

export async function updatePushNotificationsSettings({
  enabled,
}: {
  enabled: boolean;
}) {
  const response = await fetch(
    `${API_BASE_URL}/wallets/update_push_notifications_settings`,
    {
      headers: {
        Authorization: await getOrRefreshAuth(),
        "Content-Type": "application/json",
      },
      method: "POST",
      body: JSON.stringify({ enabled }),
    }
  );

  await assertOk(response);
}

// transactions section

export const TransactionFeesZ = z.object({
  networkFee: z.number(),
  rentFee: z.number(),
  priorityFee: z.number(),
});

export type TransactionFees = z.infer<typeof TransactionFeesZ>;

export const TransactionFees = {
  total: (fees: TransactionFees) => {
    return fees.networkFee + fees.rentFee + fees.priorityFee;
  },
  add: (fees: TransactionFees, other: TransactionFees): TransactionFees => {
    return {
      networkFee: fees.networkFee + other.networkFee,
      rentFee: fees.rentFee + other.rentFee,
      priorityFee: fees.priorityFee + other.priorityFee,
    };
  },
  zero: () => {
    return { networkFee: 0, rentFee: 0, priorityFee: 0 };
  },
};

const GetWalletReferralResponseZ = z.object({
  code: z.string(),
  refereesCount: z.number(),
});

export async function getWalletReferral({ walletKey }: { walletKey: Address }) {
  const response = await fetch(
    `${API_BASE_URL}/wallets/${walletKey}/referral`,
    {
      headers: {
        "Content-Type": "application/json",
        Authorization: await getOrRefreshAuth(),
      },
    }
  );

  await assertOk(response);
  return GetWalletReferralResponseZ.parse(await response.json());
}

export type Action =
  | {
      type: "jupiterSwap";
      vaultIndex: number;
      quote: SwapQuote;
      mule: Address;
      dynamicSlippage: {
        maxBps: number;
      } | null;
    }
  | {
      type: "sendSol";
      vaultIndex: number;
      to: Address;
      lamports: number;
    }
  | {
      type: "sendToken";
      vaultIndex: number;
      to: Address;
      amount: number;
      decimals: number;
      tokenMint: Address;
    }
  | {
      type: "spendingLimitUse";
      vaultIndex: number;
      spendingLimitAddress: Address;
      amount: number;
      recipient: Address;
    }
  | {
      type: "spendingLimitAdd";
      vaultIndex: number;
      member: Address;
      amount: number;
      mint: Address | null;
      period: SpendingLimitPeriod;
    }
  | {
      type: "spendingLimitRemove";
      address: Address;
      vaultIndex: number;
    }
  | {
      type: "spendingLimitTransfer";
      vaultIndex: number;
      oldSpendingLimit: Address;
      newMember: Address;
    }
  | {
      type: "payFeesWithFuseSolEnable";
      vaultIndex: number;
    }
  | {
      type: "payFeesWithFuseSolDisable";
      vaultIndex: number;
    }
  | {
      type: "liquidStakeDeposit";
      vaultIndex: number;
      lamports: number;
    }
  | {
      type: "liquidStakeWithdraw";
      vaultIndex: number;
      amount: number;
    }
  | {
      type: "stake";
      //todo revise this?
      details:
        | {
            type: "deposit";
            vaultIndex: number;
            lamports: number;
            voteKey: Address;
          }
        | {
            type: "deactivate";
            vaultIndex: number;
            stakeKey: Address;
          }
        | {
            type: "withdraw";
            vaultIndex: number;
            lamports: number;
            stakeKey: Address;
          };
    }
  | {
      type: "updateRecoveryKeys";
      details:
        | { type: "addKey"; newKey: RecoveryKey }
        | { type: "removeKey"; oldKey: Address }
        | { type: "changeKey"; oldKey: Address; newKey: RecoveryKey };
    }
  | {
      type: "updateCloudKey";
      newKey: CloudKey;
    }
  | {
      type: "recoveryInit";
      newKey: RecoveringKeyDetails;
    }
  | {
      type: "recoveryExecute";
      activityId: string;
      recoveryKey: Address | null;
    }
  | {
      type: "recoveryApprove";
      activityId: string;
      recoveryKey: Address;
    }
  | {
      type: "recoveryCancel";
      activityId: string;
    }
  | {
      type: "sendNft";
      id: Address;
      vaultIndex: number;
      to: Address;
      amount: number;
    }
  | {
      type: "relayerWithdraw";
      to: Address;
    }
  | {
      type: "driftEarnDeposit";
      vaultIndex: number;
      deposit: {
        type: string;
        amount: number;
        decimals: number;
      };
    }
  | {
      type: "driftEarnWithdraw";
      vaultIndex: number;
      deposit: {
        type: string;
        amount: number;
        decimals: number;
      };
    }
  | {
      type: "luloProtectedEarnDeposit";
      vaultIndex: number;
      deposit: {
        type: "usdc";
        amount: number;
        decimals: number;
      };
    }
  | {
      type: "luloProtectedEarnWithdraw";
      vaultIndex: number;
      withdrawal: {
        type: "usdc";
        amount: number;
        decimals: number;
      };
    }
  | {
      type: "kaminoEarnDeposit";
      vaultIndex: number;
      deposit: {
        type: "usdc";
        amount: number;
        decimals: number;
      };
    }
  | {
      type: "kaminoEarnWithdraw";
      vaultIndex: number;
      withdrawal: {
        type: "usdc";
        amount: number;
        decimals: number;
      };
    }
  | {
      type: "sendFiat";
      vaultIndex: number;
      source: {
        mint: Address;
        amount: number;
      };
      destination: {
        externalAccountId: string;
      };
    }
  | {
      type: "cardCreate";
      vaultIndex: number;
    }
  | {
      type: "cardTopUp";
      vaultIndex: number;
      amount: number;
      mint: Address;
      spendingLimitAddress: Address | null;
    }
  | {
      type: "cardWithdrawInit";
      vaultIndex: number;
      amount: number;
      mint: Address;
    }
  | {
      type: "cardWithdrawExecute";
      activityId: string;
    }
  | {
      type: "cardSLChangeInit";
      vaultIndex: number;
      amount: number;
      mint: Address;
      period: SpendingLimitPeriod;
    }
  | {
      type: "cardSLChangeExecute";
      activityId: string;
    }
  | {
      type: "cardActivityCancel";
      activityId: string;
    };

const RelayerTopUpZ = z.discriminatedUnion("type", [
  z.object({
    type: z.literal("sol"),
    amount: z.number(),
    vaultIndex: z.number(),
  }),
  z.object({
    type: z.literal("fuseSol"),
    solAmount: z.number(),
    amount: z.number(),
    vaultIndex: z.number(),
  }),
]);

const OffRampFeeZ = z.object({
  type: z.literal("offRamp"),
  railsFee: z.number(),
  fuseFee: z.number(),
});
export type OffRampFee = z.infer<typeof OffRampFeeZ>;

const TokenFeeZ = z.object({
  type: z.literal("tokenFee"),
  mint: AddressZ,
  feeBps: z.number(),
});

export type TokenFee = z.infer<typeof TokenFeeZ>;

const PlatformFeeZ = z.discriminatedUnion("type", [OffRampFeeZ, TokenFeeZ]);

export type PlatformFee = z.infer<typeof PlatformFeeZ>;

export const SubscriptionPlan = {
  isFree(planId: string) {
    return planId === "fuse_free";
  },
};

const SubscriptionUsageZ = z.object({
  planId: z.string(),
  lamportsUsed: z.number(),
  lamportsLimit: z.number(),
  activitiesUsed: z.number(),
  activitiesLimit: z.number(),
});

export const SolanaFeesZ = z.discriminatedUnion("type", [
  z.object({
    type: z.literal("alwaysSponsored"),
    sponsoredFees: TransactionFeesZ,
  }),
  z.object({
    type: z.literal("sponsoredBySubscription"),
    sponsoredFees: TransactionFeesZ,
    subscriptionUsage: SubscriptionUsageZ,
  }),
  z.object({
    type: z.literal("selfFunded"),
    fees: TransactionFeesZ,
    subscriptionUsage: SubscriptionUsageZ,
  }),
]);

export type SolanaFees = z.infer<typeof SolanaFeesZ>;

const PrepareActionResponseZ = z.object({
  actionRequestId: z.string(),
  transactions: Base64Z.array(),
  solanaFees: SolanaFeesZ.nullable(),
  //deprecated
  fees: TransactionFeesZ,
  //deprecated
  relayerTopUp: RelayerTopUpZ.nullable(),
  //deprecated
  platformFee: PlatformFeeZ.nullable(),
  platformFees: z.array(PlatformFeeZ),
});

export type PrepareActionResponse = z.infer<typeof PrepareActionResponseZ>;

export async function prepareAction(action: Action) {
  const response = await fetch(`${API_BASE_URL}/actions/prepare`, {
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
    method: "POST",
    body: JSON.stringify({ action }),
  });

  await assertOk(response);

  return PrepareActionResponseZ.parse(await response.json());
}

const SimulateActionResponseZ = z.object({
  solanaFees: SolanaFeesZ.nullable(),
  platformFees: z.array(PlatformFeeZ),
});

export async function simulateAction(action: Action) {
  const response = await fetch(`${API_BASE_URL}/actions/simulate`, {
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
    method: "POST",
    body: JSON.stringify({ action }),
  });
  await assertOk(response);

  return SimulateActionResponseZ.parse(await response.json());
}

const ExecuteActionResponseZ = z.object({
  activityId: z.string(),
});

export async function executeAction({
  actionRequestId,
  signedTransactions,
}: {
  actionRequestId?: string;
  signedTransactions: Base64[];
}) {
  const response = await fetch(`${API_BASE_URL}/actions/execute`, {
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
    method: "POST",
    body: JSON.stringify({ actionRequestId, transactions: signedTransactions }),
  });

  await assertOk(response);

  return ExecuteActionResponseZ.parse(await response.json());
}

const DeactivateWalletResponseZ = z.object({
  deactivationId: z.string(),
});

export async function deactivateWallet() {
  const response = await fetch(`${API_BASE_URL}/wallets/deactivate`, {
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
    method: "POST",
  });

  await assertOk(response);

  return DeactivateWalletResponseZ.parse(await response.json());
}

export async function verifyWalletDeactivation({
  deactivationId,
}: {
  deactivationId: string;
}) {
  const response = await fetch(
    `${API_BASE_URL}/wallets/deactivate/${deactivationId}`,
    {
      headers: {
        "Content-Type": "application/json",
        Authorization: await getOrRefreshAuth(),
      },
    }
  );

  await assertOk(response);
}
