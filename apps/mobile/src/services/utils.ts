import { z } from "zod";
import * as SecureStore from "expo-secure-store";
import { router } from "expo-router";

export async function assertOk(response: Response) {
  if (!response.ok) {
    const status = response.status;
    const text = await response.text();

    let clientErrorResponse;
    try {
      const responseText = JSON.parse(text);
      clientErrorResponse = ClientErrorResponseZ.parse(responseText).reason;
    } catch (e) {
      console.debug("Failed to parse client error response", e);
      clientErrorResponse = null;
    }

    if (clientErrorResponse?.type === "invalidAuthentication") {
      console.debug("Resetting auth token...");
      await SecureStore.deleteItemAsync("AUTH_TOKEN");

      router.replace("/locked/device-key-unavailable");
      throw new Error("authentication failed");
    }

    throw clientErrorResponse
      ? new ClientError(response.url, status, clientErrorResponse)
      : new Error(`[${status}] ${response.url}: ${text}`);
  }
}

export const AmountMissingZ = z.object({
  needed: z.number(),
  available: z.number(),
});

const ClientErrorReasonZ = z.discriminatedUnion("type", [
  z.object({
    type: z.literal("notEnoughSol"),
    needed: z.number(),
    available: z.number(),
  }),
  z.object({ type: z.literal("slippageToleranceExceeded") }),
  z.object({ type: z.literal("transactionTooLarge") }),
  z.object({ type: z.literal("invalidAuthentication") }),
  z.object({ type: z.literal("unauthorizedAccess") }),
  z.object({ type: z.literal("badRequest") }),
  z.object({ type: z.literal("serverError") }),
  z.object({ type: z.literal("confirmationTimeout") }),
  z.object({ type: z.literal("walletExists") }),
  z.object({ type: z.literal("payFeesWithFuseSolAlreadyEnabled") }),
  z.object({ type: z.literal("payFeesWithFuseSolAlreadyDisabled") }),
  z.object({ type: z.literal("invalidCreateKey") }),
  z.object({ type: z.literal("walletsLimitReached") }),
]);

type ClientErrorReason = z.infer<typeof ClientErrorReasonZ>;

const ClientErrorResponseZ = z.object({
  reason: ClientErrorReasonZ,
});

export class ClientError extends Error {
  reason: ClientErrorReason;

  constructor(url: string, status: number, reason: ClientErrorReason) {
    super(`[${status}] ${url}: ${reason.type}`);
    this.reason = reason;
  }

  toMessage() {
    switch (this.reason.type) {
      case "notEnoughSol":
        return "Not enough SOL";

      case "slippageToleranceExceeded":
        return "slippage exceeded";

      case "invalidAuthentication":
        return "authentication failed";

      case "unauthorizedAccess":
        return "unauthorized access";

      case "badRequest":
        return "invalid request";

      case "serverError":
        return "server error";

      case "confirmationTimeout":
        return "confirmation timeout";

      case "transactionTooLarge":
        return "failed to create transaction";

      case "walletExists":
        return "wallet already exists";

      case "payFeesWithFuseSolAlreadyEnabled":
        return "fuseSOL is already used to pay fees";

      case "payFeesWithFuseSolAlreadyDisabled":
        return "Pay Fees with fuseSOL is already disabled";

      case "invalidCreateKey":
        return "Invalid Create Key";

      case "walletsLimitReached":
        return "Wallets limit reached";

      default:
        return this.reason satisfies never;
    }
  }
}

export function isClientErrorReasonType<T extends ClientErrorReason["type"]>(
  error: unknown,
  reasonType: T
): error is ClientError & { reason: Extract<ClientErrorReason, { type: T }> } {
  return error instanceof ClientError && error.reason.type === reasonType;
}

export function clientErrorMessage(prefix: string, error: unknown) {
  if (error instanceof ClientError) {
    return prefix.length > 0
      ? `${prefix}: ${error.toMessage()}`
      : error.toMessage();
  }

  return prefix;
}
