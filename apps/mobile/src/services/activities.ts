import { z } from "zod";
import { API_BASE_URL } from "~/constants/apiBaseUrl";
import { Address, AddressZ } from "@squads/models/solana";
import { Base58Z } from "@squads/models/encoding";
import { RecoveringKeyDetailsZ } from "~/services/recovery";
import { CloudKeyZ, RecoveryKeyZ, SolanaFeesZ } from "~/services/wallets";
import { assertOk } from "~/services/utils";
import { getOrRefreshAuth } from "~/services/auth-utils";
import { SpendingLimitPeriodZ } from "~/services/spendingLimits";
import { mints } from "~/constants/tokens";
import { assertNever } from "~/utils/assertNever";
import { SFSymbol } from "expo-symbols";
import { ColorName } from "~/constants/Colors";

const TokenFeeWithAmountZ = z.object({
  amount: z.number(),
  feeBps: z.number(),
});

const FeesZ = z.object({
  networkFee: z.number(),
  rentFee: z.number(),
  priorityFee: z.number(),
});

const SendSolZ = z.object({
  type: z.literal("sendSol"),
  vaultIndex: z.number(),
  to: AddressZ,
  lamports: z.number(),
  usdPrice: z.number().nullable(),
});

const SendTokenZ = z.object({
  type: z.literal("sendToken"),
  vaultIndex: z.number(),
  to: AddressZ,
  tokenMint: AddressZ,
  amount: z.number(),
  decimals: z.number(),
  usdPrice: z.number().nullable(),
  tokenFee: TokenFeeWithAmountZ.nullable(),
});

const UpdateRecoveryKeysDetailsZ = z.union([
  z.object({
    type: z.literal("addKey"),
    newKey: RecoveryKeyZ,
  }),
  z.object({
    type: z.literal("removeKey"),
    oldKey: RecoveryKeyZ,
  }),
  z.object({
    type: z.literal("changeKey"),
    oldKey: RecoveryKeyZ,
    newKey: RecoveryKeyZ,
  }),
]);

const UpdateRecoveryKeysZ = z.object({
  type: z.literal("updateRecoveryKeys"),
  details: UpdateRecoveryKeysDetailsZ,
});

const UpdateCloudKeyZ = z.object({
  type: z.literal("updateCloudKey"),
  newKey: CloudKeyZ,
});

const ReceiveTokenZ = z.object({
  type: z.literal("receiveToken"),
  vaultIndex: z.number(),
  from: AddressZ,
  amount: z.number(),
  decimals: z.number(),
  tokenMint: AddressZ,
  usdPrice: z.number().nullable(),
});

const ReceiveSolZ = z.object({
  type: z.literal("receiveSol"),
  vaultIndex: z.number(),
  from: AddressZ,
  lamports: z.number(),
  usdPrice: z.number().nullable(),
});

const WalletCreatedZ = z.object({
  type: z.literal("walletCreated"),
});

const WalletRecoveryZ = z.object({
  type: z.literal("walletRecovery"),
  oldKey: AddressZ,
  newKey: RecoveringKeyDetailsZ,
});

const SwapZ = z.object({
  type: z.literal("swap"),
  inputMint: AddressZ,
  inAmount: z.number(),
  outputMint: AddressZ,
  outAmount: z.number(),
  quotedOutAmount: z.number().nullable(),
  routes: z.array(z.string()),
  inUsdPrice: z.number().nullable(),
  outUsdPrice: z.number().nullable(),
  slippageBps: z.number().nullable(),
  inputTokenFee: TokenFeeWithAmountZ.nullable(),
  outputTokenFee: TokenFeeWithAmountZ.nullable(),
});

const UnknownActivityZ = z.object({
  type: z.literal("unknown"),
});

const MarinadeNativeStakeZ = z.object({
  type: z.literal("marinadeNativeStake"),
  vaultIndex: z.number(),
  lamports: z.number(),
  usdPrice: z.number().nullable(),
});

const MarinadeNativeDeactivateZ = z.object({
  type: z.literal("marinadeNativeDeactivate"),
  vaultIndex: z.number(),
  lamports: z.number(),
  usdPrice: z.number().nullable(),
});

const MarinadeNativeWithdrawZ = z.object({
  type: z.literal("marinadeNativeWithdraw"),
  vaultIndex: z.number(),
  lamports: z.number(),
  usdPrice: z.number().nullable(),
});

const StakeZ = z.object({
  type: z.literal("stake"),
  usdPrice: z.number().nullable(),
  details: z.discriminatedUnion("type", [
    z.object({
      type: z.literal("deposit"),
      vaultIndex: z.number(),
      lamports: z.number(),
      voteKey: AddressZ,
    }),
    z.object({
      type: z.literal("deactivate"),
      vaultIndex: z.number(),
      stakeKey: AddressZ,
      lamports: z.number(),
      voteKey: AddressZ,
    }),
    z.object({
      type: z.literal("withdraw"),
      vaultIndex: z.number(),
      lamports: z.number(),
      stakeKey: AddressZ,
      voteKey: AddressZ,
    }),
  ]),
});

const DepositLiquidStakeZ = z.object({
  type: z.literal("depositLiquidStake"),
  vaultIndex: z.number(),
  lamports: z.number(),
  usdPrice: z.number().nullable(),
  lstMint: AddressZ,
  lstPrice: z.number(),
});

const LiquidStakeWithdrawZ = z.object({
  type: z.literal("liquidStakeWithdraw"),
  transactionIndex: z.number(),
  vaultIndex: z.number(),
  amount: z.number(),
  lstMint: AddressZ,
  lstPrice: z.number(),
  usdPrice: z.number().nullable(),
});

const SpendingLimitZ = z.object({
  type: z.literal("spendingLimit"),
  details: z.discriminatedUnion("type", [
    z.object({
      type: z.literal("add"),
      address: AddressZ,
      vaultIndex: z.number(),
      member: AddressZ,
      amount: z.number(),
      mint: AddressZ.nullable(),
      decimals: z.number(),
      period: SpendingLimitPeriodZ,
    }),
    z.object({
      type: z.literal("remove"),
      address: AddressZ,
      vaultIndex: z.number(),
      member: AddressZ,
      amount: z.number(),
      mint: AddressZ.nullable(),
      decimals: z.number(),
      period: SpendingLimitPeriodZ,
    }),
  ]),
});

const SpendingLimitTransferZ = z.object({
  type: z.literal("spendingLimitTransfer"),
  oldAddress: AddressZ,
  newAddress: AddressZ,
  transactionIndex: z.number(),
  vaultIndex: z.number(),
  oldMember: AddressZ,
  newMember: AddressZ,
  mint: AddressZ.nullable(),
  amount: z.number(),
  decimals: z.number(),
  period: SpendingLimitPeriodZ,
});

const UseSpendingLimitZ = z.object({
  type: z.literal("spendingLimitUse"),
  vaultIndex: z.number(),
  spendingLimitAddress: AddressZ,
  amount: z.number(),
  mint: AddressZ.nullable(),
  decimals: z.number(),
  recipient: AddressZ,
  usdPrice: z.number().nullable(),
});

const PayFeesWithFuseSolEnableZ = z.object({
  type: z.literal("payFeesWithFuseSolEnable"),
  vaultIndex: z.number(),
  spendingLimitAddress: AddressZ,
});

const PayFeesWithFuseSolDisableZ = z.object({
  type: z.literal("payFeesWithFuseSolDisable"),
  vaultIndex: z.number(),
  spendingLimitAddress: AddressZ,
});

const NftMetadataZ = z.object({
  name: z.string(),
  imageUrl: z.string().nullable(),
  videoUrl: z.string().nullable(),
});

const ReceiveNftZ = z.object({
  type: z.literal("receiveNft"),
  vaultIndex: z.number(),
  from: AddressZ.nullable(),
  id: AddressZ,
  amount: z.number(),
  metadata: NftMetadataZ.nullable(),
});

const SendNftZ = z.object({
  type: z.literal("sendNft"),
  vaultIndex: z.number(),
  to: AddressZ,
  id: AddressZ,
  amount: z.number(),
  metadata: NftMetadataZ.nullable(),
});

const RelayerTopUpActivityDetailsZ = z.object({
  type: z.literal("relayerTopUp"),
});

const RelayerWithdrawZ = z.object({
  type: z.literal("relayerWithdraw"),
  amount: z.number(),
  to: AddressZ,
});

const DriftEarnDepositUSDCZ = z.object({
  type: z.literal("usdc"),
  amount: z.number(),
  decimals: z.number(),
});

const DriftEarnDepositPYUSDZ = z.object({
  type: z.literal("pyusd"),
  amount: z.number(),
  decimals: z.number(),
});

const DriftEarnDepositUSDSZ = z.object({
  type: z.literal("usds"),
  amount: z.number(),
  decimals: z.number(),
});

const DriftEarnDepositDetailsZ = z.discriminatedUnion("type", [
  DriftEarnDepositUSDCZ,
  DriftEarnDepositPYUSDZ,
  DriftEarnDepositUSDSZ,
]);

export type DriftEarnDepositDetails = z.infer<typeof DriftEarnDepositDetailsZ>;
export const DriftEarnDepositDetails = {
  mint: (details: DriftEarnDepositDetails) => {
    switch (details.type) {
      case "usdc":
        return mints.usdc;
      case "pyusd":
        return mints.pyusd;
      case "usds":
        return mints.usds;
    }
  },
};

const DriftEarnDepositZ = z.object({
  type: z.literal("driftEarnDeposit"),
  vaultIndex: z.number(),
  deposit: DriftEarnDepositDetailsZ,
});

const DriftEarnWithdrawZ = z.object({
  type: z.literal("driftEarnWithdraw"),
  vaultIndex: z.number(),
  deposit: DriftEarnDepositDetailsZ,
});

const LuloProtectedEarnDepositUSDCZ = z.object({
  type: z.literal("usdc"),
  amount: z.number(),
  decimals: z.number(),
});

const LuloProtectedEarnDepositDetailsZ = z.discriminatedUnion("type", [
  LuloProtectedEarnDepositUSDCZ,
]);
export type LuloProtectedEarnDepositDetails = z.infer<
  typeof LuloProtectedEarnDepositDetailsZ
>;
export const LuloProtectedEarnDepositDetails = {
  mint: (details: LuloProtectedEarnDepositDetails) => {
    switch (details.type) {
      case "usdc":
        return mints.usdc;

      default:
        assertNever(details.type);
    }
  },
};

const LuloProtectedEarnDepositZ = z.object({
  type: z.literal("luloProtectedEarnDeposit"),
  vaultIndex: z.number(),
  deposit: LuloProtectedEarnDepositDetailsZ,
});

const LuloProtectedEarnWithdrawZ = z.object({
  type: z.literal("luloProtectedEarnWithdraw"),
  vaultIndex: z.number(),
  withdrawal: LuloProtectedEarnDepositDetailsZ,
});

const KaminoEarnDepositUSDCZ = z.object({
  type: z.literal("usdc"),
  amount: z.number(),
  decimals: z.number(),
});

const KaminoEarnDepositDetailsZ = z.discriminatedUnion("type", [
  KaminoEarnDepositUSDCZ,
]);

export type KaminoEarnDepositDetails = z.infer<
  typeof KaminoEarnDepositDetailsZ
>;
export const KaminoEarnDepositDetails = {
  mint: (details: KaminoEarnDepositDetails) => {
    switch (details.type) {
      case "usdc":
        return mints.usdc;
    }
  },
};

const KaminoEarnDepositZ = z.object({
  type: z.literal("kaminoEarnDeposit"),
  vaultIndex: z.number(),
  deposit: KaminoEarnDepositDetailsZ,
});

const KaminoEarnWithdrawZ = z.object({
  type: z.literal("kaminoEarnWithdraw"),
  vaultIndex: z.number(),
  withdrawal: KaminoEarnDepositDetailsZ,
});

const KycStartedZ = z.object({
  type: z.literal("kycStarted"),
});

const KycApprovedZ = z.object({
  type: z.literal("kycApproved"),
});

const KycRejectedZ = z.object({
  type: z.literal("kycRejected"),
  details: z
    .object({
      reason: z.string(),
      rejectedAt: z.number(),
    })
    .nullish(),
});

const FiatSenderDetailsZ = z.discriminatedUnion("payment_rail", [
  z.object({
    payment_rail: z.literal("wire"),
    imad: z.string(),
    bankName: z.string(),
    bankRoutingNumber: z.string(),
    bankBeneficiaryName: z.string(),
    bankBeneficiaryAddress: z.string(),
  }),
  z.object({
    payment_rail: z.literal("achPush"),
    description: z.string(),
    senderName: z.string(),
    senderBankRoutingNumber: z.string(),
    traceNumber: z.string(),
  }),
  z.object({
    payment_rail: z.literal("sepa"),
    bic: z.string().nullable(),
    ibanLast4: z.string(),
    senderName: z.string(),
    uetr: z.string(),
    exchangeRate: z.number().nullable(),
  }),
]);

const TransferFeesZ = z.object({
  developmentFee: z.number(),
  exchangeFee: z.number(),
  gasFee: z.number(),
});

type TransferFees = z.infer<typeof TransferFeesZ>;

export const TransferFees = {
  totalFees: (fees: TransferFees) => {
    return fees.developmentFee + fees.exchangeFee + fees.gasFee;
  },
};

const VirtualAccountReceiveZ = z.object({
  type: z.literal("virtualAccountReceive"),
  source: FiatSenderDetailsZ,
  amount: z.number(),
  currency: z.string(),
  fees: TransferFeesZ,
  depositId: z.string(),
  status: z
    .union([
      z.literal("funds_received"),
      z.literal("payment_submitted"),
      z.literal("payment_processed"),
      z.literal("refunded"),
      z.literal("microdeposit"),
    ])
    .default("payment_processed"),
});

export type VirtualAccountReceive = z.infer<typeof VirtualAccountReceiveZ>;

const CurrencyZ = z.union([z.literal("usd"), z.literal("eur")]);

const SendFiatZ = z.object({
  type: z.literal("sendFiat"),
  vaultIndex: z.number(),
  source: z.object({
    amount: z.number(),
    decimals: z.number(),
    mint: AddressZ,
    usdPrice: z.number().nullable(),
  }),
  destination: z.object({
    accountOwnerName: z.string(),
    bankAccount: z.discriminatedUnion("type", [
      z.object({
        type: z.literal("us"),
        bankName: z.string(),
        accountNumberLast4: z.string(),
      }),
      z.object({
        type: z.literal("iban"),
        bankName: z.string(),
        accountNumberLast4: z.string(),
      }),
    ]),
    paymentRail: z.string(),
    currency: CurrencyZ,
    exchangeRate: z.number().nullable(),
  }),
  fees: TransferFeesZ,
  status: z
    .union([
      z.literal("awaiting_funds"),
      z.literal("in_review"),
      z.literal("funds_received"),
      z.literal("payment_submitted"),
      z.literal("payment_processed"),
      z.literal("canceled"),
      z.literal("error"),
      z.literal("returned"),
      z.literal("refunded"),
    ])
    .default("payment_processed"),
  transferId: z.string(),
});

export type SendFiat = z.infer<typeof SendFiatZ>;

export const SendFiatStatus = {
  isUnsuccessful: (status: SendFiat["status"]) => {
    return (
      status === "canceled" ||
      status === "error" ||
      status === "returned" ||
      status === "refunded"
    );
  },
  isCompleted: (status: SendFiat["status"]) => {
    return (
      status === "payment_processed" || SendFiatStatus.isUnsuccessful(status)
    );
  },
  view: (
    status: SendFiat["status"]
  ): {
    label: string;
    icon: SFSymbol;
    color: ColorName;
  } => {
    switch (status) {
      case "awaiting_funds":
        return {
          label: "Awaiting Crypto",
          icon: "clock.fill",
          color: "textSecondary",
        };
      case "in_review":
        return {
          label: "In review",
          icon: "magnifyingglass.circle.fill",
          color: "textSecondary",
        };
      case "funds_received":
        return {
          label: "Received Crypto",
          icon: "arrow.down.to.line.circle.fill",
          color: "textSecondary",
        };
      case "payment_submitted":
        return {
          label: "Payout on the way",
          icon: "paperplane.circle.fill",
          color: "textSecondary",
        };
      case "payment_processed":
        return {
          label: "Completed",
          icon: "checkmark.circle.fill",
          color: "green",
        };
      case "canceled":
      case "error":
        return {
          label: "Failed",
          icon: "xmark.circle.fill",
          color: "red",
        };
      case "returned":
        return {
          label: "Pending refund",
          icon: "arrow.counterclockwise.circle.fill",
          color: "textSecondary",
        };
      case "refunded":
        return {
          label: "Refunded",
          icon: "arrow.counterclockwise.circle.fill",
          color: "blue",
        };
      default:
        status satisfies never;
        return {
          label: "Completed",
          icon: "checkmark.circle.fill",
          color: "green",
        };
    }
  },
};

export const RecipientBankAccount = {
  getLabel(bankAccount: SendFiat["destination"]["bankAccount"]) {
    return `${bankAccount.bankName}  •  ${bankAccount.accountNumberLast4}`;
  },
};

export const VirtualAccountTransferStatus = {
  view: (
    status: VirtualAccountReceive["status"]
  ): {
    label: string;
    icon: SFSymbol;
    color: ColorName;
  } => {
    switch (status) {
      case "funds_received":
        return {
          label: "Processing conversion",
          icon: "arrow.left.arrow.right.circle.fill",
          color: "textSecondary",
        };
      case "payment_submitted":
        return {
          label: "Awaiting deposit",
          icon: "clock.fill",
          color: "textSecondary",
        };
      case "microdeposit":
      case "payment_processed":
        return {
          label: "Completed",
          icon: "checkmark.circle.fill",
          color: "green",
        };
      case "refunded":
        return {
          label: "Refunded",
          icon: "arrow.counterclockwise.circle.fill",
          color: "blue",
        };
      default:
        status satisfies never;
        return {
          label: "Completed",
          icon: "checkmark.circle.fill",
          color: "green",
        };
    }
  },
  isCompleted: (status: VirtualAccountReceive["status"]) => {
    return (
      status === "payment_processed" ||
      status === "microdeposit" ||
      status === "refunded"
    );
  },
};

const CardCreateZ = z.object({
  type: z.literal("cardCreate"),
});

const CardTopUpZ = z.object({
  type: z.literal("cardTopUp"),
  vaultIndex: z.number(),
  amount: z.number(),
  decimals: z.number(),
  mint: AddressZ,
});

export const CardWithdrawZ = z.object({
  type: z.literal("cardWithdraw"),
  vaultIndex: z.number(),
  amount: z.number(),
  decimals: z.number(),
  mint: AddressZ,
});

export const CardSLChangeZ = z.object({
  type: z.literal("cardSLChange"),
  vaultIndex: z.number(),
  oldAmount: z.number(),
  newAmount: z.number(),
  mint: AddressZ,
  period: SpendingLimitPeriodZ,
});

export const CardReferralRewardZ = z.object({
  type: z.literal("cardReferralReward"),
  amount: z.number(),
  decimals: z.number(),
  mint: AddressZ,
});

const ActivityDetailsZ = z
  .discriminatedUnion("type", [
    WalletCreatedZ,
    SendSolZ,
    SendTokenZ,
    UpdateRecoveryKeysZ,
    UpdateCloudKeyZ,
    ReceiveTokenZ,
    ReceiveSolZ,
    WalletRecoveryZ,
    SwapZ,
    MarinadeNativeStakeZ,
    MarinadeNativeDeactivateZ,
    MarinadeNativeWithdrawZ,
    StakeZ,
    DepositLiquidStakeZ,
    LiquidStakeWithdrawZ,
    SpendingLimitZ,
    SpendingLimitTransferZ,
    UseSpendingLimitZ,
    PayFeesWithFuseSolEnableZ,
    PayFeesWithFuseSolDisableZ,
    UnknownActivityZ,
    ReceiveNftZ,
    SendNftZ,
    RelayerTopUpActivityDetailsZ,
    RelayerWithdrawZ,
    DriftEarnDepositZ,
    DriftEarnWithdrawZ,
    LuloProtectedEarnDepositZ,
    LuloProtectedEarnWithdrawZ,
    KaminoEarnDepositZ,
    KaminoEarnWithdrawZ,
    KycStartedZ,
    KycApprovedZ,
    KycRejectedZ,
    VirtualAccountReceiveZ,
    SendFiatZ,
    CardCreateZ,
    CardTopUpZ,
    CardWithdrawZ,
    CardSLChangeZ,
    CardReferralRewardZ,
  ])
  .catch(({ error }) => {
    const [issue] = error.issues;
    if (issue.code === "invalid_union_discriminator") {
      console.error(error);
      return { type: "unknown" };
    }

    throw error;
  });

const CleanupStatusZ = z.union([
  z.literal("notNeeded"),
  z.literal("done"),
  z.literal("needsManual"),
]);

const ActivityFailureReasonZ = z.union([
  z.literal("internalServerError"),
  z.literal("transactionSimulationFailed"),
  z.literal("notEnoughSol"),
  z.literal("slippageToleranceExceeded"),
  z.literal("transactionError"),
  z.literal("confirmationTimeout"),
  z.literal("rpcError"),
  z.literal("failedToUpdateOffchainState"),
]);

const PendingStateZ = z.union([
  z.literal("awaitsSendConfirmation"),
  z.literal("awaitsApproval"),
  z.literal("awaitsExecution"),
]);

export type PendingState = z.infer<typeof PendingStateZ>;

const ActivityStatusZ = z
  .discriminatedUnion("type", [
    z.object({
      type: z.literal("pending"),
      state: PendingStateZ,
    }),
    z.object({ type: z.literal("confirmed"), cleanup: CleanupStatusZ }),
    z.object({ type: z.literal("cancelled"), cleanup: CleanupStatusZ }),
    z.object({
      type: z.literal("failed"),
      cleanup: CleanupStatusZ,
      reason: ActivityFailureReasonZ,
      logs: z.array(z.string()).nullable(),
    }),
    z.object({ type: z.literal("unknown") }),
  ])
  .catch(({ error }) => {
    const [issue] = error.issues;
    if (issue.code === "invalid_union_discriminator") {
      console.error(error);
      return { type: "unknown" };
    }

    throw error;
  });

export type ActivityStatus = z.infer<typeof ActivityStatusZ>;

const ActivityTransactionZ = z.object({
  timestamp: z.number(),
  signature: Base58Z(),
  transactionType: z.union([
    z.literal("init"),
    z.literal("execute"),
    z.literal("cleanup"),
    z.literal("relayerTopUp"),
    z.literal("relayerSponsorship"),
  ]),
  fees: FeesZ.nullable(),
});

export type ActivityTransaction = z.infer<typeof ActivityTransactionZ>;

export const ActivityTransactionType = {
  label(txType: ActivityTransaction["transactionType"]) {
    switch (txType) {
      case "init":
        return "Prepare";

      case "execute":
        return "Execute";

      case "cleanup":
        return "Cleanup";

      case "relayerTopUp":
        return "Paymaster top-up";

      case "relayerSponsorship":
        return "Sponsorship";

      default:
        txType satisfies never;
        return "";
    }
  },
};

const RelayerTopUpZ = z.discriminatedUnion("type", [
  z.object({
    type: z.literal("sol"),
    amount: z.number(),
    vaultIndex: z.number(),
  }),
  z.object({
    type: z.literal("fuseSol"),
    solAmount: z.number(),
    amount: z.number(),
    vaultIndex: z.number(),
  }),
]);

export const ActivityZ = z.object({
  activityId: z.string().uuid(),
  status: ActivityStatusZ,
  details: ActivityDetailsZ,
  transactions: z.array(ActivityTransactionZ),
  timestamp: z.number(),
  relayerTopUp: RelayerTopUpZ.nullable(),
  solanaFees: z.array(SolanaFeesZ),
});

export type Activity = z.infer<typeof ActivityZ>;

export const Activity = {
  is<T extends Activity["details"]["type"]>(
    activity: Activity,
    type: T
  ): activity is Activity & { details: { type: T } } {
    return activity.details.type === type;
  },
};

const ResponseZ = z.array(ActivityZ);

export async function getActivities({ walletKey }: { walletKey: Address }) {
  const response = await fetch(
    `${API_BASE_URL}/wallets/${walletKey}/activities`,
    {
      headers: { Authorization: await getOrRefreshAuth() },
    }
  );

  await assertOk(response);

  const data = await response.json();
  return ResponseZ.parse(data);
}

export async function confirmActivity({ activityId }: { activityId: string }) {
  const response = await fetch(
    `${API_BASE_URL}/activities/await_confirmation/${activityId}`,
    {
      headers: {
        Authorization: await getOrRefreshAuth(),
      },
    }
  );

  await assertOk(response);

  const data = await response.json();

  return ActivityStatusZ.parse(data);
}

type ActivityStatusQuery =
  | { type: "confirmed" | "cancelled" | "failed" }
  | { type: "pending"; state: PendingState };

export async function awaitActivityStatus({
  activityId,
  status,
}: {
  activityId: string;
  status: ActivityStatusQuery;
}) {
  const response = await fetch(
    `${API_BASE_URL}/activities/${activityId}/await_status`,
    {
      method: "POST",
      body: JSON.stringify(status),
      headers: {
        "Content-Type": "application/json",
        Authorization: await getOrRefreshAuth(),
      },
    }
  );

  await assertOk(response);

  const data = await response.json();

  return ActivityStatusZ.parse(data);
}

export function isActivityRelatedToMint(
  activity: Activity,
  mint: string,
  vaultAddress: Address
): boolean {
  const { details, status } = activity;

  if (
    status.type === "cancelled" ||
    status.type === "unknown" ||
    status.type === "failed"
  ) {
    return false;
  }

  switch (details.type) {
    case "sendSol":
    case "receiveSol":
    case "marinadeNativeStake":
    case "marinadeNativeDeactivate":
    case "marinadeNativeWithdraw":
    case "stake":
    case "relayerTopUp":
      return mint === mints.sol;

    case "sendToken":
    case "receiveToken":
      return details.tokenMint === mint;

    case "swap":
      return details.inputMint === mint || details.outputMint === mint;

    case "depositLiquidStake":
    case "liquidStakeWithdraw":
      return mint === mints.sol || details.lstMint === mint;

    case "spendingLimit":
      return details.details.mint === mint;

    case "spendingLimitTransfer":
    case "spendingLimitUse":
      return details.mint === mint;

    case "relayerWithdraw":
      return mint === mints.sol && details.to === vaultAddress;

    case "driftEarnDeposit":
    case "driftEarnWithdraw":
      return (
        (mint === mints.pyusd && details.deposit.type === "pyusd") ||
        (mint === mints.usdc && details.deposit.type === "usdc") ||
        (mint === mints.usds && details.deposit.type === "usds")
      );

    case "luloProtectedEarnDeposit":
    case "luloProtectedEarnWithdraw":
      return mint === mints.usdc;

    case "kaminoEarnDeposit":
    case "kaminoEarnWithdraw":
      return mint === mints.usdc;

    case "virtualAccountReceive":
      return mint === mints.usdc;

    case "sendFiat":
      return mint == details.source.mint;

    case "cardTopUp":
    case "cardWithdraw":
      return mint == details.mint;

    case "walletCreated":
    case "updateRecoveryKeys":
    case "updateCloudKey":
    case "walletRecovery":
    case "payFeesWithFuseSolEnable":
    case "payFeesWithFuseSolDisable":
    case "unknown":
    case "receiveNft":
    case "sendNft":
    case "kycStarted":
    case "kycApproved":
    case "kycRejected":
    case "cardCreate":
    case "cardSLChange":
    case "cardReferralReward":
      return false;

    default:
      assertNever(details);
  }
}
