import { Address, AddressZ } from "@squads/models/solana";
import { API_BASE_URL } from "~/constants/apiBaseUrl";
import { z } from "zod";
import { assertOk } from "~/services/utils";
import { getOrRefreshAuth } from "~/services/auth-utils";
import { Period } from "~/services/balances";

const TokenZ = z.object({
  mint: AddressZ,
  decimals: z.number(),
  name: z.string(),
  symbol: z.string(),
  logoUri: z.string().nullable(),
});

export type Token = z.infer<typeof TokenZ>;

const ResponseZ = z.record(AddressZ, TokenZ);
const TokensPriceResponseZ = z.record(AddressZ, z.number().nullable());

const TokenDetailsZ = z.object({
  mint: AddressZ,
  marketCap: z.number().nullable(),
  volume24h: z.number().nullable(),
  about: z.string().nullable(),
});

export type TokenDetails = z.infer<typeof TokenDetailsZ>;

export async function getTrustedTokens() {
  const response = await fetch(`${API_BASE_URL}/tokens/trusted`, {
    headers: { Authorization: await getOrRefreshAuth() },
  });
  await assertOk(response);

  const data = await response.json();
  return ResponseZ.parse(data) as Record<Address, Token>;
}

export async function getTrustedTokensPrice() {
  const response = await fetch(`${API_BASE_URL}/tokens/trusted/prices`, {
    headers: { Authorization: await getOrRefreshAuth() },
  });
  await assertOk(response);

  const data = await response.json();
  return TokensPriceResponseZ.parse(data) as Record<Address, number | null>;
}

export async function getToken({ mint }: { mint: Address }) {
  const response = await fetch(`${API_BASE_URL}/tokens/${mint}`, {
    headers: { Authorization: await getOrRefreshAuth() },
  });
  await assertOk(response);

  const data = await response.json();
  return TokenZ.nullable().parse(data);
}

export async function getTokenDetails({ mint }: { mint: Address }) {
  const response = await fetch(`${API_BASE_URL}/tokens/${mint}/details`, {
    headers: { Authorization: await getOrRefreshAuth() },
  });
  await assertOk(response);

  const data = await response.json();

  return TokenDetailsZ.nullable().parse(data);
}

const TokenPriceZ = z.object({
  price: z.number(),
});

export type TokenPrice = z.infer<typeof TokenPriceZ>;

const TokenPriceResponseZ = z.object({
  token: TokenPriceZ.nullable(),
});

export async function getPeriodTokenPrice({
  mint,
  period,
}: {
  mint: Address;
  period: Period;
}) {
  const input = `${API_BASE_URL}/tokens/${mint}/period_price/${period}`;

  const response = await fetch(input, {
    headers: { Authorization: await getOrRefreshAuth() },
  });
  await assertOk(response);

  const data = await response.json();
  return TokenPriceResponseZ.parse(data);
}

export const HistoricalPriceZ = z.object({
  timestamp: z.number(),
  usdcPrice: z.number(),
});
export type HistoricalPrice = z.infer<typeof HistoricalPriceZ>;

export const HistoricalPriceResponseZ = z.array(HistoricalPriceZ);

export async function getHistoricalPrices({
  walletKey,
  mint,
  period,
}: {
  walletKey: Address;
  mint: Address;
  period: Period;
}) {
  const response = await fetch(
    `${API_BASE_URL}/tokens/${mint}/price_history/${period}`,
    {
      headers: { Authorization: await getOrRefreshAuth() },
    }
  );

  if (!response.ok) {
    throw new Error(
      `Failed to get historical prices: ${
        response.status
      }: ${await response.text()}`
    );
  }

  const data = await response.json();
  return HistoricalPriceResponseZ.parse(data);
}
