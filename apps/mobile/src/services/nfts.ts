import { z } from "zod";
import { API_BASE_URL } from "~/constants/apiBaseUrl";
import { getOrRefreshAuth } from "~/services/auth-utils";
import { assertOk } from "~/services/utils";

const NftAssetZ = z.object({
  address: z.string(),
  name: z.string(),
  imageUrl: z.string().nullable(),
  videoUrl: z.string().nullable(),
  collectionId: z.string().nullable(),
});

const NftCollectionZ = z.object({
  id: z.string(),
  name: z.string(),
  imageUrl: z.string(),
  description: z.string().nullable(),
});

const NftsByVaultZ = z.object({
  nfts: z.array(NftAssetZ),
  collections: z.array(NftCollectionZ),
});

export type NftsByVault = z.infer<typeof NftsByVaultZ>;

export async function getNftsByVault({ vaultKey }: { vaultKey: string }) {
  const response = await fetch(`${API_BASE_URL}/nfts/by_vault/${vaultKey}`, {
    headers: { Authorization: await getOrRefreshAuth() },
  });

  await assertOk(response);

  const data = await response.json();
  return NftsByVaultZ.parse(data);
}

const DetailedNftZ = z.object({
  address: z.string(),
  name: z.string(),
  imageUrl: z.string().nullable(),
  videoUrl: z.string().nullable(),
  description: z.string().nullable(),
  collection: NftCollectionZ.nullable(),
});

export type DetailedNft = z.infer<typeof DetailedNftZ>;

export async function getDetailedNft({ id }: { id: string }) {
  const response = await fetch(`${API_BASE_URL}/nfts/by_id/${id}`, {
    headers: { Authorization: await getOrRefreshAuth() },
  });

  await assertOk(response);

  const data = await response.json();
  return DetailedNftZ.parse(data);
}
