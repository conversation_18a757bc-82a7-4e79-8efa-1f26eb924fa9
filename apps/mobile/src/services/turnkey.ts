import { Address, AddressZ } from "@squads/models/solana";
import { API_BASE_URL } from "~/constants/apiBaseUrl";
import { getOrRefreshAuth } from "~/services/auth-utils";
import { assertOk } from "~/services/utils";
import { Base64 } from "~/services/wallets";
import { PublicKey, VersionedTransaction } from "@solana/web3.js";
import {
  uint8ArrayFromHexString,
  uint8ArrayToHexString,
} from "@turnkey/encoding";
import { TurnkeySuborgStamper } from "~/utils/turnkey";
import { createActivityPoller, TurnkeyClient } from "@turnkey/http";
import { z } from "zod";

const CreateEmailRecoveryResponseZ = z.object({
  subOrganizationId: z.string(),
  publicKey: AddressZ,
  otpId: z.string(),
});

export async function createEmailKey({
  walletKey,
  email,
  targetPublicKey,
}: {
  walletKey: Address;
  email: string;
  targetPublicKey: string;
}) {
  const body = JSON.stringify({ walletKey, email, targetPublicKey });

  const response = await fetch(
    `${API_BASE_URL}/wallets/${walletKey}/create_email_key_using_otp`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: await getOrRefreshAuth(),
      },
      body: body,
    }
  );

  await assertOk(response);

  return CreateEmailRecoveryResponseZ.parse(await response.json());
}

const InitEmailOtpAuthResponseZ = z.object({
  otpId: z.string(),
  expiresAt: z.number(),
});

export async function initEmailOtpAuth({
  walletKey,
  email,
  subOrganizationId,
  targetPublicKey,
}: {
  walletKey: Address;
  email: string;
  subOrganizationId: string;
  targetPublicKey: string;
}) {
  const response = await fetch(
    `${API_BASE_URL}/wallets/${walletKey}/init_email_otp_auth`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: await getOrRefreshAuth(),
      },
      body: JSON.stringify({
        email,
        subOrganizationId,
        targetPublicKey,
      }),
    }
  );

  await assertOk(response);

  return InitEmailOtpAuthResponseZ.parse(await response.json());
}

const EmailOtpAuthResponseZ = z.object({
  credentialBundle: z.string(),
  expiresAt: z.number(),
});

export async function getEmailOtpAuthBundle({
  walletKey,
  subOrganizationId,
  otpId,
  otpCode,
  targetPublicKey,
}: {
  walletKey: Address;
  subOrganizationId: string;
  otpId: string;
  otpCode: string;
  targetPublicKey: string;
}) {
  const response = await fetch(
    `${API_BASE_URL}/wallets/${walletKey}/email_otp_auth`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: await getOrRefreshAuth(),
      },
      body: JSON.stringify({
        subOrganizationId,
        otpId,
        otpCode,
        targetPublicKey,
      }),
    }
  );

  await assertOk(response);

  return EmailOtpAuthResponseZ.parse(await response.json());
}

export async function signTurnkeyTransaction({
  encodedTransaction,
  stamper,
  details,
}: {
  encodedTransaction: Base64;
  stamper: TurnkeySuborgStamper;
  details: {
    userOrganizationId: string;
    userPublicKey: string;
  };
}) {
  const versionedTransaction = VersionedTransaction.deserialize(
    new Uint8Array(Buffer.from(encodedTransaction, "base64"))
  );

  const bytes = new Uint8Array(versionedTransaction.message.serialize());
  const hexPayload = uint8ArrayToHexString(bytes);

  const client = new TurnkeyClient(
    { baseUrl: "https://api.turnkey.com" },
    stamper
  );

  const activityPoller = createActivityPoller({
    client,
    requestFn: client.signRawPayload,
  });
  const activity = await activityPoller({
    organizationId: details.userOrganizationId,
    timestampMs: String(Date.now()),
    type: "ACTIVITY_TYPE_SIGN_RAW_PAYLOAD_V2",
    parameters: {
      encoding: "PAYLOAD_ENCODING_HEXADECIMAL",
      hashFunction: "HASH_FUNCTION_NOT_APPLICABLE",
      signWith: details.userPublicKey,
      payload: hexPayload,
    },
  });

  let result = activity.result.signRawPayloadResult;
  if (!result) {
    throw new Error(
      `Failed to sign transaction. Turnkey activity: ${activity.id}`
    );
  }

  const signature = uint8ArrayFromHexString(result.r + result.s);

  versionedTransaction.addSignature(
    new PublicKey(details.userPublicKey),
    signature
  );

  return Buffer.from(versionedTransaction.serialize()).toString(
    "base64"
  ) as Base64;
}

export async function getTurnkeyAccount({
  organizationId,
  stamper,
}: {
  organizationId: string;
  stamper: TurnkeySuborgStamper;
}) {
  const client = new TurnkeyClient(
    { baseUrl: "https://api.turnkey.com" },
    stamper
  );

  return client.getWhoami({ organizationId });
}
