import { z } from "zod";
import { API_BASE_URL } from "~/constants/apiBaseUrl";
import { Address, AddressZ } from "@squads/models/solana";
import { assertOk } from "~/services/utils";
import { getOrRefreshAuth } from "~/services/auth-utils";
import { tokenUsdValue } from "~/utils/tokens";
import { StakeAccountZ } from "~/services/nativeStake";
import { assertNever } from "~/utils/assertNever";
import { EarnProvider } from "./earn";

const TokenMetadataZ = z.object({
  name: z.string(),
  symbol: z.string(),
  logoUri: z.string().nullable(),
});

export type TokenMetadata = z.infer<typeof TokenMetadataZ>;

const TokenEarnZ = z.object({
  deposit: z.number(),
  total: z.number(),
  total24h: z.number().nullable(),
  // total7d: z.number().nullable(),
});

export type TokenEarn = z.infer<typeof TokenEarnZ>;

const DriftEarnZ = z.object({
  /** Total balance including earned interest */
  total: z.number(),
  /** Total balance including earned interest 24h ago */
  total24h: z.number().nullable(),
});
export type DriftEarn = z.infer<typeof DriftEarnZ>;

const LuloProtectedEarnZ = z.object({
  /** Total balance including earned interest */
  total: z.number(),
  /** Total balance including earned interest 24h ago */
  total24h: z.number().nullable(),
});
export type LuloProtectedEarn = z.infer<typeof LuloProtectedEarnZ>;

const KaminoEarnZ = z.object({
  /** Total balance including earned interest */
  total: z.number(),
  /** Total balance including earned interest 24h ago */
  total24h: z.number().nullable(),
});

const TokenBalanceZ = z.object({
  mint: AddressZ.nullable(),
  amount: z.number(),
  stakedAmount: z.number().nullable(),
  stakeAccounts: z.array(StakeAccountZ),
  decimals: z.number(),
  usdcPrice: z.number().nullable(),
  usdcPrice24h: z.number().nullable(),
  amount24h: z.number().nullable(),
  stakedAmount24h: z.number().nullable(),
  metadata: TokenMetadataZ,
  driftEarn: DriftEarnZ.nullable(),
  luloProtectedEarn: LuloProtectedEarnZ.nullable(),
  kaminoEarn: KaminoEarnZ.nullable(),
  isVerified: z.boolean(),
});

const TokenPeriodBalancesZ = z.object({
  mint: AddressZ.nullable(),
  amount: z.number().nullable(),
  usdPrice: z.number().nullable(),
  decimals: z.number(),
  stakedAmount: z.number().nullable(),
  earnTotal: z.number().nullable(),
});

export type TokenBalance = z.infer<typeof TokenBalanceZ>;
export type TokenPeriodBalances = z.infer<typeof TokenPeriodBalancesZ>;

export const TokenBalance = {
  total: (
    tokenBalance: TokenBalance,
    options?: { includeEarn?: boolean; includeStaked?: boolean }
  ) => {
    const includeEarn = options?.includeEarn ?? true;
    const includeStaked = options?.includeStaked ?? true;

    const available = tokenBalance.amount ?? 0;
    const staked = tokenBalance.stakedAmount ?? 0;
    const earning = TokenBalance.earning(tokenBalance);

    return (
      available + (includeStaked ? staked : 0) + (includeEarn ? earning : 0)
    );
  },

  earning: (tokenBalance: TokenBalance) => {
    return (
      (tokenBalance.driftEarn?.total ?? 0) +
      (tokenBalance.luloProtectedEarn?.total ?? 0) +
      (tokenBalance.kaminoEarn?.total ?? 0)
    );
  },

  usdValue: (token: TokenBalance) => {
    const totalAmount = TokenBalance.total(token);

    return tokenUsdValue({
      amount: totalAmount,
      decimals: token.decimals,
      usdcPrice: token.usdcPrice,
    });
  },
};

const ResponseZ = z.array(TokenBalanceZ);

export async function getBalance({ address }: { address: Address }) {
  const response = await fetch(`${API_BASE_URL}/balances/${address}`, {
    headers: { Authorization: await getOrRefreshAuth() },
  });

  await assertOk(response);
  const data = await response.json();
  return ResponseZ.parse(data);
}

const EarnBalanceZ = z.record(
  AddressZ,
  z.object({
    balance: z.number(),
    netDeposited: z.number(),
  })
);

export type EarnBalance = z.infer<typeof EarnBalanceZ>;

export const HistoricalBalanceZ = z.object({
  timestamp: z.number(),
  usdcBalance: z.number(),
  driftEarn: EarnBalanceZ,
  luloProtectedEarn: EarnBalanceZ,
  kaminoEarn: EarnBalanceZ,
});
export type HistoricalBalance = z.infer<typeof HistoricalBalanceZ>;

export const HistoricalBalanceResponseZ = z.array(HistoricalBalanceZ);

export const PeriodsZ = z.enum(["day", "week", "month", "halfYear", "year"]);
export type Period = z.infer<typeof PeriodsZ>;
export const EarnPeriodsZ = z.enum(["7d", "30d", "1y", "all"]);
export type EarnPeriod = z.infer<typeof EarnPeriodsZ>;

const earnPeriodToPeriodMap: Record<EarnPeriod, Period> = {
  "7d": "week",
  "30d": "month",
  "1y": "year",
  all: "year", // FIXME: Add support for "all" period on the backend.
};

export function earnPeriodToPeriod(earnPeriod: EarnPeriod): Period {
  return earnPeriodToPeriodMap[earnPeriod];
}

export async function getHistoricalBalances({
  vault,
  period,
}: {
  vault: Address;
  period: Period;
}) {
  const response = await fetch(
    `${API_BASE_URL}/balances/history/${period}/${vault}`,
    { headers: { Authorization: await getOrRefreshAuth() } }
  );

  if (!response.ok) {
    throw new Error(
      `Failed to get historical prices: ${
        response.status
      }: ${await response.text()}`
    );
  }

  const data = await response.json();
  return HistoricalBalanceResponseZ.parse(data);
}

export async function getPeriodBalances({
  address,
  period,
}: {
  address: Address;
  period: Period;
}) {
  const response = await fetch(
    `${API_BASE_URL}/balances/period/${period}/${address}`,
    { headers: { Authorization: await getOrRefreshAuth() } }
  );

  if (!response.ok) {
    throw new Error(
      `Failed to get period balances: ${
        response.status
      }: ${await response.text()}`
    );
  }

  const data = await response.json();
  return z.array(TokenPeriodBalancesZ).parse(data);
}

export const BalancesBatchResponseZ = z.object({
  accounts: z.record(
    AddressZ,
    z.object({
      lamports: z.number(),
    })
  ),
});

export async function getBalancesBatch({
  addresses,
}: {
  walletKey: Address;
  addresses: Address[];
}) {
  const response = await fetch(`${API_BASE_URL}/balances/batch`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
    body: JSON.stringify({ addresses }),
  });

  await assertOk(response);

  const data = await response.json();
  return BalancesBatchResponseZ.parse(data);
}

const TopUpAmountZ = z.discriminatedUnion("type", [
  z.object({
    type: z.literal("sol"),
    min: z.number(),
    max: z.number(),
  }),
  z.object({
    type: z.literal("fuseSol"),
    sol: z.object({
      min: z.number(),
      max: z.number(),
    }),
    fuseSol: z.object({
      min: z.number(),
      max: z.number(),
    }),
  }),
]);

export function calculatePeriodChange(
  balances: TokenBalance[],
  periodBalances: TokenPeriodBalances[]
) {
  let totalUsdDiff = 0;
  let totalUsdPeriod = 0;
  let totalUsd = 0;

  let seenMintAddresses = new Set<Address>();

  balances.forEach((balance) => {
    const periodBalance = periodBalances.find((pb) => pb.mint === balance.mint);
    seenMintAddresses.add(balance.mint as Address);

    const totalAmount = TokenBalance.total(balance);

    const totalAmountPeriod =
      balance.mint === null
        ? (periodBalance?.amount ?? 0) + (periodBalance?.stakedAmount ?? 0)
        : (periodBalance?.amount ?? 0) + (periodBalance?.earnTotal ?? 0);

    const balanceInUsd = tokenUsdValue({
      amount: totalAmount,
      decimals: balance.decimals,
      usdcPrice: balance.usdcPrice,
    });
    const balanceInUsdPeriod = tokenUsdValue({
      amount: totalAmountPeriod,
      decimals: balance.decimals,
      usdcPrice: periodBalance?.usdPrice ?? 0,
    });

    totalUsdDiff += balanceInUsd - balanceInUsdPeriod;
    totalUsd += balanceInUsd;
    totalUsdPeriod += balanceInUsdPeriod;
  });

  periodBalances.forEach((periodBalance) => {
    if (!seenMintAddresses.has(periodBalance.mint as Address)) {
      const totalAmountPeriod =
        periodBalance.mint === null
          ? (periodBalance?.amount ?? 0) + (periodBalance?.stakedAmount ?? 0)
          : (periodBalance?.amount ?? 0) + (periodBalance?.earnTotal ?? 0);

      const balanceInUsdPeriod = tokenUsdValue({
        amount: totalAmountPeriod,
        decimals: periodBalance.decimals,
        usdcPrice: periodBalance.usdPrice,
      });

      totalUsdDiff -= balanceInUsdPeriod;
      totalUsdPeriod += balanceInUsdPeriod;
    }
  });

  const percentageDiff =
    totalUsdPeriod !== 0
      ? ((totalUsd - totalUsdPeriod) / totalUsdPeriod) * 100
      : 0;

  return {
    totalUsdDiff,
    percentageDiff,
  };
}

export type EarnedAtPointInTime = "now" | "7dAgo" | "30dAgo" | "1yAgo";

const earnPeriodToEarnedAtPointInTimeMap: Record<
  EarnPeriod,
  EarnedAtPointInTime
> = {
  "7d": "7dAgo",
  "30d": "30dAgo",
  "1y": "1yAgo",
  all: "now",
};

export function earnPeriodToEarnedAtPointInTime(
  earnPeriod: EarnPeriod
): EarnedAtPointInTime {
  return earnPeriodToEarnedAtPointInTimeMap[earnPeriod];
}

export function calculateEarnedAtPointInTime(
  provider: EarnProvider,
  xAgo: EarnedAtPointInTime,
  currentUsdBalance: number,
  /** should be of the same period as the `xAgo` */
  historicalBalances: HistoricalBalance[]
) {
  if (xAgo === "now") {
    // Explanation of logic with an example.
    // 1. We currently have 1200 in balance.
    //    At the same time, the historical entry has:
    //      balance is 1200,
    //      netDeposited is 1100.
    //    Which means we have earned 100.
    // 2. We withdraw 200 from drift earn to our main wallet.
    //    For the first 2 minutes, there will be no new historical entry.
    //    At that point, the current balance is 1000.
    //    The latest historical entry has:
    //      balance is 1200,
    //     netDeposited 900.
    // 3. So when calculating, we need to calculate the delta
    //    between the current balance and the historical entry balance.

    const latestHistoricalEntry = historicalBalances.at(-1);
    if (latestHistoricalEntry === undefined) return undefined;

    const latestHistoricalEarn = {
      drift: latestHistoricalEntry.driftEarn,
      lulo: latestHistoricalEntry.luloProtectedEarn,
      kamino: latestHistoricalEntry.kaminoEarn,
    }[provider];

    const latestHistoricalEarnBalance = Object.values(
      latestHistoricalEarn
    ).reduce((acc, curr) => acc + curr!.balance, 0);

    const deltaOfCurrentAndCooldown =
      currentUsdBalance - latestHistoricalEarnBalance;

    const latestHistoricalEarnNetDeposited = Object.values(
      latestHistoricalEarn
    ).reduce((acc, curr) => acc + curr!.netDeposited, 0);

    const currentNetDeposited =
      latestHistoricalEarnNetDeposited + deltaOfCurrentAndCooldown;

    const lifetimeEarned = currentUsdBalance - currentNetDeposited;
    return lifetimeEarned;
  } else if (xAgo === "7dAgo" || xAgo === "30dAgo" || xAgo === "1yAgo") {
    // For the same calculation at a previous point in time, we don't need to calculate the delta
    // since it's not real-time.

    const historicalXAgoEntry = historicalBalances[0];
    if (historicalXAgoEntry === undefined) return undefined;

    const historicalXAgoEarn = {
      drift: historicalXAgoEntry.driftEarn,
      lulo: historicalXAgoEntry.luloProtectedEarn,
      kamino: historicalXAgoEntry.kaminoEarn,
    }[provider];

    const historicalXAgoBalance = Object.values(historicalXAgoEarn).reduce(
      (acc, curr) => acc + curr!.balance,
      0
    );
    const historicalXAgoNetDeposited = Object.values(historicalXAgoEarn).reduce(
      (acc, curr) => acc + curr!.netDeposited,
      0
    );

    const earnedXAgo = historicalXAgoBalance - historicalXAgoNetDeposited;
    return earnedXAgo;
  }
  assertNever(xAgo);
}
