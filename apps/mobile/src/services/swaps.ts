import { z } from "zod";
import { Address, AddressZ } from "@squads/models/solana";
import { API_BASE_URL } from "~/constants/apiBaseUrl";
import { assertOk } from "~/services/utils";
import { getOrRefreshAuth } from "~/services/auth-utils";

const SwapMode = z.enum(["ExactIn", "ExactOut"]);

const PlatformFee = z
  .object({
    amount: z.number(),
    feeBps: z.number(),
  })
  .nullable();

const SwapInfo = z.object({
  ammKey: AddressZ,
  label: z.string(),
  inputMint: AddressZ,
  outputMint: AddressZ,
  inAmount: z.string(),
  outAmount: z.string(),
  feeAmount: z.string(),
  feeMint: AddressZ,
});

const RoutePlanStep = z.object({
  swapInfo: SwapInfo,
  percent: z.number(),
});

const RoutePlanWithMetadata = z.array(RoutePlanStep);

const SwapQuoteZ = z.object({
  inputMint: AddressZ,
  inAmount: z.string(),
  outputMint: AddressZ,
  outAmount: z.string(),
  swapMode: SwapMode,
  /** Slippage in basis points */
  slippageBps: z.number(),
  platformFee: PlatformFee,
  routePlan: RoutePlanWithMetadata,
  otherAmountThreshold: z.string(),
  /** Price impact in percents (stringified number) */
  priceImpactPct: z.string(),
});

export type SwapQuote = z.infer<typeof SwapQuoteZ>;

export type QuoteRequest = {
  inputMint: Address;
  outputMint: Address;
  amount: number;
  slippageBps: number;
};

export async function getQuote({
  walletKey,
  quote,
}: {
  walletKey: Address;
  quote: QuoteRequest;
}) {
  const searchParams = new URLSearchParams({
    inputMint: quote.inputMint,
    outputMint: quote.outputMint,
    amount: quote.amount.toString(),
    slippageBps: quote.slippageBps.toString(),
    multileg: "true",
  });
  const response = await fetch(`${API_BASE_URL}/swaps/quote?${searchParams}`, {
    headers: { Authorization: await getOrRefreshAuth() },
  });
  await assertOk(response);

  const data = await response.json();
  return SwapQuoteZ.parse(data);
}
