import { z } from "zod";
import { API_BASE_URL } from "~/constants/apiBaseUrl";
import { getOrRefreshAuth } from "~/services/auth-utils";
import { assertOk } from "~/services/utils";

const TextContentZ = z.object({
  text: z.string(),
  matches: z.array(z.object({
    endOffset: z.number(),
  })).default([]),
});


const PlacePredictionZ = z.object({
  place: z.string(),
  placeId: z.string(),
  text: TextContentZ,
  types: z.array(z.string()).default([]),
});

const AutocompleteSuggestionZ = z.object({
  placePrediction: PlacePredictionZ,
});

const AddressAutocompleteResponseZ = z.object({
  suggestions: z.array(AutocompleteSuggestionZ),
});

export type AddressAutocompleteResponse = z.infer<typeof AddressAutocompleteResponseZ>;
export type PlacePrediction = z.infer<typeof PlacePredictionZ>;

export interface PlaceSuggestion {
  id: string;
  fullAddress: string;
  types: string[];
}

export async function getAddressAutocomplete(
  input: string
): Promise<PlaceSuggestion[]> {
  if (input.trim().length < 3) {
    return [];
  }

  const response = await fetch(`${API_BASE_URL}/address-autocomplete`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
    body: JSON.stringify({ input: input.trim() }),
  });

  await assertOk(response);
  const data = await response.json();
  const parsedData = AddressAutocompleteResponseZ.parse(data);

  return parsedData.suggestions.map((suggestion): PlaceSuggestion => {
    const prediction = suggestion.placePrediction;
    return {
      id: prediction.placeId,
      fullAddress: prediction.text.text,
      types: prediction.types,
    };
  });
}

export interface PlaceDetailsRequest {
  placeId: string;
}

export interface AddressComponent {
  longText: string;
  shortText: string;
  types: string[];
}

export interface PlaceDetailsResponse {
  addressComponents: AddressComponent[];
}

export async function getPlaceDetails(
  placeId: string
): Promise<PlaceDetailsResponse> {
  const response = await fetch(`${API_BASE_URL}/place-details`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: await getOrRefreshAuth(),
    },
    body: JSON.stringify({ placeId }),
  });

  await assertOk(response);
  const data = await response.json();

  return data as PlaceDetailsResponse;
}
