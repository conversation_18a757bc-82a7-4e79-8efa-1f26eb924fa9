import { Magic } from "@magic-sdk/react-native-expo";
import { SolanaExtension } from "@magic-ext/solana";
import { appConfig } from "~/state/config";
import { Address, AddressZ } from "@squads/models/solana";
import { VersionedTransaction } from "@solana/web3.js";

export const magicRecoveryApi = new Magic(
  appConfig.magicLink.recovery.publicKey,
  {
    extensions: [
      new SolanaExtension({ rpcUrl: "https://api.mainnet-beta.solana.com" }),
    ],
  }
);

type MagicApi = typeof magicRecoveryApi;

type LoginWithOptHandle = ReturnType<
  typeof magicRecoveryApi.auth.loginWithEmailOTP
>;

type VerificationResult =
  | { type: "success"; address: Address }
  | { type: "wrong-code" }
  | { type: "expired-email" }
  | { type: "timeout" };

export type MagicLinkEmailControl = {
  email: string;
  verify: (code: string) => Promise<VerificationResult>;
  cancel: () => Promise<void>;
};

export async function signTransaction(
  magicApi: MagicApi,
  transaction: VersionedTransaction
) {
  const { rawTransaction } = await magicApi.solana.signTransaction(
    transaction,
    {
      requireAllSignatures: false,
      verifySignatures: false,
    }
  );
  return VersionedTransaction.deserialize(
    new Uint8Array(Buffer.from(rawTransaction))
  );
}

export async function logout(magicApi: MagicApi) {
  await magicApi.user
    .logout()
    .catch((e) => console.error("Failed logging out from magic link", e));
}

export async function sendMagicLinkEmail(
  magicApi: MagicApi,
  email: string
): Promise<MagicLinkEmailControl> {
  await withTimeout(5_000, magicApi.user.logout()).catch(() => {});

  let handle = magicApi.auth.loginWithEmailOTP({
    email,
    showUI: false,
    deviceCheckUI: false,
  });
  console.debug(`initiated magic link email to ${email}`);

  //for debugging
  handle.eventNames().forEach((name) => {
    handle.on(name, () => console.log("EVENT " + name));
  });

  handle.catch((e) => {
    console.debug("error in sendMagicLinkEmail", e);
  });

  const promise = new Promise<MagicLinkEmailControl>((res, rej) => {
    handle.on("email-otp-sent", () => {
      console.debug(`magic link email sent to ${email}`);
      res({
        email,
        verify: getVerifyEmailFn(magicApi, handle),
        cancel: cancelFn(handle),
      });
    });
    handle.on("error", (e: any) => {
      rej(e);
    });
  });

  return Promise.race([
    promise,
    new Promise<MagicLinkEmailControl>((_, rej) =>
      setTimeout(() => rej("timeout"), 60_000)
    ),
  ]).catch((e) => {
    handle.emit("cancel");
    throw e;
  });
}

function cancelFn(handle: LoginWithOptHandle) {
  return () => {
    console.debug("cancelling magic link auth...");

    handle.emit("cancel");
    return new Promise<void>((res) => {
      handle.on("settled", () => {
        console.debug("magic link auth cancelled");
        res();
      });
    });
  };
}

export function getVerifyEmailFn(
  magicApi: MagicApi,
  handle: LoginWithOptHandle
) {
  return (code: string) => {
    const promise = new Promise<VerificationResult>((res, rej) => {
      handle.on("invalid-email-otp", () => {
        res({ type: "wrong-code" });
      });

      handle.on("expired-email-otp", () => {
        res({ type: "expired-email" });
      });

      handle.on("done", async () => {
        const info = await magicApi.user.getInfo();
        const address = AddressZ.parse(info.publicAddress);

        res({ type: "success", address });
      });

      handle.on("error", (e) => {
        rej(e);
      });
    });

    handle.emit("verify-email-otp", code);

    return Promise.race<VerificationResult>([
      promise,
      new Promise((res) => setTimeout(() => res({ type: "timeout" }), 60_000)),
    ]);
  };
}

//magic link recovery api
const MAGIC_LINK_API_TIMEOUT = 60_000;

export async function sendRecoveryEmail(
  email: string
): Promise<MagicLinkEmailControl> {
  return withTimeout(
    MAGIC_LINK_API_TIMEOUT,
    withRetry(() => sendMagicLinkEmail(magicRecoveryApi, email), 3)
  );
}

export async function signRecoveryTransaction(
  transaction: VersionedTransaction
) {
  return withTimeout(
    MAGIC_LINK_API_TIMEOUT,
    signTransaction(magicRecoveryApi, transaction)
  );
}

export async function logoutRecoveryApi() {
  return withTimeout(5_000, logout(magicRecoveryApi));
}

export async function preloadRecoveryApi() {
  return magicRecoveryApi.preload();
}

export function RecoveryApiRelayer() {
  return <magicRecoveryApi.Relayer backgroundColor={"#323233"} />;
}

function withTimeout<T>(timeout: number, promise: Promise<T>) {
  return Promise.race([
    promise,
    new Promise<T>((_, reject) =>
      setTimeout(() => reject(new Error("magic link api timeout")), timeout)
    ),
  ]);
}

async function withRetry<T>(fn: () => Promise<T>, retries = 3): Promise<T> {
  return fn().catch((e) => {
    if (retries <= 0) throw e;

    console.log("retrying magic link api call");
    return withRetry(fn, retries - 1);
  });
}
