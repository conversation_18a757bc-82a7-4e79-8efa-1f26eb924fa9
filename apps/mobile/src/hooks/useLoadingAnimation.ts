import {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";
import { useEffect } from "react";

export function useLoadingAnimation() {
  const minOpacity = 0.1;
  const opacity = useSharedValue(minOpacity);

  useEffect(() => {
    opacity.value = withRepeat(
      withTiming(opacity.value === minOpacity ? 1 : minOpacity, {
        duration: 800,
        easing: Easing.linear,
      }),
      -1,
      true
    );
  }, []);

  return useAnimatedStyle(() => {
    return { opacity: opacity.value };
  });
}
