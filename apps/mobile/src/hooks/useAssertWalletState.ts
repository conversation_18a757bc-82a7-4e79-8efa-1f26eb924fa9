import { useActiveWallet } from "~/state/wallet";
import { isOffline, showOfflineWarning } from "~/components/OnlineIndicator";
import { Toast, useToast } from "~/components/Toaster";
import {
  isICloudKeyUnavailable,
  showCloudKeyUnavailableModal,
} from "~/components/CloudKeyUnavailableModal";
import { hasActiveTransaction } from "~/hooks/useActiveTransaction";
import { showImproveSecurityModal } from "~/components/security/useImproveSecurityModalState";
import { useLazySecurityWarning } from "~/hooks/useSecurityWarning";
import { Wallet } from "~/services/wallets";

export function useIsWalletInactive(checks?: {
  simplifiedSecurityCheck?: boolean;
}) {
  const { wallet } = useActiveWallet();

  const { showSecurityWarning } = useLazySecurityWarning();

  const { toast } = useToast();

  return async (beforeModalOpen?: () => Promise<void>) => {
    if (isOffline()) {
      showOfflineWarning(toast);
      return true;
    }

    if (await isICloudKeyUnavailable(wallet)) {
      await beforeModalOpen?.();
      showCloudKeyUnavailableModal();
      return true;
    }

    if (hasActiveTransaction()) {
      toast.info("Wait for transaction to complete", {
        id: "active_transaction",
      });
      return true;
    }

    if (checks?.simplifiedSecurityCheck !== false && showSecurityWarning) {
      const { postpone } = await showImproveSecurityModal();
      if (!postpone) {
        return true;
      }
    }

    return false;
  };
}

export function useIsWalletOffline() {
  const { wallet } = useActiveWallet();
  const { toast } = useToast();

  return async () => isWalletOffline(toast, wallet);
}

export async function isWalletOffline(toast: Toast, wallet: Wallet) {
  if (isOffline()) {
    showOfflineWarning(toast);
    return true;
  }

  if (await isICloudKeyUnavailable(wallet)) {
    showCloudKeyUnavailableModal();
    return true;
  }

  return false;
}
