import { useToast } from "~/components/Toaster";
import { useMutation } from "@tanstack/react-query";
import { Alert, Linking } from "react-native";
import * as Device from "expo-device";
import BleTransport from "@ledgerhq/react-native-hw-transport-ble";

export function useBleStatusCheckMutation({
  onAvailable,
}: {
  onAvailable: (isSimulator?: boolean) => void;
}) {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async () => {
      const bleStatus = await getBleStatus();
      if (bleStatus?.type === "available" || bleStatus?.type === "simulator") {
        onAvailable();
        return;
      }

      if (bleStatus?.type === "unauthorized") {
        Alert.alert(
          "Bluetooth access",
          "Please authorize Fuse app to use bluetooth",
          [
            { text: "Dismiss", style: "cancel" },
            {
              text: "Open Settings",
              onPress: () => Linking.openURL("app-settings:"),
              isPreferred: true,
            },
          ]
        );
      } else if (bleStatus?.type === "powered-off") {
        toast.error("Bluetooth is powered off", {
          id: "ble-fail",
        });
      } else {
        console.warn("Bluetooth is unavailable", {
          status: bleStatus,
        });
        toast.error("Bluetooth is unavailable", {
          id: "ble-fail",
        });
      }
    },
  });
}

function getBleStatus(): Promise<{
  type: "unauthorized" | "available" | "powered-off" | "simulator";
  details?: any;
}> {
  if (!Device.isDevice) {
    return Promise.resolve({ type: "simulator" });
  }

  return new Promise((resolve, reject) => {
    const subscription = BleTransport.observeState({
      next: (e) => {
        if (!e.available && e.type === "PoweredOff") {
          if (subscription) subscription.unsubscribe();
          if (listenTimeoutId) clearTimeout(listenTimeoutId);

          resolve({ type: "powered-off" });
        } else if (!e.available && e.type === "Unauthorized") {
          if (subscription) subscription.unsubscribe();
          if (listenTimeoutId) clearTimeout(listenTimeoutId);

          resolve({ type: "unauthorized" });
        } else if (e.available) {
          if (subscription) subscription.unsubscribe();
          if (listenTimeoutId) clearTimeout(listenTimeoutId);

          resolve({ type: "available" });
        }
      },
      error: (error) => reject(error),
      complete: () => {
        console.log("bte state observation complete");
      },
    });

    const listenTimeoutId = setTimeout(() => {
      subscription.unsubscribe();
      reject(new Error("timeout"));
    }, 20_000);
  });
}
