import { useEffect, useState } from "react";
import * as Device from "expo-device";
import BleTransport from "@ledgerhq/react-native-hw-transport-ble";
import * as Haptics from "expo-haptics";
import { DeviceModel } from "@ledgerhq/devices";

type DiscoverableLedgerDevice = {
  id: string;
  localName: string;
  name: string;
};

const supportedDevices = [
  "13d63400-2c97-6004-0000-4c6564676572", //stax
  "13d63400-2c97-3004-0000-4c6564676572", //europa (flex)
  "13d63400-2c97-0004-0000-4c6564676572", //nano x
];

function isSupportedDevice(device: DeviceModel): boolean {
  const foundDevice = supportedDevices.find((supportedServiceUuid) => {
    return (device.bluetoothSpec ?? []).find(
      (spec) => spec.serviceUuid === supportedServiceUuid
    );
  });

  return foundDevice !== undefined;
}

export function useDiscoverableLedgerDevices() {
  const [devices, setDevices] = useState<DiscoverableLedgerDevice[]>([]);

  useEffect(() => {
    if (!Device.isDevice) {
      new Promise((resolve) => setTimeout(resolve, 500)).then(async () => {
        setDevices([
          {
            id: "simulator",
            name: "Ledger simulator",
            localName: "Ledger simulator",
          },
          {
            id: "simulator_2",
            name: "Ledger simulator #2",
            localName: "Ledger simulator #2",
          },
        ]);
      });

      return;
    }

    //todo add timeouts
    const subscription = BleTransport.listen({
      next: (e) => {
        // await new Promise((resolve) => setTimeout(resolve, 3000));
        if (
          e.type === "add" &&
          e.deviceModel &&
          isSupportedDevice(e.deviceModel)
        ) {
          setDevices((devices) => {
            if (devices.some((d) => d.id === e.descriptor.id)) {
              return devices;
            } else {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              return [...devices, e.descriptor];
            }
          });
        }
      },
      error: (error) => {
        console.log("error", error);
      },
      complete: () => {
        console.log("complete");
      },
    });

    return () => subscription.unsubscribe();
  }, []);

  return devices;
}
