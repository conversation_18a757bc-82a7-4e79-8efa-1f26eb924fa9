import { useEffect, useRef } from "react";

//https://overreacted.io/making-setinterval-declarative-with-react-hooks
export function useInterval(callback: () => void, delay: number | null) {
  const savedCallback = useRef<() => void | null>(null);

  // Remember the latest callback.
  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  // Set up the interval.
  useEffect(() => {
    function tick() {
      savedCallback.current?.();
    }

    if (delay !== null) {
      let id = setInterval(tick, delay);
      return () => clearInterval(id);
    }
  }, [delay]);
}
