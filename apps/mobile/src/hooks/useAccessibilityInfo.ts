import { AccessibilityInfo } from "react-native";
import { create } from "zustand";

type AccessibilityInfoState = {
  reducedTransparency: boolean;
};

export const useAccessibilityInfo = create<AccessibilityInfoState>((set) => {
  return { reducedTransparency: false };
});

AccessibilityInfo.isReduceTransparencyEnabled().then((enabled) => {
  useAccessibilityInfo.setState({ reducedTransparency: enabled });
});

AccessibilityInfo.addEventListener("reduceTransparencyChanged", (enabled) => {
  useAccessibilityInfo.setState({ reducedTransparency: enabled });
});
