import { useActiveWallet } from "~/state/wallet";
import { useLazyUsdBalance } from "~/state/balances";
import { Settings } from "react-native";
import { DateTime } from "luxon";
import { appConfig } from "~/state/config";
import { DurationLike } from "luxon/src/duration";
import { create } from "zustand";

const NOTIFICATION_USD_LIMIT: number =
  appConfig.environment === "production" ? 1_000 : 10;
const NOTIFICATION_PERIOD: DurationLike =
  appConfig.environment === "production" ? { week: 1 } : { minutes: 1 };

const useLastNotifiedTimestamp = create<{
  lastNotifiedTimestamp: number;
  setLastNotifiedTimestamp: (lastNotifiedTimestamp: number) => void;
}>((set) => ({
  lastNotifiedTimestamp: Settings.get("IMPROVE_SECURITY_LAST_NOTIFIED") ?? 0,
  setLastNotifiedTimestamp: (lastNotifiedTimestamp: number) => {
    set({ lastNotifiedTimestamp });
    Settings.set({ IMPROVE_SECURITY_LAST_NOTIFIED: lastNotifiedTimestamp });
  },
}));

export function useLazySecurityWarning() {
  const { wallet } = useActiveWallet();
  const { lastNotifiedTimestamp } = useLastNotifiedTimestamp();
  const usdBalanceResponse = useLazyUsdBalance({
    address: wallet.defaultVault,
  });

  if (!usdBalanceResponse.data) {
    return { showSecurityWarning: false, exceedUsdLimit: false };
  }

  const usdcBalance = usdBalanceResponse.data;
  const lastNotificationExpired =
    DateTime.now() >
    DateTime.fromSeconds(lastNotifiedTimestamp).plus(NOTIFICATION_PERIOD);

  const exceedUsdLimit = usdcBalance >= NOTIFICATION_USD_LIMIT;
  const showSecurityNotification =
    wallet.keys.recoveryKeys === null &&
    (exceedUsdLimit || lastNotificationExpired);

  return { showSecurityWarning: showSecurityNotification, exceedUsdLimit };
}

export function postponeSecurityWarning() {
  useLastNotifiedTimestamp
    .getState()
    .setLastNotifiedTimestamp(DateTime.now().toSeconds());
}
