import { Component, type JSX } from "react";
import {
  Dimensions,
  Platform,
  SafeAreaView,
  StyleSheet,
  ViewStyle,
} from "react-native";
import { FullWindowOverlay } from "react-native-screens";
import Toast, { ToastOptions, ToastProps } from "./toast";
import { View } from "~/components/Themed";

const { height, width } = Dimensions.get("window");

export interface Props extends ToastOptions {
  renderToast?(toast: ToastProps): JSX.Element;
  renderType?: { [type: string]: (toast: ToastProps) => JSX.Element };
  offset?: number;
  offsetTop?: number;
  offsetBottom?: number;
  swipeEnabled?: boolean;
}

interface State {
  toasts: Array<ToastProps>;
}

class ToastContainer extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      toasts: [],
    };
  }
  /**
   * Shows a new toast. Returns id
   */
  show = (message: string | JSX.Element, toastOptions?: ToastOptions) => {
    const id = toastOptions?.id || Math.random().toString();
    const onDestroy = () => {
      toastOptions?.onClose && toastOptions?.onClose();
      this.setState({ toasts: this.state.toasts.filter((t) => t.id !== id) });
    };

    requestAnimationFrame(() => {
      const shouldUpdate = this.state.toasts.some((t) => t.id === id);

      const nextToasts = shouldUpdate
        ? this.state.toasts.map((toast) =>
            toast.id === id
              ? { ...toast, message, ...toastOptions, updatedAt: Date.now() }
              : toast
          )
        : [
            {
              id,
              updatedAt: Date.now(),
              onDestroy,
              message,
              open: true,
              onHide: () => this.hide(id),
              ...this.props,
              ...toastOptions,
            },
            ...this.state.toasts.filter((t) => t.open),
          ];

      this.setState({
        toasts: nextToasts,
      });
    });

    return id;
  };

  /**
   * Updates a toast, To use this create you must pass an id to show method first, then pass it here to update the toast.
   */
  update = (
    id: string,
    message: string | JSX.Element,
    toastOptions?: ToastOptions
  ) => {
    this.setState({
      toasts: this.state.toasts.map((toast) =>
        toast.id === id
          ? { ...toast, message, ...toastOptions, updatedAt: Date.now() }
          : toast
      ),
    });
  };

  /**
   * Removes a toast from stack
   */
  hide = (id: string) => {
    this.setState({
      toasts: this.state.toasts.map((t) =>
        t.id === id ? { ...t, open: false } : t
      ),
    });
  };

  /**
   * Removes all toasts in stack
   */
  hideAll = () => {
    this.setState({
      toasts: this.state.toasts.map((t) => ({ ...t, open: false })),
    });
  };

  /**
   * Check if a toast is currently open
   */
  isOpen = (id: string) => {
    return this.state.toasts.some((t) => t.id === id && t.open);
  };

  /*
   * Only top toasts are supported for now
   * */
  renderTopToasts() {
    const { toasts } = this.state;
    const { offset, offsetTop } = this.props;
    const style: ViewStyle = {
      top: offsetTop || offset,
      width: width,
      justifyContent: "flex-start",
      flexDirection: "column-reverse",
    };
    return (
      <View style={[styles.container, style]} pointerEvents="box-none">
        <SafeAreaView style={styles.stack} pointerEvents={"box-none"}>
          {toasts
            .filter((t) => t.placement === "top")
            .map((toast) => (
              <Toast key={toast.id} {...toast} />
            ))}
        </SafeAreaView>
      </View>
    );
  }

  render() {
    return (
      <FullWindowOverlay>
        <View pointerEvents={"box-none"}>{this.renderTopToasts()}</View>
      </FullWindowOverlay>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 0,
    // @ts-ignore: fixed is available on web.
    position: Platform.OS === "web" ? "fixed" : "absolute",
    maxWidth: "100%",
    zIndex: 999999,
    elevation: 999999,
    alignSelf: "center",
    ...(Platform.OS === "web"
      ? { overflow: "hidden", userSelect: "none" }
      : null),
  },
  stack: {
    gap: 6,
  },
  message: {
    color: "#333",
  },
});

export default ToastContainer;
