import { FC, isValidElement, useEffect, useRef, type JSX } from "react";
import {
  StyleProp,
  StyleSheet,
  Text,
  TextStyle,
  View,
  ViewStyle,
} from "react-native";
import Animated, {
  Extrapolation,
  interpolate,
  ReduceMotion,
  runOnJS,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withSpring,
  withTiming,
  withSequence,
} from "react-native-reanimated";
import { useDimensions } from "./utils/useDimensions";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import { snapPoint } from "~/utils/animations";
import { SPRING } from "~/constants/animations";

export interface ToastOptions {
  /**
   * Id is optional, you may provide an id only if you want to update toast later using toast.update()
   */
  id?: string;

  /**
   * Customize toast icon
   */
  icon?: JSX.Element;

  /**
   * Toast types, You can implement your custom types with JSX using renderType method on ToastContainer.
   */
  type?: "normal" | "success" | "danger" | "warning" | string;

  /**
   * In ms, How long toast will stay before it go away
   */
  duration?: number;

  /**
   * Customize when toast should be placed
   */
  placement?: "top";
  // | "bottom" | "center";

  /**
   * Customize style of toast
   */
  style?: StyleProp<ViewStyle>;

  /**
   * Customize style of toast text
   */
  textStyle?: StyleProp<TextStyle>;

  /**
   * Customize how fast toast will show and hide
   */
  animationDuration?: number;

  /**
   * Customize how toast is animated when added or removed
   */
  animationType?: "slide-in" | "zoom-in";

  /**
   * Customize success type icon
   */
  successIcon?: JSX.Element;

  /**
   * Customize danger type icon
   */
  dangerIcon?: JSX.Element;

  /**
   * Customize warning type icon
   */
  warningIcon?: JSX.Element;

  /**
   * Customize success type color. changes toast background color
   */
  successColor?: string;

  /**
   * Customize danger type color. changes toast background color
   */
  dangerColor?: string;

  /**
   * Customize warning type color. changes toast background color
   */
  warningColor?: string;

  /**
   * Customize normal type color. changes toast background color
   */
  normalColor?: string;

  /**
   * Register event for when toast is pressed. If you're using a custom toast you have to pass this to a Touchable.
   */
  onPress?(id: string): void;

  /**
   * Execute event after toast is closed
   */
  onClose?(): void;

  /**
   * Payload data for custom toasts. You can pass whatever you want
   */
  data?: any;

  swipeEnabled?: boolean;

  /**
   * If true, toast will use opaque background instead of the blurred one.
   */
  opaque?: boolean;
}

export interface ToastProps extends ToastOptions {
  id: string;
  /** Used to reset auto-close timeout on update */
  updatedAt: number;
  onDestroy(): void;
  message: string | JSX.Element;
  open: boolean;
  renderToast?(toast: ToastProps): JSX.Element;
  renderType?: { [type: string]: (toast: ToastProps) => JSX.Element };
  onHide(): void;
}

const Toast: FC<ToastProps> = (props) => {
  let {
    id,
    updatedAt,
    onDestroy,
    icon,
    type = "normal",
    message,
    duration = 5000,
    style,
    textStyle,
    animationDuration = 250,
    animationType = "slide-in",
    successIcon,
    dangerIcon,
    warningIcon,
    successColor,
    dangerColor,
    warningColor,
    normalColor,
    placement,
    swipeEnabled,
    onPress,
  } = props;

  const progress = useSharedValue(0);
  const swipeTranslateY = useSharedValue(0);
  const closeTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const dims = useDimensions();

  useEffect(() => {
    progress.value = withTiming(1, {
      duration: animationDuration,
    });

    // TODO: pause the close timeout when the toast is interacted with
    if (duration !== 0 && typeof duration === "number") {
      closeTimeoutRef.current = setTimeout(() => {
        handleClose();
      }, duration);
    }

    return () => {
      closeTimeoutRef.current && clearTimeout(closeTimeoutRef.current);
    };
  }, [duration, updatedAt]);

  // Handles hide & hideAll
  useEffect(() => {
    if (!props.open) {
      // Unregister close timeout
      closeTimeoutRef.current && clearTimeout(closeTimeoutRef.current);

      // Close progress them remove from stack.
      handleClose();
    }
  }, [props.open]);

  const handleClose = () => {
    progress.value = withSequence(
      // Anticipation
      withTiming(1.1, {
        duration: 150,
      }),
      // Animation
      withTiming(
        0,
        {
          duration: animationDuration,
        },
        () => {
          "worklet";
          runOnJS(onDestroy)();
        }
      )
    );
  };

  const swipeOpacity = useDerivedValue(() => {
    return interpolate(
      swipeTranslateY.value,
      [-60, 0],
      [0, 1],
      Extrapolation.CLAMP
    );
  });

  const panGesture = Gesture.Pan()
    .onUpdate((event) => {
      swipeTranslateY.value = interpolate(
        event.translationY,
        [0, 2000],
        [0, 100],
        Extrapolation.IDENTITY
      );
    })
    .onEnd((event) => {
      const destinationPoint = snapPoint(
        event.translationY,
        event.velocityY,
        [-60, 0]
      );

      if (destinationPoint !== 0) {
        swipeTranslateY.value = withSpring(
          destinationPoint,
          {
            ...SPRING,
            velocity: event.velocityY / 2,
            reduceMotion: ReduceMotion.Never,
          },
          () => {
            runOnJS(onDestroy)();
          }
        );
      } else {
        swipeTranslateY.value = withSpring(0, SPRING);
      }
    });

  const tapScale = useSharedValue(1);
  const tapGesture = Gesture.Tap()
    .onBegin(() => {
      tapScale.value = withSpring(0.95, SPRING);
    })
    .onEnd(() => {
      tapScale.value = withSpring(1, SPRING);
      onPress && runOnJS(onPress)(id);
    });

  if (icon === undefined) {
    switch (type) {
      case "success": {
        if (successIcon) {
          icon = successIcon;
        }
        break;
      }

      case "danger": {
        if (dangerIcon) {
          icon = dangerIcon;
        }
        break;
      }
      case "warning": {
        if (warningIcon) {
          icon = warningIcon;
        }
        break;
      }
    }
  }

  let backgroundColor = "";
  switch (type) {
    case "success":
      backgroundColor = successColor || "rgb(46, 125, 50)";
      break;
    case "danger":
      backgroundColor = dangerColor || "rgb(211, 47, 47)";
      break;
    case "warning":
      backgroundColor = warningColor || "rgb(237, 108, 2)";
      break;
    default:
      backgroundColor = normalColor || "#333";
  }

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity:
        swipeTranslateY.value !== 0 ? swipeOpacity.value : progress.value,
      transform: [
        {
          translateY:
            interpolate(
              progress.value,
              [0, 1],
              // placement === "bottom" ? [20, 0] : [-20, 0] // 0 : 150, 0.5 : 75, 1 : 0)
              [-20, 0] // 0 : 150, 0.5 : 75, 1 : 0)
            ) + swipeTranslateY.value,
        },
        {
          scale: tapScale.value,
        },
      ],
    };
  });

  return (
    <Animated.View
      pointerEvents="box-none"
      style={[styles.container, animatedStyle]}
    >
      <GestureDetector gesture={Gesture.Simultaneous(panGesture, tapGesture)}>
        {props.renderType && props.renderType[type] ? (
          props.renderType[type](props)
        ) : props.renderToast ? (
          props.renderToast(props)
        ) : (
          <View
            style={[
              styles.toastContainer,
              { maxWidth: (dims.width / 10) * 9, backgroundColor },
              style,
            ]}
          >
            {icon ? <View style={styles.iconContainer}>{icon}</View> : null}
            {isValidElement(message) ? (
              message
            ) : (
              <Text style={[styles.message, textStyle]}>{message}</Text>
            )}
          </View>
        )}
      </GestureDetector>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
    alignItems: "center",
  },
  toastContainer: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRadius: 5,
    marginVertical: 5,
    flexDirection: "row",
    alignItems: "center",
    overflow: "hidden",
  },
  message: {
    color: "#fff",
    fontWeight: "500",
  },
  iconContainer: {
    marginRight: 5,
  },
});

export default Toast;
