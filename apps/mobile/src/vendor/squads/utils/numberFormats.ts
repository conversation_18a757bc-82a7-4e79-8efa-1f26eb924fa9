export function formatCurrency(currency: string) {
  return new Intl.NumberFormat("en", {
    style: "currency",
    currency,
  })
    .format(0)
    .replace("0.00", "");
}

export function getMaximumFractionDigits(totalAmount: number) {
  if (totalAmount === 0) {
    return 2;
  }

  return totalAmount < 1
    ? Math.max(-Math.floor(Math.log10(Math.abs(totalAmount))), 5)
    : Math.max(4 - Math.floor(Math.log10(Math.abs(totalAmount))), 0);
}

export function formatFiatValue(
  value: number,
  options?: {
    currency?: "EUR" | "USD" | string;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number | "auto";
  }
) {
  return new Intl.NumberFormat("en", {
    maximumFractionDigits:
      options?.maximumFractionDigits === "auto"
        ? getMaximumFractionDigits(value)
        : (options?.maximumFractionDigits ?? 2),
    minimumFractionDigits: options?.minimumFractionDigits,
    notation: "standard",
    style: options?.currency ? "currency" : undefined,
    currency: options?.currency,
  }).format(value);
}

export function formatUsdValue(
  value: number,
  options?: {
    renderCurrency?: boolean;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number | "auto";
  }
) {
  const currency = options?.renderCurrency !== false ? "USD" : undefined;

  return formatFiatValue(value, {
    currency,
    minimumFractionDigits: options?.minimumFractionDigits,
    maximumFractionDigits: options?.maximumFractionDigits,
  });
}

export function formatFiatValueWorklet(
  value: number,
  options?: {
    currency?: "EUR" | "USD" | string;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
  }
) {
  "worklet";
  return new Intl.NumberFormat("en", {
    maximumFractionDigits: options?.maximumFractionDigits ?? 2,
    minimumFractionDigits: options?.minimumFractionDigits,
    notation: "standard",
    style: options?.currency ? "currency" : undefined,
    currency: options?.currency,
  }).format(value);
}

export function formatUsdValueWorklet(
  value: number,
  options?: {
    renderCurrency?: boolean;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
  }
) {
  "worklet";
  const currency = options?.renderCurrency !== false ? "USD" : undefined;

  return formatFiatValueWorklet(value, {
    currency,
    minimumFractionDigits: options?.minimumFractionDigits,
    maximumFractionDigits:
      options?.maximumFractionDigits ?? options?.minimumFractionDigits,
  });
}

export function abbreviateAmount(
  value: number,
  { type, decimals = 2 }: { type: "usd" | "percentage"; decimals?: number }
) {
  const absValue = Math.abs(value);
  if (absValue < 1000) {
    return type === "percentage"
      ? absValue.toFixed(decimals)
      : formatUsdValue(absValue, {
          renderCurrency: false,
          minimumFractionDigits: decimals,
        });
  } else if (absValue < 1_000_000) {
    return `${(absValue / 1_000).toFixed(decimals)}K`;
  } else if (absValue < 1_000_000_000) {
    return `${(absValue / 1_000_000).toFixed(decimals)}M`;
  } else if (absValue < 1_000_000_000_000) {
    return `${(absValue / 1_000_000_000).toFixed(decimals)}B`;
  } else if (absValue < 1_000_000_000_000_000) {
    return `${(absValue / 1_000_000_000_000).toFixed(decimals)}T`;
  } else {
    return `${(absValue / 1_000_000_000_000_000).toFixed(decimals)}Q`;
  }
}

export function formatTokenAmount(
  value: number,
  symbol: string | undefined,
  options?: {
    maximumFractionDigits?: number;
    minimumFractionDigits?: number;
    roundingMode?: "floor" | "ceil";
    filterSmallAmounts?: boolean;
  }
) {
  const shouldFilterSmallAmount = options?.filterSmallAmounts ?? false;
  if (shouldFilterSmallAmount && Math.abs(value) < 0.001) {
    return `<0.001${symbol ? "\xa0" + symbol : ""}`;
  }

  const defaultMaximumFractionDigits =
    value === 0
      ? 2
      : value > 0 && value < 1
        ? Math.ceil(Math.log10(1 / value)) + 2
        : Math.max(4 - Math.floor(Math.log10(value)), 2);

  const maximumFractionDigits =
    options?.maximumFractionDigits ?? defaultMaximumFractionDigits;

  let roundedValue =
    options?.roundingMode === "floor"
      ? Math.floor(value * 10 ** maximumFractionDigits) /
        10 ** maximumFractionDigits
      : options?.roundingMode === "ceil"
        ? Math.ceil(value * 10 ** maximumFractionDigits) /
          10 ** maximumFractionDigits
        : value;

  return `${tokenAmountFormatter({
    maximumFractionDigits,
    minimumFractionDigits: options?.minimumFractionDigits,
  }).format(roundedValue)}${symbol ? "\xa0" + symbol : ""}`;
}

function tokenAmountFormatter(options?: {
  maximumFractionDigits: number;
  minimumFractionDigits?: number;
}) {
  return new Intl.NumberFormat("en", {
    maximumFractionDigits: options?.maximumFractionDigits ?? 5,
    minimumFractionDigits: options?.minimumFractionDigits ?? 0,
    notation: "standard",
    style: "decimal",
  });
}

export function formatPercent(value: number) {
  return new Intl.NumberFormat("en", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value * 100);
}

export function formatAmountInputWithSeparators(input: string) {
  const [integer, decimal] = input.split(".");
  const formattedInteger = new Intl.NumberFormat("en", {
    maximumFractionDigits: 0,
  }).format(parseInt(integer || "0", 10));

  return decimal !== undefined
    ? `${formattedInteger}.${decimal}`
    : formattedInteger;
}

export function formatAmountInputWithSeparatorsWorklet(input: string) {
  "worklet";
  const [integer, decimal] = input.split(".");
  const formattedInteger = new Intl.NumberFormat("en", {
    maximumFractionDigits: 0,
  }).format(parseInt(integer || "0", 10));

  return decimal !== undefined
    ? `${formattedInteger}.${decimal}`
    : formattedInteger;
}

export function formatEpoch(secondsRemaining:number) {
    return  secondsRemaining > 60 * 60 * 24 ? "d'd' hh'h' mm'm'" : "hh'h' mm'm'"; 
}
