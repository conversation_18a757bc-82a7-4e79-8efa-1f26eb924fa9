import { base58 } from "@squads/utils/encoding";
import { z } from "zod";
import { Base58Z } from "./encoding";

const MESSAGE_SECRET_KEY_IS_NOT_ARRAY = "SecretKey is not a JSON array";
const MESSAGE_SECRET_KEY_MUST_BE_64_BYTES =
  "SecretKey must consist of 64 numbers";

/** Solana account address. */
export const AddressZ = Base58Z({
  regex_error: "Invalid Solana address",
}).refine(
  (value) => {
    try {
      return base58.decode(value).length === 32;
    } catch {
      return false;
    }
  },
  { message: "Invalid Solana address" }
);
export type Address = z.infer<typeof AddressZ>;

export const PublicKeyZ = z
  .instanceof(Uint8Array)
  .refine((value) => value.length === 32, {
    message: "PublicKey must be 32 bytes",
  });
export type PublicKey = z.infer<typeof PublicKeyZ>;

/** Solana transaction signature (id). */
export const TransactionSignatureZ = Base58Z();
export type TransactionSignature = z.infer<typeof TransactionSignatureZ>;

export const SerializedTransactionZ = Base58Z();
export type SerializedTransaction = z.infer<typeof SerializedTransactionZ>;

/** Solana secret keypair stored as a Base58 string. */
export const SecretKeyBase58Z = Base58Z();
export type SecretKeyBase58 = z.infer<typeof SecretKeyBase58Z>;

/** Solana secret keypair stored as a Base58 string. */
export const SecretKeyJsonStringZ = z
  .string()
  .transform((value, ctx) => {
    let parsedJson: unknown;

    // Check if the value is a valid JSON.
    try {
      parsedJson = JSON.parse(value);
    } catch {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: MESSAGE_SECRET_KEY_IS_NOT_ARRAY,
      });
      return [];
    }

    // Check that the parsed JSON is an array.
    if (!Array.isArray(parsedJson)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: MESSAGE_SECRET_KEY_IS_NOT_ARRAY,
      });
      return [];
    }

    //Check that the array is 64 bytes long.
    if (parsedJson.length !== 64) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: MESSAGE_SECRET_KEY_MUST_BE_64_BYTES,
      });
      return [];
    }

    // Check that all array elements are numbers.
    if (!parsedJson.every((n) => typeof n === "number")) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: MESSAGE_SECRET_KEY_MUST_BE_64_BYTES,
      });
      return [];
    }

    return parsedJson as number[];
  })
  .brand<"SecretKeyJsonString">();
export type SecretKeyJsonString = z.infer<typeof SecretKeyJsonStringZ>;

/** Solana secret keypair. */
export const SecretKeyZ = z.union([SecretKeyBase58Z, SecretKeyJsonStringZ]);
export type SecretKey = z.infer<typeof SecretKeyZ>;
