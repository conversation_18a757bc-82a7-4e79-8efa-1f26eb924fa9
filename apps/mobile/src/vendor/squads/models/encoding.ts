import { z } from "zod";

/**
 *  Base58-encoded string.
 */
export const Base58Z = ({
  invalid_type_error,
  required_error,
  regex_error = "The string is not a Base58",
}: {
  invalid_type_error?: string | undefined;
  required_error?: string | undefined;
  regex_error?: string | undefined;
} = {}) =>
  z
    .string({ invalid_type_error, required_error })
    .regex(/^[1-9A-HJ-NP-Za-km-z]+$/, regex_error)
    .brand<"Base58">();

export type Base58 = z.infer<ReturnType<typeof Base58Z>>;

/** Branded string type that represents some hashed content. */
export const HashZ = z.string().brand<"HashZ">();
export type Hash = z.infer<typeof HashZ>;
