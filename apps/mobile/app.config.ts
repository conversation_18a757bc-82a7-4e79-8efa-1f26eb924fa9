import { EASConfig, ExpoConfig } from "expo/config";
import packageJson from "./package.json";

type Environment = "development" | "staging" | "production";

type AppConfig = {
  name: string;
  slug: string;
  icon: string;
  environment: Environment;
  identifier: string;
  easProjectId: string;
  apiUrl: string;
  associatedDomain: string;
  icloud: {
    environment: "Development" | "Production";
    containerId: string;
  };
  magicLink: {
    recovery: {
      publicKey: string;
    };
  };
  fuseWebUrl: string;
};

const development: AppConfig = {
  name: "Fuse (development)",
  slug: "fuse",
  icon: "./assets/images/icon-secondary.png",
  environment: "development",
  identifier: "com.fusewallet.development",
  easProjectId: "0fbb4fde-3d35-4250-910d-2a7f0fe525fd",
  associatedDomain: "fuse-invite-staging.vercel.app",
  apiUrl: process.env.EXPO_PUBLIC_LOCAL_API_URL ?? "http://localhost:4444",
  icloud: {
    environment: "Development",
    containerId: "iCloud.com.fusewallet.development",
  },
  magicLink: {
    recovery: {
      publicKey: "pk_live_198BD780745376BD",
    },
  },
  fuseWebUrl: "http://localhost:3000",
};

const staging: AppConfig = {
  name: "Fuse (staging)",
  slug: "fuse",
  icon: "./assets/images/icon.png",
  environment: "staging",
  identifier: "com.fusewallet.staging",
  easProjectId: "0fbb4fde-3d35-4250-910d-2a7f0fe525fd",
  associatedDomain: "fuse-invite-staging.vercel.app",
  apiUrl: "https://staging-mobile-api-app-kj5ch.ondigitalocean.app",
  icloud: {
    environment: "Development",
    containerId: "iCloud.com.fusewallet.staging",
  },
  magicLink: {
    recovery: {
      publicKey: "pk_live_198BD780745376BD",
    },
  },
  fuseWebUrl: "https://fuse-staging.vercel.app",
};

const production: AppConfig = {
  name: "Fuse",
  slug: "fuse",
  icon: "./assets/images/icon.png",
  environment: "production",
  identifier: "com.fusewallet",
  easProjectId: "0fbb4fde-3d35-4250-910d-2a7f0fe525fd",
  associatedDomain: "invite.fusewallet.com",
  apiUrl: "https://mobile-api-app-utadw.ondigitalocean.app",
  icloud: {
    environment: "Production",
    containerId: "iCloud.com.fusewallet",
  },
  magicLink: {
    recovery: {
      publicKey: "pk_live_198BD780745376BD",
    },
  },
  fuseWebUrl: "https://recovery.fusewallet.com",
};

function getAppConfig(environment: Environment): AppConfig {
  switch (environment) {
    case "development":
      return development;
    case "staging":
      return staging;
    case "production":
      return production;
    default:
      throw new Error("Invalid ENVIRONMENT");
  }
}

type ExpoExtras = {
  extra: {
    eas: EASConfig;
    appConfig: AppConfig;
  };
};

export default (): ExpoConfig & ExpoExtras => {
  const environment: Environment =
    (process.env.ENVIRONMENT as Environment) ?? "development";
  const appConfig = getAppConfig(environment);

  return {
    name: appConfig.name,
    slug: appConfig.slug,
    version: environment === "staging" ? "staging" : packageJson.version,
    owner: "squadslabs",
    orientation: "portrait",
    icon: appConfig.icon,
    scheme: "fuse",
    userInterfaceStyle: "automatic",
    newArchEnabled: false,
    platforms: ["ios"],
    runtimeVersion: {
      policy: "appVersion",
    },
    assetBundlePatterns: ["**/*"],

    ios: {
      associatedDomains: [`applinks:${appConfig.associatedDomain}`],
      infoPlist: {
        UIViewControllerBasedStatusBarAppearance: true,
        RCTAsyncStorageExcludeFromBackup: false,
        NSLocationWhenInUseUsageDescription:
          "Persona uses location to verify identity and enhance security during onboarding.",
      },
      config: {
        usesNonExemptEncryption: false,
      },
      bundleIdentifier: appConfig.identifier,
      appleTeamId: "QT87TNR2WX",
      entitlements: {
        "com.apple.developer.icloud-container-environment":
          appConfig.icloud.environment,
        "com.apple.developer.icloud-container-identifiers": [
          appConfig.icloud.containerId,
        ],
        "com.apple.developer.icloud-services": ["CloudKit"],
        "com.apple.developer.payment-pass-provisioning": true,
      },
    },
    updates: { url: "https://u.expo.dev/" + appConfig.easProjectId },
    extra: {
      appConfig,
      eas: { projectId: appConfig.easProjectId },
    },
    plugins: [
      "expo-router",
      "expo-secure-store",
      [
        "react-native-ble-plx",
        {
          bluetoothAlwaysPermission:
            "Fuse requires the use of Bluetooth to connect to hardware devices for signing and recovery of your wallet",
        },
      ],
      "expo-font",
      [
        "expo-local-authentication",
        {
          faceIDPermission:
            "$(PRODUCT_NAME) uses Face ID to secure your private key.",
        },
      ],
      [
        "expo-splash-screen",
        {
          backgroundColor: "#000000",
          image: "./assets/images/splash.png",
          imageWidth: 180,
        },
      ],
      [
        "react-native-vision-camera",
        {
          cameraPermissionText: "Allow $(PRODUCT_NAME) to access your camera",
          enableCodeScanner: true,
          enableMicrophonePermission: false,
          enableLocation: false,
        },
      ],
      [
        "expo-build-properties",
        {
          ios: {
            deploymentTarget: "16.6",
            extraPods: [{ name: "TweetNacl", version: "~> 1.0.2" }],
          },
        },
      ],
      [
        "expo-alternate-app-icons",
        [
          "./assets/images/icon.png",
          "./assets/images/icon-secondary.png",
          "./assets/images/app-icon-plus.png",
        ],
      ],
      //todo make optional
      [
        "@sentry/react-native/expo",
        {
          organization: "squads-dapp",
          project: "fuse-mobile",
        },
      ],
      [
        "expo-notifications",
        {
          enableBackgroundRemoteNotifications: true,
        },
      ],
    ],
    experiments: {
      tsconfigPaths: true,
      typedRoutes: true,
      buildCacheProvider: "eas",
    },
  };
};
